# Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
extends:
  - recommended-strict

apis:
  mobile@0:
    root: specs/openapi/mobile/api.yaml
  sme@1:
    root: specs/openapi/sme/api.yaml
  paymentsgw@2:
    root: specs/openapi/paymentsgw/api.yaml
  balancegw@3:
    root: specs/openapi/balancegw/api.yaml
  documentsgw@4:
    root: specs/openapi/documentsgw/api.yaml
  crmgw@5:
    root: specs/openapi/crmgw/api.yaml
  collectiongw@6:
    root: specs/openapi/collectiongw/api.yaml
  landing@7:
    root: specs/openapi/landing/api.yaml

# https://redocly.com/docs/redoc/config
theme:
  openapi:
    theme:
      colors:
        primary:
          main: '#660099'

rules:
  info-license: off
  tag-description: off
  operation-4xx-response: off
  no-required-schema-properties-undefined: error
  operation-description: error
  operation-operationId: error
  operation-tag-defined: error
  paths-kebab-case: error
  security-defined: error
  boolean-parameter-prefixes:
    severity: error
    prefixes: [ "can", "is", "has" ]
  component-name-unique:
    severity: error
    schemas: error
    parameters: off
    responses: warn
    requestBodies: warn

  # custom section
  # для порядка
  rule/description-capitalized:
    subject:
      type: any
      property: description
    assertions:
      pattern: '^([|>][ \t]*)?(<br>)?[A-ZА-Я]'
    message: 'Description must start with a capital letter (A-Z, А-Я).'
  rule/summary-capitalized:
    subject:
      type: any
      property: summary
    assertions:
      pattern: '^[A-ZА-Я]'
    message: 'Summary must start with a capital letter (A-Z, А-Я).'
  rule/required-items-in-array-schemas:
    subject:
      type: Schema
    assertions:
      required:
        - items
    where:
      - subject:
          type: Schema
          property: type
        assertions:
          const: array
          defined: true
    message: The 'items' field is required for schemas of array type.
  # для красоты отображения в отрендеренных спецификациях
  rule/operation-summary-max-length:
    subject:
      type: Operation
      property: summary
    message: Operation summary must have a maximum of 60 characters.
    assertions:
      maxLength: 60