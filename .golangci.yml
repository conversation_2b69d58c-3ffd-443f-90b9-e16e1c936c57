# Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
issues:
  exclude-files:
    - ".*_gen\\.go"
    - ".*_test\\.go"
    - "services/.*/.*_hook\\.go$"
    - ".*\\.gen\\.go$"
  exclude-dirs-use-default: false
  exclude-dirs:
    - "services/.*/storage/postgres/ent"
    - "services/.*/storage/postgres/schema"
    - "errs"
    - ".*mock.*"

linters-settings:
  funlen:
    lines: 200
    statements: 75
  dupl:
    threshold: 700
  goconst:
    min-occurrences: 4
  exhaustive:
    default-signifies-exhaustive: true

linters:
  disable-all: true
  enable:
    - dogsled
    - dupl
    - errcheck
    - exhaustive
    - funlen
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - rowserrcheck
    - staticcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - whitespace