// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

type Health struct {
	ent.Schema
}

func (Health) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default((func() uuid.UUID)(uuid.New)),
	}
}

func (Health) Annotations() []schema.Annotation {
	return []schema.Annotation{}
}
