package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/mixin"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/entity"
)

type BankStatement struct {
	ent.Schema
}

func (BankStatement) Fields() []ent.Field {
	return []ent.Field{
		field.UUID(
			"alt_score_uuid", uuid.UUID{},
		).Annotations(annotation.CustomAnnotation{Description: "uuid документа из бд documents"}),
		field.Bool("is_discrepancy").Annotations(annotation.CustomAnnotation{Description: "Расхождение в пределах до 5000"}),
		field.JSON(
			"user_detail", entity.UserDetail{},
		).Annotations(annotation.CustomAnnotation{Description: "Данные клиента"}),
		field.JSON(
			"transactions", []entity.Transaction{},
		).Annotations(annotation.CustomAnnotation{Description: "Транзакции"}),
	}
}

func (BankStatement) Annotations() []schema.Annotation {
	return []schema.Annotation{}
}

func (BankStatement) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
