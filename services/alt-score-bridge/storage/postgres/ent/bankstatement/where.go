// Code generated by ent, DO NOT EDIT.

package bankstatement

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldUpdateTime, v))
}

// AltScoreUUID applies equality check predicate on the "alt_score_uuid" field. It's identical to AltScoreUUIDEQ.
func AltScoreUUID(v uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldAltScoreUUID, v))
}

// IsDiscrepancy applies equality check predicate on the "is_discrepancy" field. It's identical to IsDiscrepancyEQ.
func IsDiscrepancy(v bool) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldIsDiscrepancy, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldLTE(FieldUpdateTime, v))
}

// AltScoreUUIDEQ applies the EQ predicate on the "alt_score_uuid" field.
func AltScoreUUIDEQ(v uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldAltScoreUUID, v))
}

// AltScoreUUIDNEQ applies the NEQ predicate on the "alt_score_uuid" field.
func AltScoreUUIDNEQ(v uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNEQ(FieldAltScoreUUID, v))
}

// AltScoreUUIDIn applies the In predicate on the "alt_score_uuid" field.
func AltScoreUUIDIn(vs ...uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldIn(FieldAltScoreUUID, vs...))
}

// AltScoreUUIDNotIn applies the NotIn predicate on the "alt_score_uuid" field.
func AltScoreUUIDNotIn(vs ...uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNotIn(FieldAltScoreUUID, vs...))
}

// AltScoreUUIDGT applies the GT predicate on the "alt_score_uuid" field.
func AltScoreUUIDGT(v uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldGT(FieldAltScoreUUID, v))
}

// AltScoreUUIDGTE applies the GTE predicate on the "alt_score_uuid" field.
func AltScoreUUIDGTE(v uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldGTE(FieldAltScoreUUID, v))
}

// AltScoreUUIDLT applies the LT predicate on the "alt_score_uuid" field.
func AltScoreUUIDLT(v uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldLT(FieldAltScoreUUID, v))
}

// AltScoreUUIDLTE applies the LTE predicate on the "alt_score_uuid" field.
func AltScoreUUIDLTE(v uuid.UUID) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldLTE(FieldAltScoreUUID, v))
}

// IsDiscrepancyEQ applies the EQ predicate on the "is_discrepancy" field.
func IsDiscrepancyEQ(v bool) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldEQ(FieldIsDiscrepancy, v))
}

// IsDiscrepancyNEQ applies the NEQ predicate on the "is_discrepancy" field.
func IsDiscrepancyNEQ(v bool) predicate.BankStatement {
	return predicate.BankStatement(sql.FieldNEQ(FieldIsDiscrepancy, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.BankStatement) predicate.BankStatement {
	return predicate.BankStatement(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.BankStatement) predicate.BankStatement {
	return predicate.BankStatement(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.BankStatement) predicate.BankStatement {
	return predicate.BankStatement(sql.NotPredicates(p))
}
