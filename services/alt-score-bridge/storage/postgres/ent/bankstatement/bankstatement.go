// Code generated by ent, DO NOT EDIT.

package bankstatement

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the bankstatement type in the database.
	Label = "bank_statement"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldAltScoreUUID holds the string denoting the alt_score_uuid field in the database.
	FieldAltScoreUUID = "alt_score_uuid"
	// FieldIsDiscrepancy holds the string denoting the is_discrepancy field in the database.
	FieldIsDiscrepancy = "is_discrepancy"
	// FieldUserDetail holds the string denoting the user_detail field in the database.
	FieldUserDetail = "user_detail"
	// FieldTransactions holds the string denoting the transactions field in the database.
	FieldTransactions = "transactions"
	// Table holds the table name of the bankstatement in the database.
	Table = "bank_statements"
)

// Columns holds all SQL columns for bankstatement fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldAltScoreUUID,
	FieldIsDiscrepancy,
	FieldUserDetail,
	FieldTransactions,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
)

// OrderOption defines the ordering options for the BankStatement queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByAltScoreUUID orders the results by the alt_score_uuid field.
func ByAltScoreUUID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAltScoreUUID, opts...).ToFunc()
}

// ByIsDiscrepancy orders the results by the is_discrepancy field.
func ByIsDiscrepancy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsDiscrepancy, opts...).ToFunc()
}
