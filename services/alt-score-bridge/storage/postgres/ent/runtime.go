// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/bankstatement"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	bankstatementMixin := schema.BankStatement{}.Mixin()
	bankstatementMixinFields0 := bankstatementMixin[0].Fields()
	_ = bankstatementMixinFields0
	bankstatementFields := schema.BankStatement{}.Fields()
	_ = bankstatementFields
	// bankstatementDescCreateTime is the schema descriptor for create_time field.
	bankstatementDescCreateTime := bankstatementMixinFields0[0].Descriptor()
	// bankstatement.DefaultCreateTime holds the default value on creation for the create_time field.
	bankstatement.DefaultCreateTime = bankstatementDescCreateTime.Default.(func() time.Time)
	// bankstatementDescUpdateTime is the schema descriptor for update_time field.
	bankstatementDescUpdateTime := bankstatementMixinFields0[1].Descriptor()
	// bankstatement.DefaultUpdateTime holds the default value on creation for the update_time field.
	bankstatement.DefaultUpdateTime = bankstatementDescUpdateTime.Default.(func() time.Time)
	// bankstatement.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	bankstatement.UpdateDefaultUpdateTime = bankstatementDescUpdateTime.UpdateDefault.(func() time.Time)
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
}
