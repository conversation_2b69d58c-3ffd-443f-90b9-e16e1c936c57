// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/bankstatement"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/predicate"
)

// BankStatementDelete is the builder for deleting a BankStatement entity.
type BankStatementDelete struct {
	config
	hooks    []Hook
	mutation *BankStatementMutation
}

// Where appends a list predicates to the BankStatementDelete builder.
func (bsd *BankStatementDelete) Where(ps ...predicate.BankStatement) *BankStatementDelete {
	bsd.mutation.Where(ps...)
	return bsd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (bsd *BankStatementDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, bsd.sqlExec, bsd.mutation, bsd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (bsd *BankStatementDelete) ExecX(ctx context.Context) int {
	n, err := bsd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (bsd *BankStatementDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(bankstatement.Table, sqlgraph.NewFieldSpec(bankstatement.FieldID, field.TypeInt))
	if ps := bsd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, bsd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	bsd.mutation.done = true
	return affected, err
}

// BankStatementDeleteOne is the builder for deleting a single BankStatement entity.
type BankStatementDeleteOne struct {
	bsd *BankStatementDelete
}

// Where appends a list predicates to the BankStatementDelete builder.
func (bsdo *BankStatementDeleteOne) Where(ps ...predicate.BankStatement) *BankStatementDeleteOne {
	bsdo.bsd.mutation.Where(ps...)
	return bsdo
}

// Exec executes the deletion query.
func (bsdo *BankStatementDeleteOne) Exec(ctx context.Context) error {
	n, err := bsdo.bsd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{bankstatement.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (bsdo *BankStatementDeleteOne) ExecX(ctx context.Context) {
	if err := bsdo.Exec(ctx); err != nil {
		panic(err)
	}
}
