// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/bankstatement"
)

// BankStatement is the model entity for the BankStatement schema.
type BankStatement struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// AltScoreUUID holds the value of the "alt_score_uuid" field.
	AltScoreUUID uuid.UUID `json:"alt_score_uuid,omitempty"`
	// IsDiscrepancy holds the value of the "is_discrepancy" field.
	IsDiscrepancy bool `json:"is_discrepancy,omitempty"`
	// UserDetail holds the value of the "user_detail" field.
	UserDetail entity.UserDetail `json:"user_detail,omitempty"`
	// Transactions holds the value of the "transactions" field.
	Transactions []entity.Transaction `json:"transactions,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*BankStatement) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case bankstatement.FieldUserDetail, bankstatement.FieldTransactions:
			values[i] = new([]byte)
		case bankstatement.FieldIsDiscrepancy:
			values[i] = new(sql.NullBool)
		case bankstatement.FieldID:
			values[i] = new(sql.NullInt64)
		case bankstatement.FieldCreateTime, bankstatement.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case bankstatement.FieldAltScoreUUID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the BankStatement fields.
func (bs *BankStatement) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case bankstatement.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			bs.ID = int(value.Int64)
		case bankstatement.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				bs.CreateTime = value.Time
			}
		case bankstatement.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				bs.UpdateTime = value.Time
			}
		case bankstatement.FieldAltScoreUUID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field alt_score_uuid", values[i])
			} else if value != nil {
				bs.AltScoreUUID = *value
			}
		case bankstatement.FieldIsDiscrepancy:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_discrepancy", values[i])
			} else if value.Valid {
				bs.IsDiscrepancy = value.Bool
			}
		case bankstatement.FieldUserDetail:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field user_detail", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &bs.UserDetail); err != nil {
					return fmt.Errorf("unmarshal field user_detail: %w", err)
				}
			}
		case bankstatement.FieldTransactions:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field transactions", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &bs.Transactions); err != nil {
					return fmt.Errorf("unmarshal field transactions: %w", err)
				}
			}
		default:
			bs.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the BankStatement.
// This includes values selected through modifiers, order, etc.
func (bs *BankStatement) Value(name string) (ent.Value, error) {
	return bs.selectValues.Get(name)
}

// Update returns a builder for updating this BankStatement.
// Note that you need to call BankStatement.Unwrap() before calling this method if this BankStatement
// was returned from a transaction, and the transaction was committed or rolled back.
func (bs *BankStatement) Update() *BankStatementUpdateOne {
	return NewBankStatementClient(bs.config).UpdateOne(bs)
}

// Unwrap unwraps the BankStatement entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (bs *BankStatement) Unwrap() *BankStatement {
	_tx, ok := bs.config.driver.(*txDriver)
	if !ok {
		panic("ent: BankStatement is not a transactional entity")
	}
	bs.config.driver = _tx.drv
	return bs
}

// String implements the fmt.Stringer.
func (bs *BankStatement) String() string {
	var builder strings.Builder
	builder.WriteString("BankStatement(")
	builder.WriteString(fmt.Sprintf("id=%v, ", bs.ID))
	builder.WriteString("create_time=")
	builder.WriteString(bs.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(bs.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("alt_score_uuid=")
	builder.WriteString(fmt.Sprintf("%v", bs.AltScoreUUID))
	builder.WriteString(", ")
	builder.WriteString("is_discrepancy=")
	builder.WriteString(fmt.Sprintf("%v", bs.IsDiscrepancy))
	builder.WriteString(", ")
	builder.WriteString("user_detail=")
	builder.WriteString(fmt.Sprintf("%v", bs.UserDetail))
	builder.WriteString(", ")
	builder.WriteString("transactions=")
	builder.WriteString(fmt.Sprintf("%v", bs.Transactions))
	builder.WriteByte(')')
	return builder.String()
}

// BankStatements is a parsable slice of BankStatement.
type BankStatements []*BankStatement
