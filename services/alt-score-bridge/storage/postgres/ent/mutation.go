// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/bankstatement"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/predicate"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeBankStatement = "BankStatement"
	TypeHealth        = "Health"
)

// BankStatementMutation represents an operation that mutates the BankStatement nodes in the graph.
type BankStatementMutation struct {
	config
	op                 Op
	typ                string
	id                 *int
	create_time        *time.Time
	update_time        *time.Time
	alt_score_uuid     *uuid.UUID
	is_discrepancy     *bool
	user_detail        *entity.UserDetail
	transactions       *[]entity.Transaction
	appendtransactions []entity.Transaction
	clearedFields      map[string]struct{}
	done               bool
	oldValue           func(context.Context) (*BankStatement, error)
	predicates         []predicate.BankStatement
}

var _ ent.Mutation = (*BankStatementMutation)(nil)

// bankstatementOption allows management of the mutation configuration using functional options.
type bankstatementOption func(*BankStatementMutation)

// newBankStatementMutation creates new mutation for the BankStatement entity.
func newBankStatementMutation(c config, op Op, opts ...bankstatementOption) *BankStatementMutation {
	m := &BankStatementMutation{
		config:        c,
		op:            op,
		typ:           TypeBankStatement,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withBankStatementID sets the ID field of the mutation.
func withBankStatementID(id int) bankstatementOption {
	return func(m *BankStatementMutation) {
		var (
			err   error
			once  sync.Once
			value *BankStatement
		)
		m.oldValue = func(ctx context.Context) (*BankStatement, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().BankStatement.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withBankStatement sets the old BankStatement of the mutation.
func withBankStatement(node *BankStatement) bankstatementOption {
	return func(m *BankStatementMutation) {
		m.oldValue = func(context.Context) (*BankStatement, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m BankStatementMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m BankStatementMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *BankStatementMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *BankStatementMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().BankStatement.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *BankStatementMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *BankStatementMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the BankStatement entity.
// If the BankStatement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BankStatementMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *BankStatementMutation) ResetCreateTime() {
	m.create_time = nil
}

// SetUpdateTime sets the "update_time" field.
func (m *BankStatementMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *BankStatementMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the BankStatement entity.
// If the BankStatement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BankStatementMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *BankStatementMutation) ResetUpdateTime() {
	m.update_time = nil
}

// SetAltScoreUUID sets the "alt_score_uuid" field.
func (m *BankStatementMutation) SetAltScoreUUID(u uuid.UUID) {
	m.alt_score_uuid = &u
}

// AltScoreUUID returns the value of the "alt_score_uuid" field in the mutation.
func (m *BankStatementMutation) AltScoreUUID() (r uuid.UUID, exists bool) {
	v := m.alt_score_uuid
	if v == nil {
		return
	}
	return *v, true
}

// OldAltScoreUUID returns the old "alt_score_uuid" field's value of the BankStatement entity.
// If the BankStatement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BankStatementMutation) OldAltScoreUUID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAltScoreUUID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAltScoreUUID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAltScoreUUID: %w", err)
	}
	return oldValue.AltScoreUUID, nil
}

// ResetAltScoreUUID resets all changes to the "alt_score_uuid" field.
func (m *BankStatementMutation) ResetAltScoreUUID() {
	m.alt_score_uuid = nil
}

// SetIsDiscrepancy sets the "is_discrepancy" field.
func (m *BankStatementMutation) SetIsDiscrepancy(b bool) {
	m.is_discrepancy = &b
}

// IsDiscrepancy returns the value of the "is_discrepancy" field in the mutation.
func (m *BankStatementMutation) IsDiscrepancy() (r bool, exists bool) {
	v := m.is_discrepancy
	if v == nil {
		return
	}
	return *v, true
}

// OldIsDiscrepancy returns the old "is_discrepancy" field's value of the BankStatement entity.
// If the BankStatement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BankStatementMutation) OldIsDiscrepancy(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsDiscrepancy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsDiscrepancy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsDiscrepancy: %w", err)
	}
	return oldValue.IsDiscrepancy, nil
}

// ResetIsDiscrepancy resets all changes to the "is_discrepancy" field.
func (m *BankStatementMutation) ResetIsDiscrepancy() {
	m.is_discrepancy = nil
}

// SetUserDetail sets the "user_detail" field.
func (m *BankStatementMutation) SetUserDetail(ed entity.UserDetail) {
	m.user_detail = &ed
}

// UserDetail returns the value of the "user_detail" field in the mutation.
func (m *BankStatementMutation) UserDetail() (r entity.UserDetail, exists bool) {
	v := m.user_detail
	if v == nil {
		return
	}
	return *v, true
}

// OldUserDetail returns the old "user_detail" field's value of the BankStatement entity.
// If the BankStatement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BankStatementMutation) OldUserDetail(ctx context.Context) (v entity.UserDetail, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserDetail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserDetail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserDetail: %w", err)
	}
	return oldValue.UserDetail, nil
}

// ResetUserDetail resets all changes to the "user_detail" field.
func (m *BankStatementMutation) ResetUserDetail() {
	m.user_detail = nil
}

// SetTransactions sets the "transactions" field.
func (m *BankStatementMutation) SetTransactions(e []entity.Transaction) {
	m.transactions = &e
	m.appendtransactions = nil
}

// Transactions returns the value of the "transactions" field in the mutation.
func (m *BankStatementMutation) Transactions() (r []entity.Transaction, exists bool) {
	v := m.transactions
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactions returns the old "transactions" field's value of the BankStatement entity.
// If the BankStatement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BankStatementMutation) OldTransactions(ctx context.Context) (v []entity.Transaction, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactions is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactions requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactions: %w", err)
	}
	return oldValue.Transactions, nil
}

// AppendTransactions adds e to the "transactions" field.
func (m *BankStatementMutation) AppendTransactions(e []entity.Transaction) {
	m.appendtransactions = append(m.appendtransactions, e...)
}

// AppendedTransactions returns the list of values that were appended to the "transactions" field in this mutation.
func (m *BankStatementMutation) AppendedTransactions() ([]entity.Transaction, bool) {
	if len(m.appendtransactions) == 0 {
		return nil, false
	}
	return m.appendtransactions, true
}

// ResetTransactions resets all changes to the "transactions" field.
func (m *BankStatementMutation) ResetTransactions() {
	m.transactions = nil
	m.appendtransactions = nil
}

// Where appends a list predicates to the BankStatementMutation builder.
func (m *BankStatementMutation) Where(ps ...predicate.BankStatement) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the BankStatementMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *BankStatementMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.BankStatement, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *BankStatementMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *BankStatementMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (BankStatement).
func (m *BankStatementMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *BankStatementMutation) Fields() []string {
	fields := make([]string, 0, 6)
	if m.create_time != nil {
		fields = append(fields, bankstatement.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, bankstatement.FieldUpdateTime)
	}
	if m.alt_score_uuid != nil {
		fields = append(fields, bankstatement.FieldAltScoreUUID)
	}
	if m.is_discrepancy != nil {
		fields = append(fields, bankstatement.FieldIsDiscrepancy)
	}
	if m.user_detail != nil {
		fields = append(fields, bankstatement.FieldUserDetail)
	}
	if m.transactions != nil {
		fields = append(fields, bankstatement.FieldTransactions)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *BankStatementMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case bankstatement.FieldCreateTime:
		return m.CreateTime()
	case bankstatement.FieldUpdateTime:
		return m.UpdateTime()
	case bankstatement.FieldAltScoreUUID:
		return m.AltScoreUUID()
	case bankstatement.FieldIsDiscrepancy:
		return m.IsDiscrepancy()
	case bankstatement.FieldUserDetail:
		return m.UserDetail()
	case bankstatement.FieldTransactions:
		return m.Transactions()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *BankStatementMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case bankstatement.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case bankstatement.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case bankstatement.FieldAltScoreUUID:
		return m.OldAltScoreUUID(ctx)
	case bankstatement.FieldIsDiscrepancy:
		return m.OldIsDiscrepancy(ctx)
	case bankstatement.FieldUserDetail:
		return m.OldUserDetail(ctx)
	case bankstatement.FieldTransactions:
		return m.OldTransactions(ctx)
	}
	return nil, fmt.Errorf("unknown BankStatement field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *BankStatementMutation) SetField(name string, value ent.Value) error {
	switch name {
	case bankstatement.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case bankstatement.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case bankstatement.FieldAltScoreUUID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAltScoreUUID(v)
		return nil
	case bankstatement.FieldIsDiscrepancy:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsDiscrepancy(v)
		return nil
	case bankstatement.FieldUserDetail:
		v, ok := value.(entity.UserDetail)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserDetail(v)
		return nil
	case bankstatement.FieldTransactions:
		v, ok := value.([]entity.Transaction)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactions(v)
		return nil
	}
	return fmt.Errorf("unknown BankStatement field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *BankStatementMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *BankStatementMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *BankStatementMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown BankStatement numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *BankStatementMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *BankStatementMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *BankStatementMutation) ClearField(name string) error {
	return fmt.Errorf("unknown BankStatement nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *BankStatementMutation) ResetField(name string) error {
	switch name {
	case bankstatement.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case bankstatement.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case bankstatement.FieldAltScoreUUID:
		m.ResetAltScoreUUID()
		return nil
	case bankstatement.FieldIsDiscrepancy:
		m.ResetIsDiscrepancy()
		return nil
	case bankstatement.FieldUserDetail:
		m.ResetUserDetail()
		return nil
	case bankstatement.FieldTransactions:
		m.ResetTransactions()
		return nil
	}
	return fmt.Errorf("unknown BankStatement field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *BankStatementMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *BankStatementMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *BankStatementMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *BankStatementMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *BankStatementMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *BankStatementMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *BankStatementMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown BankStatement unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *BankStatementMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown BankStatement edge %s", name)
}

// HealthMutation represents an operation that mutates the Health nodes in the graph.
type HealthMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Health, error)
	predicates    []predicate.Health
}

var _ ent.Mutation = (*HealthMutation)(nil)

// healthOption allows management of the mutation configuration using functional options.
type healthOption func(*HealthMutation)

// newHealthMutation creates new mutation for the Health entity.
func newHealthMutation(c config, op Op, opts ...healthOption) *HealthMutation {
	m := &HealthMutation{
		config:        c,
		op:            op,
		typ:           TypeHealth,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withHealthID sets the ID field of the mutation.
func withHealthID(id uuid.UUID) healthOption {
	return func(m *HealthMutation) {
		var (
			err   error
			once  sync.Once
			value *Health
		)
		m.oldValue = func(ctx context.Context) (*Health, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Health.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withHealth sets the old Health of the mutation.
func withHealth(node *Health) healthOption {
	return func(m *HealthMutation) {
		m.oldValue = func(context.Context) (*Health, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m HealthMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m HealthMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Health entities.
func (m *HealthMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *HealthMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *HealthMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Health.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the HealthMutation builder.
func (m *HealthMutation) Where(ps ...predicate.Health) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the HealthMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *HealthMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Health, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *HealthMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *HealthMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Health).
func (m *HealthMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *HealthMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *HealthMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *HealthMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Health field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *HealthMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *HealthMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Health numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *HealthMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *HealthMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *HealthMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Health nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *HealthMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *HealthMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *HealthMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *HealthMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *HealthMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *HealthMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *HealthMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *HealthMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Health unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *HealthMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Health edge %s", name)
}
