// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// BankStatementsColumns holds the columns for the "bank_statements" table.
	BankStatementsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "create_time", Type: field.TypeTime},
		{Name: "update_time", Type: field.TypeTime},
		{Name: "alt_score_uuid", Type: field.TypeUUID},
		{Name: "is_discrepancy", Type: field.TypeBool},
		{Name: "user_detail", Type: field.TypeJSON},
		{Name: "transactions", Type: field.TypeJSON},
	}
	// BankStatementsTable holds the schema information for the "bank_statements" table.
	BankStatementsTable = &schema.Table{
		Name:       "bank_statements",
		Columns:    BankStatementsColumns,
		PrimaryKey: []*schema.Column{BankStatementsColumns[0]},
	}
	// HealthsColumns holds the columns for the "healths" table.
	HealthsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
	}
	// HealthsTable holds the schema information for the "healths" table.
	HealthsTable = &schema.Table{
		Name:       "healths",
		Columns:    HealthsColumns,
		PrimaryKey: []*schema.Column{HealthsColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		BankStatementsTable,
		HealthsTable,
	}
)

func init() {
}
