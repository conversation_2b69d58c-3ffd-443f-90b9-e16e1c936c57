// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/bankstatement"
)

// BankStatementCreate is the builder for creating a BankStatement entity.
type BankStatementCreate struct {
	config
	mutation *BankStatementMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (bsc *BankStatementCreate) SetCreateTime(t time.Time) *BankStatementCreate {
	bsc.mutation.SetCreateTime(t)
	return bsc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (bsc *BankStatementCreate) SetNillableCreateTime(t *time.Time) *BankStatementCreate {
	if t != nil {
		bsc.SetCreateTime(*t)
	}
	return bsc
}

// SetUpdateTime sets the "update_time" field.
func (bsc *BankStatementCreate) SetUpdateTime(t time.Time) *BankStatementCreate {
	bsc.mutation.SetUpdateTime(t)
	return bsc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (bsc *BankStatementCreate) SetNillableUpdateTime(t *time.Time) *BankStatementCreate {
	if t != nil {
		bsc.SetUpdateTime(*t)
	}
	return bsc
}

// SetAltScoreUUID sets the "alt_score_uuid" field.
func (bsc *BankStatementCreate) SetAltScoreUUID(u uuid.UUID) *BankStatementCreate {
	bsc.mutation.SetAltScoreUUID(u)
	return bsc
}

// SetIsDiscrepancy sets the "is_discrepancy" field.
func (bsc *BankStatementCreate) SetIsDiscrepancy(b bool) *BankStatementCreate {
	bsc.mutation.SetIsDiscrepancy(b)
	return bsc
}

// SetUserDetail sets the "user_detail" field.
func (bsc *BankStatementCreate) SetUserDetail(ed entity.UserDetail) *BankStatementCreate {
	bsc.mutation.SetUserDetail(ed)
	return bsc
}

// SetTransactions sets the "transactions" field.
func (bsc *BankStatementCreate) SetTransactions(e []entity.Transaction) *BankStatementCreate {
	bsc.mutation.SetTransactions(e)
	return bsc
}

// Mutation returns the BankStatementMutation object of the builder.
func (bsc *BankStatementCreate) Mutation() *BankStatementMutation {
	return bsc.mutation
}

// Save creates the BankStatement in the database.
func (bsc *BankStatementCreate) Save(ctx context.Context) (*BankStatement, error) {
	bsc.defaults()
	return withHooks(ctx, bsc.sqlSave, bsc.mutation, bsc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (bsc *BankStatementCreate) SaveX(ctx context.Context) *BankStatement {
	v, err := bsc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (bsc *BankStatementCreate) Exec(ctx context.Context) error {
	_, err := bsc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bsc *BankStatementCreate) ExecX(ctx context.Context) {
	if err := bsc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bsc *BankStatementCreate) defaults() {
	if _, ok := bsc.mutation.CreateTime(); !ok {
		v := bankstatement.DefaultCreateTime()
		bsc.mutation.SetCreateTime(v)
	}
	if _, ok := bsc.mutation.UpdateTime(); !ok {
		v := bankstatement.DefaultUpdateTime()
		bsc.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (bsc *BankStatementCreate) check() error {
	if _, ok := bsc.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "BankStatement.create_time"`)}
	}
	if _, ok := bsc.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "BankStatement.update_time"`)}
	}
	if _, ok := bsc.mutation.AltScoreUUID(); !ok {
		return &ValidationError{Name: "alt_score_uuid", err: errors.New(`ent: missing required field "BankStatement.alt_score_uuid"`)}
	}
	if _, ok := bsc.mutation.IsDiscrepancy(); !ok {
		return &ValidationError{Name: "is_discrepancy", err: errors.New(`ent: missing required field "BankStatement.is_discrepancy"`)}
	}
	if _, ok := bsc.mutation.UserDetail(); !ok {
		return &ValidationError{Name: "user_detail", err: errors.New(`ent: missing required field "BankStatement.user_detail"`)}
	}
	if _, ok := bsc.mutation.Transactions(); !ok {
		return &ValidationError{Name: "transactions", err: errors.New(`ent: missing required field "BankStatement.transactions"`)}
	}
	return nil
}

func (bsc *BankStatementCreate) sqlSave(ctx context.Context) (*BankStatement, error) {
	if err := bsc.check(); err != nil {
		return nil, err
	}
	_node, _spec := bsc.createSpec()
	if err := sqlgraph.CreateNode(ctx, bsc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	bsc.mutation.id = &_node.ID
	bsc.mutation.done = true
	return _node, nil
}

func (bsc *BankStatementCreate) createSpec() (*BankStatement, *sqlgraph.CreateSpec) {
	var (
		_node = &BankStatement{config: bsc.config}
		_spec = sqlgraph.NewCreateSpec(bankstatement.Table, sqlgraph.NewFieldSpec(bankstatement.FieldID, field.TypeInt))
	)
	_spec.OnConflict = bsc.conflict
	if value, ok := bsc.mutation.CreateTime(); ok {
		_spec.SetField(bankstatement.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := bsc.mutation.UpdateTime(); ok {
		_spec.SetField(bankstatement.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := bsc.mutation.AltScoreUUID(); ok {
		_spec.SetField(bankstatement.FieldAltScoreUUID, field.TypeUUID, value)
		_node.AltScoreUUID = value
	}
	if value, ok := bsc.mutation.IsDiscrepancy(); ok {
		_spec.SetField(bankstatement.FieldIsDiscrepancy, field.TypeBool, value)
		_node.IsDiscrepancy = value
	}
	if value, ok := bsc.mutation.UserDetail(); ok {
		_spec.SetField(bankstatement.FieldUserDetail, field.TypeJSON, value)
		_node.UserDetail = value
	}
	if value, ok := bsc.mutation.Transactions(); ok {
		_spec.SetField(bankstatement.FieldTransactions, field.TypeJSON, value)
		_node.Transactions = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.BankStatement.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.BankStatementUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (bsc *BankStatementCreate) OnConflict(opts ...sql.ConflictOption) *BankStatementUpsertOne {
	bsc.conflict = opts
	return &BankStatementUpsertOne{
		create: bsc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.BankStatement.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (bsc *BankStatementCreate) OnConflictColumns(columns ...string) *BankStatementUpsertOne {
	bsc.conflict = append(bsc.conflict, sql.ConflictColumns(columns...))
	return &BankStatementUpsertOne{
		create: bsc,
	}
}

type (
	// BankStatementUpsertOne is the builder for "upsert"-ing
	//  one BankStatement node.
	BankStatementUpsertOne struct {
		create *BankStatementCreate
	}

	// BankStatementUpsert is the "OnConflict" setter.
	BankStatementUpsert struct {
		*sql.UpdateSet
	}
)

// SetAltScoreUUID sets the "alt_score_uuid" field.
func (u *BankStatementUpsert) SetAltScoreUUID(v uuid.UUID) *BankStatementUpsert {
	u.Set(bankstatement.FieldAltScoreUUID, v)
	return u
}

// UpdateAltScoreUUID sets the "alt_score_uuid" field to the value that was provided on create.
func (u *BankStatementUpsert) UpdateAltScoreUUID() *BankStatementUpsert {
	u.SetExcluded(bankstatement.FieldAltScoreUUID)
	return u
}

// SetIsDiscrepancy sets the "is_discrepancy" field.
func (u *BankStatementUpsert) SetIsDiscrepancy(v bool) *BankStatementUpsert {
	u.Set(bankstatement.FieldIsDiscrepancy, v)
	return u
}

// UpdateIsDiscrepancy sets the "is_discrepancy" field to the value that was provided on create.
func (u *BankStatementUpsert) UpdateIsDiscrepancy() *BankStatementUpsert {
	u.SetExcluded(bankstatement.FieldIsDiscrepancy)
	return u
}

// SetUserDetail sets the "user_detail" field.
func (u *BankStatementUpsert) SetUserDetail(v entity.UserDetail) *BankStatementUpsert {
	u.Set(bankstatement.FieldUserDetail, v)
	return u
}

// UpdateUserDetail sets the "user_detail" field to the value that was provided on create.
func (u *BankStatementUpsert) UpdateUserDetail() *BankStatementUpsert {
	u.SetExcluded(bankstatement.FieldUserDetail)
	return u
}

// SetTransactions sets the "transactions" field.
func (u *BankStatementUpsert) SetTransactions(v []entity.Transaction) *BankStatementUpsert {
	u.Set(bankstatement.FieldTransactions, v)
	return u
}

// UpdateTransactions sets the "transactions" field to the value that was provided on create.
func (u *BankStatementUpsert) UpdateTransactions() *BankStatementUpsert {
	u.SetExcluded(bankstatement.FieldTransactions)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create.
// Using this option is equivalent to using:
//
//	client.BankStatement.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//		).
//		Exec(ctx)
func (u *BankStatementUpsertOne) UpdateNewValues() *BankStatementUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(bankstatement.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(bankstatement.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.BankStatement.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *BankStatementUpsertOne) Ignore() *BankStatementUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *BankStatementUpsertOne) DoNothing() *BankStatementUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the BankStatementCreate.OnConflict
// documentation for more info.
func (u *BankStatementUpsertOne) Update(set func(*BankStatementUpsert)) *BankStatementUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&BankStatementUpsert{UpdateSet: update})
	}))
	return u
}

// SetAltScoreUUID sets the "alt_score_uuid" field.
func (u *BankStatementUpsertOne) SetAltScoreUUID(v uuid.UUID) *BankStatementUpsertOne {
	return u.Update(func(s *BankStatementUpsert) {
		s.SetAltScoreUUID(v)
	})
}

// UpdateAltScoreUUID sets the "alt_score_uuid" field to the value that was provided on create.
func (u *BankStatementUpsertOne) UpdateAltScoreUUID() *BankStatementUpsertOne {
	return u.Update(func(s *BankStatementUpsert) {
		s.UpdateAltScoreUUID()
	})
}

// SetIsDiscrepancy sets the "is_discrepancy" field.
func (u *BankStatementUpsertOne) SetIsDiscrepancy(v bool) *BankStatementUpsertOne {
	return u.Update(func(s *BankStatementUpsert) {
		s.SetIsDiscrepancy(v)
	})
}

// UpdateIsDiscrepancy sets the "is_discrepancy" field to the value that was provided on create.
func (u *BankStatementUpsertOne) UpdateIsDiscrepancy() *BankStatementUpsertOne {
	return u.Update(func(s *BankStatementUpsert) {
		s.UpdateIsDiscrepancy()
	})
}

// SetUserDetail sets the "user_detail" field.
func (u *BankStatementUpsertOne) SetUserDetail(v entity.UserDetail) *BankStatementUpsertOne {
	return u.Update(func(s *BankStatementUpsert) {
		s.SetUserDetail(v)
	})
}

// UpdateUserDetail sets the "user_detail" field to the value that was provided on create.
func (u *BankStatementUpsertOne) UpdateUserDetail() *BankStatementUpsertOne {
	return u.Update(func(s *BankStatementUpsert) {
		s.UpdateUserDetail()
	})
}

// SetTransactions sets the "transactions" field.
func (u *BankStatementUpsertOne) SetTransactions(v []entity.Transaction) *BankStatementUpsertOne {
	return u.Update(func(s *BankStatementUpsert) {
		s.SetTransactions(v)
	})
}

// UpdateTransactions sets the "transactions" field to the value that was provided on create.
func (u *BankStatementUpsertOne) UpdateTransactions() *BankStatementUpsertOne {
	return u.Update(func(s *BankStatementUpsert) {
		s.UpdateTransactions()
	})
}

// Exec executes the query.
func (u *BankStatementUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for BankStatementCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *BankStatementUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *BankStatementUpsertOne) ID(ctx context.Context) (id int, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *BankStatementUpsertOne) IDX(ctx context.Context) int {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// BankStatementCreateBulk is the builder for creating many BankStatement entities in bulk.
type BankStatementCreateBulk struct {
	config
	err      error
	builders []*BankStatementCreate
	conflict []sql.ConflictOption
}

// Save creates the BankStatement entities in the database.
func (bscb *BankStatementCreateBulk) Save(ctx context.Context) ([]*BankStatement, error) {
	if bscb.err != nil {
		return nil, bscb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(bscb.builders))
	nodes := make([]*BankStatement, len(bscb.builders))
	mutators := make([]Mutator, len(bscb.builders))
	for i := range bscb.builders {
		func(i int, root context.Context) {
			builder := bscb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*BankStatementMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, bscb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = bscb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, bscb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, bscb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (bscb *BankStatementCreateBulk) SaveX(ctx context.Context) []*BankStatement {
	v, err := bscb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (bscb *BankStatementCreateBulk) Exec(ctx context.Context) error {
	_, err := bscb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bscb *BankStatementCreateBulk) ExecX(ctx context.Context) {
	if err := bscb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.BankStatement.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.BankStatementUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (bscb *BankStatementCreateBulk) OnConflict(opts ...sql.ConflictOption) *BankStatementUpsertBulk {
	bscb.conflict = opts
	return &BankStatementUpsertBulk{
		create: bscb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.BankStatement.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (bscb *BankStatementCreateBulk) OnConflictColumns(columns ...string) *BankStatementUpsertBulk {
	bscb.conflict = append(bscb.conflict, sql.ConflictColumns(columns...))
	return &BankStatementUpsertBulk{
		create: bscb,
	}
}

// BankStatementUpsertBulk is the builder for "upsert"-ing
// a bulk of BankStatement nodes.
type BankStatementUpsertBulk struct {
	create *BankStatementCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.BankStatement.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//		).
//		Exec(ctx)
func (u *BankStatementUpsertBulk) UpdateNewValues() *BankStatementUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(bankstatement.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(bankstatement.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.BankStatement.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *BankStatementUpsertBulk) Ignore() *BankStatementUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *BankStatementUpsertBulk) DoNothing() *BankStatementUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the BankStatementCreateBulk.OnConflict
// documentation for more info.
func (u *BankStatementUpsertBulk) Update(set func(*BankStatementUpsert)) *BankStatementUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&BankStatementUpsert{UpdateSet: update})
	}))
	return u
}

// SetAltScoreUUID sets the "alt_score_uuid" field.
func (u *BankStatementUpsertBulk) SetAltScoreUUID(v uuid.UUID) *BankStatementUpsertBulk {
	return u.Update(func(s *BankStatementUpsert) {
		s.SetAltScoreUUID(v)
	})
}

// UpdateAltScoreUUID sets the "alt_score_uuid" field to the value that was provided on create.
func (u *BankStatementUpsertBulk) UpdateAltScoreUUID() *BankStatementUpsertBulk {
	return u.Update(func(s *BankStatementUpsert) {
		s.UpdateAltScoreUUID()
	})
}

// SetIsDiscrepancy sets the "is_discrepancy" field.
func (u *BankStatementUpsertBulk) SetIsDiscrepancy(v bool) *BankStatementUpsertBulk {
	return u.Update(func(s *BankStatementUpsert) {
		s.SetIsDiscrepancy(v)
	})
}

// UpdateIsDiscrepancy sets the "is_discrepancy" field to the value that was provided on create.
func (u *BankStatementUpsertBulk) UpdateIsDiscrepancy() *BankStatementUpsertBulk {
	return u.Update(func(s *BankStatementUpsert) {
		s.UpdateIsDiscrepancy()
	})
}

// SetUserDetail sets the "user_detail" field.
func (u *BankStatementUpsertBulk) SetUserDetail(v entity.UserDetail) *BankStatementUpsertBulk {
	return u.Update(func(s *BankStatementUpsert) {
		s.SetUserDetail(v)
	})
}

// UpdateUserDetail sets the "user_detail" field to the value that was provided on create.
func (u *BankStatementUpsertBulk) UpdateUserDetail() *BankStatementUpsertBulk {
	return u.Update(func(s *BankStatementUpsert) {
		s.UpdateUserDetail()
	})
}

// SetTransactions sets the "transactions" field.
func (u *BankStatementUpsertBulk) SetTransactions(v []entity.Transaction) *BankStatementUpsertBulk {
	return u.Update(func(s *BankStatementUpsert) {
		s.SetTransactions(v)
	})
}

// UpdateTransactions sets the "transactions" field to the value that was provided on create.
func (u *BankStatementUpsertBulk) UpdateTransactions() *BankStatementUpsertBulk {
	return u.Update(func(s *BankStatementUpsert) {
		s.UpdateTransactions()
	})
}

// Exec executes the query.
func (u *BankStatementUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the BankStatementCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for BankStatementCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *BankStatementUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
