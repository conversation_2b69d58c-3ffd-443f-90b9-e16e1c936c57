// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/bankstatement"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/postgres/ent/predicate"
)

// BankStatementUpdate is the builder for updating BankStatement entities.
type BankStatementUpdate struct {
	config
	hooks    []Hook
	mutation *BankStatementMutation
}

// Where appends a list predicates to the BankStatementUpdate builder.
func (bsu *BankStatementUpdate) Where(ps ...predicate.BankStatement) *BankStatementUpdate {
	bsu.mutation.Where(ps...)
	return bsu
}

// SetAltScoreUUID sets the "alt_score_uuid" field.
func (bsu *BankStatementUpdate) SetAltScoreUUID(u uuid.UUID) *BankStatementUpdate {
	bsu.mutation.SetAltScoreUUID(u)
	return bsu
}

// SetNillableAltScoreUUID sets the "alt_score_uuid" field if the given value is not nil.
func (bsu *BankStatementUpdate) SetNillableAltScoreUUID(u *uuid.UUID) *BankStatementUpdate {
	if u != nil {
		bsu.SetAltScoreUUID(*u)
	}
	return bsu
}

// SetIsDiscrepancy sets the "is_discrepancy" field.
func (bsu *BankStatementUpdate) SetIsDiscrepancy(b bool) *BankStatementUpdate {
	bsu.mutation.SetIsDiscrepancy(b)
	return bsu
}

// SetNillableIsDiscrepancy sets the "is_discrepancy" field if the given value is not nil.
func (bsu *BankStatementUpdate) SetNillableIsDiscrepancy(b *bool) *BankStatementUpdate {
	if b != nil {
		bsu.SetIsDiscrepancy(*b)
	}
	return bsu
}

// SetUserDetail sets the "user_detail" field.
func (bsu *BankStatementUpdate) SetUserDetail(ed entity.UserDetail) *BankStatementUpdate {
	bsu.mutation.SetUserDetail(ed)
	return bsu
}

// SetNillableUserDetail sets the "user_detail" field if the given value is not nil.
func (bsu *BankStatementUpdate) SetNillableUserDetail(ed *entity.UserDetail) *BankStatementUpdate {
	if ed != nil {
		bsu.SetUserDetail(*ed)
	}
	return bsu
}

// SetTransactions sets the "transactions" field.
func (bsu *BankStatementUpdate) SetTransactions(e []entity.Transaction) *BankStatementUpdate {
	bsu.mutation.SetTransactions(e)
	return bsu
}

// AppendTransactions appends e to the "transactions" field.
func (bsu *BankStatementUpdate) AppendTransactions(e []entity.Transaction) *BankStatementUpdate {
	bsu.mutation.AppendTransactions(e)
	return bsu
}

// Mutation returns the BankStatementMutation object of the builder.
func (bsu *BankStatementUpdate) Mutation() *BankStatementMutation {
	return bsu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (bsu *BankStatementUpdate) Save(ctx context.Context) (int, error) {
	bsu.defaults()
	return withHooks(ctx, bsu.sqlSave, bsu.mutation, bsu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (bsu *BankStatementUpdate) SaveX(ctx context.Context) int {
	affected, err := bsu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (bsu *BankStatementUpdate) Exec(ctx context.Context) error {
	_, err := bsu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bsu *BankStatementUpdate) ExecX(ctx context.Context) {
	if err := bsu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bsu *BankStatementUpdate) defaults() {
	if _, ok := bsu.mutation.UpdateTime(); !ok {
		v := bankstatement.UpdateDefaultUpdateTime()
		bsu.mutation.SetUpdateTime(v)
	}
}

func (bsu *BankStatementUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(bankstatement.Table, bankstatement.Columns, sqlgraph.NewFieldSpec(bankstatement.FieldID, field.TypeInt))
	if ps := bsu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := bsu.mutation.UpdateTime(); ok {
		_spec.SetField(bankstatement.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := bsu.mutation.AltScoreUUID(); ok {
		_spec.SetField(bankstatement.FieldAltScoreUUID, field.TypeUUID, value)
	}
	if value, ok := bsu.mutation.IsDiscrepancy(); ok {
		_spec.SetField(bankstatement.FieldIsDiscrepancy, field.TypeBool, value)
	}
	if value, ok := bsu.mutation.UserDetail(); ok {
		_spec.SetField(bankstatement.FieldUserDetail, field.TypeJSON, value)
	}
	if value, ok := bsu.mutation.Transactions(); ok {
		_spec.SetField(bankstatement.FieldTransactions, field.TypeJSON, value)
	}
	if value, ok := bsu.mutation.AppendedTransactions(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, bankstatement.FieldTransactions, value)
		})
	}
	if n, err = sqlgraph.UpdateNodes(ctx, bsu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{bankstatement.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	bsu.mutation.done = true
	return n, nil
}

// BankStatementUpdateOne is the builder for updating a single BankStatement entity.
type BankStatementUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *BankStatementMutation
}

// SetAltScoreUUID sets the "alt_score_uuid" field.
func (bsuo *BankStatementUpdateOne) SetAltScoreUUID(u uuid.UUID) *BankStatementUpdateOne {
	bsuo.mutation.SetAltScoreUUID(u)
	return bsuo
}

// SetNillableAltScoreUUID sets the "alt_score_uuid" field if the given value is not nil.
func (bsuo *BankStatementUpdateOne) SetNillableAltScoreUUID(u *uuid.UUID) *BankStatementUpdateOne {
	if u != nil {
		bsuo.SetAltScoreUUID(*u)
	}
	return bsuo
}

// SetIsDiscrepancy sets the "is_discrepancy" field.
func (bsuo *BankStatementUpdateOne) SetIsDiscrepancy(b bool) *BankStatementUpdateOne {
	bsuo.mutation.SetIsDiscrepancy(b)
	return bsuo
}

// SetNillableIsDiscrepancy sets the "is_discrepancy" field if the given value is not nil.
func (bsuo *BankStatementUpdateOne) SetNillableIsDiscrepancy(b *bool) *BankStatementUpdateOne {
	if b != nil {
		bsuo.SetIsDiscrepancy(*b)
	}
	return bsuo
}

// SetUserDetail sets the "user_detail" field.
func (bsuo *BankStatementUpdateOne) SetUserDetail(ed entity.UserDetail) *BankStatementUpdateOne {
	bsuo.mutation.SetUserDetail(ed)
	return bsuo
}

// SetNillableUserDetail sets the "user_detail" field if the given value is not nil.
func (bsuo *BankStatementUpdateOne) SetNillableUserDetail(ed *entity.UserDetail) *BankStatementUpdateOne {
	if ed != nil {
		bsuo.SetUserDetail(*ed)
	}
	return bsuo
}

// SetTransactions sets the "transactions" field.
func (bsuo *BankStatementUpdateOne) SetTransactions(e []entity.Transaction) *BankStatementUpdateOne {
	bsuo.mutation.SetTransactions(e)
	return bsuo
}

// AppendTransactions appends e to the "transactions" field.
func (bsuo *BankStatementUpdateOne) AppendTransactions(e []entity.Transaction) *BankStatementUpdateOne {
	bsuo.mutation.AppendTransactions(e)
	return bsuo
}

// Mutation returns the BankStatementMutation object of the builder.
func (bsuo *BankStatementUpdateOne) Mutation() *BankStatementMutation {
	return bsuo.mutation
}

// Where appends a list predicates to the BankStatementUpdate builder.
func (bsuo *BankStatementUpdateOne) Where(ps ...predicate.BankStatement) *BankStatementUpdateOne {
	bsuo.mutation.Where(ps...)
	return bsuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (bsuo *BankStatementUpdateOne) Select(field string, fields ...string) *BankStatementUpdateOne {
	bsuo.fields = append([]string{field}, fields...)
	return bsuo
}

// Save executes the query and returns the updated BankStatement entity.
func (bsuo *BankStatementUpdateOne) Save(ctx context.Context) (*BankStatement, error) {
	bsuo.defaults()
	return withHooks(ctx, bsuo.sqlSave, bsuo.mutation, bsuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (bsuo *BankStatementUpdateOne) SaveX(ctx context.Context) *BankStatement {
	node, err := bsuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (bsuo *BankStatementUpdateOne) Exec(ctx context.Context) error {
	_, err := bsuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bsuo *BankStatementUpdateOne) ExecX(ctx context.Context) {
	if err := bsuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bsuo *BankStatementUpdateOne) defaults() {
	if _, ok := bsuo.mutation.UpdateTime(); !ok {
		v := bankstatement.UpdateDefaultUpdateTime()
		bsuo.mutation.SetUpdateTime(v)
	}
}

func (bsuo *BankStatementUpdateOne) sqlSave(ctx context.Context) (_node *BankStatement, err error) {
	_spec := sqlgraph.NewUpdateSpec(bankstatement.Table, bankstatement.Columns, sqlgraph.NewFieldSpec(bankstatement.FieldID, field.TypeInt))
	id, ok := bsuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "BankStatement.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := bsuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, bankstatement.FieldID)
		for _, f := range fields {
			if !bankstatement.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != bankstatement.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := bsuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := bsuo.mutation.UpdateTime(); ok {
		_spec.SetField(bankstatement.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := bsuo.mutation.AltScoreUUID(); ok {
		_spec.SetField(bankstatement.FieldAltScoreUUID, field.TypeUUID, value)
	}
	if value, ok := bsuo.mutation.IsDiscrepancy(); ok {
		_spec.SetField(bankstatement.FieldIsDiscrepancy, field.TypeBool, value)
	}
	if value, ok := bsuo.mutation.UserDetail(); ok {
		_spec.SetField(bankstatement.FieldUserDetail, field.TypeJSON, value)
	}
	if value, ok := bsuo.mutation.Transactions(); ok {
		_spec.SetField(bankstatement.FieldTransactions, field.TypeJSON, value)
	}
	if value, ok := bsuo.mutation.AppendedTransactions(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, bankstatement.FieldTransactions, value)
		})
	}
	_node = &BankStatement{config: bsuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, bsuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{bankstatement.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	bsuo.mutation.done = true
	return _node, nil
}
