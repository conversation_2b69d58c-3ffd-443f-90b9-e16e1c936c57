package storage

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ParsedBankStatement interface {
	SaveRawParsedFile(ctx context.Context, data interface{}, collection string) (string, error)
}

func (s *storageImpl) SaveRawParsedFile(ctx context.Context, data interface{}, collection string) (string, error) {
	coll := s.MongoClient.DB.Collection(collection)

	res, err := coll.InsertOne(ctx, data)
	if err != nil {
		return "", err
	}

	if docID, ok := res.InsertedID.(primitive.ObjectID); ok {
		return docID.Hex(), nil
	}
	if docID, ok := res.InsertedID.(string); ok {
		return docID, nil
	}
	return fmt.Sprintf("%v", res.InsertedID), nil
}
