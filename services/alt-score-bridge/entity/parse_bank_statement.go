package entity

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/altscore"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/alt-score-bridge"
)

type (
	ParseBankStatementReq struct {
		AltScoreUUID string
		Params       ParseBankStatementReqParams      `json:"params" bson:"params"`
		ClientInfo   *ParseBankStatementReqClientInfo `json:"client_info" bson:"client_info"`
	}

	ParseBankStatementReqParams struct {
		Statement []byte `json:"statement" bson:"statement"`
		BankName  string `json:"bank_name" bson:"bank_name"`
	}

	ParseBankStatementReqClientInfo struct {
		IIN           string `json:"iin" bson:"iin"`
		FullName      string `json:"full_name" bson:"full_name"`
		UserType      string `json:"user_type" bson:"user_type"`
		ApplicationID uint64 `json:"application_id" bson:"application_id"`
		ClientID      uint64 `json:"client_id" bson:"client_id"`
		TrackID       uint64 `json:"track_id" bson:"track_id"`
	}

	ParseBankStatementResult struct {
		AltScoreID    int
		IsDiscrepancy bool
		UserDetail    UserDetail
		Accounts      []Account
		RequestLogID  *string
	}

	UserDetail struct {
		FullName   string
		IIN        *string
		BIN        *string
		BankName   string
		CardNumber *string
		Period     Period
	}

	Period struct {
		StartDate string
		EndDate   string
	}

	Account struct {
		AccountNumber string
		Currency      string
		Balance       Balance
		Transaction   *TransactionSummary
		Total         *Total
		Limit         *Limit

		Transactions []Transaction
	}

	Balance struct {
		Before     *float64
		After      *float64
		Processing *float64
	}

	TransactionSummary struct {
		Replenishment   *float64
		Transfers       *float64
		Purchases       *float64
		Withdrawals     *float64
		Other           *float64
		Payment         *float64
		LoanPayment     *float64
		TotalReceipt    *float64
		TotalExpense    *float64
		TotalCommission *float64
	}

	Total struct {
		Debit  *float64
		Credit *float64
	}

	Limit struct {
		RemainingSalary *float64
		Transfers       *float64
	}

	Transaction struct {
		Date          string
		CompletedDate *string
		Amount        *float64
		Currency      *string
		Type          *string
		Detail        string
		Commission    *float64
		IsProcessing  *bool
		DocNumber     *string
		Debit         *float64
		Credit        *float64
		AccountNumber *string
		To            *To
	}

	To struct {
		Title string
		Bik   *string
		Knp   *string
		Bin   string
	}

	ParseBankStatementRawData = utils.RawData[*ParseBankStatementReqParams, *altscore.ParseBankStatementResponse, *utils.RequestLog]
)

// MakeParseBankStatementPbToEntity создает объект из pb.ParseBankStatementRequest в ParseBankStatementReq для передачи в usecase
func MakeParseBankStatementPbToEntity(req *pb.ParseBankStatementRequest) *ParseBankStatementReq {
	if req == nil {
		return &ParseBankStatementReq{}
	}

	var ci *ParseBankStatementReqClientInfo
	if req.ClientInfo != nil {
		ci = &ParseBankStatementReqClientInfo{
			IIN:           req.ClientInfo.Iin,
			FullName:      req.ClientInfo.FullName,
			UserType:      req.ClientInfo.UserType,
			ApplicationID: req.ClientInfo.ApplicationId,
			ClientID:      req.ClientInfo.ClientId,
			TrackID:       req.ClientInfo.TrackId,
		}
	}

	return &ParseBankStatementReq{
		AltScoreUUID: req.AltScoreUuid,
		Params: ParseBankStatementReqParams{
			Statement: req.BankStatement,
			BankName:  req.BankName,
		},
		ClientInfo: ci,
	}
}

// MakeParseBankStatementEntityToPb создает объект из ParseBankStatement в pb.ParseBankStatementResponse для возврата ответа из сервиса
func MakeParseBankStatementEntityToPb(res *ParseBankStatementResult) *pb.ParseBankStatementResponse {
	if res == nil {
		return &pb.ParseBankStatementResponse{}
	}

	pbAccounts := make([]*pb.Account, len(res.Accounts))
	for i, acc := range res.Accounts {
		pbAccounts[i] = &pb.Account{
			AccountNumber: acc.AccountNumber,
			Currency:      acc.Currency,
			Balance: &pb.Balance{
				Before:     acc.Balance.Before,
				After:      acc.Balance.After,
				Processing: acc.Balance.Processing,
			},
			Transaction:  makeTransactionSummaryEntityToPb(acc.Transaction),
			Total:        makeTotalEntityToPb(acc.Total),
			Limit:        makeLimitEntityToPb(acc.Limit),
			Transactions: makeTransactionsEntityToPb(acc.Transactions),
		}
	}

	return &pb.ParseBankStatementResponse{
		RequestLogId:  res.RequestLogID,
		AltScoreId:    int64(res.AltScoreID),
		IsDiscrepancy: res.IsDiscrepancy,
		UserDetail: &pb.UserDetail{
			FullName:   res.UserDetail.FullName,
			Iin:        res.UserDetail.IIN,
			Bin:        res.UserDetail.BIN,
			BankName:   res.UserDetail.BankName,
			CardNumber: res.UserDetail.CardNumber,
			Period: &pb.Period{
				StartDate: res.UserDetail.Period.StartDate,
				EndDate:   res.UserDetail.Period.EndDate,
			},
		},
		Accounts: pbAccounts,
	}
}

func makeTransactionSummaryEntityToPb(trSummary *TransactionSummary) *pb.TransactionSummary {
	if trSummary == nil {
		return nil
	}

	return &pb.TransactionSummary{
		Replenishment:   trSummary.Replenishment,
		Transfers:       trSummary.Transfers,
		Purchases:       trSummary.Purchases,
		Withdrawals:     trSummary.Withdrawals,
		Other:           trSummary.Other,
		Payment:         trSummary.Payment,
		LoanPayment:     trSummary.LoanPayment,
		TotalReceipt:    trSummary.TotalReceipt,
		TotalExpense:    trSummary.TotalExpense,
		TotalCommission: trSummary.TotalCommission,
	}
}

func makeTransactionsEntityToPb(transactions []Transaction) []*pb.Transaction {
	if transactions == nil {
		return nil
	}

	pbTransactions := make([]*pb.Transaction, len(transactions))
	for i, transaction := range transactions {
		pbTransactions[i] = &pb.Transaction{
			Date:          transaction.Date,
			CompletedDate: transaction.CompletedDate,
			Amount:        transaction.Amount,
			Currency:      transaction.Currency,
			Type:          transaction.Type,
			Detail:        transaction.Detail,
			Commission:    transaction.Commission,
			IsProcessing:  transaction.IsProcessing,
			DocNumber:     transaction.DocNumber,
			Debit:         transaction.Debit,
			Credit:        transaction.Credit,
			AccountNumber: transaction.AccountNumber,
		}

		if transaction.To != nil {
			pbTransactions[i].To = &pb.To{
				Title: transaction.To.Title,
				Bik:   transaction.To.Bik,
				Knp:   transaction.To.Knp,
				Bin:   transaction.To.Bin,
			}
		}
	}

	return pbTransactions
}

func makeTotalEntityToPb(total *Total) *pb.Total {
	if total == nil {
		return nil
	}

	return &pb.Total{
		Debit:  total.Debit,
		Credit: total.Credit,
	}
}

func makeLimitEntityToPb(limit *Limit) *pb.Limit {
	if limit == nil {
		return nil
	}

	return &pb.Limit{
		RemainingSalary: limit.RemainingSalary,
		Transfers:       limit.Transfers,
	}
}

func MakeClientRespToEntity(clientResp *altscore.ParseBankStatementResponse) *ParseBankStatementResult {
	if clientResp == nil {
		return &ParseBankStatementResult{}
	}

	accounts := make([]Account, len(clientResp.Response.Accounts))
	for i, acc := range clientResp.Response.Accounts {
		var trSummary *TransactionSummary
		if acc.Transaction != nil {
			trSummary = &TransactionSummary{
				Replenishment:   acc.Transaction.Replenishment,
				Transfers:       acc.Transaction.Transfers,
				Purchases:       acc.Transaction.Purchases,
				Withdrawals:     acc.Transaction.Withdrawals,
				Other:           acc.Transaction.Other,
				Payment:         acc.Transaction.Payment,
				LoanPayment:     acc.Transaction.LoanPayment,
				TotalReceipt:    acc.Transaction.TotalReceipt,
				TotalExpense:    acc.Transaction.TotalExpense,
				TotalCommission: acc.Transaction.TotalCommission,
			}
		}

		var total *Total
		if acc.Total != nil {
			total = &Total{
				Debit:  acc.Total.Debit,
				Credit: acc.Total.Credit,
			}
		}

		var limit *Limit
		if acc.Limit != nil {
			limit = &Limit{
				RemainingSalary: acc.Limit.RemainingSalary,
				Transfers:       acc.Limit.Transfers,
			}
		}

		transactions := makeClientTransactionsToEntity(acc.Transactions)

		accounts[i] = Account{
			AccountNumber: acc.AccountNumber,
			Currency:      acc.Currency,
			Balance: Balance{
				Before:     acc.Balance.Before,
				After:      acc.Balance.After,
				Processing: acc.Balance.Processing,
			},
			Transaction:  trSummary,
			Total:        total,
			Limit:        limit,
			Transactions: transactions,
		}
	}

	userDetail := UserDetail{
		FullName:   clientResp.Response.UserDetail.FullName,
		IIN:        clientResp.Response.UserDetail.IIN,
		BIN:        clientResp.Response.UserDetail.BIN,
		BankName:   clientResp.Response.UserDetail.BankName,
		CardNumber: clientResp.Response.UserDetail.CardNumber,
		Period: Period{
			StartDate: clientResp.Response.UserDetail.Period.StartDate,
			EndDate:   clientResp.Response.UserDetail.Period.EndDate,
		},
	}

	return &ParseBankStatementResult{
		IsDiscrepancy: clientResp.Response.IsDiscrepancy,
		UserDetail:    userDetail,
		Accounts:      accounts,
	}
}

func makeClientTransactionsToEntity(transactions []altscore.Transaction) []Transaction {
	if transactions == nil {
		return nil
	}

	clientTransactions := make([]Transaction, len(transactions))
	for i, t := range transactions {
		clientTransactions[i] = Transaction{
			Date:          t.Date,
			CompletedDate: t.CompletedDate,
			Amount:        t.Amount,
			Currency:      t.Currency,
			Type:          t.Type,
			Detail:        t.Detail,
			Commission:    t.Commission,
			IsProcessing:  t.IsProcessing,
			DocNumber:     t.DocNumber,
			Debit:         t.Debit,
			Credit:        t.Credit,
			AccountNumber: t.AccountNumber,
		}

		if t.To != nil {
			clientTransactions[i].To = &To{
				Title: t.To.Title,
				Bik:   t.To.Bik,
				Knp:   t.To.Knp,
				Bin:   t.To.Bin,
			}
		}
	}

	return clientTransactions
}

func NewParseBankStatementRawData(
	ctx context.Context,
	req *ParseBankStatementReqParams,
	res *altscore.ParseBankStatementResponse,
	rl *utils.RequestLog,
	err error,
) *ParseBankStatementRawData {
	rd := utils.NewRawData[*ParseBankStatementReqParams, *altscore.ParseBankStatementResponse, *utils.RequestLog](req)

	userID, _ := utils.GetUserIDFromContext(ctx)
	requestID := utils.ExtractRequestID(ctx)

	rd.WithUserID(userID).
		WithRequestID(requestID).
		WithTimeStamp().
		WithResponse(&res).
		WithError(err).
		WithPayload(&rl)

	return rd
}

// Реализация интерфейса utils.IClientInfo для ParseBankStatementReqClientInfo
func (ci *ParseBankStatementReqClientInfo) GetIIN() string           { return ci.IIN }
func (ci *ParseBankStatementReqClientInfo) GetFullName() string      { return ci.FullName }
func (ci *ParseBankStatementReqClientInfo) GetUserType() string      { return ci.UserType }
func (ci *ParseBankStatementReqClientInfo) GetApplicationID() uint64 { return ci.ApplicationID }
func (ci *ParseBankStatementReqClientInfo) GetClientID() uint64      { return ci.ClientID }
func (ci *ParseBankStatementReqClientInfo) GetTrackID() uint64       { return ci.TrackID }
