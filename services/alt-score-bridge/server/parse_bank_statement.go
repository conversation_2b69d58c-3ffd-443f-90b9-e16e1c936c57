package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/alt-score-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/entity"
)

func (s *Server) ParseBankStatement(ctx context.Context, req *pb.ParseBankStatementRequest) (*pb.ParseBankStatementResponse, error) {
	parseBankStatementEntity := entity.MakeParseBankStatementPbToEntity(req)

	parseBankStatement, err := s.useCase.ParseBankStatement(ctx, parseBankStatementEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeParseBankStatementEntityToPb(parseBankStatement), nil
}
