package tests

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"

	"github.com/golang/mock/gomock"

	"github.com/go-resty/resty/v2"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/altscore"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/alt-score-bridge"
)

func (s *Suite) TestParseBankStatement_SuccessFullFields() {
	httpServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json; charset=utf-8")
		w.WriteHeader(http.StatusOK)
		resp := altscore.AltScoreResponse{
			IsDiscrepancy: true,
		}
		_ = json.NewEncoder(w).Encode(resp)
	}))
	defer httpServer.Close()

	var reqLogGlobal *utils.RequestLog
	client := altscore.AltScoreProviderImpl{
		BaseURL:    httpServer.URL,
		HTTPClient: resty.New(),
	}
	s.mocks.Providers.AltScoreProvider.EXPECT().ParseBankStatement(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, params *altscore.ParseBankStatementReq) (*altscore.ParseBankStatementResponse, *utils.RequestLog, error) {
			resp, reqLog, err := client.ParseBankStatement(ctx, params)
			reqLogGlobal = reqLog
			fmt.Printf("\n+ utils.RequestLog - %+v\n\n", reqLog)

			s.Assert().Equal(altscore.AltScoreServiceName, reqLogGlobal.ServiceName)
			s.Assert().Equal(altscore.ParseBankStatementEndpoint, reqLogGlobal.ServiceMethod)
			s.Assert().Equal("POST", reqLogGlobal.RequestHTTPMethod)
			s.Assert().Equal("application/json; charset=utf-8", reqLogGlobal.ResponseContentType)
			s.Assert().Equal(200, reqLogGlobal.ResponseCode)

			return resp, reqLog, err
		})

	grpcRequest := pb.ParseBankStatementRequest{
		BankStatement: []byte("test statement"),
		BankName:      "KASPI",
		AltScoreUuid:  "123e4567-e89b-12d3-a456-************",
		ClientInfo: &pb.ParseBankStatementRequestClientInfo{
			Iin:           "************",
			FullName:      "Test User",
			UserType:      "INDIVIDUAL",
			ApplicationId: 123,
			ClientId:      456,
			TrackId:       789,
		},
	}
	resp, err := s.grpc.ParseBankStatement(s.ctx, &grpcRequest)

	fmt.Printf("\n+ *alt_score_bridge.ParseBankStatementResponse - %+v\n\n", resp)
	s.Assert().NotEmpty(resp.RequestLogId)

	fmt.Printf("\n+ Global utils.RequestLog - %+v\n\n", reqLogGlobal)
	s.Assert().Equal(grpcRequest.AltScoreUuid, reqLogGlobal.RequestBody)
	s.Assert().Equal(utils.CalcHash(grpcRequest.BankStatement), reqLogGlobal.RequestParamsHash)

	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(true, resp.IsDiscrepancy)
}

func (s *Suite) TestParseBankStatement_Success() {
	expectedResp := &altscore.ParseBankStatementResponse{
		Response: altscore.AltScoreResponse{
			IsDiscrepancy: false,
			UserDetail: altscore.UserDetailWithAccounts{
				FullName:   "ФИО клиента",
				IIN:        ptrString(""),
				BankName:   "KASPI",
				CardNumber: ptrString("Номер карточки"),
				Period: altscore.Period{
					StartDate: "2025-04-24",
					EndDate:   "2025-05-24",
				},
			},
			Accounts: []altscore.Account{
				{
					AccountNumber: "Номер счета",
					Currency:      "Валюта",
					Balance: altscore.Balance{
						Before: ptrFloat(747.51),
						After:  ptrFloat(11005.15),
					},
					Transaction: &altscore.TransactionSummary{
						Replenishment: ptrFloat(817888.0),
						Transfers:     ptrFloat(-601165.0),
						Purchases:     ptrFloat(-194791.0),
						Withdrawals:   ptrFloat(-11000.0),
						Other:         ptrFloat(-674.36),
					},
					Limit: &altscore.Limit{
						RemainingSalary: ptrFloat(0.0),
						Transfers:       ptrFloat(290000.0),
					},
					Transactions: []altscore.Transaction{
						{
							Date:   "2025-05-23T00:00:00",
							Amount: ptrFloat(-200.0),
							Type:   ptrString("Разное"),
							Detail: "Комиссия за перевод на карту др. банка",
						},
					},
				},
			},
		},
	}

	expectedReqLog := &utils.RequestLog{
		ServiceName:         altscore.AltScoreServiceName,
		ServiceMethod:       altscore.ParseBankStatementEndpoint,
		RequestHTTPMethod:   "POST",
		ResponseContentType: "application/json; charset=utf-8",
		ResponseCode:        200,
		RequestBody:         "123e4567-e89b-12d3-a456-************",
	}

	s.mocks.Providers.AltScoreProvider.EXPECT().
		ParseBankStatement(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, params *altscore.ParseBankStatementReq) (*altscore.ParseBankStatementResponse, *utils.RequestLog, error) {
			return expectedResp, expectedReqLog, nil
		})

	grpcRequest := pb.ParseBankStatementRequest{
		BankStatement: []byte("test statement"),
		BankName:      "KASPI",
		AltScoreUuid:  "123e4567-e89b-12d3-a456-************",
		ClientInfo: &pb.ParseBankStatementRequestClientInfo{
			Iin:           "************",
			FullName:      "Test User",
			UserType:      "INDIVIDUAL",
			ApplicationId: 123,
			ClientId:      456,
			TrackId:       789,
		},
	}

	resp, err := s.grpc.ParseBankStatement(s.ctx, &grpcRequest)

	s.Require().NoError(err)
	s.Require().NotNil(resp)

	s.Assert().False(resp.IsDiscrepancy)
	s.Assert().Equal("ФИО клиента", resp.UserDetail.FullName)
	s.Assert().Equal("2025-04-24", resp.UserDetail.Period.StartDate)
	s.Assert().Equal("2025-05-24", resp.UserDetail.Period.EndDate)
	s.Require().Len(resp.Accounts, 1)
	s.Assert().Equal(-601165.0, *resp.Accounts[0].Transaction.Transfers)
	s.Require().Len(resp.Accounts[0].Transactions, 1)

	s.Assert().NotEmpty(resp.RequestLogId)
}

func (s *Suite) TestParseBankStatement_HTTPError400() {
	httpServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json; charset=utf-8")
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(`{"message": "Bad request", "statusCode": 400, "error": "BadRequest"}`))
	}))
	defer httpServer.Close()

	var reqLogGlobal *utils.RequestLog
	client := altscore.AltScoreProviderImpl{
		BaseURL:    httpServer.URL,
		HTTPClient: resty.New(),
	}
	s.mocks.Providers.AltScoreProvider.EXPECT().ParseBankStatement(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, params *altscore.ParseBankStatementReq) (*altscore.ParseBankStatementResponse, *utils.RequestLog, error) {
			resp, reqLog, err := client.ParseBankStatement(ctx, params)
			reqLogGlobal = reqLog
			fmt.Printf("\n+ utils.RequestLog - %+v\n\n", reqLog)

			s.Assert().Equal(altscore.AltScoreServiceName, reqLogGlobal.ServiceName)
			s.Assert().Equal(altscore.ParseBankStatementEndpoint, reqLogGlobal.ServiceMethod)
			s.Assert().Equal("POST", reqLogGlobal.RequestHTTPMethod)
			s.Assert().Equal("application/json; charset=utf-8", reqLogGlobal.ResponseContentType)
			s.Assert().Equal(400, reqLogGlobal.ResponseCode)

			return resp, reqLog, err
		}).Times(3)

	grpcRequest := pb.ParseBankStatementRequest{
		BankStatement: []byte("test statement"),
		BankName:      "KASPI",
		AltScoreUuid:  "123e4567-e89b-12d3-a456-************",
	}
	resp, err := s.grpc.ParseBankStatement(s.ctx, &grpcRequest)

	fmt.Printf("\n+ *alt_score_bridge.ParseBankStatementResponse - %+v\n\n", resp)

	fmt.Printf("\n+ Global utils.RequestLog - %+v\n\n", reqLogGlobal)
	s.Assert().Equal(grpcRequest.AltScoreUuid, reqLogGlobal.RequestBody)
	s.Assert().Equal(utils.CalcHash(grpcRequest.BankStatement), reqLogGlobal.RequestParamsHash)

	s.Require().Error(err)
	s.Contains(err.Error(), "message: Bad request, status code: 400")
}

func (s *Suite) TestParseBankStatement_HTTPError500() {
	var reqCount int32
	httpServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json; charset=utf-8")
		reqCount++
		switch reqCount {
		case 1:
			w.WriteHeader(http.StatusInternalServerError)
			_, _ = w.Write([]byte(`{"message": "Internal server error", "statusCode": 500, "error": "InternalServerError"}`))
		case 2:
			w.WriteHeader(http.StatusBadGateway)
			_, _ = w.Write([]byte(`{"message": "Bad gateway", "statusCode": 502, "error": "BadGateway"}`))
		case 3:
			w.WriteHeader(http.StatusServiceUnavailable)
			_, _ = w.Write([]byte(`{"message": "Service unavailable", "statusCode": 503, "error": "ServiceUnavailable"}`))
		}
	}))
	defer httpServer.Close()

	var reqLogGlobal *utils.RequestLog
	client := altscore.AltScoreProviderImpl{
		BaseURL:    httpServer.URL,
		HTTPClient: resty.New(),
	}
	s.mocks.Providers.AltScoreProvider.EXPECT().ParseBankStatement(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, params *altscore.ParseBankStatementReq) (*altscore.ParseBankStatementResponse, *utils.RequestLog, error) {
			resp, reqLog, err := client.ParseBankStatement(ctx, params)
			reqLogGlobal = reqLog
			fmt.Printf("\n+ utils.RequestLog - %+v\n\n", reqLog)

			s.Assert().Equal(altscore.AltScoreServiceName, reqLogGlobal.ServiceName)
			s.Assert().Equal(altscore.ParseBankStatementEndpoint, reqLogGlobal.ServiceMethod)
			s.Assert().Equal("POST", reqLogGlobal.RequestHTTPMethod)
			s.Assert().Equal("application/json; charset=utf-8", reqLogGlobal.ResponseContentType)
			s.Assert().GreaterOrEqual(reqLogGlobal.ResponseCode, 500)

			return resp, reqLog, err
		}).Times(3)

	grpcRequest := pb.ParseBankStatementRequest{
		BankStatement: []byte("test statement"),
		BankName:      "KASPI",
		AltScoreUuid:  "123e4567-e89b-12d3-a456-************",
	}
	resp, err := s.grpc.ParseBankStatement(s.ctx, &grpcRequest)

	fmt.Printf("\n+ *alt_score_bridge.ParseBankStatementResponse - %+v\n\n", resp)

	fmt.Printf("\n+ Global utils.RequestLog - %+v\n\n", reqLogGlobal)
	s.Assert().Equal(grpcRequest.AltScoreUuid, reqLogGlobal.RequestBody)
	s.Assert().Equal(utils.CalcHash(grpcRequest.BankStatement), reqLogGlobal.RequestParamsHash)

	s.Require().Error(err)
	s.Contains(err.Error(), "response error")
}

func ptrString(s string) *string { return &s }

func ptrFloat(v float64) *float64 { return &v }
