// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/alt-score-bridge (interfaces: AltscorebridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	alt_score_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/alt-score-bridge"
)

// MockAltscorebridgeClient is a mock of AltscorebridgeClient interface.
type MockAltscorebridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockAltscorebridgeClientMockRecorder
}

// MockAltscorebridgeClientMockRecorder is the mock recorder for MockAltscorebridgeClient.
type MockAltscorebridgeClientMockRecorder struct {
	mock *MockAltscorebridgeClient
}

// NewMockAltscorebridgeClient creates a new mock instance.
func NewMockAltscorebridgeClient(ctrl *gomock.Controller) *MockAltscorebridgeClient {
	mock := &MockAltscorebridgeClient{ctrl: ctrl}
	mock.recorder = &MockAltscorebridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAltscorebridgeClient) EXPECT() *MockAltscorebridgeClientMockRecorder {
	return m.recorder
}

// HealthCheck mocks base method.
func (m *MockAltscorebridgeClient) HealthCheck(arg0 context.Context, arg1 *alt_score_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*alt_score_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*alt_score_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockAltscorebridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockAltscorebridgeClient)(nil).HealthCheck), varargs...)
}

// ParseBankStatement mocks base method.
func (m *MockAltscorebridgeClient) ParseBankStatement(arg0 context.Context, arg1 *alt_score_bridge.ParseBankStatementRequest, arg2 ...grpc.CallOption) (*alt_score_bridge.ParseBankStatementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ParseBankStatement", varargs...)
	ret0, _ := ret[0].(*alt_score_bridge.ParseBankStatementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseBankStatement indicates an expected call of ParseBankStatement.
func (mr *MockAltscorebridgeClientMockRecorder) ParseBankStatement(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseBankStatement", reflect.TypeOf((*MockAltscorebridgeClient)(nil).ParseBankStatement), varargs...)
}
