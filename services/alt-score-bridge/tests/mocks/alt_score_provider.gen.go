// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/altscore (interfaces: AltScoreProvider)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	altscore "git.redmadrobot.com/zaman/backend/zaman/pkg/altscore"
	utils "git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
)

// MockAltScoreProvider is a mock of AltScoreProvider interface.
type MockAltScoreProvider struct {
	ctrl     *gomock.Controller
	recorder *MockAltScoreProviderMockRecorder
}

// MockAltScoreProviderMockRecorder is the mock recorder for MockAltScoreProvider.
type MockAltScoreProviderMockRecorder struct {
	mock *MockAltScoreProvider
}

// NewMockAltScoreProvider creates a new mock instance.
func NewMockAltScoreProvider(ctrl *gomock.Controller) *MockAltScoreProvider {
	mock := &MockAltScoreProvider{ctrl: ctrl}
	mock.recorder = &MockAltScoreProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAltScoreProvider) EXPECT() *MockAltScoreProviderMockRecorder {
	return m.recorder
}

// ParseBankStatement mocks base method.
func (m *MockAltScoreProvider) ParseBankStatement(arg0 context.Context, arg1 *altscore.ParseBankStatementReq) (*altscore.ParseBankStatementResponse, *utils.RequestLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseBankStatement", arg0, arg1)
	ret0, _ := ret[0].(*altscore.ParseBankStatementResponse)
	ret1, _ := ret[1].(*utils.RequestLog)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ParseBankStatement indicates an expected call of ParseBankStatement.
func (mr *MockAltScoreProviderMockRecorder) ParseBankStatement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseBankStatement", reflect.TypeOf((*MockAltScoreProvider)(nil).ParseBankStatement), arg0, arg1)
}
