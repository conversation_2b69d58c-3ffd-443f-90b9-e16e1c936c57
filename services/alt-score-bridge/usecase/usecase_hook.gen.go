// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/usecase -i AltScoreBridge -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ AltScoreBridge = (*AltScoreBridgeHook)(nil)

// AltScoreBridgeHook implements AltScoreBridge interface wrapper
type AltScoreBridgeHook struct {
	AltScoreBridge
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// HealthCheck implements AltScoreBridge
func (_w *AltScoreBridgeHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.AltScoreBridge, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.AltScoreBridge, "HealthCheck", _params)

	hp1, err = _w.AltScoreBridge.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.AltScoreBridge, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements AltScoreBridge
func (_w *AltScoreBridgeHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.AltScoreBridge, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.AltScoreBridge, "HealthEvent", _params)

	_w.AltScoreBridge.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.AltScoreBridge, "HealthEvent", []any{})
	return
}

// InitConsumer implements AltScoreBridge
func (_w *AltScoreBridgeHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.AltScoreBridge, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.AltScoreBridge, "InitConsumer", _params)

	_w.AltScoreBridge.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.AltScoreBridge, "InitConsumer", []any{})
	return
}

// ParseBankStatement implements AltScoreBridge
func (_w *AltScoreBridgeHook) ParseBankStatement(ctx context.Context, req *entity.ParseBankStatementReq) (pp1 *entity.ParseBankStatementResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.AltScoreBridge, "ParseBankStatement", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.AltScoreBridge, "ParseBankStatement", _params)

	pp1, err = _w.AltScoreBridge.ParseBankStatement(_ctx, req)
	_w._postCall.Hook(_ctx, _w.AltScoreBridge, "ParseBankStatement", []any{pp1, err})
	return pp1, err
}

// NewAltScoreBridgeHook returns AltScoreBridgeHook
func NewAltScoreBridgeHook(object AltScoreBridge, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *AltScoreBridgeHook {
	return &AltScoreBridgeHook{
		AltScoreBridge: object,
		_beforeCall:    beforeCall,
		_postCall:      postCall,
		_onPanic:       onPanic,
	}
}
