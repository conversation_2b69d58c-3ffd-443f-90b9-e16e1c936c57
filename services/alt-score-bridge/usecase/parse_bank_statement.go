package usecase

import (
	"context"
	"errors"
	"strings"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/generic"

	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/storage/mongo"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/altscore"
	"git.redmadrobot.com/zaman/backend/zaman/services/alt-score-bridge/entity"
)

const defaultParseBankStatementRequestRetries = 3

func (u *useCasesImpl) ParseBankStatement(
	ctx context.Context,
	req *entity.ParseBankStatementReq,
) (*entity.ParseBankStatementResult, error) {
	result, altScoreErr := u.runParseBankStatementRequest(ctx, req)
	if altScoreErr != nil {
		return nil, altScoreErr
	}

	return result, nil
}

func (u *useCasesImpl) runParseBankStatementRequest(
	ctx context.Context,
	req *entity.ParseBankStatementReq,
) (*entity.ParseBankStatementResult, error) {
	httpReq := &altscore.ParseBankStatementReq{
		AltScoreRequest: altscore.AltScoreRequest{
			Statement: req.Params.Statement,
			BankName:  strings.ToUpper(req.Params.BankName),
		},
	}

	retryParams := generic.ParamsRetryWithBackoff[entity.ParseBankStatementResult]{
		MaxRetries: defaultParseBankStatementRequestRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: checkRetryNecessity,
		Operation: func() (*entity.ParseBankStatementResult, error) {
			resp, reqLog, err := u.Providers.AltScoreProvider.ParseBankStatement(ctx, httpReq)

			if reqLog != nil {
				reqLog.RequestBody = req.AltScoreUUID
				if req.ClientInfo != nil {
					reqLog.FillClientInfo(req.ClientInfo)
				}
			}

			rawData := entity.NewParseBankStatementRawData(ctx, &req.Params, resp, reqLog, err)

			docID, saveErr := u.Providers.Storage.SaveRawParsedFile(ctx, rawData, mongo.CollectionParseBankStatement)
			if saveErr != nil {
				logs.FromContext(ctx).Error().Msgf("Failed to save raw data: %v", saveErr)
			}

			result := entity.MakeClientRespToEntity(resp)

			if req.ClientInfo != nil {
				result.RequestLogID = &docID
			}

			return result, err
		},
	}

	return generic.RetryWithBackoff(retryParams)
}

func checkRetryNecessity(err error) bool {
	errorsList := []error{
		altscore.ErrIntegrationError,
	}
	for _, e := range errorsList {
		if errors.Is(err, e) {
			return true
		}
	}
	return false
}
