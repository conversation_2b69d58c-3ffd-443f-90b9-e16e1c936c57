package tests

import (
	"context"
	"errors"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	colvirConsts "git.redmadrobot.com/zaman/backend/zaman/services/colvir-bridge/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

type AccountDBSME struct {
	ID               uuid.UUID
	ArrestBlocking   string
	Balance          decimal.Decimal
	BalanceNatival   decimal.Decimal
	BlockedBalance   decimal.Decimal
	AvailableBalance decimal.Decimal
	ArrestDebtAmount decimal.Decimal
	ClientCode       string
	ClientIin        string
	ClientName       string
	DateOpened       string
	DateClosed       string
	Status           string
	Type             string
	Currency         string
	HasArrest        string
}

var colvirAccounts = []*colvirBridge.Account{
	{
		Iban:       "IBAN3",
		DateOpened: "2021-03-01",
		Currency:   consts.CurrencyUSD.String(),
		Type:       consts.CurrentType.String(),
		Status:     colvirConsts.AccountOpened.String(),
	},
	{
		Iban:       "IBAN1",
		DateOpened: "2021-01-01",
		Currency:   consts.CurrencyEUR.String(),
		Type:       consts.CurrentType.String(),
		Status:     colvirConsts.AccountOpened.String(),
	},
	{
		Iban:       "IBAN2",
		DateOpened: "2021-02-01",
		Currency:   consts.CurrencyKZT.String(),
		Type:       consts.CurrentType.String(),
		Status:     colvirConsts.AccountOpened.String(),
	},
}

func (s *Suite) TestMakeColvirAccountsSMEListFilter_EdgeCases() {
	colvirAccounts := []*colvirBridge.Account{
		{
			Iban:       "IBAN_CORRECT_DATE",
			DateOpened: "2020-01-01",
			Currency:   consts.CurrencyKZT.String(),
			Type:       consts.CurrentType.String(),
			Status:     colvirConsts.AccountOpened.String(),
		},
		{
			Iban:       "WRONG_DATE",
			DateOpened: "wrong-date",
			Currency:   consts.CurrencyKZT.String(),
			Type:       consts.CurrentType.String(),
			Status:     colvirConsts.AccountOpened.String(),
		},
		{
			Iban:       "IBAN_EMPTY_DATE",
			DateOpened: "",
			Currency:   consts.CurrencyKZT.String(),
			Type:       consts.CurrentType.String(),
			Status:     colvirConsts.AccountOpened.String(),
		},
		{
			Iban:       "IBAN_WRONG_STATUS",
			DateOpened: "2022-01-01",
			Currency:   consts.CurrencyKZT.String(),
			Type:       consts.CurrentType.String(),
			Status:     colvirConsts.AccountClosed.String(),
		},
		{
			Iban:       "IBAN_WRONG_TYPE",
			DateOpened: "2022-01-01",
			Currency:   consts.CurrencyKZT.String(),
			Type:       consts.OthersType.String(),
			Status:     colvirConsts.AccountOpened.String(),
		},
		{
			Iban:       "IBAN_DB_ONLY",
			DateOpened: "2021-01-01",
			Currency:   consts.CurrencyKZT.String(),
			Type:       consts.CurrentType.String(),
			Status:     colvirConsts.AccountOpened.String(),
		},
	}

	accountsFromDB := []*ent.Accounts{
		{Iban: "IBAN_DB_ONLY"},
	}

	result := entity.MakeColvirAccountsSMEListFilter(colvirAccounts, accountsFromDB)

	s.Require().Len(result, 1)

	s.Equal("IBAN_CORRECT_DATE", result[0].Iban)
}

func (s *Suite) TestMakeColvirAccountsSMEListFilter() {
	colvirAccounts := []*colvirBridge.Account{
		{
			ID:         "1",
			Iban:       "IBAN1",
			DateOpened: "2021-01-01",
			Currency:   consts.CurrencyKZT.String(),
			Type:       consts.CurrentType.String(),
			Status:     colvirConsts.AccountOpened.String(),
		},
		{
			ID:         "2",
			Iban:       "IBAN2",
			DateOpened: "2021-02-01",
			Currency:   consts.CurrencyUSD.String(),
			Type:       consts.CurrentType.String(),
			Status:     colvirConsts.AccountOpened.String(),
		},
		{
			ID:         "3",
			Iban:       "IBAN3",
			DateOpened: "2021-03-01",
			Currency:   consts.CurrencyEUR.String(),
			Type:       consts.OthersType.String(),
			Status:     colvirConsts.AccountOpened.String(),
		},
		{
			ID:         "4",
			Iban:       "IBAN4",
			DateOpened: "2021-04-01",
			Currency:   consts.CurrencyKZT.String(),
			Type:       consts.CurrentType.String(),
			Status:     colvirConsts.AccountClosed.String(),
		},
	}

	accountsFromDB := []*ent.Accounts{
		{Iban: "IBAN3"},
	}

	result := entity.MakeColvirAccountsSMEListFilter(colvirAccounts, accountsFromDB)

	s.Require().Len(result, 3)
	foundIBAN3 := false
	for _, a := range result {
		if a.Iban == "IBAN3" {
			foundIBAN3 = true
		}
		s.Require().NotEqual(colvirConsts.AccountClosed.String(), a.Status)
	}
	s.Require().True(foundIBAN3)

	s.Require().True(result[0].DateOpened <= result[1].DateOpened)
	s.Require().True(result[1].DateOpened <= result[2].DateOpened)
}

func (s *Suite) TestMakeColvirAccountsSMEListFilter_SortAndCurrency() {
	accountsFromDB := []*ent.Accounts{}

	result := entity.MakeColvirAccountsSMEListFilter(colvirAccounts, accountsFromDB)

	s.Require().Len(result, 3)

	// Проверяем сортировку по дате открытия счета
	s.Assert().Equal("IBAN1", result[0].Iban)
	s.Assert().Equal("IBAN2", result[1].Iban)
	s.Assert().Equal("IBAN3", result[2].Iban)

	// Проверяем что все валюты присутствуют
	currencies := map[string]bool{}
	for _, acc := range result {
		currencies[acc.Currency] = true
	}
	s.Assert().True(currencies[consts.CurrencyUSD.String()])
	s.Assert().True(currencies[consts.CurrencyEUR.String()])
	s.Assert().True(currencies[consts.CurrencyKZT.String()])
}

func (s *Suite) TestMakeColvirAccountsSMEListFilter_WithAccountsFromDB() {
	colvirAccounts := []*colvirBridge.Account{
		{Iban: "IBAN1", Currency: consts.CurrencyUSD.String(), Balance: 100, Type: consts.CurrentType.String(), Status: colvirConsts.AccountOpened.String(), DateOpened: "2021-01-01"},
		{Iban: "IBAN2", Currency: consts.CurrencyUSD.String(), Balance: 150, Type: consts.CurrentType.String(), Status: colvirConsts.AccountOpened.String(), DateOpened: "2021-02-01"},
		{Iban: "IBAN3", Currency: consts.CurrencyEUR.String(), Balance: 200, Type: consts.CurrentType.String(), Status: colvirConsts.AccountOpened.String(), DateOpened: "2021-03-01"},
	}
	accountsFromDB := []*ent.Accounts{
		{Iban: "IBAN1"},
	}

	req := require.New(s.T())
	result := entity.MakeColvirAccountsSMEListFilter(colvirAccounts, accountsFromDB)

	req.Len(result, 2)
	req.Equal("IBAN2", result[0].Iban)
	req.Equal("IBAN3", result[1].Iban)
}

func (s *Suite) TestGetAccountsSME_Success() {
	s.ctx = utils.PutSmeOrigin(s.ctx)

	req := require.New(s.T())

	userInfo := make(map[string]interface{})
	userInfo["user_id"] = testUserID
	ctx := context.WithValue(s.ctx, userInfoKey, userInfo)

	err := s.postgresDB.Accounts.Create().
		SetArrestBlocking("0").
		SetBalance(decimal.New(1010, 2)).
		SetBalanceNatival(decimal.New(0, 2)).
		SetBlockedBalance(decimal.New(0, 2)).
		SetAvailableBalance(decimal.New(1010, 2)).
		SetArrestDebtAmount(decimal.New(0, 2)).
		SetClientCode(testClientCode).
		SetIban("********************").
		SetClientIin(testUserIin).
		SetClientName("Test Client").
		SetDateOpened("2021-01-01").
		SetDateClosed("2021-01-01").
		SetStatus(colvirConsts.AccountOpened.String()).
		SetType(consts.CurrentType.String()).
		SetCurrency(consts.CurrencyKZT.String()).
		SetHasArrest("0").
		SetOrigin(utils.UserOriginSme).
		Exec(s.ctx)
	require.NoError(s.T(), err)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(gomock.Any(), gomock.Any()).
		Return(&users.GetUserByIDResp{Iin: testUserIin}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClient(gomock.Any(), gomock.Any()).
		Return(&colvirBridge.FindClientResp{Clients: []*colvirBridge.Client{
			{
				Code: testClientCode,
			},
		}}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClientAccountList(gomock.Any(), gomock.Any()).
		Return(&colvirBridge.FindClientAccountListResp{Accounts: []*colvirBridge.Account{
			{
				ID:             uuid.NewString(),
				Status:         colvirConsts.AccountOpened.String(),
				Type:           consts.CurrentType.String(),
				Currency:       consts.CurrencyCNY.String(),
				ClientCode:     testClientCode,
				Iban:           "********************",
				Number:         "********************",
				DateOpened:     "2021-01-01",
				Balance:        1000.0,
				BalanceNatival: 1000.0,
				BlockedBalance: 0.0,
				HasArrest:      "0",
			},
			{
				ID:             testAccountID.String(),
				Status:         colvirConsts.AccountOpened.String(),
				Type:           consts.CurrentType.String(),
				Currency:       consts.CurrencyKZT.String(),
				ClientCode:     testClientCode,
				Iban:           "********************",
				Number:         "********************",
				DateOpened:     "2021-01-01",
				Balance:        1010.0,
				BalanceNatival: 1000.0,
				BlockedBalance: 0.0,
				HasArrest:      "0",
			},
			{
				ID:             uuid.NewString(),
				Status:         colvirConsts.AccountOpened.String(),
				Type:           consts.OthersType.String(),
				Currency:       consts.CurrencyKZT.String(),
				ClientCode:     testClientCode,
				Iban:           "********************",
				Number:         "********************",
				DateOpened:     "2021-01-01",
				Balance:        1000.0,
				BalanceNatival: 1000.0,
				BlockedBalance: 0.0,
				HasArrest:      "0",
			},
		}}, nil).Times(1)

	grpcResp, err := s.grpc.GetAccountsSME(ctx, &pb.GetAccountsSMERequest{})

	s.Require().NoError(err)
	s.Require().NotNil(grpcResp)

	req.NoError(err)
	req.NotNil(grpcResp)

	expected := map[string]struct {
		Status        string
		Type          string
		Currency      string
		OpenDate      string
		Balance       float64
		BalanceNatval float64
		Available     float64
		PlanSum       float64
	}{
		"********************": {
			Status:        consts.AccountActive.Str(),
			Type:          consts.CurrentType.String(),
			Currency:      consts.CurrencyKZT.String(),
			OpenDate:      "2021-01-01",
			Balance:       1010.0,
			BalanceNatval: 1000.0,
			Available:     1010.0,
			PlanSum:       0.0,
		},
		"********************": {
			Status:        consts.AccountActive.Str(),
			Type:          consts.CurrentType.String(),
			Currency:      consts.CurrencyCNY.String(),
			OpenDate:      "2021-01-01",
			Balance:       1000.0,
			BalanceNatval: 1000.0,
			Available:     1000.0,
			PlanSum:       0.0,
		},
	}

	req.Len(grpcResp.AccountsSME, 2)

	for _, account := range grpcResp.AccountsSME {
		req.NotEmpty(account.ID)
		exp, ok := expected[account.Iban]
		req.True(ok, "unexpected IBAN: %s", account.Iban)

		req.Equal(exp.Status, account.Status)
		req.Equal(exp.Type, account.Type)
		req.Equal(exp.Currency, account.Currency)
		req.Equal(exp.OpenDate, account.OpenDate)
		req.Equal(exp.Balance, account.Balance)
		req.Equal(exp.BalanceNatval, account.BalanceNatval)
		req.Equal(exp.Available, account.AvailableBalance)
		req.Equal(exp.PlanSum, account.PlanSum)
	}
}

func (s *Suite) TestGetAccountsSMEFromColvir_Success() {
	s.ctx = utils.PutSmeOrigin(s.ctx)
	req := require.New(s.T())

	userInfo := make(map[string]interface{})
	userInfo["user_id"] = testUserID
	ctx := context.WithValue(s.ctx, userInfoKey, userInfo)

	err := s.postgresDB.Accounts.Create().
		SetArrestBlocking("0").
		SetBalance(decimal.New(1010, 2)).
		SetBalanceNatival(decimal.New(0, 2)).
		SetBlockedBalance(decimal.New(0, 2)).
		SetAvailableBalance(decimal.New(1010, 2)).
		SetArrestDebtAmount(decimal.New(0, 2)).
		SetClientCode(testClientCode).
		SetIban("********************").
		SetClientIin(testUserIin).
		SetClientName("Test Client").
		SetDateOpened("2021-01-01").
		SetDateClosed("2021-01-01").
		SetStatus(colvirConsts.AccountOpened.String()).
		SetType(consts.CurrentType.String()).
		SetCurrency(consts.CurrencyKZT.String()).
		SetHasArrest("0").
		SetOrigin(utils.UserOriginSme).
		Exec(s.ctx)
	require.NoError(s.T(), err)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(gomock.Any(), gomock.Any()).
		Return(&users.GetUserByIDResp{Iin: testUserIin}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClient(gomock.Any(), gomock.Any()).
		Return(&colvirBridge.FindClientResp{Clients: []*colvirBridge.Client{
			{
				Code: testClientCode,
			},
		}}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClientAccountList(gomock.Any(), gomock.Any()).
		Return(&colvirBridge.FindClientAccountListResp{Accounts: []*colvirBridge.Account{
			{
				ID:             testAccountID.String(),
				Status:         colvirConsts.AccountOpened.String(),
				ClientCode:     testClientCode,
				Type:           consts.OthersType.String(),
				Currency:       consts.CurrencyKZT.String(),
				Iban:           "********************",
				Number:         "********************",
				DateOpened:     "2021-01-01",
				Balance:        1000.0,
				BalanceNatival: 1000.0,
				BlockedBalance: 0.0,
				HasArrest:      "0",
			},
			{
				ID:             testAccountID.String(),
				ClientCode:     testClientCode,
				Status:         colvirConsts.AccountOpened.String(),
				Type:           consts.CurrentType.String(),
				Currency:       consts.CurrencyKZT.String(),
				Iban:           "********************",
				Number:         "********************",
				DateOpened:     "2021-01-01",
				Balance:        1010.0,
				BalanceNatival: 1000.0,
				BlockedBalance: 0.0,
				HasArrest:      "0",
			},
			{
				ID:             uuid.NewString(),
				ClientCode:     testClientCode,
				Status:         colvirConsts.AccountOpened.String(),
				Type:           consts.OthersType.String(),
				Currency:       consts.CurrencyKZT.String(),
				Iban:           "********************",
				Number:         "********************",
				DateOpened:     "2021-01-01",
				Balance:        1000.0,
				BalanceNatival: 1000.0,
				BlockedBalance: 0.0,
				HasArrest:      "0",
			},
		}}, nil).Times(1)

	grpcResp, err := s.grpc.GetAccountsSME(ctx, &pb.GetAccountsSMERequest{})

	s.Require().NoError(err)
	s.Require().NotNil(grpcResp)

	req.NoError(err)
	req.NotNil(grpcResp)

	req.Len(grpcResp.AccountsSME, 1)
	req.NotEmpty(grpcResp.AccountsSME[0].ID)
	req.Equal(consts.AccountActive.Str(), grpcResp.AccountsSME[0].Status)
	req.Equal(consts.CurrentType.String(), grpcResp.AccountsSME[0].Type)
	req.Equal(consts.CurrencyKZT.String(), grpcResp.AccountsSME[0].Currency)
	req.Equal("********************", grpcResp.AccountsSME[0].Iban)
	req.Equal("2021-01-01", grpcResp.AccountsSME[0].OpenDate)
	req.Equal(1010.0, grpcResp.AccountsSME[0].Balance)
	req.Equal(1000.0, grpcResp.AccountsSME[0].BalanceNatval)
	req.Equal(1010.0, grpcResp.AccountsSME[0].AvailableBalance)
	req.Equal(0.0, grpcResp.AccountsSME[0].PlanSum)
	req.False(grpcResp.AccountsSME[0].Arrest.Blocking)
}

func (s *Suite) TestGetAccountsSME_FromDB_Success() {
	s.ctx = utils.PutSmeOrigin(s.ctx)
	req := require.New(s.T())

	userInfo := make(map[string]interface{})
	userInfo["user_id"] = testUserID
	ctx := context.WithValue(s.ctx, userInfoKey, userInfo)

	err := s.postgresDB.Accounts.Create().
		SetArrestBlocking("0").
		SetBalance(decimal.New(1010, 2)).
		SetBalanceNatival(decimal.New(0, 2)).
		SetBlockedBalance(decimal.New(0, 2)).
		SetAvailableBalance(decimal.New(1010, 2)).
		SetArrestDebtAmount(decimal.New(0, 2)).
		SetClientCode(testClientCode).
		SetIban("********************").
		SetClientIin(testUserIin).
		SetClientName("Test Client").
		SetDateOpened("2021-01-01").
		SetDateClosed("2021-01-01").
		SetStatus(colvirConsts.AccountOpened.String()).
		SetType(consts.CurrentType.String()).
		SetCurrency(consts.CurrencyKZT.String()).
		SetHasArrest("0").
		SetOrigin(utils.UserOriginSme).
		Exec(s.ctx)
	require.NoError(s.T(), err)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(gomock.Any(), gomock.Any()).
		Return(&users.GetUserByIDResp{Iin: testUserIin}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClient(gomock.Any(), gomock.Any()).
		Return(&colvirBridge.FindClientResp{Clients: []*colvirBridge.Client{
			{
				Code: testClientCode,
			},
		}}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClientAccountList(gomock.Any(), gomock.Any()).
		Return(&colvirBridge.FindClientAccountListResp{Accounts: []*colvirBridge.Account{
			{
				ID:             testAccountID.String(),
				ClientCode:     testClientCode,
				Status:         colvirConsts.AccountOpened.String(),
				Type:           consts.CurrentType.String(),
				Currency:       consts.CurrencyKZT.String(),
				Iban:           "********************",
				Number:         "********************",
				DateOpened:     "2021-01-01",
				Balance:        2010.0,
				BalanceNatival: 1000.0,
				BlockedBalance: 0.0,
				HasArrest:      "0",
			},
			{
				ID:             testAccountID.String(),
				ClientCode:     testClientCode,
				Status:         colvirConsts.AccountOpened.String(),
				Type:           consts.CurrentType.String(),
				Currency:       consts.CurrencyKZT.String(),
				Iban:           "********************",
				Number:         "********************",
				DateOpened:     "2021-01-01",
				Balance:        1010.0,
				BalanceNatival: 1000.0,
				BlockedBalance: 0.0,
				HasArrest:      "0",
			},
		}}, nil).Times(1)

	grpcResp, err := s.grpc.GetAccountsSME(ctx, &pb.GetAccountsSMERequest{})

	req.NoError(err)
	req.NotNil(grpcResp)
	req.Len(grpcResp.AccountsSME, 1)
}

func (s *Suite) TestGetAccountsSME_UserNotFound() {
	s.ctx = utils.PutSmeOrigin(s.ctx)
	req := require.New(s.T())

	s.mocks.GRPC.Users.EXPECT().GetUserByID(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("user not found")).Times(1)

	userInfo := make(map[string]interface{})
	userInfo["user_id"] = testUserID
	ctx := context.WithValue(s.ctx, userInfoKey, userInfo)

	grpcResp, err := s.grpc.GetAccountsSME(ctx, &pb.GetAccountsSMERequest{})

	s.Require().Error(err)
	s.Require().Nil(grpcResp)

	req.Error(err)
	req.Nil(grpcResp)
	req.Contains(err.Error(), "could not get user")
}

func (s *Suite) TestGetAccountsSME_ClientNotFound() {
	s.ctx = utils.PutSmeOrigin(s.ctx)
	req := require.New(s.T())

	s.mocks.GRPC.Users.EXPECT().GetUserByID(gomock.Any(), gomock.Any()).
		Return(&users.GetUserByIDResp{Iin: testUserIin}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClient(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("client not found")).Times(1)

	userInfo := make(map[string]interface{})
	userInfo["user_id"] = testUserID
	ctx := context.WithValue(s.ctx, userInfoKey, userInfo)

	grpcResp, err := s.grpc.GetAccountsSME(ctx, &pb.GetAccountsSMERequest{})

	s.Require().Error(err)
	s.Require().Nil(grpcResp)

	req.Error(err)
	req.Nil(grpcResp)
	req.Contains(err.Error(), "client not found")
}

func (s *Suite) TestGetAccountsSME_FindClientAccountList_Error() {
	s.ctx = utils.PutSmeOrigin(s.ctx)
	req := require.New(s.T())

	userInfo := make(map[string]interface{})
	userInfo["user_id"] = testUserID
	ctx := context.WithValue(s.ctx, userInfoKey, userInfo)

	// Создаем запись в БД (чтобы база была не пустая)
	err := s.postgresDB.Accounts.Create().
		SetArrestBlocking("0").
		SetBalance(decimal.New(1010, 2)).
		SetBalanceNatival(decimal.New(0, 2)).
		SetBlockedBalance(decimal.New(0, 2)).
		SetAvailableBalance(decimal.New(1010, 2)).
		SetArrestDebtAmount(decimal.New(0, 2)).
		SetClientCode(testClientCode).
		SetIban("********************").
		SetClientIin(testUserIin).
		SetClientName("Test Client").
		SetDateOpened("2021-01-01").
		SetDateClosed("2021-01-01").
		SetStatus(colvirConsts.AccountOpened.String()).
		SetType(consts.CurrentType.String()).
		SetCurrency(consts.CurrencyKZT.String()).
		SetHasArrest("0").
		SetOrigin(utils.UserOriginSme).
		Exec(s.ctx)
	require.NoError(s.T(), err)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(gomock.Any(), gomock.Any()).
		Return(&users.GetUserByIDResp{Iin: testUserIin}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClient(gomock.Any(), gomock.Any()).
		Return(&colvirBridge.FindClientResp{Clients: []*colvirBridge.Client{
			{Code: testClientCode},
		}}, nil).Times(1)

	s.mocks.GRPC.Colvirbridge.EXPECT().FindClientAccountList(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("colvir error")).Times(1)

	grpcResp, err := s.grpc.GetAccountsSME(ctx, &pb.GetAccountsSMERequest{})

	req.NoError(err)
	req.NotNil(grpcResp)
	req.Len(grpcResp.AccountsSME, 1)
}
