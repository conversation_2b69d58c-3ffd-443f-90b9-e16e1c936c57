package entity

import (
	commonConsts "git.redmadrobot.com/zaman/backend/zaman/pkg/common"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
)

type (
	SaveAccountReq struct {
		Account *AccountSaveToDB
	}

	AccountSaveToDB struct {
		Balance          *AccountBalance
		Client           *AccountClient
		Date             *AccountDate
		Document         *AccountDocument
		Arrest           *AccountArrest
		Currency         string `json:"currency"`          // Валюта счета
		DeaReferenceID   string `json:"dea_reference_id"`  // ID ссылки на сделку
		Iban             string `json:"iban"`              // IBAN счета
		ID               string `json:"id"`                // ID счета
		Origin           string `json:"origin"`            // Источник счета
		ShortName        string `json:"short_name"`        // Краткое название счета
		Status           string `json:"status"`            // Статус счета
		Title            string `json:"title"`             // Название счета
		Type             string `json:"type"`              // Тип счета
		IsMain           bool   `json:"is_main"`           // Флаг, указывающий, является ли счет основным
		AccessionAccount bool   `json:"accession_account"` // Есть ли уже присоединённый счет?
	}

	AccountBalance struct {
		Main      float64 `json:"main"`      // Основной баланс
		Natival   float64 `json:"natival"`   // Баланс в нацвалюте
		Available float64 `json:"available"` // Доступный баланс
		Blocked   float64 `json:"blocked"`   // Заблокированный баланс
	}

	AccountClient struct {
		Iin  string `json:"iin"`  // ИИН клиента
		Code string `json:"code"` // Код клиента
		Name string `json:"name"` // Имя клиента
		Type string `json:"type"` // Тип клиента
	}

	AccountDate struct {
		Opened string `json:"opened"` // Дата открытия счета
		Closed string `json:"closed"` // Дата закрытия счета
	}

	AccountDocument struct {
		ID     string `json:"id"`     // ID документа
		Number string `json:"number"` // Номер документа
	}

	AccountArrest struct {
		Blocking            bool    `json:"blocking"`              // Флаг блокировки счета
		DebtAmount          float64 `json:"debt_amount"`           // Сумма долга
		Date                string  `json:"date"`                  // Дата ареста
		Code                string  `json:"code"`                  // Код ареста
		BenName             string  `json:"ben_name"`              // Имя бенефициара
		ExecutorContactInfo string  `json:"executor_contact_info"` // Контактная информация исполнителя
	}

	SaveAccountResult struct {
		Success bool
	}
)

func MakeCardsDtoToAccountSaveReq(cardsAccounts *CardAndAccountDTO) *SaveAccountReq {
	if cardsAccounts == nil {
		return &SaveAccountReq{
			Account: &AccountSaveToDB{
				Balance:  &AccountBalance{},
				Client:   &AccountClient{},
				Date:     &AccountDate{},
				Document: &AccountDocument{},
				Arrest:   &AccountArrest{},
			},
		}
	}

	return &SaveAccountReq{
		Account: &AccountSaveToDB{
			Balance: &AccountBalance{},
			Client: &AccountClient{
				Iin:  cardsAccounts.UserIIN,
				Code: cardsAccounts.ClientCode,
				Name: cardsAccounts.ClientName,
			},
			Document: &AccountDocument{},
			Date: &AccountDate{
				Opened: cardsAccounts.Account.OpenDate.Format(commonConsts.DFDefault.Str()),
			},
			Arrest: &AccountArrest{
				Blocking: false,
			},
			Status:           cardsAccounts.Account.Status.Str(),
			Currency:         cardsAccounts.Account.Currency.Str(),
			DeaReferenceID:   cardsAccounts.Account.DeaReferenceID,
			Iban:             cardsAccounts.Account.Iban,
			ID:               cardsAccounts.Account.ID,
			IsMain:           cardsAccounts.Account.IsMain,
			AccessionAccount: cardsAccounts.Account.AccessionAccount,
		},
	}
}

// MakeSaveAccountPbToEntity создает объект из pb.SaveAccountRequest в SaveAccountReq для передачи в usecase
func MakeSaveAccountPbToEntity(req *pb.SaveAccountRequest) *SaveAccountReq {
	if req == nil {
		return &SaveAccountReq{Account: &AccountSaveToDB{
			Balance:  &AccountBalance{},
			Client:   &AccountClient{},
			Date:     &AccountDate{},
			Document: &AccountDocument{},
		}}
	}

	// Create a new AccountSaveToDB with default values
	account := AccountSaveToDB{
		Balance:  &AccountBalance{},
		Client:   &AccountClient{},
		Date:     &AccountDate{},
		Document: &AccountDocument{},
		Arrest:   &AccountArrest{},
	}

	// If req.Account is not nil, copy its values
	if req.Account != nil {
		// Copy basic fields
		account.Currency = req.Account.Currency
		account.DeaReferenceID = req.Account.DeaReferenceID
		account.Iban = req.Account.Iban
		account.ID = req.Account.ID
		account.Origin = req.Account.Origin
		account.ShortName = req.Account.ShortName
		account.Status = req.Account.Status
		account.Title = req.Account.Title
		account.Type = req.Account.Type
		account.IsMain = req.Account.IsMain
		account.AccessionAccount = req.Account.AccessionAccount

		// Copy Balance if not nil
		if req.Account.Balance != nil {
			account.Balance.Main = req.Account.Balance.Main
			account.Balance.Natival = req.Account.Balance.Natival
			account.Balance.Available = req.Account.Balance.Available
			account.Balance.Blocked = req.Account.Balance.Blocked
		}

		// Copy Client if not nil
		if req.Account.Client != nil {
			account.Client.Iin = req.Account.Client.Iin
			account.Client.Code = req.Account.Client.Code
			account.Client.Name = req.Account.Client.Name
			account.Client.Type = req.Account.Client.Type
		}

		// Copy Date if not nil
		if req.Account.Date != nil {
			account.Date.Opened = req.Account.Date.Opened
			account.Date.Closed = req.Account.Date.Closed
		}

		// Copy Document if not nil
		if req.Account.Document != nil {
			account.Document.ID = req.Account.Document.Id
			account.Document.Number = req.Account.Document.Number
		}

		// Copy Arrest if not nil
		if req.Account.Arrest != nil {
			account.Arrest.Blocking = req.Account.Arrest.Blocking
			account.Arrest.DebtAmount = req.Account.Arrest.DebtAmount
			account.Arrest.Date = req.Account.Arrest.Date
			account.Arrest.Code = req.Account.Arrest.Code
			account.Arrest.BenName = req.Account.Arrest.BenName
			account.Arrest.ExecutorContactInfo = req.Account.Arrest.ExecutorContactInfo
		}
	}

	return &SaveAccountReq{
		Account: &account,
	}
}

// MakeSaveAccountEntityToPb создает объект из SaveAccount в pb.SaveAccountResponse для возврата ответа из сервиса
func MakeSaveAccountEntityToPb(res *SaveAccountResult) *pb.SaveAccountResponse {
	return &pb.SaveAccountResponse{
		IsSuccess: res.Success,
	}
}
