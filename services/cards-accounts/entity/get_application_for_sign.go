package entity

import (
	"context"
	"fmt"
	"strings"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	common "git.redmadrobot.com/zaman/backend/zaman/pkg/common"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	amlBridgePb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	pkbBridgePb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
)

type (
	GetApplicationForSignReq struct {
		DocType    string
		Currensies []string
	}

	GetApplicationForSignResult struct {
		Documents []Document
	}

	Document struct {
		ID      string
		Title   string
		Type    string
		Version int32
		Link    string
		Signed  bool
	}
)

// MakeGetApplicationForSignPbToEntity создает объект из pb.GetApplicationForSignReq в GetApplicationForSignReq для передачи в usecase
func MakeGetApplicationForSignPbToEntity(req *pb.GetApplicationForSignReq) *GetApplicationForSignReq {
	if req == nil {
		return &GetApplicationForSignReq{}
	}

	return &GetApplicationForSignReq{
		DocType:    req.DocType.String(),
		Currensies: req.Currencies,
	}
}

// MakeGetApplicationForSignEntityToPb создает объект из GetApplicationForSign в pb.GetApplicationForSignResp для возврата ответа из сервиса
func MakeGetApplicationForSignEntityToPb(res *GetApplicationForSignResult) *pb.GetApplicationForSignResp {
	if res == nil {
		return &pb.GetApplicationForSignResp{}
	}

	documents := make([]*pb.Document, 0, len(res.Documents))
	for _, doc := range res.Documents {
		documents = append(documents, &pb.Document{
			Id:      doc.ID,
			Title:   doc.Title,
			Type:    doc.Type,
			Version: doc.Version,
			Link:    doc.Link,
			Signed:  doc.Signed,
		})
	}

	return &pb.GetApplicationForSignResp{
		Documents: documents,
	}
}

// PrepareAmlNewCheckRequest формирует запрос на AML проверку для нового клиента
func PrepareAmlNewCheckRequest(ctx context.Context, userID string, personalInfo *pkbBridgePb.GetPersonalInfoByIinResp) *amlBridgePb.NewCheckOnlineClientCardRequest {
	// TODO: допилить маппинг для получения
	requestID := utils.ExtractRequestID(ctx)
	// UID = уникальный номер запроса с префиксом “UPDATEKK” (пример: <UID>UPDATEKK:-C8B859E9-70CB-73F0-E053-9B00A8C079C2</UID>)
	amlCheckReqID := fmt.Sprintf("%s:%s", "UPDATEKK", requestID)
	ursName := formatUrsName(personalInfo)
	bsClientID := fmt.Sprintf("000-%s", userID)
	bankClientCode := consts.AmlTypeBankClientCode.String()
	defaultIsNewFlag := consts.AmlDefaultFlag.String()
	defaultBankClientFlag := consts.AmlTypeDefaultRetail.String()
	documentCode := "1"

	var (
		docSeries    string
		docNumber    string
		docIssueDate string
		docExireDate string
	)

	timeNowKz, err := utils.GetCurrentKzTime()
	if err != nil {
		logs.FromContext(ctx).Error().Err(err).Msg("Failed to get current KZ time")
	}

	for _, doc := range personalInfo.Documents {
		if doc.Type.Code == consts.DefaultDocumentClientCode.String() && doc.Status.Code == consts.DefaultDocumentStatusCode {
			documentCode = consts.DefaultDocumentCode
			docSeries = consts.DefaultDocumentSeries
			docNumber = doc.Number
			docIssueDate = doc.BeginDate
			docExireDate = doc.EndDate
			break
		}
	}

	return &amlBridgePb.NewCheckOnlineClientCardRequest{
		TypeList:        consts.AmlTypeList.String(),
		CheckType:       consts.AmlTypeDefaultRetail.String(),
		Instance:        consts.AmlTypeInstance.String(),
		IssuedBid:       consts.AmlTypeInstance.String(),
		User:            consts.AmlTypeUser.String(),
		Uid:             amlCheckReqID,
		Product:         consts.AmlTypeProduct.String(),
		Roles:           consts.AmlTypeRoles.String(),
		BankClientCode:  &bankClientCode,
		OperationDate:   timeNowKz.Format(utils.OperationDateFormat),
		ClientId:        personalInfo.Iin,
		BsClientId:      &bsClientID,
		IsNew:           &defaultIsNewFlag,
		UrName:          ursName,
		FirstName:       personalInfo.Name,
		Lastname:        personalInfo.Surname,
		Middlename:      personalInfo.Patronymic,
		Type:            consts.AmlTypeDefaultRetail.String(),
		BankClient:      &defaultBankClientFlag,
		ResCountryCode:  consts.ResidenceKZ.String(),
		AcBirthdate:     personalInfo.Dob,
		AcBirthplace:    personalInfo.BirthPlace.City,
		AcRegCountry:    personalInfo.Citizenship.Code, // проверить
		AcDocTypeCode:   &documentCode,
		AcDocSeries:     &docSeries,
		AcDocNumber:     &docNumber,
		AcDocWhom:       findIssueOrganization(personalInfo),
		AcDocIssueDate:  &docIssueDate,
		AcDocExpireDate: &docExireDate,
	}
}

// formatUrsName форматирует имя пользователя в формате "Фамилия Имя.Отчество." или "Фамилия И.О." в зависимости от наличия отчества.
func formatUrsName(coidPersonalData *pkbBridgePb.GetPersonalInfoByIinResp) string {
	if coidPersonalData == nil {
		return consts.EmptyString
	}

	name := strings.TrimSpace(coidPersonalData.Name)
	surname := strings.TrimSpace(coidPersonalData.Surname)
	patronymic := strings.TrimSpace(coidPersonalData.Patronymic)

	var nameFirstLetter string
	if len(name) > 0 {
		runes := []rune(name)
		nameFirstLetter = string(runes[0])
	}

	if !common.IsEmptyString(patronymic) {
		var patronymicFirstLetter string

		if len(patronymic) > 0 {
			runes := []rune(patronymic)
			patronymicFirstLetter = string(runes[0])
		}

		return fmt.Sprintf("%s %s.%s.", surname, nameFirstLetter, patronymicFirstLetter)
	}

	return fmt.Sprintf("%s %s.", surname, nameFirstLetter)
}

// findIssueOrganization ищет организацию, выдавшую документ, в ответе на запрос личной информации по ИИН.
func findIssueOrganization(req *pkbBridgePb.GetPersonalInfoByIinResp) *string {
	docStatus := consts.EmptyString
	for _, doc := range req.Documents {
		if doc.Status.Code == consts.DefaultDocumentStatusCode {
			return &doc.IssueOrg.NameKZ
		}
	}
	return &docStatus
}
