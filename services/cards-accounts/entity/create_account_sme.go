package entity

import (
	dictionaryPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	pkbBridgePb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

// Структура для хранения данных необходимых для создания ИП
type SmeIPCreationData struct {
	JurInfo  *pkbBridgePb.SendJurSearchByIinResp
	IPInfo   *usersPb.SmeIPInfo
	Permits  *pkbBridgePb.GetPermitDocumentsByIinResp
	Kato     *dictionaryPb.KATOMapFromTSOIDResp
	UserInfo *usersPb.GetPersonalDataResp
}
