package entity

import (
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
)

type (
	GetAccountReq struct {
		ID string
	}

	GetAccountResult struct {
		ID               string
		Status           string
		DocumentID       string
		DocumentNumber   string
		Type             string
		Currency         string
		Iban             string
		OpenDate         string
		CloseDate        string
		Balance          float64
		BalanceNatval    float64
		PlanSum          float64
		AvailableBalance float64
		Arrest           Arrest
	}
)

// MakeGetAccountPbToEntity создает объект из pb.GetAccountRequest в GetAccountReq для передачи в usecase
func MakeGetAccountPbToEntity(req *pb.GetAccountRequest) *GetAccountReq {
	return &GetAccountReq{
		ID: req.AccountId,
	}
}

// MakeGetAccountEntityToPb создает объект из GetAccount в pb.GetAccountResponse для возврата ответа из сервиса
func MakeGetAccountEntityToPb(res *GetAccountResult) *pb.GetAccountResponse {
	return &pb.GetAccountResponse{
		ID:               res.ID,
		Status:           res.Status,
		DocumentID:       res.DocumentID,
		DocumentNumber:   res.DocumentNumber,
		Type:             res.Type,
		Currency:         res.Currency,
		Iban:             res.Iban,
		OpenDate:         res.OpenDate,
		CloseDate:        res.CloseDate,
		Balance:          res.Balance,
		BalanceNatval:    res.BalanceNatval,
		PlanSum:          res.PlanSum,
		AvailableBalance: res.AvailableBalance,
		Arrest:           MakeArrestEntityToPb(&res.Arrest),
	}
}

// MakeArrestEntityToPb создает объект из Arrest в pb.Arrest для возврата ответа из сервиса
func MakeArrestEntityToPb(res *Arrest) *pb.Arrest {
	return &pb.Arrest{
		Blocking: res.Blocking,
	}
}

func MakeAccountEntToEntity(entAcccount *ent.Accounts) *Account {
	balance, _ := entAcccount.Balance.Float64()
	balanceNatival, _ := entAcccount.BalanceNatival.Float64()
	availableBalance, _ := entAcccount.AvailableBalance.Float64()
	blockedBalance, _ := entAcccount.BlockedBalance.Float64()
	accountStatus := entAcccount.Status

	if entAcccount.Status == consts.ColvirAccountOpened.Str() {
		accountStatus = consts.AccountActive.Str()
	}

	return &Account{
		ID:               entAcccount.ID.String(),
		Status:           accountStatus,
		Type:             entAcccount.Type,
		Currency:         entAcccount.Currency,
		Iban:             entAcccount.Iban,
		OpenDate:         entAcccount.DateOpened,
		CloseDate:        entAcccount.DateClosed,
		Balance:          balance,
		BalanceNatval:    balanceNatival,
		AvailableBalance: availableBalance,
		PlanSum:          blockedBalance,
		ClientIIN:        entAcccount.ClientIin,
		// Проверяем, что арест - заблокирован
		Arrest: Arrest{
			Blocking: entAcccount.IsArrested,
		},
	}
}
