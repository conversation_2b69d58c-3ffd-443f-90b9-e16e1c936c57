package entity

import (
	"sort"
	"time"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	colvirConsts "git.redmadrobot.com/zaman/backend/zaman/services/colvir-bridge/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
)

type (
	GetAccountsSMEReq struct{}

	AccountSME struct {
		ID               string
		Status           string
		DocumentID       string
		Type             string
		Currency         string
		Iban             string
		OpenDate         string
		CloseDate        string
		Balance          float64
		BalanceNatval    float64
		PlanSum          float64
		AvailableBalance float64
		Arrest           Arrest
		ClientType       string
	}

	GetAccountsSMEResult struct{ Accounts []AccountSME }
)

// MakeGetAccountsSMEPbToEntity создает объект из pb.GetAccountsSMERequest в GetAccountsSMEReq для передачи в usecase
func MakeGetAccountsSMEPbToEntity(req *pb.GetAccountsSMERequest) *GetAccountsSMEReq {
	return &GetAccountsSMEReq{}
}

// MakeGetAccountsSMEEntityToPb создает объект из GetAccountsSME в pb.GetAccountsSMEResponse для возврата ответа из сервиса
func MakeGetAccountsSMEEntityToPb(res *GetAccountsSMEResult) *pb.GetAccountsSMEResponse {
	if res == nil {
		return &pb.GetAccountsSMEResponse{}
	}
	accounts := make([]*pb.AccountSME, len(res.Accounts))
	for i, account := range res.Accounts {
		accounts[i] = &pb.AccountSME{
			ID:               account.ID,
			Status:           account.Status,
			Type:             account.Type,
			Currency:         account.Currency,
			Iban:             account.Iban,
			OpenDate:         account.OpenDate,
			CloseDate:        account.CloseDate,
			Balance:          account.Balance,
			BalanceNatval:    account.BalanceNatval,
			PlanSum:          account.PlanSum,
			AvailableBalance: account.AvailableBalance,
			ClientType:       account.ClientType,
			Arrest: &pb.Arrest{
				Blocking: account.Arrest.Blocking,
			},
		}
	}

	return &pb.GetAccountsSMEResponse{AccountsSME: accounts}
}

func MakeAccountsSMEEntToEntity(entAccount []*ent.Accounts) []AccountSME {
	if len(entAccount) == 0 {
		return nil
	}

	accounts := make([]AccountSME, 0, len(entAccount))
	for _, account := range entAccount {
		accounts = append(accounts, *makeAccountSMEEntToEntity(account))
	}

	return accounts
}

func makeAccountSMEEntToEntity(entAcccount *ent.Accounts) *AccountSME {
	if entAcccount == nil {
		return nil
	}
	balance, _ := entAcccount.Balance.Float64()
	balanceNatival, _ := entAcccount.BalanceNatival.Float64()
	availableBalance, _ := entAcccount.AvailableBalance.Float64()
	blockedBalance, _ := entAcccount.BlockedBalance.Float64()
	accountStatus := entAcccount.Status

	if entAcccount.Status == consts.ColvirAccountOpened.Str() {
		accountStatus = consts.AccountActive.Str()
	}

	return &AccountSME{
		ID:               entAcccount.ID.String(),
		Status:           accountStatus,
		Type:             entAcccount.Type,
		Currency:         entAcccount.Currency,
		Iban:             entAcccount.Iban,
		OpenDate:         entAcccount.DateOpened,
		CloseDate:        entAcccount.DateClosed,
		Balance:          balance,
		BalanceNatval:    balanceNatival,
		AvailableBalance: availableBalance,
		PlanSum:          blockedBalance,
		ClientType:       entAcccount.ClientType,
		// Проверяем, что арест - заблокирован
		Arrest: Arrest{
			Blocking: entAcccount.IsArrested,
		},
	}
}

func copyAccount(account *colvirBridge.Account) *colvirBridge.Account {
	if account == nil {
		return nil
	}

	return &colvirBridge.Account{
		ID:                    account.ID,
		Status:                account.Status,
		Title:                 account.Title,
		ShortName:             account.ShortName,
		Number:                account.Number,
		Type:                  account.Type,
		Currency:              account.Currency,
		Iban:                  account.Iban,
		Bank:                  account.Bank,
		DateOpened:            account.DateOpened,
		DateClosed:            account.DateClosed,
		Balance:               account.Balance,
		BalanceNatival:        account.BalanceNatival,
		BlockedBalance:        account.BlockedBalance,
		BlockedBalanceNatival: account.BlockedBalanceNatival,
		StatusExtCode:         account.StatusExtCode,
		HasArrest:             account.HasArrest,
		OwnerName:             account.OwnerName,
		CardTwoExists:         account.CardTwoExists,
		ClientCode:            account.ClientCode,
	}
}

func accountSMECheck(account *colvirBridge.Account) bool {
	if account == nil {
		return false
	}

	return account.Type == consts.CurrentType.String() &&
		(account.Status == colvirConsts.AccountOpened.String() || account.Status == colvirConsts.AccountArrest.String())
}

func MakeColvirAccountsSMEListFilter(accountList []*colvirBridge.Account, entAccount []*ent.Accounts) []*colvirBridge.Account {
	ibanFromDB := make(map[string]struct{}, len(entAccount))
	for _, acc := range entAccount {
		ibanFromDB[acc.Iban] = struct{}{}
	}
	candidates := make(map[string]*colvirBridge.Account)

	for _, account := range accountList {
		if account == nil {
			continue
		}

		_, inDB := ibanFromDB[account.Iban]
		if !inDB && !accountSMECheck(account) {
			continue
		}

		// Выбираем аккаунт с максимальным балансом по валюте
		curr := account.Currency
		currAcc, exists := candidates[curr]
		if !exists || account.Balance > currAcc.Balance {
			candidates[curr] = account
		}
	}

	result := make([]*colvirBridge.Account, 0, len(candidates))
	for _, acc := range candidates {
		result = append(result, copyAccount(acc))
	}

	// Сортируем по дате открытия
	type accountWithDate struct {
		account *colvirBridge.Account
		date    time.Time
	}
	accountsWithDates := make([]accountWithDate, 0, len(result))
	for _, acc := range result {
		t, err := utils.GetDayOnlyTimeFromDateString(acc.DateOpened)
		if err != nil {
			continue
		}
		accountsWithDates = append(accountsWithDates, accountWithDate{acc, t})
	}

	sort.Slice(accountsWithDates, func(i, j int) bool {
		return accountsWithDates[i].date.Before(accountsWithDates[j].date)
	})

	sortedAccounts := make([]*colvirBridge.Account, 0, len(accountsWithDates))
	for _, ad := range accountsWithDates {
		sortedAccounts = append(sortedAccounts, ad.account)
	}

	return sortedAccounts
}
