package entity

import (
	"time"

	"github.com/google/uuid"

	commonConsts "git.redmadrobot.com/zaman/backend/zaman/pkg/common"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/embossing"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	colvirConsts "git.redmadrobot.com/zaman/backend/zaman/services/colvir-bridge/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	processingBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

type (
	CardAndAccountDTO struct {
		ID            uuid.UUID  `json:"id"`              // ID карты
		AccountID     string     `json:"account_id"`      // ID счета, к которому привязана карта
		EmbossingName string     `json:"embossing_name"`  // Эмбоссированное имя. Имя и фамилия которая будет печататься на пластиковой карте.
		FinContractID uuid.UUID  `json:"fin_contract_id"` // ID финансового договора, к которому относится карта
		UserID        string     `json:"user_id"`         // ID пользователя, которому принадлежит карта
		UserIIN       string     `json:"user_iin"`        // ИИН пользователя, которому принадлежит карта
		CardNumber    string     `json:"number"`          // Номер карты
		Pan           string     `json:"pan"`             // PAN (Primary Account Number) карты
		CardType      string     `json:"card_type"`       // Тип карты (например, виртуальная, пластиковая)
		Status        string     `json:"status"`          // Статус карты (например, активна, заблокирована)
		CreatedAt     time.Time  `json:"created_at"`      // Дата и время создания карты
		UpdatedAt     time.Time  `json:"updated_at"`      // Дата и время последнего обновления карты
		ExpiresAt     time.Time  `json:"expires_at"`      // Дата и время истечения срока действия карты
		IsVirtual     bool       `json:"is_virtual"`      // Флаг, указывающий, является ли карта виртуальной
		ClientCode    string     `json:"client_code"`     // Код клиента
		ClientName    string     `json:"client_name"`     // Имя клиента
		ClientID      string     `json:"client_id"`       // ID клиента
		ClientDepID   string     `json:"client_dep_id"`   // ID подразделения клиента
		UserLocale    string     `json:"user_locale"`     // Локаль пользователя
		Account       AccountDTO `json:"account"`         // Счет, к которому привязана карта
	}
	CreateVirtualCardReq struct {
		UserID string `json:"user_id"` // ID пользователя, для которого создается виртуальная карта
	}

	CreateVirtualCardResult struct {
		CardID    string `json:"card_id"`    // ID созданной виртуальной карты
		AccountID string `json:"account_id"` // ID счета, к которому привязана виртуальная карта
		IsSuccess bool   `json:"is_success"` // Флаг успешности операции создания карты
	}
)

// NewCardAndAccountDTO создает новый экземпляр CardAndAccountDTO с инициализированными полями по умолчанию
func NewCardAndAccountDTO(userID string) *CardAndAccountDTO {
	accountID := uuid.NewString() // Генерация нового уникального ID для счета
	return &CardAndAccountDTO{
		ID:            uuid.New(), // Генерация нового уникального ID для карты
		AccountID:     accountID,
		FinContractID: uuid.New(),         // Генерация нового уникального ID для финансового договора
		UserID:        userID,             // Инициализация UserID из параметра
		EmbossingName: consts.EmptyString, // Имя на карте по умолчанию
		CardNumber:    consts.EmptyString,
		Pan:           consts.EmptyString, // Номер карты по умолчанию
		CardType:      consts.CardVirtual.Str(),
		Status:        consts.CardInOpening.Str(), // Статус карты по умолчанию
		CreatedAt:     time.Time{},
		UpdatedAt:     time.Time{},
		ExpiresAt:     time.Time{}, // Дата истечения срока действия карты по умолчанию
		IsVirtual:     true,
		ClientCode:    consts.EmptyString,
		ClientID:      consts.EmptyString,
		ClientDepID:   consts.EmptyString,
		UserLocale:    consts.EmptyString,
		Account: AccountDTO{
			ID:             accountID,
			Currency:       commonConsts.CurrencyKZT,
			DeaReferenceID: consts.EmptyString,                                   // ID договора РКО
			OpenDate:       time.Time{},                                          // начало Epoch time по-умолчанию
			Status:         consts.AccountInOpening,                              // статус в Cards-Accounts
			ColvirStatus:   colvirConsts.AccountStatus(colvirConsts.EmptyString), // статус в Colvir
		}, // Инициализация пустого AccountDTO
	}
}

// MakeCreateVirtualCardPbToEntity создает объект из pb.CreateVirtualCardRequest в CreateVirtualCardReq для передачи в usecase
func MakeCreateVirtualCardPbToEntity(req *pb.CreateVirtualCardRequest) *CreateVirtualCardReq {
	if req == nil {
		return &CreateVirtualCardReq{}
	}
	// write your mapping code here
	return &CreateVirtualCardReq{
		UserID: req.UserID,
	}
}

// MakeCreateVirtualCardEntityToPb создает объект из CreateVirtualCard в pb.CreateVirtualCardResponse для возврата ответа из сервиса
func MakeCreateVirtualCardEntityToPb(res *CreateVirtualCardResult) *pb.CreateVirtualCardResponse {
	if res == nil {
		return &pb.CreateVirtualCardResponse{}
	}
	return &pb.CreateVirtualCardResponse{
		CardID:    res.CardID,
		AccountID: res.AccountID,
		IsSuccess: res.IsSuccess,
	}
}

// MakeCreateClientAccountAndCardReq создает объект из GetPersonalDataResp, CardAndAccountDTO и Document в CreateClientAccountAndCardReq для передачи в usecase
func MakeCreateClientAccountAndCardReq(
	userPersData *usersPb.GetPersonalDataResp,
	card *CardAndAccountDTO,
	document *processingBridge.Document,
	embossed embossing.UserNameResult,
) *processingBridge.CreateClientAccountAndCardReq {
	// по умолчанию мужской пол
	gender := consts.GenderMale
	if userPersData.Gender != nil && userPersData.Gender.Code != 1 {
		gender = consts.GenderFemale
	}

	personType := processingBridge.PersonType_PR
	if userPersData.Citizenship != nil && userPersData.Citizenship.Code != consts.CountryCodeKZ.Int64() {
		personType = processingBridge.PersonType_PNR
	}

	return &processingBridge.CreateClientAccountAndCardReq{
		Person: &processingBridge.Person{
			PersonType:   personType,
			Iin:          userPersData.Iin,
			Rid:          card.UserID,
			FirstName:    userPersData.Name,
			LastName:     userPersData.Surname,
			MiddleName:   userPersData.Patronymic,
			ScreenName:   embossed.FullName,
			Gender:       gender.String(),
			BirthDate:    userPersData.BirthDate,
			MobilePhone:  userPersData.Phone,
			WorkPhone:    userPersData.Phone,
			Citizenship:  userPersData.Citizenship.Code,
			LastNameLat:  embossed.LastName,
			FirstNameLat: embossed.FirstName,
			Document:     document,
			BranchCode:   consts.DefaultBrachCode,
			RiskLevel:    consts.DefaultRiskLevel,
			ResidentAddress: &processingBridge.ResidentAddress{
				CountryId:     userPersData.Citizenship.Code,
				CityTitle:     regAddressCity(userPersData),
				AddressInCity: regAddressStreet(userPersData),
				Zip:           consts.DefaultZipCodeKZ,
			},
		},
		AccountDetails: &processingBridge.AccountDetails{
			ContractTypeRid:  consts.ContractKZT.Str(),
			ContractRid:      card.AccountID,
			Iban:             card.Account.Iban,
			ContractCurrency: card.Account.Currency.Str(),
		},
		CardDetails: &processingBridge.CardDetails{
			ContractTypeRid: consts.ContractTypePlatinumRID.Str(),
			ContractRid:     card.ID.String(),
			CardParams: &processingBridge.CardParams{
				EmbossName: embossed.FullName,
			},
		},
	}
}

// regAddressStreet возвращает улицу из регистрационного адреса пользователя
func regAddressStreet(userPersData *usersPb.GetPersonalDataResp) string {
	if userPersData == nil || userPersData.RegAddress == nil {
		return consts.EmptyString
	}

	return userPersData.RegAddress.Street
}

// regAddressCity возвращает город из регистрационного адреса пользователя
func regAddressCity(userPersData *usersPb.GetPersonalDataResp) string {
	if userPersData == nil || userPersData.RegAddress == nil {
		return consts.EmptyString
	}

	return userPersData.RegAddress.City
}
