// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// ClientVerificationResultsDelete is the builder for deleting a ClientVerificationResults entity.
type ClientVerificationResultsDelete struct {
	config
	hooks    []Hook
	mutation *ClientVerificationResultsMutation
}

// Where appends a list predicates to the ClientVerificationResultsDelete builder.
func (cvrd *ClientVerificationResultsDelete) Where(ps ...predicate.ClientVerificationResults) *ClientVerificationResultsDelete {
	cvrd.mutation.Where(ps...)
	return cvrd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (cvrd *ClientVerificationResultsDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, cvrd.sqlExec, cvrd.mutation, cvrd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (cvrd *ClientVerificationResultsDelete) ExecX(ctx context.Context) int {
	n, err := cvrd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (cvrd *ClientVerificationResultsDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(clientverificationresults.Table, sqlgraph.NewFieldSpec(clientverificationresults.FieldID, field.TypeUUID))
	if ps := cvrd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, cvrd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	cvrd.mutation.done = true
	return affected, err
}

// ClientVerificationResultsDeleteOne is the builder for deleting a single ClientVerificationResults entity.
type ClientVerificationResultsDeleteOne struct {
	cvrd *ClientVerificationResultsDelete
}

// Where appends a list predicates to the ClientVerificationResultsDelete builder.
func (cvrdo *ClientVerificationResultsDeleteOne) Where(ps ...predicate.ClientVerificationResults) *ClientVerificationResultsDeleteOne {
	cvrdo.cvrd.mutation.Where(ps...)
	return cvrdo
}

// Exec executes the deletion query.
func (cvrdo *ClientVerificationResultsDeleteOne) Exec(ctx context.Context) error {
	n, err := cvrdo.cvrd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{clientverificationresults.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (cvrdo *ClientVerificationResultsDeleteOne) ExecX(ctx context.Context) {
	if err := cvrdo.Exec(ctx); err != nil {
		panic(err)
	}
}
