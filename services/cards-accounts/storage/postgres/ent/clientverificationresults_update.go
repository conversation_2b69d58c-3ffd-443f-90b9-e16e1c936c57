// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// ClientVerificationResultsUpdate is the builder for updating ClientVerificationResults entities.
type ClientVerificationResultsUpdate struct {
	config
	hooks    []Hook
	mutation *ClientVerificationResultsMutation
}

// Where appends a list predicates to the ClientVerificationResultsUpdate builder.
func (cvru *ClientVerificationResultsUpdate) Where(ps ...predicate.ClientVerificationResults) *ClientVerificationResultsUpdate {
	cvru.mutation.Where(ps...)
	return cvru
}

// SetUpdateTime sets the "update_time" field.
func (cvru *ClientVerificationResultsUpdate) SetUpdateTime(t time.Time) *ClientVerificationResultsUpdate {
	cvru.mutation.SetUpdateTime(t)
	return cvru
}

// SetUserID sets the "user_id" field.
func (cvru *ClientVerificationResultsUpdate) SetUserID(u uuid.UUID) *ClientVerificationResultsUpdate {
	cvru.mutation.SetUserID(u)
	return cvru
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cvru *ClientVerificationResultsUpdate) SetNillableUserID(u *uuid.UUID) *ClientVerificationResultsUpdate {
	if u != nil {
		cvru.SetUserID(*u)
	}
	return cvru
}

// SetStatus sets the "status" field.
func (cvru *ClientVerificationResultsUpdate) SetStatus(s string) *ClientVerificationResultsUpdate {
	cvru.mutation.SetStatus(s)
	return cvru
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (cvru *ClientVerificationResultsUpdate) SetNillableStatus(s *string) *ClientVerificationResultsUpdate {
	if s != nil {
		cvru.SetStatus(*s)
	}
	return cvru
}

// SetDate sets the "date" field.
func (cvru *ClientVerificationResultsUpdate) SetDate(t time.Time) *ClientVerificationResultsUpdate {
	cvru.mutation.SetDate(t)
	return cvru
}

// SetNillableDate sets the "date" field if the given value is not nil.
func (cvru *ClientVerificationResultsUpdate) SetNillableDate(t *time.Time) *ClientVerificationResultsUpdate {
	if t != nil {
		cvru.SetDate(*t)
	}
	return cvru
}

// SetVerificationResult sets the "verification_result" field.
func (cvru *ClientVerificationResultsUpdate) SetVerificationResult(s string) *ClientVerificationResultsUpdate {
	cvru.mutation.SetVerificationResult(s)
	return cvru
}

// SetNillableVerificationResult sets the "verification_result" field if the given value is not nil.
func (cvru *ClientVerificationResultsUpdate) SetNillableVerificationResult(s *string) *ClientVerificationResultsUpdate {
	if s != nil {
		cvru.SetVerificationResult(*s)
	}
	return cvru
}

// SetRejectionReason sets the "rejection_reason" field.
func (cvru *ClientVerificationResultsUpdate) SetRejectionReason(s string) *ClientVerificationResultsUpdate {
	cvru.mutation.SetRejectionReason(s)
	return cvru
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (cvru *ClientVerificationResultsUpdate) SetNillableRejectionReason(s *string) *ClientVerificationResultsUpdate {
	if s != nil {
		cvru.SetRejectionReason(*s)
	}
	return cvru
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (cvru *ClientVerificationResultsUpdate) ClearRejectionReason() *ClientVerificationResultsUpdate {
	cvru.mutation.ClearRejectionReason()
	return cvru
}

// Mutation returns the ClientVerificationResultsMutation object of the builder.
func (cvru *ClientVerificationResultsUpdate) Mutation() *ClientVerificationResultsMutation {
	return cvru.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cvru *ClientVerificationResultsUpdate) Save(ctx context.Context) (int, error) {
	cvru.defaults()
	return withHooks(ctx, cvru.sqlSave, cvru.mutation, cvru.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cvru *ClientVerificationResultsUpdate) SaveX(ctx context.Context) int {
	affected, err := cvru.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cvru *ClientVerificationResultsUpdate) Exec(ctx context.Context) error {
	_, err := cvru.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cvru *ClientVerificationResultsUpdate) ExecX(ctx context.Context) {
	if err := cvru.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cvru *ClientVerificationResultsUpdate) defaults() {
	if _, ok := cvru.mutation.UpdateTime(); !ok {
		v := clientverificationresults.UpdateDefaultUpdateTime()
		cvru.mutation.SetUpdateTime(v)
	}
}

func (cvru *ClientVerificationResultsUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(clientverificationresults.Table, clientverificationresults.Columns, sqlgraph.NewFieldSpec(clientverificationresults.FieldID, field.TypeUUID))
	if ps := cvru.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cvru.mutation.UpdateTime(); ok {
		_spec.SetField(clientverificationresults.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := cvru.mutation.UserID(); ok {
		_spec.SetField(clientverificationresults.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := cvru.mutation.Status(); ok {
		_spec.SetField(clientverificationresults.FieldStatus, field.TypeString, value)
	}
	if value, ok := cvru.mutation.Date(); ok {
		_spec.SetField(clientverificationresults.FieldDate, field.TypeTime, value)
	}
	if value, ok := cvru.mutation.VerificationResult(); ok {
		_spec.SetField(clientverificationresults.FieldVerificationResult, field.TypeString, value)
	}
	if value, ok := cvru.mutation.RejectionReason(); ok {
		_spec.SetField(clientverificationresults.FieldRejectionReason, field.TypeString, value)
	}
	if cvru.mutation.RejectionReasonCleared() {
		_spec.ClearField(clientverificationresults.FieldRejectionReason, field.TypeString)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cvru.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{clientverificationresults.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cvru.mutation.done = true
	return n, nil
}

// ClientVerificationResultsUpdateOne is the builder for updating a single ClientVerificationResults entity.
type ClientVerificationResultsUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ClientVerificationResultsMutation
}

// SetUpdateTime sets the "update_time" field.
func (cvruo *ClientVerificationResultsUpdateOne) SetUpdateTime(t time.Time) *ClientVerificationResultsUpdateOne {
	cvruo.mutation.SetUpdateTime(t)
	return cvruo
}

// SetUserID sets the "user_id" field.
func (cvruo *ClientVerificationResultsUpdateOne) SetUserID(u uuid.UUID) *ClientVerificationResultsUpdateOne {
	cvruo.mutation.SetUserID(u)
	return cvruo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cvruo *ClientVerificationResultsUpdateOne) SetNillableUserID(u *uuid.UUID) *ClientVerificationResultsUpdateOne {
	if u != nil {
		cvruo.SetUserID(*u)
	}
	return cvruo
}

// SetStatus sets the "status" field.
func (cvruo *ClientVerificationResultsUpdateOne) SetStatus(s string) *ClientVerificationResultsUpdateOne {
	cvruo.mutation.SetStatus(s)
	return cvruo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (cvruo *ClientVerificationResultsUpdateOne) SetNillableStatus(s *string) *ClientVerificationResultsUpdateOne {
	if s != nil {
		cvruo.SetStatus(*s)
	}
	return cvruo
}

// SetDate sets the "date" field.
func (cvruo *ClientVerificationResultsUpdateOne) SetDate(t time.Time) *ClientVerificationResultsUpdateOne {
	cvruo.mutation.SetDate(t)
	return cvruo
}

// SetNillableDate sets the "date" field if the given value is not nil.
func (cvruo *ClientVerificationResultsUpdateOne) SetNillableDate(t *time.Time) *ClientVerificationResultsUpdateOne {
	if t != nil {
		cvruo.SetDate(*t)
	}
	return cvruo
}

// SetVerificationResult sets the "verification_result" field.
func (cvruo *ClientVerificationResultsUpdateOne) SetVerificationResult(s string) *ClientVerificationResultsUpdateOne {
	cvruo.mutation.SetVerificationResult(s)
	return cvruo
}

// SetNillableVerificationResult sets the "verification_result" field if the given value is not nil.
func (cvruo *ClientVerificationResultsUpdateOne) SetNillableVerificationResult(s *string) *ClientVerificationResultsUpdateOne {
	if s != nil {
		cvruo.SetVerificationResult(*s)
	}
	return cvruo
}

// SetRejectionReason sets the "rejection_reason" field.
func (cvruo *ClientVerificationResultsUpdateOne) SetRejectionReason(s string) *ClientVerificationResultsUpdateOne {
	cvruo.mutation.SetRejectionReason(s)
	return cvruo
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (cvruo *ClientVerificationResultsUpdateOne) SetNillableRejectionReason(s *string) *ClientVerificationResultsUpdateOne {
	if s != nil {
		cvruo.SetRejectionReason(*s)
	}
	return cvruo
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (cvruo *ClientVerificationResultsUpdateOne) ClearRejectionReason() *ClientVerificationResultsUpdateOne {
	cvruo.mutation.ClearRejectionReason()
	return cvruo
}

// Mutation returns the ClientVerificationResultsMutation object of the builder.
func (cvruo *ClientVerificationResultsUpdateOne) Mutation() *ClientVerificationResultsMutation {
	return cvruo.mutation
}

// Where appends a list predicates to the ClientVerificationResultsUpdate builder.
func (cvruo *ClientVerificationResultsUpdateOne) Where(ps ...predicate.ClientVerificationResults) *ClientVerificationResultsUpdateOne {
	cvruo.mutation.Where(ps...)
	return cvruo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cvruo *ClientVerificationResultsUpdateOne) Select(field string, fields ...string) *ClientVerificationResultsUpdateOne {
	cvruo.fields = append([]string{field}, fields...)
	return cvruo
}

// Save executes the query and returns the updated ClientVerificationResults entity.
func (cvruo *ClientVerificationResultsUpdateOne) Save(ctx context.Context) (*ClientVerificationResults, error) {
	cvruo.defaults()
	return withHooks(ctx, cvruo.sqlSave, cvruo.mutation, cvruo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cvruo *ClientVerificationResultsUpdateOne) SaveX(ctx context.Context) *ClientVerificationResults {
	node, err := cvruo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cvruo *ClientVerificationResultsUpdateOne) Exec(ctx context.Context) error {
	_, err := cvruo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cvruo *ClientVerificationResultsUpdateOne) ExecX(ctx context.Context) {
	if err := cvruo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cvruo *ClientVerificationResultsUpdateOne) defaults() {
	if _, ok := cvruo.mutation.UpdateTime(); !ok {
		v := clientverificationresults.UpdateDefaultUpdateTime()
		cvruo.mutation.SetUpdateTime(v)
	}
}

func (cvruo *ClientVerificationResultsUpdateOne) sqlSave(ctx context.Context) (_node *ClientVerificationResults, err error) {
	_spec := sqlgraph.NewUpdateSpec(clientverificationresults.Table, clientverificationresults.Columns, sqlgraph.NewFieldSpec(clientverificationresults.FieldID, field.TypeUUID))
	id, ok := cvruo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "ClientVerificationResults.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cvruo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, clientverificationresults.FieldID)
		for _, f := range fields {
			if !clientverificationresults.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != clientverificationresults.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cvruo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cvruo.mutation.UpdateTime(); ok {
		_spec.SetField(clientverificationresults.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := cvruo.mutation.UserID(); ok {
		_spec.SetField(clientverificationresults.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := cvruo.mutation.Status(); ok {
		_spec.SetField(clientverificationresults.FieldStatus, field.TypeString, value)
	}
	if value, ok := cvruo.mutation.Date(); ok {
		_spec.SetField(clientverificationresults.FieldDate, field.TypeTime, value)
	}
	if value, ok := cvruo.mutation.VerificationResult(); ok {
		_spec.SetField(clientverificationresults.FieldVerificationResult, field.TypeString, value)
	}
	if value, ok := cvruo.mutation.RejectionReason(); ok {
		_spec.SetField(clientverificationresults.FieldRejectionReason, field.TypeString, value)
	}
	if cvruo.mutation.RejectionReasonCleared() {
		_spec.ClearField(clientverificationresults.FieldRejectionReason, field.TypeString)
	}
	_node = &ClientVerificationResults{config: cvruo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cvruo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{clientverificationresults.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cvruo.mutation.done = true
	return _node, nil
}
