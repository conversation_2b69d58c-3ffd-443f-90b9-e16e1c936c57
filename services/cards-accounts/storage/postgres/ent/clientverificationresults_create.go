// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
)

// ClientVerificationResultsCreate is the builder for creating a ClientVerificationResults entity.
type ClientVerificationResultsCreate struct {
	config
	mutation *ClientVerificationResultsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (cvrc *ClientVerificationResultsCreate) SetCreateTime(t time.Time) *ClientVerificationResultsCreate {
	cvrc.mutation.SetCreateTime(t)
	return cvrc
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (cvrc *ClientVerificationResultsCreate) SetNillableCreateTime(t *time.Time) *ClientVerificationResultsCreate {
	if t != nil {
		cvrc.SetCreateTime(*t)
	}
	return cvrc
}

// SetUpdateTime sets the "update_time" field.
func (cvrc *ClientVerificationResultsCreate) SetUpdateTime(t time.Time) *ClientVerificationResultsCreate {
	cvrc.mutation.SetUpdateTime(t)
	return cvrc
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (cvrc *ClientVerificationResultsCreate) SetNillableUpdateTime(t *time.Time) *ClientVerificationResultsCreate {
	if t != nil {
		cvrc.SetUpdateTime(*t)
	}
	return cvrc
}

// SetUserID sets the "user_id" field.
func (cvrc *ClientVerificationResultsCreate) SetUserID(u uuid.UUID) *ClientVerificationResultsCreate {
	cvrc.mutation.SetUserID(u)
	return cvrc
}

// SetStatus sets the "status" field.
func (cvrc *ClientVerificationResultsCreate) SetStatus(s string) *ClientVerificationResultsCreate {
	cvrc.mutation.SetStatus(s)
	return cvrc
}

// SetDate sets the "date" field.
func (cvrc *ClientVerificationResultsCreate) SetDate(t time.Time) *ClientVerificationResultsCreate {
	cvrc.mutation.SetDate(t)
	return cvrc
}

// SetVerificationResult sets the "verification_result" field.
func (cvrc *ClientVerificationResultsCreate) SetVerificationResult(s string) *ClientVerificationResultsCreate {
	cvrc.mutation.SetVerificationResult(s)
	return cvrc
}

// SetRejectionReason sets the "rejection_reason" field.
func (cvrc *ClientVerificationResultsCreate) SetRejectionReason(s string) *ClientVerificationResultsCreate {
	cvrc.mutation.SetRejectionReason(s)
	return cvrc
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (cvrc *ClientVerificationResultsCreate) SetNillableRejectionReason(s *string) *ClientVerificationResultsCreate {
	if s != nil {
		cvrc.SetRejectionReason(*s)
	}
	return cvrc
}

// SetID sets the "id" field.
func (cvrc *ClientVerificationResultsCreate) SetID(u uuid.UUID) *ClientVerificationResultsCreate {
	cvrc.mutation.SetID(u)
	return cvrc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (cvrc *ClientVerificationResultsCreate) SetNillableID(u *uuid.UUID) *ClientVerificationResultsCreate {
	if u != nil {
		cvrc.SetID(*u)
	}
	return cvrc
}

// Mutation returns the ClientVerificationResultsMutation object of the builder.
func (cvrc *ClientVerificationResultsCreate) Mutation() *ClientVerificationResultsMutation {
	return cvrc.mutation
}

// Save creates the ClientVerificationResults in the database.
func (cvrc *ClientVerificationResultsCreate) Save(ctx context.Context) (*ClientVerificationResults, error) {
	cvrc.defaults()
	return withHooks(ctx, cvrc.sqlSave, cvrc.mutation, cvrc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cvrc *ClientVerificationResultsCreate) SaveX(ctx context.Context) *ClientVerificationResults {
	v, err := cvrc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cvrc *ClientVerificationResultsCreate) Exec(ctx context.Context) error {
	_, err := cvrc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cvrc *ClientVerificationResultsCreate) ExecX(ctx context.Context) {
	if err := cvrc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cvrc *ClientVerificationResultsCreate) defaults() {
	if _, ok := cvrc.mutation.CreateTime(); !ok {
		v := clientverificationresults.DefaultCreateTime()
		cvrc.mutation.SetCreateTime(v)
	}
	if _, ok := cvrc.mutation.UpdateTime(); !ok {
		v := clientverificationresults.DefaultUpdateTime()
		cvrc.mutation.SetUpdateTime(v)
	}
	if _, ok := cvrc.mutation.ID(); !ok {
		v := clientverificationresults.DefaultID()
		cvrc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cvrc *ClientVerificationResultsCreate) check() error {
	if _, ok := cvrc.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "ClientVerificationResults.create_time"`)}
	}
	if _, ok := cvrc.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "ClientVerificationResults.update_time"`)}
	}
	if _, ok := cvrc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "ClientVerificationResults.user_id"`)}
	}
	if _, ok := cvrc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "ClientVerificationResults.status"`)}
	}
	if _, ok := cvrc.mutation.Date(); !ok {
		return &ValidationError{Name: "date", err: errors.New(`ent: missing required field "ClientVerificationResults.date"`)}
	}
	if _, ok := cvrc.mutation.VerificationResult(); !ok {
		return &ValidationError{Name: "verification_result", err: errors.New(`ent: missing required field "ClientVerificationResults.verification_result"`)}
	}
	return nil
}

func (cvrc *ClientVerificationResultsCreate) sqlSave(ctx context.Context) (*ClientVerificationResults, error) {
	if err := cvrc.check(); err != nil {
		return nil, err
	}
	_node, _spec := cvrc.createSpec()
	if err := sqlgraph.CreateNode(ctx, cvrc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	cvrc.mutation.id = &_node.ID
	cvrc.mutation.done = true
	return _node, nil
}

func (cvrc *ClientVerificationResultsCreate) createSpec() (*ClientVerificationResults, *sqlgraph.CreateSpec) {
	var (
		_node = &ClientVerificationResults{config: cvrc.config}
		_spec = sqlgraph.NewCreateSpec(clientverificationresults.Table, sqlgraph.NewFieldSpec(clientverificationresults.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = cvrc.conflict
	if id, ok := cvrc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := cvrc.mutation.CreateTime(); ok {
		_spec.SetField(clientverificationresults.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := cvrc.mutation.UpdateTime(); ok {
		_spec.SetField(clientverificationresults.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := cvrc.mutation.UserID(); ok {
		_spec.SetField(clientverificationresults.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := cvrc.mutation.Status(); ok {
		_spec.SetField(clientverificationresults.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := cvrc.mutation.Date(); ok {
		_spec.SetField(clientverificationresults.FieldDate, field.TypeTime, value)
		_node.Date = value
	}
	if value, ok := cvrc.mutation.VerificationResult(); ok {
		_spec.SetField(clientverificationresults.FieldVerificationResult, field.TypeString, value)
		_node.VerificationResult = value
	}
	if value, ok := cvrc.mutation.RejectionReason(); ok {
		_spec.SetField(clientverificationresults.FieldRejectionReason, field.TypeString, value)
		_node.RejectionReason = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ClientVerificationResults.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ClientVerificationResultsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (cvrc *ClientVerificationResultsCreate) OnConflict(opts ...sql.ConflictOption) *ClientVerificationResultsUpsertOne {
	cvrc.conflict = opts
	return &ClientVerificationResultsUpsertOne{
		create: cvrc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ClientVerificationResults.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (cvrc *ClientVerificationResultsCreate) OnConflictColumns(columns ...string) *ClientVerificationResultsUpsertOne {
	cvrc.conflict = append(cvrc.conflict, sql.ConflictColumns(columns...))
	return &ClientVerificationResultsUpsertOne{
		create: cvrc,
	}
}

type (
	// ClientVerificationResultsUpsertOne is the builder for "upsert"-ing
	//  one ClientVerificationResults node.
	ClientVerificationResultsUpsertOne struct {
		create *ClientVerificationResultsCreate
	}

	// ClientVerificationResultsUpsert is the "OnConflict" setter.
	ClientVerificationResultsUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *ClientVerificationResultsUpsert) SetUpdateTime(v time.Time) *ClientVerificationResultsUpsert {
	u.Set(clientverificationresults.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsert) UpdateUpdateTime() *ClientVerificationResultsUpsert {
	u.SetExcluded(clientverificationresults.FieldUpdateTime)
	return u
}

// SetUserID sets the "user_id" field.
func (u *ClientVerificationResultsUpsert) SetUserID(v uuid.UUID) *ClientVerificationResultsUpsert {
	u.Set(clientverificationresults.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsert) UpdateUserID() *ClientVerificationResultsUpsert {
	u.SetExcluded(clientverificationresults.FieldUserID)
	return u
}

// SetStatus sets the "status" field.
func (u *ClientVerificationResultsUpsert) SetStatus(v string) *ClientVerificationResultsUpsert {
	u.Set(clientverificationresults.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsert) UpdateStatus() *ClientVerificationResultsUpsert {
	u.SetExcluded(clientverificationresults.FieldStatus)
	return u
}

// SetDate sets the "date" field.
func (u *ClientVerificationResultsUpsert) SetDate(v time.Time) *ClientVerificationResultsUpsert {
	u.Set(clientverificationresults.FieldDate, v)
	return u
}

// UpdateDate sets the "date" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsert) UpdateDate() *ClientVerificationResultsUpsert {
	u.SetExcluded(clientverificationresults.FieldDate)
	return u
}

// SetVerificationResult sets the "verification_result" field.
func (u *ClientVerificationResultsUpsert) SetVerificationResult(v string) *ClientVerificationResultsUpsert {
	u.Set(clientverificationresults.FieldVerificationResult, v)
	return u
}

// UpdateVerificationResult sets the "verification_result" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsert) UpdateVerificationResult() *ClientVerificationResultsUpsert {
	u.SetExcluded(clientverificationresults.FieldVerificationResult)
	return u
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *ClientVerificationResultsUpsert) SetRejectionReason(v string) *ClientVerificationResultsUpsert {
	u.Set(clientverificationresults.FieldRejectionReason, v)
	return u
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsert) UpdateRejectionReason() *ClientVerificationResultsUpsert {
	u.SetExcluded(clientverificationresults.FieldRejectionReason)
	return u
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (u *ClientVerificationResultsUpsert) ClearRejectionReason() *ClientVerificationResultsUpsert {
	u.SetNull(clientverificationresults.FieldRejectionReason)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.ClientVerificationResults.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(clientverificationresults.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ClientVerificationResultsUpsertOne) UpdateNewValues() *ClientVerificationResultsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(clientverificationresults.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(clientverificationresults.FieldCreateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ClientVerificationResults.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ClientVerificationResultsUpsertOne) Ignore() *ClientVerificationResultsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ClientVerificationResultsUpsertOne) DoNothing() *ClientVerificationResultsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ClientVerificationResultsCreate.OnConflict
// documentation for more info.
func (u *ClientVerificationResultsUpsertOne) Update(set func(*ClientVerificationResultsUpsert)) *ClientVerificationResultsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ClientVerificationResultsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *ClientVerificationResultsUpsertOne) SetUpdateTime(v time.Time) *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertOne) UpdateUpdateTime() *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateUpdateTime()
	})
}

// SetUserID sets the "user_id" field.
func (u *ClientVerificationResultsUpsertOne) SetUserID(v uuid.UUID) *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertOne) UpdateUserID() *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateUserID()
	})
}

// SetStatus sets the "status" field.
func (u *ClientVerificationResultsUpsertOne) SetStatus(v string) *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertOne) UpdateStatus() *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateStatus()
	})
}

// SetDate sets the "date" field.
func (u *ClientVerificationResultsUpsertOne) SetDate(v time.Time) *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetDate(v)
	})
}

// UpdateDate sets the "date" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertOne) UpdateDate() *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateDate()
	})
}

// SetVerificationResult sets the "verification_result" field.
func (u *ClientVerificationResultsUpsertOne) SetVerificationResult(v string) *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetVerificationResult(v)
	})
}

// UpdateVerificationResult sets the "verification_result" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertOne) UpdateVerificationResult() *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateVerificationResult()
	})
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *ClientVerificationResultsUpsertOne) SetRejectionReason(v string) *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetRejectionReason(v)
	})
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertOne) UpdateRejectionReason() *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateRejectionReason()
	})
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (u *ClientVerificationResultsUpsertOne) ClearRejectionReason() *ClientVerificationResultsUpsertOne {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.ClearRejectionReason()
	})
}

// Exec executes the query.
func (u *ClientVerificationResultsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ClientVerificationResultsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ClientVerificationResultsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ClientVerificationResultsUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: ClientVerificationResultsUpsertOne.ID is not supported by MySQL driver. Use ClientVerificationResultsUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ClientVerificationResultsUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ClientVerificationResultsCreateBulk is the builder for creating many ClientVerificationResults entities in bulk.
type ClientVerificationResultsCreateBulk struct {
	config
	err      error
	builders []*ClientVerificationResultsCreate
	conflict []sql.ConflictOption
}

// Save creates the ClientVerificationResults entities in the database.
func (cvrcb *ClientVerificationResultsCreateBulk) Save(ctx context.Context) ([]*ClientVerificationResults, error) {
	if cvrcb.err != nil {
		return nil, cvrcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cvrcb.builders))
	nodes := make([]*ClientVerificationResults, len(cvrcb.builders))
	mutators := make([]Mutator, len(cvrcb.builders))
	for i := range cvrcb.builders {
		func(i int, root context.Context) {
			builder := cvrcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ClientVerificationResultsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cvrcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = cvrcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cvrcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cvrcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cvrcb *ClientVerificationResultsCreateBulk) SaveX(ctx context.Context) []*ClientVerificationResults {
	v, err := cvrcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cvrcb *ClientVerificationResultsCreateBulk) Exec(ctx context.Context) error {
	_, err := cvrcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cvrcb *ClientVerificationResultsCreateBulk) ExecX(ctx context.Context) {
	if err := cvrcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ClientVerificationResults.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ClientVerificationResultsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (cvrcb *ClientVerificationResultsCreateBulk) OnConflict(opts ...sql.ConflictOption) *ClientVerificationResultsUpsertBulk {
	cvrcb.conflict = opts
	return &ClientVerificationResultsUpsertBulk{
		create: cvrcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ClientVerificationResults.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (cvrcb *ClientVerificationResultsCreateBulk) OnConflictColumns(columns ...string) *ClientVerificationResultsUpsertBulk {
	cvrcb.conflict = append(cvrcb.conflict, sql.ConflictColumns(columns...))
	return &ClientVerificationResultsUpsertBulk{
		create: cvrcb,
	}
}

// ClientVerificationResultsUpsertBulk is the builder for "upsert"-ing
// a bulk of ClientVerificationResults nodes.
type ClientVerificationResultsUpsertBulk struct {
	create *ClientVerificationResultsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.ClientVerificationResults.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(clientverificationresults.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ClientVerificationResultsUpsertBulk) UpdateNewValues() *ClientVerificationResultsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(clientverificationresults.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(clientverificationresults.FieldCreateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ClientVerificationResults.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ClientVerificationResultsUpsertBulk) Ignore() *ClientVerificationResultsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ClientVerificationResultsUpsertBulk) DoNothing() *ClientVerificationResultsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ClientVerificationResultsCreateBulk.OnConflict
// documentation for more info.
func (u *ClientVerificationResultsUpsertBulk) Update(set func(*ClientVerificationResultsUpsert)) *ClientVerificationResultsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ClientVerificationResultsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *ClientVerificationResultsUpsertBulk) SetUpdateTime(v time.Time) *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertBulk) UpdateUpdateTime() *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateUpdateTime()
	})
}

// SetUserID sets the "user_id" field.
func (u *ClientVerificationResultsUpsertBulk) SetUserID(v uuid.UUID) *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertBulk) UpdateUserID() *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateUserID()
	})
}

// SetStatus sets the "status" field.
func (u *ClientVerificationResultsUpsertBulk) SetStatus(v string) *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertBulk) UpdateStatus() *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateStatus()
	})
}

// SetDate sets the "date" field.
func (u *ClientVerificationResultsUpsertBulk) SetDate(v time.Time) *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetDate(v)
	})
}

// UpdateDate sets the "date" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertBulk) UpdateDate() *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateDate()
	})
}

// SetVerificationResult sets the "verification_result" field.
func (u *ClientVerificationResultsUpsertBulk) SetVerificationResult(v string) *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetVerificationResult(v)
	})
}

// UpdateVerificationResult sets the "verification_result" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertBulk) UpdateVerificationResult() *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateVerificationResult()
	})
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *ClientVerificationResultsUpsertBulk) SetRejectionReason(v string) *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.SetRejectionReason(v)
	})
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *ClientVerificationResultsUpsertBulk) UpdateRejectionReason() *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.UpdateRejectionReason()
	})
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (u *ClientVerificationResultsUpsertBulk) ClearRejectionReason() *ClientVerificationResultsUpsertBulk {
	return u.Update(func(s *ClientVerificationResultsUpsert) {
		s.ClearRejectionReason()
	})
}

// Exec executes the query.
func (u *ClientVerificationResultsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ClientVerificationResultsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ClientVerificationResultsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ClientVerificationResultsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
