// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
)

// FinContract is the model entity for the FinContract schema.
type FinContract struct {
	config `json:"-"`
	// ID of the ent.
	// идентификатор фин. контракта
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// тип контракта
	ContractType fincontract.ContractType `json:"contract_type,omitempty"`
	// валюта фин. контракта (совпадает с валютой счета)
	ContractCurrency fincontract.ContractCurrency `json:"contract_currency,omitempty"`
	// IBAN фин. контракта (совпадает с IBAN счета)
	Iban string `json:"iban,omitempty"`
	// статус фин. контракта
	Status fincontract.Status `json:"status,omitempty"`
	// дата и время создания фин. контракта
	CreationDate time.Time `json:"creation_date,omitempty"`
	// CardID holds the value of the "card_id" field.
	CardID *uuid.UUID `json:"card_id,omitempty"`
	// код контракта, например, номер договора с банком
	ContractCode *string `json:"contract_code,omitempty"`
	// дата открытия контракта, если контракт открыт
	ContractDataOpen time.Time `json:"contract_data_open,omitempty"`
	// дата закрытия контракта, если контракт закрыт
	ContractDataClose *time.Time `json:"contract_data_close,omitempty"`
	// идентификатор пользователя, которому принадлежит фин. контракт
	UserID *string `json:"user_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the FinContractQuery when eager-loading is set.
	Edges              FinContractEdges `json:"edges"`
	fin_contract_cards *uuid.UUID
	selectValues       sql.SelectValues
}

// FinContractEdges holds the relations/edges for other nodes in the graph.
type FinContractEdges struct {
	// Cards holds the value of the cards edge.
	Cards *Cards `json:"cards,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// CardsOrErr returns the Cards value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e FinContractEdges) CardsOrErr() (*Cards, error) {
	if e.Cards != nil {
		return e.Cards, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: cards.Label}
	}
	return nil, &NotLoadedError{edge: "cards"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*FinContract) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case fincontract.FieldCardID:
			values[i] = &sql.NullScanner{S: new(uuid.UUID)}
		case fincontract.FieldContractType, fincontract.FieldContractCurrency, fincontract.FieldIban, fincontract.FieldStatus, fincontract.FieldContractCode, fincontract.FieldUserID:
			values[i] = new(sql.NullString)
		case fincontract.FieldCreateTime, fincontract.FieldUpdateTime, fincontract.FieldCreationDate, fincontract.FieldContractDataOpen, fincontract.FieldContractDataClose:
			values[i] = new(sql.NullTime)
		case fincontract.FieldID:
			values[i] = new(uuid.UUID)
		case fincontract.ForeignKeys[0]: // fin_contract_cards
			values[i] = &sql.NullScanner{S: new(uuid.UUID)}
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the FinContract fields.
func (_m *FinContract) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case fincontract.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case fincontract.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case fincontract.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case fincontract.FieldContractType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field contract_type", values[i])
			} else if value.Valid {
				_m.ContractType = fincontract.ContractType(value.String)
			}
		case fincontract.FieldContractCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field contract_currency", values[i])
			} else if value.Valid {
				_m.ContractCurrency = fincontract.ContractCurrency(value.String)
			}
		case fincontract.FieldIban:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field iban", values[i])
			} else if value.Valid {
				_m.Iban = value.String
			}
		case fincontract.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				_m.Status = fincontract.Status(value.String)
			}
		case fincontract.FieldCreationDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field creation_date", values[i])
			} else if value.Valid {
				_m.CreationDate = value.Time
			}
		case fincontract.FieldCardID:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field card_id", values[i])
			} else if value.Valid {
				_m.CardID = new(uuid.UUID)
				*_m.CardID = *value.S.(*uuid.UUID)
			}
		case fincontract.FieldContractCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field contract_code", values[i])
			} else if value.Valid {
				_m.ContractCode = new(string)
				*_m.ContractCode = value.String
			}
		case fincontract.FieldContractDataOpen:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field contract_data_open", values[i])
			} else if value.Valid {
				_m.ContractDataOpen = value.Time
			}
		case fincontract.FieldContractDataClose:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field contract_data_close", values[i])
			} else if value.Valid {
				_m.ContractDataClose = new(time.Time)
				*_m.ContractDataClose = value.Time
			}
		case fincontract.FieldUserID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				_m.UserID = new(string)
				*_m.UserID = value.String
			}
		case fincontract.ForeignKeys[0]:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field fin_contract_cards", values[i])
			} else if value.Valid {
				_m.fin_contract_cards = new(uuid.UUID)
				*_m.fin_contract_cards = *value.S.(*uuid.UUID)
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the FinContract.
// This includes values selected through modifiers, order, etc.
func (_m *FinContract) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// QueryCards queries the "cards" edge of the FinContract entity.
func (_m *FinContract) QueryCards() *CardsQuery {
	return NewFinContractClient(_m.config).QueryCards(_m)
}

// Update returns a builder for updating this FinContract.
// Note that you need to call FinContract.Unwrap() before calling this method if this FinContract
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *FinContract) Update() *FinContractUpdateOne {
	return NewFinContractClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the FinContract entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *FinContract) Unwrap() *FinContract {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: FinContract is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *FinContract) String() string {
	var builder strings.Builder
	builder.WriteString("FinContract(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("contract_type=")
	builder.WriteString(fmt.Sprintf("%v", _m.ContractType))
	builder.WriteString(", ")
	builder.WriteString("contract_currency=")
	builder.WriteString(fmt.Sprintf("%v", _m.ContractCurrency))
	builder.WriteString(", ")
	builder.WriteString("iban=")
	builder.WriteString(_m.Iban)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", _m.Status))
	builder.WriteString(", ")
	builder.WriteString("creation_date=")
	builder.WriteString(_m.CreationDate.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := _m.CardID; v != nil {
		builder.WriteString("card_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.ContractCode; v != nil {
		builder.WriteString("contract_code=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("contract_data_open=")
	builder.WriteString(_m.ContractDataOpen.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := _m.ContractDataClose; v != nil {
		builder.WriteString("contract_data_close=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := _m.UserID; v != nil {
		builder.WriteString("user_id=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// FinContracts is a parsable slice of FinContract.
type FinContracts []*FinContract
