// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// AccountsDelete is the builder for deleting a Accounts entity.
type AccountsDelete struct {
	config
	hooks    []Hook
	mutation *AccountsMutation
}

// Where appends a list predicates to the AccountsDelete builder.
func (_d *AccountsDelete) Where(ps ...predicate.Accounts) *AccountsDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *AccountsDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *AccountsDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *AccountsDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(accounts.Table, sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// AccountsDeleteOne is the builder for deleting a single Accounts entity.
type AccountsDeleteOne struct {
	_d *AccountsDelete
}

// Where appends a list predicates to the AccountsDelete builder.
func (_d *AccountsDeleteOne) Where(ps ...predicate.Accounts) *AccountsDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *AccountsDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{accounts.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *AccountsDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
