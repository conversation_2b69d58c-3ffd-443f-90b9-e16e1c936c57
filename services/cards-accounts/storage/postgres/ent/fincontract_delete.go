// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// FinContractDelete is the builder for deleting a FinContract entity.
type FinContractDelete struct {
	config
	hooks    []Hook
	mutation *FinContractMutation
}

// Where appends a list predicates to the FinContractDelete builder.
func (_d *FinContractDelete) Where(ps ...predicate.FinContract) *FinContractDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *FinContractDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *FinContractDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *FinContractDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(fincontract.Table, sqlgraph.NewFieldSpec(fincontract.FieldID, field.TypeUUID))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// FinContractDeleteOne is the builder for deleting a single FinContract entity.
type FinContractDeleteOne struct {
	_d *FinContractDelete
}

// Where appends a list predicates to the FinContractDelete builder.
func (_d *FinContractDeleteOne) Where(ps ...predicate.FinContract) *FinContractDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *FinContractDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{fincontract.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *FinContractDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
