// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
)

// ClientVerificationResults is the model entity for the ClientVerificationResults schema.
type ClientVerificationResults struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// Status holds the value of the "status" field.
	Status string `json:"status,omitempty"`
	// Date holds the value of the "date" field.
	Date time.Time `json:"date,omitempty"`
	// VerificationResult holds the value of the "verification_result" field.
	VerificationResult string `json:"verification_result,omitempty"`
	// RejectionReason holds the value of the "rejection_reason" field.
	RejectionReason *string `json:"rejection_reason,omitempty"`
	selectValues    sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ClientVerificationResults) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case clientverificationresults.FieldStatus, clientverificationresults.FieldVerificationResult, clientverificationresults.FieldRejectionReason:
			values[i] = new(sql.NullString)
		case clientverificationresults.FieldCreateTime, clientverificationresults.FieldUpdateTime, clientverificationresults.FieldDate:
			values[i] = new(sql.NullTime)
		case clientverificationresults.FieldID, clientverificationresults.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ClientVerificationResults fields.
func (cvr *ClientVerificationResults) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case clientverificationresults.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				cvr.ID = *value
			}
		case clientverificationresults.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				cvr.CreateTime = value.Time
			}
		case clientverificationresults.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				cvr.UpdateTime = value.Time
			}
		case clientverificationresults.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				cvr.UserID = *value
			}
		case clientverificationresults.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				cvr.Status = value.String
			}
		case clientverificationresults.FieldDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field date", values[i])
			} else if value.Valid {
				cvr.Date = value.Time
			}
		case clientverificationresults.FieldVerificationResult:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field verification_result", values[i])
			} else if value.Valid {
				cvr.VerificationResult = value.String
			}
		case clientverificationresults.FieldRejectionReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rejection_reason", values[i])
			} else if value.Valid {
				cvr.RejectionReason = new(string)
				*cvr.RejectionReason = value.String
			}
		default:
			cvr.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ClientVerificationResults.
// This includes values selected through modifiers, order, etc.
func (cvr *ClientVerificationResults) Value(name string) (ent.Value, error) {
	return cvr.selectValues.Get(name)
}

// Update returns a builder for updating this ClientVerificationResults.
// Note that you need to call ClientVerificationResults.Unwrap() before calling this method if this ClientVerificationResults
// was returned from a transaction, and the transaction was committed or rolled back.
func (cvr *ClientVerificationResults) Update() *ClientVerificationResultsUpdateOne {
	return NewClientVerificationResultsClient(cvr.config).UpdateOne(cvr)
}

// Unwrap unwraps the ClientVerificationResults entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cvr *ClientVerificationResults) Unwrap() *ClientVerificationResults {
	_tx, ok := cvr.config.driver.(*txDriver)
	if !ok {
		panic("ent: ClientVerificationResults is not a transactional entity")
	}
	cvr.config.driver = _tx.drv
	return cvr
}

// String implements the fmt.Stringer.
func (cvr *ClientVerificationResults) String() string {
	var builder strings.Builder
	builder.WriteString("ClientVerificationResults(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cvr.ID))
	builder.WriteString("create_time=")
	builder.WriteString(cvr.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(cvr.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", cvr.UserID))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(cvr.Status)
	builder.WriteString(", ")
	builder.WriteString("date=")
	builder.WriteString(cvr.Date.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("verification_result=")
	builder.WriteString(cvr.VerificationResult)
	builder.WriteString(", ")
	if v := cvr.RejectionReason; v != nil {
		builder.WriteString("rejection_reason=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// ClientVerificationResultsSlice is a parsable slice of ClientVerificationResults.
type ClientVerificationResultsSlice []*ClientVerificationResults
