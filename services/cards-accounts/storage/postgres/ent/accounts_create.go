// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
)

// AccountsCreate is the builder for creating a Accounts entity.
type AccountsCreate struct {
	config
	mutation *AccountsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *AccountsCreate) SetCreateTime(v time.Time) *AccountsCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableCreateTime(v *time.Time) *AccountsCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *AccountsCreate) SetUpdateTime(v time.Time) *AccountsCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableUpdateTime(v *time.Time) *AccountsCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetClientIin sets the "client_iin" field.
func (_c *AccountsCreate) SetClientIin(v string) *AccountsCreate {
	_c.mutation.SetClientIin(v)
	return _c
}

// SetClientCode sets the "client_code" field.
func (_c *AccountsCreate) SetClientCode(v string) *AccountsCreate {
	_c.mutation.SetClientCode(v)
	return _c
}

// SetClientName sets the "client_name" field.
func (_c *AccountsCreate) SetClientName(v string) *AccountsCreate {
	_c.mutation.SetClientName(v)
	return _c
}

// SetTitle sets the "title" field.
func (_c *AccountsCreate) SetTitle(v string) *AccountsCreate {
	_c.mutation.SetTitle(v)
	return _c
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableTitle(v *string) *AccountsCreate {
	if v != nil {
		_c.SetTitle(*v)
	}
	return _c
}

// SetShortName sets the "short_name" field.
func (_c *AccountsCreate) SetShortName(v string) *AccountsCreate {
	_c.mutation.SetShortName(v)
	return _c
}

// SetNillableShortName sets the "short_name" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableShortName(v *string) *AccountsCreate {
	if v != nil {
		_c.SetShortName(*v)
	}
	return _c
}

// SetCurrency sets the "currency" field.
func (_c *AccountsCreate) SetCurrency(v string) *AccountsCreate {
	_c.mutation.SetCurrency(v)
	return _c
}

// SetIban sets the "iban" field.
func (_c *AccountsCreate) SetIban(v string) *AccountsCreate {
	_c.mutation.SetIban(v)
	return _c
}

// SetType sets the "type" field.
func (_c *AccountsCreate) SetType(v string) *AccountsCreate {
	_c.mutation.SetType(v)
	return _c
}

// SetStatus sets the "status" field.
func (_c *AccountsCreate) SetStatus(v string) *AccountsCreate {
	_c.mutation.SetStatus(v)
	return _c
}

// SetDateOpened sets the "date_opened" field.
func (_c *AccountsCreate) SetDateOpened(v string) *AccountsCreate {
	_c.mutation.SetDateOpened(v)
	return _c
}

// SetBalance sets the "balance" field.
func (_c *AccountsCreate) SetBalance(v decimal.Decimal) *AccountsCreate {
	_c.mutation.SetBalance(v)
	return _c
}

// SetBalanceNatival sets the "balance_natival" field.
func (_c *AccountsCreate) SetBalanceNatival(v decimal.Decimal) *AccountsCreate {
	_c.mutation.SetBalanceNatival(v)
	return _c
}

// SetBlockedBalance sets the "blocked_balance" field.
func (_c *AccountsCreate) SetBlockedBalance(v decimal.Decimal) *AccountsCreate {
	_c.mutation.SetBlockedBalance(v)
	return _c
}

// SetAvailableBalance sets the "available_balance" field.
func (_c *AccountsCreate) SetAvailableBalance(v decimal.Decimal) *AccountsCreate {
	_c.mutation.SetAvailableBalance(v)
	return _c
}

// SetDateClosed sets the "date_closed" field.
func (_c *AccountsCreate) SetDateClosed(v string) *AccountsCreate {
	_c.mutation.SetDateClosed(v)
	return _c
}

// SetArrestBlocking sets the "arrest_blocking" field.
func (_c *AccountsCreate) SetArrestBlocking(v string) *AccountsCreate {
	_c.mutation.SetArrestBlocking(v)
	return _c
}

// SetArrestDebtAmount sets the "arrest_debt_amount" field.
func (_c *AccountsCreate) SetArrestDebtAmount(v decimal.Decimal) *AccountsCreate {
	_c.mutation.SetArrestDebtAmount(v)
	return _c
}

// SetHasArrest sets the "has_arrest" field.
func (_c *AccountsCreate) SetHasArrest(v string) *AccountsCreate {
	_c.mutation.SetHasArrest(v)
	return _c
}

// SetOrigin sets the "origin" field.
func (_c *AccountsCreate) SetOrigin(v string) *AccountsCreate {
	_c.mutation.SetOrigin(v)
	return _c
}

// SetDeaReferenceID sets the "dea_reference_id" field.
func (_c *AccountsCreate) SetDeaReferenceID(v string) *AccountsCreate {
	_c.mutation.SetDeaReferenceID(v)
	return _c
}

// SetNillableDeaReferenceID sets the "dea_reference_id" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableDeaReferenceID(v *string) *AccountsCreate {
	if v != nil {
		_c.SetDeaReferenceID(*v)
	}
	return _c
}

// SetClientType sets the "client_type" field.
func (_c *AccountsCreate) SetClientType(v string) *AccountsCreate {
	_c.mutation.SetClientType(v)
	return _c
}

// SetNillableClientType sets the "client_type" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableClientType(v *string) *AccountsCreate {
	if v != nil {
		_c.SetClientType(*v)
	}
	return _c
}

// SetIsMain sets the "is_main" field.
func (_c *AccountsCreate) SetIsMain(v bool) *AccountsCreate {
	_c.mutation.SetIsMain(v)
	return _c
}

// SetNillableIsMain sets the "is_main" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableIsMain(v *bool) *AccountsCreate {
	if v != nil {
		_c.SetIsMain(*v)
	}
	return _c
}

// SetAccessionAccount sets the "accession_account" field.
func (_c *AccountsCreate) SetAccessionAccount(v bool) *AccountsCreate {
	_c.mutation.SetAccessionAccount(v)
	return _c
}

// SetNillableAccessionAccount sets the "accession_account" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableAccessionAccount(v *bool) *AccountsCreate {
	if v != nil {
		_c.SetAccessionAccount(*v)
	}
	return _c
}

// SetIsArrested sets the "is_arrested" field.
func (_c *AccountsCreate) SetIsArrested(v bool) *AccountsCreate {
	_c.mutation.SetIsArrested(v)
	return _c
}

// SetNillableIsArrested sets the "is_arrested" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableIsArrested(v *bool) *AccountsCreate {
	if v != nil {
		_c.SetIsArrested(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *AccountsCreate) SetID(v uuid.UUID) *AccountsCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *AccountsCreate) SetNillableID(v *uuid.UUID) *AccountsCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// Mutation returns the AccountsMutation object of the builder.
func (_c *AccountsCreate) Mutation() *AccountsMutation {
	return _c.mutation
}

// Save creates the Accounts in the database.
func (_c *AccountsCreate) Save(ctx context.Context) (*Accounts, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *AccountsCreate) SaveX(ctx context.Context) *Accounts {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *AccountsCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *AccountsCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *AccountsCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := accounts.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := accounts.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.DeaReferenceID(); !ok {
		v := accounts.DefaultDeaReferenceID
		_c.mutation.SetDeaReferenceID(v)
	}
	if _, ok := _c.mutation.ClientType(); !ok {
		v := accounts.DefaultClientType
		_c.mutation.SetClientType(v)
	}
	if _, ok := _c.mutation.IsMain(); !ok {
		v := accounts.DefaultIsMain
		_c.mutation.SetIsMain(v)
	}
	if _, ok := _c.mutation.AccessionAccount(); !ok {
		v := accounts.DefaultAccessionAccount
		_c.mutation.SetAccessionAccount(v)
	}
	if _, ok := _c.mutation.IsArrested(); !ok {
		v := accounts.DefaultIsArrested
		_c.mutation.SetIsArrested(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := accounts.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *AccountsCreate) check() error {
	if _, ok := _c.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "Accounts.create_time"`)}
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "Accounts.update_time"`)}
	}
	if _, ok := _c.mutation.ClientIin(); !ok {
		return &ValidationError{Name: "client_iin", err: errors.New(`ent: missing required field "Accounts.client_iin"`)}
	}
	if _, ok := _c.mutation.ClientCode(); !ok {
		return &ValidationError{Name: "client_code", err: errors.New(`ent: missing required field "Accounts.client_code"`)}
	}
	if _, ok := _c.mutation.ClientName(); !ok {
		return &ValidationError{Name: "client_name", err: errors.New(`ent: missing required field "Accounts.client_name"`)}
	}
	if _, ok := _c.mutation.Currency(); !ok {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required field "Accounts.currency"`)}
	}
	if _, ok := _c.mutation.Iban(); !ok {
		return &ValidationError{Name: "iban", err: errors.New(`ent: missing required field "Accounts.iban"`)}
	}
	if _, ok := _c.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Accounts.type"`)}
	}
	if _, ok := _c.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Accounts.status"`)}
	}
	if _, ok := _c.mutation.DateOpened(); !ok {
		return &ValidationError{Name: "date_opened", err: errors.New(`ent: missing required field "Accounts.date_opened"`)}
	}
	if _, ok := _c.mutation.Balance(); !ok {
		return &ValidationError{Name: "balance", err: errors.New(`ent: missing required field "Accounts.balance"`)}
	}
	if _, ok := _c.mutation.BalanceNatival(); !ok {
		return &ValidationError{Name: "balance_natival", err: errors.New(`ent: missing required field "Accounts.balance_natival"`)}
	}
	if _, ok := _c.mutation.BlockedBalance(); !ok {
		return &ValidationError{Name: "blocked_balance", err: errors.New(`ent: missing required field "Accounts.blocked_balance"`)}
	}
	if _, ok := _c.mutation.AvailableBalance(); !ok {
		return &ValidationError{Name: "available_balance", err: errors.New(`ent: missing required field "Accounts.available_balance"`)}
	}
	if _, ok := _c.mutation.DateClosed(); !ok {
		return &ValidationError{Name: "date_closed", err: errors.New(`ent: missing required field "Accounts.date_closed"`)}
	}
	if _, ok := _c.mutation.ArrestBlocking(); !ok {
		return &ValidationError{Name: "arrest_blocking", err: errors.New(`ent: missing required field "Accounts.arrest_blocking"`)}
	}
	if _, ok := _c.mutation.ArrestDebtAmount(); !ok {
		return &ValidationError{Name: "arrest_debt_amount", err: errors.New(`ent: missing required field "Accounts.arrest_debt_amount"`)}
	}
	if _, ok := _c.mutation.HasArrest(); !ok {
		return &ValidationError{Name: "has_arrest", err: errors.New(`ent: missing required field "Accounts.has_arrest"`)}
	}
	if _, ok := _c.mutation.Origin(); !ok {
		return &ValidationError{Name: "origin", err: errors.New(`ent: missing required field "Accounts.origin"`)}
	}
	if _, ok := _c.mutation.DeaReferenceID(); !ok {
		return &ValidationError{Name: "dea_reference_id", err: errors.New(`ent: missing required field "Accounts.dea_reference_id"`)}
	}
	if _, ok := _c.mutation.ClientType(); !ok {
		return &ValidationError{Name: "client_type", err: errors.New(`ent: missing required field "Accounts.client_type"`)}
	}
	if _, ok := _c.mutation.IsMain(); !ok {
		return &ValidationError{Name: "is_main", err: errors.New(`ent: missing required field "Accounts.is_main"`)}
	}
	if _, ok := _c.mutation.AccessionAccount(); !ok {
		return &ValidationError{Name: "accession_account", err: errors.New(`ent: missing required field "Accounts.accession_account"`)}
	}
	if _, ok := _c.mutation.IsArrested(); !ok {
		return &ValidationError{Name: "is_arrested", err: errors.New(`ent: missing required field "Accounts.is_arrested"`)}
	}
	return nil
}

func (_c *AccountsCreate) sqlSave(ctx context.Context) (*Accounts, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *AccountsCreate) createSpec() (*Accounts, *sqlgraph.CreateSpec) {
	var (
		_node = &Accounts{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(accounts.Table, sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(accounts.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(accounts.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.ClientIin(); ok {
		_spec.SetField(accounts.FieldClientIin, field.TypeString, value)
		_node.ClientIin = value
	}
	if value, ok := _c.mutation.ClientCode(); ok {
		_spec.SetField(accounts.FieldClientCode, field.TypeString, value)
		_node.ClientCode = value
	}
	if value, ok := _c.mutation.ClientName(); ok {
		_spec.SetField(accounts.FieldClientName, field.TypeString, value)
		_node.ClientName = value
	}
	if value, ok := _c.mutation.Title(); ok {
		_spec.SetField(accounts.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := _c.mutation.ShortName(); ok {
		_spec.SetField(accounts.FieldShortName, field.TypeString, value)
		_node.ShortName = value
	}
	if value, ok := _c.mutation.Currency(); ok {
		_spec.SetField(accounts.FieldCurrency, field.TypeString, value)
		_node.Currency = value
	}
	if value, ok := _c.mutation.Iban(); ok {
		_spec.SetField(accounts.FieldIban, field.TypeString, value)
		_node.Iban = value
	}
	if value, ok := _c.mutation.GetType(); ok {
		_spec.SetField(accounts.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := _c.mutation.Status(); ok {
		_spec.SetField(accounts.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := _c.mutation.DateOpened(); ok {
		_spec.SetField(accounts.FieldDateOpened, field.TypeString, value)
		_node.DateOpened = value
	}
	if value, ok := _c.mutation.Balance(); ok {
		_spec.SetField(accounts.FieldBalance, field.TypeFloat64, value)
		_node.Balance = value
	}
	if value, ok := _c.mutation.BalanceNatival(); ok {
		_spec.SetField(accounts.FieldBalanceNatival, field.TypeFloat64, value)
		_node.BalanceNatival = value
	}
	if value, ok := _c.mutation.BlockedBalance(); ok {
		_spec.SetField(accounts.FieldBlockedBalance, field.TypeFloat64, value)
		_node.BlockedBalance = value
	}
	if value, ok := _c.mutation.AvailableBalance(); ok {
		_spec.SetField(accounts.FieldAvailableBalance, field.TypeFloat64, value)
		_node.AvailableBalance = value
	}
	if value, ok := _c.mutation.DateClosed(); ok {
		_spec.SetField(accounts.FieldDateClosed, field.TypeString, value)
		_node.DateClosed = value
	}
	if value, ok := _c.mutation.ArrestBlocking(); ok {
		_spec.SetField(accounts.FieldArrestBlocking, field.TypeString, value)
		_node.ArrestBlocking = value
	}
	if value, ok := _c.mutation.ArrestDebtAmount(); ok {
		_spec.SetField(accounts.FieldArrestDebtAmount, field.TypeFloat64, value)
		_node.ArrestDebtAmount = value
	}
	if value, ok := _c.mutation.HasArrest(); ok {
		_spec.SetField(accounts.FieldHasArrest, field.TypeString, value)
		_node.HasArrest = value
	}
	if value, ok := _c.mutation.Origin(); ok {
		_spec.SetField(accounts.FieldOrigin, field.TypeString, value)
		_node.Origin = value
	}
	if value, ok := _c.mutation.DeaReferenceID(); ok {
		_spec.SetField(accounts.FieldDeaReferenceID, field.TypeString, value)
		_node.DeaReferenceID = value
	}
	if value, ok := _c.mutation.ClientType(); ok {
		_spec.SetField(accounts.FieldClientType, field.TypeString, value)
		_node.ClientType = value
	}
	if value, ok := _c.mutation.IsMain(); ok {
		_spec.SetField(accounts.FieldIsMain, field.TypeBool, value)
		_node.IsMain = value
	}
	if value, ok := _c.mutation.AccessionAccount(); ok {
		_spec.SetField(accounts.FieldAccessionAccount, field.TypeBool, value)
		_node.AccessionAccount = value
	}
	if value, ok := _c.mutation.IsArrested(); ok {
		_spec.SetField(accounts.FieldIsArrested, field.TypeBool, value)
		_node.IsArrested = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Accounts.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AccountsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *AccountsCreate) OnConflict(opts ...sql.ConflictOption) *AccountsUpsertOne {
	_c.conflict = opts
	return &AccountsUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Accounts.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *AccountsCreate) OnConflictColumns(columns ...string) *AccountsUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &AccountsUpsertOne{
		create: _c,
	}
}

type (
	// AccountsUpsertOne is the builder for "upsert"-ing
	//  one Accounts node.
	AccountsUpsertOne struct {
		create *AccountsCreate
	}

	// AccountsUpsert is the "OnConflict" setter.
	AccountsUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *AccountsUpsert) SetUpdateTime(v time.Time) *AccountsUpsert {
	u.Set(accounts.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateUpdateTime() *AccountsUpsert {
	u.SetExcluded(accounts.FieldUpdateTime)
	return u
}

// SetClientIin sets the "client_iin" field.
func (u *AccountsUpsert) SetClientIin(v string) *AccountsUpsert {
	u.Set(accounts.FieldClientIin, v)
	return u
}

// UpdateClientIin sets the "client_iin" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateClientIin() *AccountsUpsert {
	u.SetExcluded(accounts.FieldClientIin)
	return u
}

// SetClientCode sets the "client_code" field.
func (u *AccountsUpsert) SetClientCode(v string) *AccountsUpsert {
	u.Set(accounts.FieldClientCode, v)
	return u
}

// UpdateClientCode sets the "client_code" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateClientCode() *AccountsUpsert {
	u.SetExcluded(accounts.FieldClientCode)
	return u
}

// SetClientName sets the "client_name" field.
func (u *AccountsUpsert) SetClientName(v string) *AccountsUpsert {
	u.Set(accounts.FieldClientName, v)
	return u
}

// UpdateClientName sets the "client_name" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateClientName() *AccountsUpsert {
	u.SetExcluded(accounts.FieldClientName)
	return u
}

// SetTitle sets the "title" field.
func (u *AccountsUpsert) SetTitle(v string) *AccountsUpsert {
	u.Set(accounts.FieldTitle, v)
	return u
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateTitle() *AccountsUpsert {
	u.SetExcluded(accounts.FieldTitle)
	return u
}

// ClearTitle clears the value of the "title" field.
func (u *AccountsUpsert) ClearTitle() *AccountsUpsert {
	u.SetNull(accounts.FieldTitle)
	return u
}

// SetShortName sets the "short_name" field.
func (u *AccountsUpsert) SetShortName(v string) *AccountsUpsert {
	u.Set(accounts.FieldShortName, v)
	return u
}

// UpdateShortName sets the "short_name" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateShortName() *AccountsUpsert {
	u.SetExcluded(accounts.FieldShortName)
	return u
}

// ClearShortName clears the value of the "short_name" field.
func (u *AccountsUpsert) ClearShortName() *AccountsUpsert {
	u.SetNull(accounts.FieldShortName)
	return u
}

// SetCurrency sets the "currency" field.
func (u *AccountsUpsert) SetCurrency(v string) *AccountsUpsert {
	u.Set(accounts.FieldCurrency, v)
	return u
}

// UpdateCurrency sets the "currency" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateCurrency() *AccountsUpsert {
	u.SetExcluded(accounts.FieldCurrency)
	return u
}

// SetIban sets the "iban" field.
func (u *AccountsUpsert) SetIban(v string) *AccountsUpsert {
	u.Set(accounts.FieldIban, v)
	return u
}

// UpdateIban sets the "iban" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateIban() *AccountsUpsert {
	u.SetExcluded(accounts.FieldIban)
	return u
}

// SetType sets the "type" field.
func (u *AccountsUpsert) SetType(v string) *AccountsUpsert {
	u.Set(accounts.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateType() *AccountsUpsert {
	u.SetExcluded(accounts.FieldType)
	return u
}

// SetStatus sets the "status" field.
func (u *AccountsUpsert) SetStatus(v string) *AccountsUpsert {
	u.Set(accounts.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateStatus() *AccountsUpsert {
	u.SetExcluded(accounts.FieldStatus)
	return u
}

// SetDateOpened sets the "date_opened" field.
func (u *AccountsUpsert) SetDateOpened(v string) *AccountsUpsert {
	u.Set(accounts.FieldDateOpened, v)
	return u
}

// UpdateDateOpened sets the "date_opened" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateDateOpened() *AccountsUpsert {
	u.SetExcluded(accounts.FieldDateOpened)
	return u
}

// SetBalance sets the "balance" field.
func (u *AccountsUpsert) SetBalance(v decimal.Decimal) *AccountsUpsert {
	u.Set(accounts.FieldBalance, v)
	return u
}

// UpdateBalance sets the "balance" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateBalance() *AccountsUpsert {
	u.SetExcluded(accounts.FieldBalance)
	return u
}

// AddBalance adds v to the "balance" field.
func (u *AccountsUpsert) AddBalance(v decimal.Decimal) *AccountsUpsert {
	u.Add(accounts.FieldBalance, v)
	return u
}

// SetBalanceNatival sets the "balance_natival" field.
func (u *AccountsUpsert) SetBalanceNatival(v decimal.Decimal) *AccountsUpsert {
	u.Set(accounts.FieldBalanceNatival, v)
	return u
}

// UpdateBalanceNatival sets the "balance_natival" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateBalanceNatival() *AccountsUpsert {
	u.SetExcluded(accounts.FieldBalanceNatival)
	return u
}

// AddBalanceNatival adds v to the "balance_natival" field.
func (u *AccountsUpsert) AddBalanceNatival(v decimal.Decimal) *AccountsUpsert {
	u.Add(accounts.FieldBalanceNatival, v)
	return u
}

// SetBlockedBalance sets the "blocked_balance" field.
func (u *AccountsUpsert) SetBlockedBalance(v decimal.Decimal) *AccountsUpsert {
	u.Set(accounts.FieldBlockedBalance, v)
	return u
}

// UpdateBlockedBalance sets the "blocked_balance" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateBlockedBalance() *AccountsUpsert {
	u.SetExcluded(accounts.FieldBlockedBalance)
	return u
}

// AddBlockedBalance adds v to the "blocked_balance" field.
func (u *AccountsUpsert) AddBlockedBalance(v decimal.Decimal) *AccountsUpsert {
	u.Add(accounts.FieldBlockedBalance, v)
	return u
}

// SetAvailableBalance sets the "available_balance" field.
func (u *AccountsUpsert) SetAvailableBalance(v decimal.Decimal) *AccountsUpsert {
	u.Set(accounts.FieldAvailableBalance, v)
	return u
}

// UpdateAvailableBalance sets the "available_balance" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateAvailableBalance() *AccountsUpsert {
	u.SetExcluded(accounts.FieldAvailableBalance)
	return u
}

// AddAvailableBalance adds v to the "available_balance" field.
func (u *AccountsUpsert) AddAvailableBalance(v decimal.Decimal) *AccountsUpsert {
	u.Add(accounts.FieldAvailableBalance, v)
	return u
}

// SetDateClosed sets the "date_closed" field.
func (u *AccountsUpsert) SetDateClosed(v string) *AccountsUpsert {
	u.Set(accounts.FieldDateClosed, v)
	return u
}

// UpdateDateClosed sets the "date_closed" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateDateClosed() *AccountsUpsert {
	u.SetExcluded(accounts.FieldDateClosed)
	return u
}

// SetArrestBlocking sets the "arrest_blocking" field.
func (u *AccountsUpsert) SetArrestBlocking(v string) *AccountsUpsert {
	u.Set(accounts.FieldArrestBlocking, v)
	return u
}

// UpdateArrestBlocking sets the "arrest_blocking" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateArrestBlocking() *AccountsUpsert {
	u.SetExcluded(accounts.FieldArrestBlocking)
	return u
}

// SetArrestDebtAmount sets the "arrest_debt_amount" field.
func (u *AccountsUpsert) SetArrestDebtAmount(v decimal.Decimal) *AccountsUpsert {
	u.Set(accounts.FieldArrestDebtAmount, v)
	return u
}

// UpdateArrestDebtAmount sets the "arrest_debt_amount" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateArrestDebtAmount() *AccountsUpsert {
	u.SetExcluded(accounts.FieldArrestDebtAmount)
	return u
}

// AddArrestDebtAmount adds v to the "arrest_debt_amount" field.
func (u *AccountsUpsert) AddArrestDebtAmount(v decimal.Decimal) *AccountsUpsert {
	u.Add(accounts.FieldArrestDebtAmount, v)
	return u
}

// SetHasArrest sets the "has_arrest" field.
func (u *AccountsUpsert) SetHasArrest(v string) *AccountsUpsert {
	u.Set(accounts.FieldHasArrest, v)
	return u
}

// UpdateHasArrest sets the "has_arrest" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateHasArrest() *AccountsUpsert {
	u.SetExcluded(accounts.FieldHasArrest)
	return u
}

// SetOrigin sets the "origin" field.
func (u *AccountsUpsert) SetOrigin(v string) *AccountsUpsert {
	u.Set(accounts.FieldOrigin, v)
	return u
}

// UpdateOrigin sets the "origin" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateOrigin() *AccountsUpsert {
	u.SetExcluded(accounts.FieldOrigin)
	return u
}

// SetDeaReferenceID sets the "dea_reference_id" field.
func (u *AccountsUpsert) SetDeaReferenceID(v string) *AccountsUpsert {
	u.Set(accounts.FieldDeaReferenceID, v)
	return u
}

// UpdateDeaReferenceID sets the "dea_reference_id" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateDeaReferenceID() *AccountsUpsert {
	u.SetExcluded(accounts.FieldDeaReferenceID)
	return u
}

// SetClientType sets the "client_type" field.
func (u *AccountsUpsert) SetClientType(v string) *AccountsUpsert {
	u.Set(accounts.FieldClientType, v)
	return u
}

// UpdateClientType sets the "client_type" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateClientType() *AccountsUpsert {
	u.SetExcluded(accounts.FieldClientType)
	return u
}

// SetIsMain sets the "is_main" field.
func (u *AccountsUpsert) SetIsMain(v bool) *AccountsUpsert {
	u.Set(accounts.FieldIsMain, v)
	return u
}

// UpdateIsMain sets the "is_main" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateIsMain() *AccountsUpsert {
	u.SetExcluded(accounts.FieldIsMain)
	return u
}

// SetAccessionAccount sets the "accession_account" field.
func (u *AccountsUpsert) SetAccessionAccount(v bool) *AccountsUpsert {
	u.Set(accounts.FieldAccessionAccount, v)
	return u
}

// UpdateAccessionAccount sets the "accession_account" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateAccessionAccount() *AccountsUpsert {
	u.SetExcluded(accounts.FieldAccessionAccount)
	return u
}

// SetIsArrested sets the "is_arrested" field.
func (u *AccountsUpsert) SetIsArrested(v bool) *AccountsUpsert {
	u.Set(accounts.FieldIsArrested, v)
	return u
}

// UpdateIsArrested sets the "is_arrested" field to the value that was provided on create.
func (u *AccountsUpsert) UpdateIsArrested() *AccountsUpsert {
	u.SetExcluded(accounts.FieldIsArrested)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Accounts.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(accounts.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AccountsUpsertOne) UpdateNewValues() *AccountsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(accounts.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(accounts.FieldCreateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Accounts.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AccountsUpsertOne) Ignore() *AccountsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AccountsUpsertOne) DoNothing() *AccountsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AccountsCreate.OnConflict
// documentation for more info.
func (u *AccountsUpsertOne) Update(set func(*AccountsUpsert)) *AccountsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AccountsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *AccountsUpsertOne) SetUpdateTime(v time.Time) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateUpdateTime() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateUpdateTime()
	})
}

// SetClientIin sets the "client_iin" field.
func (u *AccountsUpsertOne) SetClientIin(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetClientIin(v)
	})
}

// UpdateClientIin sets the "client_iin" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateClientIin() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateClientIin()
	})
}

// SetClientCode sets the "client_code" field.
func (u *AccountsUpsertOne) SetClientCode(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetClientCode(v)
	})
}

// UpdateClientCode sets the "client_code" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateClientCode() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateClientCode()
	})
}

// SetClientName sets the "client_name" field.
func (u *AccountsUpsertOne) SetClientName(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetClientName(v)
	})
}

// UpdateClientName sets the "client_name" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateClientName() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateClientName()
	})
}

// SetTitle sets the "title" field.
func (u *AccountsUpsertOne) SetTitle(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetTitle(v)
	})
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateTitle() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateTitle()
	})
}

// ClearTitle clears the value of the "title" field.
func (u *AccountsUpsertOne) ClearTitle() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.ClearTitle()
	})
}

// SetShortName sets the "short_name" field.
func (u *AccountsUpsertOne) SetShortName(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetShortName(v)
	})
}

// UpdateShortName sets the "short_name" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateShortName() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateShortName()
	})
}

// ClearShortName clears the value of the "short_name" field.
func (u *AccountsUpsertOne) ClearShortName() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.ClearShortName()
	})
}

// SetCurrency sets the "currency" field.
func (u *AccountsUpsertOne) SetCurrency(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetCurrency(v)
	})
}

// UpdateCurrency sets the "currency" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateCurrency() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateCurrency()
	})
}

// SetIban sets the "iban" field.
func (u *AccountsUpsertOne) SetIban(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetIban(v)
	})
}

// UpdateIban sets the "iban" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateIban() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateIban()
	})
}

// SetType sets the "type" field.
func (u *AccountsUpsertOne) SetType(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateType() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateType()
	})
}

// SetStatus sets the "status" field.
func (u *AccountsUpsertOne) SetStatus(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateStatus() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateStatus()
	})
}

// SetDateOpened sets the "date_opened" field.
func (u *AccountsUpsertOne) SetDateOpened(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetDateOpened(v)
	})
}

// UpdateDateOpened sets the "date_opened" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateDateOpened() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateDateOpened()
	})
}

// SetBalance sets the "balance" field.
func (u *AccountsUpsertOne) SetBalance(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetBalance(v)
	})
}

// AddBalance adds v to the "balance" field.
func (u *AccountsUpsertOne) AddBalance(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.AddBalance(v)
	})
}

// UpdateBalance sets the "balance" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateBalance() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateBalance()
	})
}

// SetBalanceNatival sets the "balance_natival" field.
func (u *AccountsUpsertOne) SetBalanceNatival(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetBalanceNatival(v)
	})
}

// AddBalanceNatival adds v to the "balance_natival" field.
func (u *AccountsUpsertOne) AddBalanceNatival(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.AddBalanceNatival(v)
	})
}

// UpdateBalanceNatival sets the "balance_natival" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateBalanceNatival() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateBalanceNatival()
	})
}

// SetBlockedBalance sets the "blocked_balance" field.
func (u *AccountsUpsertOne) SetBlockedBalance(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetBlockedBalance(v)
	})
}

// AddBlockedBalance adds v to the "blocked_balance" field.
func (u *AccountsUpsertOne) AddBlockedBalance(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.AddBlockedBalance(v)
	})
}

// UpdateBlockedBalance sets the "blocked_balance" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateBlockedBalance() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateBlockedBalance()
	})
}

// SetAvailableBalance sets the "available_balance" field.
func (u *AccountsUpsertOne) SetAvailableBalance(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetAvailableBalance(v)
	})
}

// AddAvailableBalance adds v to the "available_balance" field.
func (u *AccountsUpsertOne) AddAvailableBalance(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.AddAvailableBalance(v)
	})
}

// UpdateAvailableBalance sets the "available_balance" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateAvailableBalance() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateAvailableBalance()
	})
}

// SetDateClosed sets the "date_closed" field.
func (u *AccountsUpsertOne) SetDateClosed(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetDateClosed(v)
	})
}

// UpdateDateClosed sets the "date_closed" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateDateClosed() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateDateClosed()
	})
}

// SetArrestBlocking sets the "arrest_blocking" field.
func (u *AccountsUpsertOne) SetArrestBlocking(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetArrestBlocking(v)
	})
}

// UpdateArrestBlocking sets the "arrest_blocking" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateArrestBlocking() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateArrestBlocking()
	})
}

// SetArrestDebtAmount sets the "arrest_debt_amount" field.
func (u *AccountsUpsertOne) SetArrestDebtAmount(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetArrestDebtAmount(v)
	})
}

// AddArrestDebtAmount adds v to the "arrest_debt_amount" field.
func (u *AccountsUpsertOne) AddArrestDebtAmount(v decimal.Decimal) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.AddArrestDebtAmount(v)
	})
}

// UpdateArrestDebtAmount sets the "arrest_debt_amount" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateArrestDebtAmount() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateArrestDebtAmount()
	})
}

// SetHasArrest sets the "has_arrest" field.
func (u *AccountsUpsertOne) SetHasArrest(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetHasArrest(v)
	})
}

// UpdateHasArrest sets the "has_arrest" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateHasArrest() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateHasArrest()
	})
}

// SetOrigin sets the "origin" field.
func (u *AccountsUpsertOne) SetOrigin(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetOrigin(v)
	})
}

// UpdateOrigin sets the "origin" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateOrigin() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateOrigin()
	})
}

// SetDeaReferenceID sets the "dea_reference_id" field.
func (u *AccountsUpsertOne) SetDeaReferenceID(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetDeaReferenceID(v)
	})
}

// UpdateDeaReferenceID sets the "dea_reference_id" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateDeaReferenceID() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateDeaReferenceID()
	})
}

// SetClientType sets the "client_type" field.
func (u *AccountsUpsertOne) SetClientType(v string) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetClientType(v)
	})
}

// UpdateClientType sets the "client_type" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateClientType() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateClientType()
	})
}

// SetIsMain sets the "is_main" field.
func (u *AccountsUpsertOne) SetIsMain(v bool) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetIsMain(v)
	})
}

// UpdateIsMain sets the "is_main" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateIsMain() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateIsMain()
	})
}

// SetAccessionAccount sets the "accession_account" field.
func (u *AccountsUpsertOne) SetAccessionAccount(v bool) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetAccessionAccount(v)
	})
}

// UpdateAccessionAccount sets the "accession_account" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateAccessionAccount() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateAccessionAccount()
	})
}

// SetIsArrested sets the "is_arrested" field.
func (u *AccountsUpsertOne) SetIsArrested(v bool) *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.SetIsArrested(v)
	})
}

// UpdateIsArrested sets the "is_arrested" field to the value that was provided on create.
func (u *AccountsUpsertOne) UpdateIsArrested() *AccountsUpsertOne {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateIsArrested()
	})
}

// Exec executes the query.
func (u *AccountsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AccountsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AccountsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AccountsUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: AccountsUpsertOne.ID is not supported by MySQL driver. Use AccountsUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AccountsUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AccountsCreateBulk is the builder for creating many Accounts entities in bulk.
type AccountsCreateBulk struct {
	config
	err      error
	builders []*AccountsCreate
	conflict []sql.ConflictOption
}

// Save creates the Accounts entities in the database.
func (_c *AccountsCreateBulk) Save(ctx context.Context) ([]*Accounts, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Accounts, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AccountsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *AccountsCreateBulk) SaveX(ctx context.Context) []*Accounts {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *AccountsCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *AccountsCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Accounts.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AccountsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *AccountsCreateBulk) OnConflict(opts ...sql.ConflictOption) *AccountsUpsertBulk {
	_c.conflict = opts
	return &AccountsUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Accounts.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *AccountsCreateBulk) OnConflictColumns(columns ...string) *AccountsUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &AccountsUpsertBulk{
		create: _c,
	}
}

// AccountsUpsertBulk is the builder for "upsert"-ing
// a bulk of Accounts nodes.
type AccountsUpsertBulk struct {
	create *AccountsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Accounts.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(accounts.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AccountsUpsertBulk) UpdateNewValues() *AccountsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(accounts.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(accounts.FieldCreateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Accounts.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AccountsUpsertBulk) Ignore() *AccountsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AccountsUpsertBulk) DoNothing() *AccountsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AccountsCreateBulk.OnConflict
// documentation for more info.
func (u *AccountsUpsertBulk) Update(set func(*AccountsUpsert)) *AccountsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AccountsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *AccountsUpsertBulk) SetUpdateTime(v time.Time) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateUpdateTime() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateUpdateTime()
	})
}

// SetClientIin sets the "client_iin" field.
func (u *AccountsUpsertBulk) SetClientIin(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetClientIin(v)
	})
}

// UpdateClientIin sets the "client_iin" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateClientIin() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateClientIin()
	})
}

// SetClientCode sets the "client_code" field.
func (u *AccountsUpsertBulk) SetClientCode(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetClientCode(v)
	})
}

// UpdateClientCode sets the "client_code" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateClientCode() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateClientCode()
	})
}

// SetClientName sets the "client_name" field.
func (u *AccountsUpsertBulk) SetClientName(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetClientName(v)
	})
}

// UpdateClientName sets the "client_name" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateClientName() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateClientName()
	})
}

// SetTitle sets the "title" field.
func (u *AccountsUpsertBulk) SetTitle(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetTitle(v)
	})
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateTitle() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateTitle()
	})
}

// ClearTitle clears the value of the "title" field.
func (u *AccountsUpsertBulk) ClearTitle() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.ClearTitle()
	})
}

// SetShortName sets the "short_name" field.
func (u *AccountsUpsertBulk) SetShortName(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetShortName(v)
	})
}

// UpdateShortName sets the "short_name" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateShortName() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateShortName()
	})
}

// ClearShortName clears the value of the "short_name" field.
func (u *AccountsUpsertBulk) ClearShortName() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.ClearShortName()
	})
}

// SetCurrency sets the "currency" field.
func (u *AccountsUpsertBulk) SetCurrency(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetCurrency(v)
	})
}

// UpdateCurrency sets the "currency" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateCurrency() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateCurrency()
	})
}

// SetIban sets the "iban" field.
func (u *AccountsUpsertBulk) SetIban(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetIban(v)
	})
}

// UpdateIban sets the "iban" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateIban() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateIban()
	})
}

// SetType sets the "type" field.
func (u *AccountsUpsertBulk) SetType(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateType() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateType()
	})
}

// SetStatus sets the "status" field.
func (u *AccountsUpsertBulk) SetStatus(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateStatus() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateStatus()
	})
}

// SetDateOpened sets the "date_opened" field.
func (u *AccountsUpsertBulk) SetDateOpened(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetDateOpened(v)
	})
}

// UpdateDateOpened sets the "date_opened" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateDateOpened() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateDateOpened()
	})
}

// SetBalance sets the "balance" field.
func (u *AccountsUpsertBulk) SetBalance(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetBalance(v)
	})
}

// AddBalance adds v to the "balance" field.
func (u *AccountsUpsertBulk) AddBalance(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.AddBalance(v)
	})
}

// UpdateBalance sets the "balance" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateBalance() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateBalance()
	})
}

// SetBalanceNatival sets the "balance_natival" field.
func (u *AccountsUpsertBulk) SetBalanceNatival(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetBalanceNatival(v)
	})
}

// AddBalanceNatival adds v to the "balance_natival" field.
func (u *AccountsUpsertBulk) AddBalanceNatival(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.AddBalanceNatival(v)
	})
}

// UpdateBalanceNatival sets the "balance_natival" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateBalanceNatival() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateBalanceNatival()
	})
}

// SetBlockedBalance sets the "blocked_balance" field.
func (u *AccountsUpsertBulk) SetBlockedBalance(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetBlockedBalance(v)
	})
}

// AddBlockedBalance adds v to the "blocked_balance" field.
func (u *AccountsUpsertBulk) AddBlockedBalance(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.AddBlockedBalance(v)
	})
}

// UpdateBlockedBalance sets the "blocked_balance" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateBlockedBalance() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateBlockedBalance()
	})
}

// SetAvailableBalance sets the "available_balance" field.
func (u *AccountsUpsertBulk) SetAvailableBalance(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetAvailableBalance(v)
	})
}

// AddAvailableBalance adds v to the "available_balance" field.
func (u *AccountsUpsertBulk) AddAvailableBalance(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.AddAvailableBalance(v)
	})
}

// UpdateAvailableBalance sets the "available_balance" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateAvailableBalance() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateAvailableBalance()
	})
}

// SetDateClosed sets the "date_closed" field.
func (u *AccountsUpsertBulk) SetDateClosed(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetDateClosed(v)
	})
}

// UpdateDateClosed sets the "date_closed" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateDateClosed() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateDateClosed()
	})
}

// SetArrestBlocking sets the "arrest_blocking" field.
func (u *AccountsUpsertBulk) SetArrestBlocking(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetArrestBlocking(v)
	})
}

// UpdateArrestBlocking sets the "arrest_blocking" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateArrestBlocking() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateArrestBlocking()
	})
}

// SetArrestDebtAmount sets the "arrest_debt_amount" field.
func (u *AccountsUpsertBulk) SetArrestDebtAmount(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetArrestDebtAmount(v)
	})
}

// AddArrestDebtAmount adds v to the "arrest_debt_amount" field.
func (u *AccountsUpsertBulk) AddArrestDebtAmount(v decimal.Decimal) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.AddArrestDebtAmount(v)
	})
}

// UpdateArrestDebtAmount sets the "arrest_debt_amount" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateArrestDebtAmount() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateArrestDebtAmount()
	})
}

// SetHasArrest sets the "has_arrest" field.
func (u *AccountsUpsertBulk) SetHasArrest(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetHasArrest(v)
	})
}

// UpdateHasArrest sets the "has_arrest" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateHasArrest() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateHasArrest()
	})
}

// SetOrigin sets the "origin" field.
func (u *AccountsUpsertBulk) SetOrigin(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetOrigin(v)
	})
}

// UpdateOrigin sets the "origin" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateOrigin() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateOrigin()
	})
}

// SetDeaReferenceID sets the "dea_reference_id" field.
func (u *AccountsUpsertBulk) SetDeaReferenceID(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetDeaReferenceID(v)
	})
}

// UpdateDeaReferenceID sets the "dea_reference_id" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateDeaReferenceID() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateDeaReferenceID()
	})
}

// SetClientType sets the "client_type" field.
func (u *AccountsUpsertBulk) SetClientType(v string) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetClientType(v)
	})
}

// UpdateClientType sets the "client_type" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateClientType() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateClientType()
	})
}

// SetIsMain sets the "is_main" field.
func (u *AccountsUpsertBulk) SetIsMain(v bool) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetIsMain(v)
	})
}

// UpdateIsMain sets the "is_main" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateIsMain() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateIsMain()
	})
}

// SetAccessionAccount sets the "accession_account" field.
func (u *AccountsUpsertBulk) SetAccessionAccount(v bool) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetAccessionAccount(v)
	})
}

// UpdateAccessionAccount sets the "accession_account" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateAccessionAccount() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateAccessionAccount()
	})
}

// SetIsArrested sets the "is_arrested" field.
func (u *AccountsUpsertBulk) SetIsArrested(v bool) *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.SetIsArrested(v)
	})
}

// UpdateIsArrested sets the "is_arrested" field to the value that was provided on create.
func (u *AccountsUpsertBulk) UpdateIsArrested() *AccountsUpsertBulk {
	return u.Update(func(s *AccountsUpsert) {
		s.UpdateIsArrested()
	})
}

// Exec executes the query.
func (u *AccountsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AccountsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AccountsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AccountsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
