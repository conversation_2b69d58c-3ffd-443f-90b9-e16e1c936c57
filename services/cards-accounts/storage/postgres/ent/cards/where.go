// Code generated by ent, DO NOT EDIT.

package cards

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldUpdateTime, v))
}

// ClientID applies equality check predicate on the "client_id" field. It's identical to ClientIDEQ.
func ClientID(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldClientID, v))
}

// AttachedAccountID applies equality check predicate on the "attached_account_id" field. It's identical to AttachedAccountIDEQ.
func AttachedAccountID(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldAttachedAccountID, v))
}

// EmbossingName applies equality check predicate on the "embossing_name" field. It's identical to EmbossingNameEQ.
func EmbossingName(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldEmbossingName, v))
}

// MaskedPan applies equality check predicate on the "masked_pan" field. It's identical to MaskedPanEQ.
func MaskedPan(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldMaskedPan, v))
}

// Wallet applies equality check predicate on the "wallet" field. It's identical to WalletEQ.
func Wallet(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldWallet, v))
}

// CreationDate applies equality check predicate on the "creation_date" field. It's identical to CreationDateEQ.
func CreationDate(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldCreationDate, v))
}

// ExpireDate applies equality check predicate on the "expire_date" field. It's identical to ExpireDateEQ.
func ExpireDate(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldExpireDate, v))
}

// ModificationDate applies equality check predicate on the "modification_date" field. It's identical to ModificationDateEQ.
func ModificationDate(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldModificationDate, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldUpdateTime, v))
}

// ClientIDEQ applies the EQ predicate on the "client_id" field.
func ClientIDEQ(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldClientID, v))
}

// ClientIDNEQ applies the NEQ predicate on the "client_id" field.
func ClientIDNEQ(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldClientID, v))
}

// ClientIDIn applies the In predicate on the "client_id" field.
func ClientIDIn(vs ...uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldClientID, vs...))
}

// ClientIDNotIn applies the NotIn predicate on the "client_id" field.
func ClientIDNotIn(vs ...uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldClientID, vs...))
}

// ClientIDGT applies the GT predicate on the "client_id" field.
func ClientIDGT(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldClientID, v))
}

// ClientIDGTE applies the GTE predicate on the "client_id" field.
func ClientIDGTE(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldClientID, v))
}

// ClientIDLT applies the LT predicate on the "client_id" field.
func ClientIDLT(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldClientID, v))
}

// ClientIDLTE applies the LTE predicate on the "client_id" field.
func ClientIDLTE(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldClientID, v))
}

// ClientIDIsNil applies the IsNil predicate on the "client_id" field.
func ClientIDIsNil() predicate.Cards {
	return predicate.Cards(sql.FieldIsNull(FieldClientID))
}

// ClientIDNotNil applies the NotNil predicate on the "client_id" field.
func ClientIDNotNil() predicate.Cards {
	return predicate.Cards(sql.FieldNotNull(FieldClientID))
}

// AttachedAccountIDEQ applies the EQ predicate on the "attached_account_id" field.
func AttachedAccountIDEQ(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldAttachedAccountID, v))
}

// AttachedAccountIDNEQ applies the NEQ predicate on the "attached_account_id" field.
func AttachedAccountIDNEQ(v uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldAttachedAccountID, v))
}

// AttachedAccountIDIn applies the In predicate on the "attached_account_id" field.
func AttachedAccountIDIn(vs ...uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldAttachedAccountID, vs...))
}

// AttachedAccountIDNotIn applies the NotIn predicate on the "attached_account_id" field.
func AttachedAccountIDNotIn(vs ...uuid.UUID) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldAttachedAccountID, vs...))
}

// AttachedAccountIDIsNil applies the IsNil predicate on the "attached_account_id" field.
func AttachedAccountIDIsNil() predicate.Cards {
	return predicate.Cards(sql.FieldIsNull(FieldAttachedAccountID))
}

// AttachedAccountIDNotNil applies the NotNil predicate on the "attached_account_id" field.
func AttachedAccountIDNotNil() predicate.Cards {
	return predicate.Cards(sql.FieldNotNull(FieldAttachedAccountID))
}

// EmbossingNameEQ applies the EQ predicate on the "embossing_name" field.
func EmbossingNameEQ(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldEmbossingName, v))
}

// EmbossingNameNEQ applies the NEQ predicate on the "embossing_name" field.
func EmbossingNameNEQ(v string) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldEmbossingName, v))
}

// EmbossingNameIn applies the In predicate on the "embossing_name" field.
func EmbossingNameIn(vs ...string) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldEmbossingName, vs...))
}

// EmbossingNameNotIn applies the NotIn predicate on the "embossing_name" field.
func EmbossingNameNotIn(vs ...string) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldEmbossingName, vs...))
}

// EmbossingNameGT applies the GT predicate on the "embossing_name" field.
func EmbossingNameGT(v string) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldEmbossingName, v))
}

// EmbossingNameGTE applies the GTE predicate on the "embossing_name" field.
func EmbossingNameGTE(v string) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldEmbossingName, v))
}

// EmbossingNameLT applies the LT predicate on the "embossing_name" field.
func EmbossingNameLT(v string) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldEmbossingName, v))
}

// EmbossingNameLTE applies the LTE predicate on the "embossing_name" field.
func EmbossingNameLTE(v string) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldEmbossingName, v))
}

// EmbossingNameContains applies the Contains predicate on the "embossing_name" field.
func EmbossingNameContains(v string) predicate.Cards {
	return predicate.Cards(sql.FieldContains(FieldEmbossingName, v))
}

// EmbossingNameHasPrefix applies the HasPrefix predicate on the "embossing_name" field.
func EmbossingNameHasPrefix(v string) predicate.Cards {
	return predicate.Cards(sql.FieldHasPrefix(FieldEmbossingName, v))
}

// EmbossingNameHasSuffix applies the HasSuffix predicate on the "embossing_name" field.
func EmbossingNameHasSuffix(v string) predicate.Cards {
	return predicate.Cards(sql.FieldHasSuffix(FieldEmbossingName, v))
}

// EmbossingNameIsNil applies the IsNil predicate on the "embossing_name" field.
func EmbossingNameIsNil() predicate.Cards {
	return predicate.Cards(sql.FieldIsNull(FieldEmbossingName))
}

// EmbossingNameNotNil applies the NotNil predicate on the "embossing_name" field.
func EmbossingNameNotNil() predicate.Cards {
	return predicate.Cards(sql.FieldNotNull(FieldEmbossingName))
}

// EmbossingNameEqualFold applies the EqualFold predicate on the "embossing_name" field.
func EmbossingNameEqualFold(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEqualFold(FieldEmbossingName, v))
}

// EmbossingNameContainsFold applies the ContainsFold predicate on the "embossing_name" field.
func EmbossingNameContainsFold(v string) predicate.Cards {
	return predicate.Cards(sql.FieldContainsFold(FieldEmbossingName, v))
}

// MaskedPanEQ applies the EQ predicate on the "masked_pan" field.
func MaskedPanEQ(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldMaskedPan, v))
}

// MaskedPanNEQ applies the NEQ predicate on the "masked_pan" field.
func MaskedPanNEQ(v string) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldMaskedPan, v))
}

// MaskedPanIn applies the In predicate on the "masked_pan" field.
func MaskedPanIn(vs ...string) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldMaskedPan, vs...))
}

// MaskedPanNotIn applies the NotIn predicate on the "masked_pan" field.
func MaskedPanNotIn(vs ...string) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldMaskedPan, vs...))
}

// MaskedPanGT applies the GT predicate on the "masked_pan" field.
func MaskedPanGT(v string) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldMaskedPan, v))
}

// MaskedPanGTE applies the GTE predicate on the "masked_pan" field.
func MaskedPanGTE(v string) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldMaskedPan, v))
}

// MaskedPanLT applies the LT predicate on the "masked_pan" field.
func MaskedPanLT(v string) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldMaskedPan, v))
}

// MaskedPanLTE applies the LTE predicate on the "masked_pan" field.
func MaskedPanLTE(v string) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldMaskedPan, v))
}

// MaskedPanContains applies the Contains predicate on the "masked_pan" field.
func MaskedPanContains(v string) predicate.Cards {
	return predicate.Cards(sql.FieldContains(FieldMaskedPan, v))
}

// MaskedPanHasPrefix applies the HasPrefix predicate on the "masked_pan" field.
func MaskedPanHasPrefix(v string) predicate.Cards {
	return predicate.Cards(sql.FieldHasPrefix(FieldMaskedPan, v))
}

// MaskedPanHasSuffix applies the HasSuffix predicate on the "masked_pan" field.
func MaskedPanHasSuffix(v string) predicate.Cards {
	return predicate.Cards(sql.FieldHasSuffix(FieldMaskedPan, v))
}

// MaskedPanIsNil applies the IsNil predicate on the "masked_pan" field.
func MaskedPanIsNil() predicate.Cards {
	return predicate.Cards(sql.FieldIsNull(FieldMaskedPan))
}

// MaskedPanNotNil applies the NotNil predicate on the "masked_pan" field.
func MaskedPanNotNil() predicate.Cards {
	return predicate.Cards(sql.FieldNotNull(FieldMaskedPan))
}

// MaskedPanEqualFold applies the EqualFold predicate on the "masked_pan" field.
func MaskedPanEqualFold(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEqualFold(FieldMaskedPan, v))
}

// MaskedPanContainsFold applies the ContainsFold predicate on the "masked_pan" field.
func MaskedPanContainsFold(v string) predicate.Cards {
	return predicate.Cards(sql.FieldContainsFold(FieldMaskedPan, v))
}

// CardTypeEQ applies the EQ predicate on the "card_type" field.
func CardTypeEQ(v CardType) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldCardType, v))
}

// CardTypeNEQ applies the NEQ predicate on the "card_type" field.
func CardTypeNEQ(v CardType) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldCardType, v))
}

// CardTypeIn applies the In predicate on the "card_type" field.
func CardTypeIn(vs ...CardType) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldCardType, vs...))
}

// CardTypeNotIn applies the NotIn predicate on the "card_type" field.
func CardTypeNotIn(vs ...CardType) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldCardType, vs...))
}

// ProductTypeEQ applies the EQ predicate on the "product_type" field.
func ProductTypeEQ(v ProductType) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldProductType, v))
}

// ProductTypeNEQ applies the NEQ predicate on the "product_type" field.
func ProductTypeNEQ(v ProductType) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldProductType, v))
}

// ProductTypeIn applies the In predicate on the "product_type" field.
func ProductTypeIn(vs ...ProductType) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldProductType, vs...))
}

// ProductTypeNotIn applies the NotIn predicate on the "product_type" field.
func ProductTypeNotIn(vs ...ProductType) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldProductType, vs...))
}

// PaymentSystemEQ applies the EQ predicate on the "payment_system" field.
func PaymentSystemEQ(v PaymentSystem) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldPaymentSystem, v))
}

// PaymentSystemNEQ applies the NEQ predicate on the "payment_system" field.
func PaymentSystemNEQ(v PaymentSystem) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldPaymentSystem, v))
}

// PaymentSystemIn applies the In predicate on the "payment_system" field.
func PaymentSystemIn(vs ...PaymentSystem) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldPaymentSystem, vs...))
}

// PaymentSystemNotIn applies the NotIn predicate on the "payment_system" field.
func PaymentSystemNotIn(vs ...PaymentSystem) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldPaymentSystem, vs...))
}

// CardClassEQ applies the EQ predicate on the "card_class" field.
func CardClassEQ(v CardClass) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldCardClass, v))
}

// CardClassNEQ applies the NEQ predicate on the "card_class" field.
func CardClassNEQ(v CardClass) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldCardClass, v))
}

// CardClassIn applies the In predicate on the "card_class" field.
func CardClassIn(vs ...CardClass) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldCardClass, vs...))
}

// CardClassNotIn applies the NotIn predicate on the "card_class" field.
func CardClassNotIn(vs ...CardClass) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldCardClass, vs...))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldStatus, vs...))
}

// TokenizationStatusEQ applies the EQ predicate on the "tokenization_status" field.
func TokenizationStatusEQ(v TokenizationStatus) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldTokenizationStatus, v))
}

// TokenizationStatusNEQ applies the NEQ predicate on the "tokenization_status" field.
func TokenizationStatusNEQ(v TokenizationStatus) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldTokenizationStatus, v))
}

// TokenizationStatusIn applies the In predicate on the "tokenization_status" field.
func TokenizationStatusIn(vs ...TokenizationStatus) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldTokenizationStatus, vs...))
}

// TokenizationStatusNotIn applies the NotIn predicate on the "tokenization_status" field.
func TokenizationStatusNotIn(vs ...TokenizationStatus) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldTokenizationStatus, vs...))
}

// WalletEQ applies the EQ predicate on the "wallet" field.
func WalletEQ(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldWallet, v))
}

// WalletNEQ applies the NEQ predicate on the "wallet" field.
func WalletNEQ(v string) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldWallet, v))
}

// WalletIn applies the In predicate on the "wallet" field.
func WalletIn(vs ...string) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldWallet, vs...))
}

// WalletNotIn applies the NotIn predicate on the "wallet" field.
func WalletNotIn(vs ...string) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldWallet, vs...))
}

// WalletGT applies the GT predicate on the "wallet" field.
func WalletGT(v string) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldWallet, v))
}

// WalletGTE applies the GTE predicate on the "wallet" field.
func WalletGTE(v string) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldWallet, v))
}

// WalletLT applies the LT predicate on the "wallet" field.
func WalletLT(v string) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldWallet, v))
}

// WalletLTE applies the LTE predicate on the "wallet" field.
func WalletLTE(v string) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldWallet, v))
}

// WalletContains applies the Contains predicate on the "wallet" field.
func WalletContains(v string) predicate.Cards {
	return predicate.Cards(sql.FieldContains(FieldWallet, v))
}

// WalletHasPrefix applies the HasPrefix predicate on the "wallet" field.
func WalletHasPrefix(v string) predicate.Cards {
	return predicate.Cards(sql.FieldHasPrefix(FieldWallet, v))
}

// WalletHasSuffix applies the HasSuffix predicate on the "wallet" field.
func WalletHasSuffix(v string) predicate.Cards {
	return predicate.Cards(sql.FieldHasSuffix(FieldWallet, v))
}

// WalletIsNil applies the IsNil predicate on the "wallet" field.
func WalletIsNil() predicate.Cards {
	return predicate.Cards(sql.FieldIsNull(FieldWallet))
}

// WalletNotNil applies the NotNil predicate on the "wallet" field.
func WalletNotNil() predicate.Cards {
	return predicate.Cards(sql.FieldNotNull(FieldWallet))
}

// WalletEqualFold applies the EqualFold predicate on the "wallet" field.
func WalletEqualFold(v string) predicate.Cards {
	return predicate.Cards(sql.FieldEqualFold(FieldWallet, v))
}

// WalletContainsFold applies the ContainsFold predicate on the "wallet" field.
func WalletContainsFold(v string) predicate.Cards {
	return predicate.Cards(sql.FieldContainsFold(FieldWallet, v))
}

// CreationDateEQ applies the EQ predicate on the "creation_date" field.
func CreationDateEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldCreationDate, v))
}

// CreationDateNEQ applies the NEQ predicate on the "creation_date" field.
func CreationDateNEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldCreationDate, v))
}

// CreationDateIn applies the In predicate on the "creation_date" field.
func CreationDateIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldCreationDate, vs...))
}

// CreationDateNotIn applies the NotIn predicate on the "creation_date" field.
func CreationDateNotIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldCreationDate, vs...))
}

// CreationDateGT applies the GT predicate on the "creation_date" field.
func CreationDateGT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldCreationDate, v))
}

// CreationDateGTE applies the GTE predicate on the "creation_date" field.
func CreationDateGTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldCreationDate, v))
}

// CreationDateLT applies the LT predicate on the "creation_date" field.
func CreationDateLT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldCreationDate, v))
}

// CreationDateLTE applies the LTE predicate on the "creation_date" field.
func CreationDateLTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldCreationDate, v))
}

// ExpireDateEQ applies the EQ predicate on the "expire_date" field.
func ExpireDateEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldExpireDate, v))
}

// ExpireDateNEQ applies the NEQ predicate on the "expire_date" field.
func ExpireDateNEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldExpireDate, v))
}

// ExpireDateIn applies the In predicate on the "expire_date" field.
func ExpireDateIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldExpireDate, vs...))
}

// ExpireDateNotIn applies the NotIn predicate on the "expire_date" field.
func ExpireDateNotIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldExpireDate, vs...))
}

// ExpireDateGT applies the GT predicate on the "expire_date" field.
func ExpireDateGT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldExpireDate, v))
}

// ExpireDateGTE applies the GTE predicate on the "expire_date" field.
func ExpireDateGTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldExpireDate, v))
}

// ExpireDateLT applies the LT predicate on the "expire_date" field.
func ExpireDateLT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldExpireDate, v))
}

// ExpireDateLTE applies the LTE predicate on the "expire_date" field.
func ExpireDateLTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldExpireDate, v))
}

// ExpireDateIsNil applies the IsNil predicate on the "expire_date" field.
func ExpireDateIsNil() predicate.Cards {
	return predicate.Cards(sql.FieldIsNull(FieldExpireDate))
}

// ExpireDateNotNil applies the NotNil predicate on the "expire_date" field.
func ExpireDateNotNil() predicate.Cards {
	return predicate.Cards(sql.FieldNotNull(FieldExpireDate))
}

// ModificationDateEQ applies the EQ predicate on the "modification_date" field.
func ModificationDateEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldEQ(FieldModificationDate, v))
}

// ModificationDateNEQ applies the NEQ predicate on the "modification_date" field.
func ModificationDateNEQ(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNEQ(FieldModificationDate, v))
}

// ModificationDateIn applies the In predicate on the "modification_date" field.
func ModificationDateIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldIn(FieldModificationDate, vs...))
}

// ModificationDateNotIn applies the NotIn predicate on the "modification_date" field.
func ModificationDateNotIn(vs ...time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldNotIn(FieldModificationDate, vs...))
}

// ModificationDateGT applies the GT predicate on the "modification_date" field.
func ModificationDateGT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGT(FieldModificationDate, v))
}

// ModificationDateGTE applies the GTE predicate on the "modification_date" field.
func ModificationDateGTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldGTE(FieldModificationDate, v))
}

// ModificationDateLT applies the LT predicate on the "modification_date" field.
func ModificationDateLT(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLT(FieldModificationDate, v))
}

// ModificationDateLTE applies the LTE predicate on the "modification_date" field.
func ModificationDateLTE(v time.Time) predicate.Cards {
	return predicate.Cards(sql.FieldLTE(FieldModificationDate, v))
}

// HasAccount applies the HasEdge predicate on the "account" edge.
func HasAccount() predicate.Cards {
	return predicate.Cards(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, AccountTable, AccountColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAccountWith applies the HasEdge predicate on the "account" edge with a given conditions (other predicates).
func HasAccountWith(preds ...predicate.Accounts) predicate.Cards {
	return predicate.Cards(func(s *sql.Selector) {
		step := newAccountStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Cards) predicate.Cards {
	return predicate.Cards(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Cards) predicate.Cards {
	return predicate.Cards(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Cards) predicate.Cards {
	return predicate.Cards(sql.NotPredicates(p))
}
