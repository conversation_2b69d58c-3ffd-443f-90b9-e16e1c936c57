// Code generated by ent, DO NOT EDIT.

package cards

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the cards type in the database.
	Label = "cards"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldClientID holds the string denoting the client_id field in the database.
	FieldClientID = "client_id"
	// FieldAttachedAccountID holds the string denoting the attached_account_id field in the database.
	FieldAttachedAccountID = "attached_account_id"
	// FieldEmbossingName holds the string denoting the embossing_name field in the database.
	FieldEmbossingName = "embossing_name"
	// FieldMaskedPan holds the string denoting the masked_pan field in the database.
	FieldMaskedPan = "masked_pan"
	// FieldCardType holds the string denoting the card_type field in the database.
	FieldCardType = "card_type"
	// FieldProductType holds the string denoting the product_type field in the database.
	FieldProductType = "product_type"
	// FieldPaymentSystem holds the string denoting the payment_system field in the database.
	FieldPaymentSystem = "payment_system"
	// FieldCardClass holds the string denoting the card_class field in the database.
	FieldCardClass = "card_class"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldTokenizationStatus holds the string denoting the tokenization_status field in the database.
	FieldTokenizationStatus = "tokenization_status"
	// FieldWallet holds the string denoting the wallet field in the database.
	FieldWallet = "wallet"
	// FieldCreationDate holds the string denoting the creation_date field in the database.
	FieldCreationDate = "creation_date"
	// FieldExpireDate holds the string denoting the expire_date field in the database.
	FieldExpireDate = "expire_date"
	// FieldModificationDate holds the string denoting the modification_date field in the database.
	FieldModificationDate = "modification_date"
	// EdgeAccount holds the string denoting the account edge name in mutations.
	EdgeAccount = "account"
	// Table holds the table name of the cards in the database.
	Table = "cards"
	// AccountTable is the table that holds the account relation/edge.
	AccountTable = "cards"
	// AccountInverseTable is the table name for the Accounts entity.
	// It exists in this package in order to avoid circular dependency with the "accounts" package.
	AccountInverseTable = "accounts"
	// AccountColumn is the table column denoting the account relation/edge.
	AccountColumn = "attached_account_id"
)

// Columns holds all SQL columns for cards fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldClientID,
	FieldAttachedAccountID,
	FieldEmbossingName,
	FieldMaskedPan,
	FieldCardType,
	FieldProductType,
	FieldPaymentSystem,
	FieldCardClass,
	FieldStatus,
	FieldTokenizationStatus,
	FieldWallet,
	FieldCreationDate,
	FieldExpireDate,
	FieldModificationDate,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// EmbossingNameValidator is a validator for the "embossing_name" field. It is called by the builders before save.
	EmbossingNameValidator func(string) error
	// MaskedPanValidator is a validator for the "masked_pan" field. It is called by the builders before save.
	MaskedPanValidator func(string) error
	// DefaultCreationDate holds the default value on creation for the "creation_date" field.
	DefaultCreationDate func() time.Time
	// DefaultModificationDate holds the default value on creation for the "modification_date" field.
	DefaultModificationDate func() time.Time
	// UpdateDefaultModificationDate holds the default value on update for the "modification_date" field.
	UpdateDefaultModificationDate func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// CardType defines the type for the "card_type" enum field.
type CardType string

// CardTypeVIRTUAL is the default value of the CardType enum.
const DefaultCardType = CardTypeVIRTUAL

// CardType values.
const (
	CardTypePHYSICAL CardType = "PHYSICAL"
	CardTypeVIRTUAL  CardType = "VIRTUAL"
)

func (ct CardType) String() string {
	return string(ct)
}

// CardTypeValidator is a validator for the "card_type" field enum values. It is called by the builders before save.
func CardTypeValidator(ct CardType) error {
	switch ct {
	case CardTypePHYSICAL, CardTypeVIRTUAL:
		return nil
	default:
		return fmt.Errorf("cards: invalid enum value for card_type field: %q", ct)
	}
}

// ProductType defines the type for the "product_type" enum field.
type ProductType string

// ProductTypeDEBIT_CARD is the default value of the ProductType enum.
const DefaultProductType = ProductTypeDEBIT_CARD

// ProductType values.
const (
	ProductTypeDEBIT_CARD ProductType = "DEBIT_CARD"
)

func (pt ProductType) String() string {
	return string(pt)
}

// ProductTypeValidator is a validator for the "product_type" field enum values. It is called by the builders before save.
func ProductTypeValidator(pt ProductType) error {
	switch pt {
	case ProductTypeDEBIT_CARD:
		return nil
	default:
		return fmt.Errorf("cards: invalid enum value for product_type field: %q", pt)
	}
}

// PaymentSystem defines the type for the "payment_system" enum field.
type PaymentSystem string

// PaymentSystemMASTERCARD is the default value of the PaymentSystem enum.
const DefaultPaymentSystem = PaymentSystemMASTERCARD

// PaymentSystem values.
const (
	PaymentSystemMASTERCARD PaymentSystem = "MASTERCARD"
)

func (ps PaymentSystem) String() string {
	return string(ps)
}

// PaymentSystemValidator is a validator for the "payment_system" field enum values. It is called by the builders before save.
func PaymentSystemValidator(ps PaymentSystem) error {
	switch ps {
	case PaymentSystemMASTERCARD:
		return nil
	default:
		return fmt.Errorf("cards: invalid enum value for payment_system field: %q", ps)
	}
}

// CardClass defines the type for the "card_class" enum field.
type CardClass string

// CardClassMastercardPlatinum is the default value of the CardClass enum.
const DefaultCardClass = CardClassMastercardPlatinum

// CardClass values.
const (
	CardClassMastercardPlatinum CardClass = "Mastercard Platinum"
)

func (cc CardClass) String() string {
	return string(cc)
}

// CardClassValidator is a validator for the "card_class" field enum values. It is called by the builders before save.
func CardClassValidator(cc CardClass) error {
	switch cc {
	case CardClassMastercardPlatinum:
		return nil
	default:
		return fmt.Errorf("cards: invalid enum value for card_class field: %q", cc)
	}
}

// Status defines the type for the "status" enum field.
type Status string

// StatusIN_OPENING is the default value of the Status enum.
const DefaultStatus = StatusIN_OPENING

// Status values.
const (
	StatusIN_OPENING Status = "IN_OPENING"
	StatusERROR      Status = "ERROR"
	StatusACTIVE     Status = "ACTIVE"
	StatusBLOCKED    Status = "BLOCKED"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusIN_OPENING, StatusERROR, StatusACTIVE, StatusBLOCKED:
		return nil
	default:
		return fmt.Errorf("cards: invalid enum value for status field: %q", s)
	}
}

// TokenizationStatus defines the type for the "tokenization_status" enum field.
type TokenizationStatus string

// TokenizationStatusNotTokenized is the default value of the TokenizationStatus enum.
const DefaultTokenizationStatus = TokenizationStatusNotTokenized

// TokenizationStatus values.
const (
	TokenizationStatusTokenized    TokenizationStatus = "tokenized"
	TokenizationStatusNeedVerify   TokenizationStatus = "need_verify"
	TokenizationStatusNotTokenized TokenizationStatus = "not_tokenized"
)

func (ts TokenizationStatus) String() string {
	return string(ts)
}

// TokenizationStatusValidator is a validator for the "tokenization_status" field enum values. It is called by the builders before save.
func TokenizationStatusValidator(ts TokenizationStatus) error {
	switch ts {
	case TokenizationStatusTokenized, TokenizationStatusNeedVerify, TokenizationStatusNotTokenized:
		return nil
	default:
		return fmt.Errorf("cards: invalid enum value for tokenization_status field: %q", ts)
	}
}

// OrderOption defines the ordering options for the Cards queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByClientID orders the results by the client_id field.
func ByClientID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldClientID, opts...).ToFunc()
}

// ByAttachedAccountID orders the results by the attached_account_id field.
func ByAttachedAccountID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAttachedAccountID, opts...).ToFunc()
}

// ByEmbossingName orders the results by the embossing_name field.
func ByEmbossingName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmbossingName, opts...).ToFunc()
}

// ByMaskedPan orders the results by the masked_pan field.
func ByMaskedPan(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaskedPan, opts...).ToFunc()
}

// ByCardType orders the results by the card_type field.
func ByCardType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCardType, opts...).ToFunc()
}

// ByProductType orders the results by the product_type field.
func ByProductType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProductType, opts...).ToFunc()
}

// ByPaymentSystem orders the results by the payment_system field.
func ByPaymentSystem(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentSystem, opts...).ToFunc()
}

// ByCardClass orders the results by the card_class field.
func ByCardClass(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCardClass, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByTokenizationStatus orders the results by the tokenization_status field.
func ByTokenizationStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTokenizationStatus, opts...).ToFunc()
}

// ByWallet orders the results by the wallet field.
func ByWallet(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWallet, opts...).ToFunc()
}

// ByCreationDate orders the results by the creation_date field.
func ByCreationDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreationDate, opts...).ToFunc()
}

// ByExpireDate orders the results by the expire_date field.
func ByExpireDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpireDate, opts...).ToFunc()
}

// ByModificationDate orders the results by the modification_date field.
func ByModificationDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModificationDate, opts...).ToFunc()
}

// ByAccountField orders the results by account field.
func ByAccountField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAccountStep(), sql.OrderByField(field, opts...))
	}
}
func newAccountStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AccountInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, AccountTable, AccountColumn),
	)
}
