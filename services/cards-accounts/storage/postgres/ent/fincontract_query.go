// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// FinContractQuery is the builder for querying FinContract entities.
type FinContractQuery struct {
	config
	ctx        *QueryContext
	order      []fincontract.OrderOption
	inters     []Interceptor
	predicates []predicate.FinContract
	withCards  *CardsQuery
	withFKs    bool
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the FinContractQuery builder.
func (_q *FinContractQuery) Where(ps ...predicate.FinContract) *FinContractQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *FinContractQuery) Limit(limit int) *FinContractQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *FinContractQuery) Offset(offset int) *FinContractQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *FinContractQuery) Unique(unique bool) *FinContractQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *FinContractQuery) Order(o ...fincontract.OrderOption) *FinContractQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// QueryCards chains the current query on the "cards" edge.
func (_q *FinContractQuery) QueryCards() *CardsQuery {
	query := (&CardsClient{config: _q.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := _q.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := _q.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(fincontract.Table, fincontract.FieldID, selector),
			sqlgraph.To(cards.Table, cards.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, fincontract.CardsTable, fincontract.CardsColumn),
		)
		fromU = sqlgraph.SetNeighbors(_q.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first FinContract entity from the query.
// Returns a *NotFoundError when no FinContract was found.
func (_q *FinContractQuery) First(ctx context.Context) (*FinContract, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{fincontract.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *FinContractQuery) FirstX(ctx context.Context) *FinContract {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first FinContract ID from the query.
// Returns a *NotFoundError when no FinContract ID was found.
func (_q *FinContractQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{fincontract.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *FinContractQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single FinContract entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one FinContract entity is found.
// Returns a *NotFoundError when no FinContract entities are found.
func (_q *FinContractQuery) Only(ctx context.Context) (*FinContract, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{fincontract.Label}
	default:
		return nil, &NotSingularError{fincontract.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *FinContractQuery) OnlyX(ctx context.Context) *FinContract {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only FinContract ID in the query.
// Returns a *NotSingularError when more than one FinContract ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *FinContractQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{fincontract.Label}
	default:
		err = &NotSingularError{fincontract.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *FinContractQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of FinContracts.
func (_q *FinContractQuery) All(ctx context.Context) ([]*FinContract, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*FinContract, *FinContractQuery]()
	return withInterceptors[[]*FinContract](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *FinContractQuery) AllX(ctx context.Context) []*FinContract {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of FinContract IDs.
func (_q *FinContractQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(fincontract.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *FinContractQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *FinContractQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*FinContractQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *FinContractQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *FinContractQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *FinContractQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the FinContractQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *FinContractQuery) Clone() *FinContractQuery {
	if _q == nil {
		return nil
	}
	return &FinContractQuery{
		config:     _q.config,
		ctx:        _q.ctx.Clone(),
		order:      append([]fincontract.OrderOption{}, _q.order...),
		inters:     append([]Interceptor{}, _q.inters...),
		predicates: append([]predicate.FinContract{}, _q.predicates...),
		withCards:  _q.withCards.Clone(),
		// clone intermediate query.
		sql:  _q.sql.Clone(),
		path: _q.path,
	}
}

// WithCards tells the query-builder to eager-load the nodes that are connected to
// the "cards" edge. The optional arguments are used to configure the query builder of the edge.
func (_q *FinContractQuery) WithCards(opts ...func(*CardsQuery)) *FinContractQuery {
	query := (&CardsClient{config: _q.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	_q.withCards = query
	return _q
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.FinContract.Query().
//		GroupBy(fincontract.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (_q *FinContractQuery) GroupBy(field string, fields ...string) *FinContractGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &FinContractGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = fincontract.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.FinContract.Query().
//		Select(fincontract.FieldCreateTime).
//		Scan(ctx, &v)
func (_q *FinContractQuery) Select(fields ...string) *FinContractSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &FinContractSelect{FinContractQuery: _q}
	sbuild.label = fincontract.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a FinContractSelect configured with the given aggregations.
func (_q *FinContractQuery) Aggregate(fns ...AggregateFunc) *FinContractSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *FinContractQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !fincontract.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *FinContractQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*FinContract, error) {
	var (
		nodes       = []*FinContract{}
		withFKs     = _q.withFKs
		_spec       = _q.querySpec()
		loadedTypes = [1]bool{
			_q.withCards != nil,
		}
	)
	if _q.withCards != nil {
		withFKs = true
	}
	if withFKs {
		_spec.Node.Columns = append(_spec.Node.Columns, fincontract.ForeignKeys...)
	}
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*FinContract).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &FinContract{config: _q.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := _q.withCards; query != nil {
		if err := _q.loadCards(ctx, query, nodes, nil,
			func(n *FinContract, e *Cards) { n.Edges.Cards = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (_q *FinContractQuery) loadCards(ctx context.Context, query *CardsQuery, nodes []*FinContract, init func(*FinContract), assign func(*FinContract, *Cards)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*FinContract)
	for i := range nodes {
		if nodes[i].fin_contract_cards == nil {
			continue
		}
		fk := *nodes[i].fin_contract_cards
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(cards.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "fin_contract_cards" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (_q *FinContractQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *FinContractQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(fincontract.Table, fincontract.Columns, sqlgraph.NewFieldSpec(fincontract.FieldID, field.TypeUUID))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, fincontract.FieldID)
		for i := range fields {
			if fields[i] != fincontract.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *FinContractQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(fincontract.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = fincontract.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// FinContractGroupBy is the group-by builder for FinContract entities.
type FinContractGroupBy struct {
	selector
	build *FinContractQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *FinContractGroupBy) Aggregate(fns ...AggregateFunc) *FinContractGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *FinContractGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FinContractQuery, *FinContractGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *FinContractGroupBy) sqlScan(ctx context.Context, root *FinContractQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// FinContractSelect is the builder for selecting fields of FinContract entities.
type FinContractSelect struct {
	*FinContractQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *FinContractSelect) Aggregate(fns ...AggregateFunc) *FinContractSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *FinContractSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FinContractQuery, *FinContractSelect](ctx, _s.FinContractQuery, _s, _s.inters, v)
}

func (_s *FinContractSelect) sqlScan(ctx context.Context, root *FinContractQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
