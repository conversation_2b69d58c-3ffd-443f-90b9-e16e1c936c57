// Code generated by ent, DO NOT EDIT.

package fincontract

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the fincontract type in the database.
	Label = "fin_contract"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldContractType holds the string denoting the contract_type field in the database.
	FieldContractType = "contract_type"
	// FieldContractCurrency holds the string denoting the contract_currency field in the database.
	FieldContractCurrency = "contract_currency"
	// FieldIban holds the string denoting the iban field in the database.
	FieldIban = "iban"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldCreationDate holds the string denoting the creation_date field in the database.
	FieldCreationDate = "creation_date"
	// FieldCardID holds the string denoting the card_id field in the database.
	FieldCardID = "card_id"
	// FieldContractCode holds the string denoting the contract_code field in the database.
	FieldContractCode = "contract_code"
	// FieldContractDataOpen holds the string denoting the contract_data_open field in the database.
	FieldContractDataOpen = "contract_data_open"
	// FieldContractDataClose holds the string denoting the contract_data_close field in the database.
	FieldContractDataClose = "contract_data_close"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// EdgeCards holds the string denoting the cards edge name in mutations.
	EdgeCards = "cards"
	// Table holds the table name of the fincontract in the database.
	Table = "fin_contracts"
	// CardsTable is the table that holds the cards relation/edge.
	CardsTable = "fin_contracts"
	// CardsInverseTable is the table name for the Cards entity.
	// It exists in this package in order to avoid circular dependency with the "cards" package.
	CardsInverseTable = "cards"
	// CardsColumn is the table column denoting the cards relation/edge.
	CardsColumn = "fin_contract_cards"
)

// Columns holds all SQL columns for fincontract fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldContractType,
	FieldContractCurrency,
	FieldIban,
	FieldStatus,
	FieldCreationDate,
	FieldCardID,
	FieldContractCode,
	FieldContractDataOpen,
	FieldContractDataClose,
	FieldUserID,
}

// ForeignKeys holds the SQL foreign-keys that are owned by the "fin_contracts"
// table and are not defined as standalone fields in the schema.
var ForeignKeys = []string{
	"fin_contract_cards",
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	for i := range ForeignKeys {
		if column == ForeignKeys[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// DefaultCreationDate holds the default value on creation for the "creation_date" field.
	DefaultCreationDate func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// ContractType defines the type for the "contract_type" enum field.
type ContractType string

// ContractTypeZ_FIN_DEF_KZT is the default value of the ContractType enum.
const DefaultContractType = ContractTypeZ_FIN_DEF_KZT

// ContractType values.
const (
	ContractTypeZ_FIN_DEF_KZT ContractType = "Z_FIN_DEF_KZT"
	ContractTypeZ_FIN_DEF_USD ContractType = "Z_FIN_DEF_USD"
	ContractTypeZ_FIN_DEF_EUR ContractType = "Z_FIN_DEF_EUR"
)

func (ct ContractType) String() string {
	return string(ct)
}

// ContractTypeValidator is a validator for the "contract_type" field enum values. It is called by the builders before save.
func ContractTypeValidator(ct ContractType) error {
	switch ct {
	case ContractTypeZ_FIN_DEF_KZT, ContractTypeZ_FIN_DEF_USD, ContractTypeZ_FIN_DEF_EUR:
		return nil
	default:
		return fmt.Errorf("fincontract: invalid enum value for contract_type field: %q", ct)
	}
}

// ContractCurrency defines the type for the "contract_currency" enum field.
type ContractCurrency string

// ContractCurrencyKZT is the default value of the ContractCurrency enum.
const DefaultContractCurrency = ContractCurrencyKZT

// ContractCurrency values.
const (
	ContractCurrencyKZT ContractCurrency = "KZT"
	ContractCurrencyUSD ContractCurrency = "USD"
	ContractCurrencyEUR ContractCurrency = "EUR"
	ContractCurrencyRUB ContractCurrency = "RUB"
	ContractCurrencyCNY ContractCurrency = "CNY"
)

func (cc ContractCurrency) String() string {
	return string(cc)
}

// ContractCurrencyValidator is a validator for the "contract_currency" field enum values. It is called by the builders before save.
func ContractCurrencyValidator(cc ContractCurrency) error {
	switch cc {
	case ContractCurrencyKZT, ContractCurrencyUSD, ContractCurrencyEUR, ContractCurrencyRUB, ContractCurrencyCNY:
		return nil
	default:
		return fmt.Errorf("fincontract: invalid enum value for contract_currency field: %q", cc)
	}
}

// Status defines the type for the "status" enum field.
type Status string

// StatusIN_OPENING is the default value of the Status enum.
const DefaultStatus = StatusIN_OPENING

// Status values.
const (
	StatusIN_OPENING Status = "IN_OPENING"
	StatusERROR      Status = "ERROR"
	StatusACTIVE     Status = "ACTIVE"
	StatusBLOCKED    Status = "BLOCKED"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusIN_OPENING, StatusERROR, StatusACTIVE, StatusBLOCKED:
		return nil
	default:
		return fmt.Errorf("fincontract: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the FinContract queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByContractType orders the results by the contract_type field.
func ByContractType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContractType, opts...).ToFunc()
}

// ByContractCurrency orders the results by the contract_currency field.
func ByContractCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContractCurrency, opts...).ToFunc()
}

// ByIban orders the results by the iban field.
func ByIban(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIban, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByCreationDate orders the results by the creation_date field.
func ByCreationDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreationDate, opts...).ToFunc()
}

// ByCardID orders the results by the card_id field.
func ByCardID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCardID, opts...).ToFunc()
}

// ByContractCode orders the results by the contract_code field.
func ByContractCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContractCode, opts...).ToFunc()
}

// ByContractDataOpen orders the results by the contract_data_open field.
func ByContractDataOpen(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContractDataOpen, opts...).ToFunc()
}

// ByContractDataClose orders the results by the contract_data_close field.
func ByContractDataClose(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContractDataClose, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByCardsField orders the results by cards field.
func ByCardsField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCardsStep(), sql.OrderByField(field, opts...))
	}
}
func newCardsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CardsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, CardsTable, CardsColumn),
	)
}
