// Code generated by ent, DO NOT EDIT.

package fincontract

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldUpdateTime, v))
}

// Iban applies equality check predicate on the "iban" field. It's identical to IbanEQ.
func Iban(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldIban, v))
}

// CreationDate applies equality check predicate on the "creation_date" field. It's identical to CreationDateEQ.
func CreationDate(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldCreationDate, v))
}

// CardID applies equality check predicate on the "card_id" field. It's identical to CardIDEQ.
func CardID(v uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldCardID, v))
}

// ContractCode applies equality check predicate on the "contract_code" field. It's identical to ContractCodeEQ.
func ContractCode(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldContractCode, v))
}

// ContractDataOpen applies equality check predicate on the "contract_data_open" field. It's identical to ContractDataOpenEQ.
func ContractDataOpen(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldContractDataOpen, v))
}

// ContractDataClose applies equality check predicate on the "contract_data_close" field. It's identical to ContractDataCloseEQ.
func ContractDataClose(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldContractDataClose, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldUserID, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldUpdateTime, v))
}

// ContractTypeEQ applies the EQ predicate on the "contract_type" field.
func ContractTypeEQ(v ContractType) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldContractType, v))
}

// ContractTypeNEQ applies the NEQ predicate on the "contract_type" field.
func ContractTypeNEQ(v ContractType) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldContractType, v))
}

// ContractTypeIn applies the In predicate on the "contract_type" field.
func ContractTypeIn(vs ...ContractType) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldContractType, vs...))
}

// ContractTypeNotIn applies the NotIn predicate on the "contract_type" field.
func ContractTypeNotIn(vs ...ContractType) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldContractType, vs...))
}

// ContractCurrencyEQ applies the EQ predicate on the "contract_currency" field.
func ContractCurrencyEQ(v ContractCurrency) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldContractCurrency, v))
}

// ContractCurrencyNEQ applies the NEQ predicate on the "contract_currency" field.
func ContractCurrencyNEQ(v ContractCurrency) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldContractCurrency, v))
}

// ContractCurrencyIn applies the In predicate on the "contract_currency" field.
func ContractCurrencyIn(vs ...ContractCurrency) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldContractCurrency, vs...))
}

// ContractCurrencyNotIn applies the NotIn predicate on the "contract_currency" field.
func ContractCurrencyNotIn(vs ...ContractCurrency) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldContractCurrency, vs...))
}

// IbanEQ applies the EQ predicate on the "iban" field.
func IbanEQ(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldIban, v))
}

// IbanNEQ applies the NEQ predicate on the "iban" field.
func IbanNEQ(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldIban, v))
}

// IbanIn applies the In predicate on the "iban" field.
func IbanIn(vs ...string) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldIban, vs...))
}

// IbanNotIn applies the NotIn predicate on the "iban" field.
func IbanNotIn(vs ...string) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldIban, vs...))
}

// IbanGT applies the GT predicate on the "iban" field.
func IbanGT(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldIban, v))
}

// IbanGTE applies the GTE predicate on the "iban" field.
func IbanGTE(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldIban, v))
}

// IbanLT applies the LT predicate on the "iban" field.
func IbanLT(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldIban, v))
}

// IbanLTE applies the LTE predicate on the "iban" field.
func IbanLTE(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldIban, v))
}

// IbanContains applies the Contains predicate on the "iban" field.
func IbanContains(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldContains(FieldIban, v))
}

// IbanHasPrefix applies the HasPrefix predicate on the "iban" field.
func IbanHasPrefix(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldHasPrefix(FieldIban, v))
}

// IbanHasSuffix applies the HasSuffix predicate on the "iban" field.
func IbanHasSuffix(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldHasSuffix(FieldIban, v))
}

// IbanEqualFold applies the EqualFold predicate on the "iban" field.
func IbanEqualFold(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEqualFold(FieldIban, v))
}

// IbanContainsFold applies the ContainsFold predicate on the "iban" field.
func IbanContainsFold(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldContainsFold(FieldIban, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldStatus, vs...))
}

// CreationDateEQ applies the EQ predicate on the "creation_date" field.
func CreationDateEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldCreationDate, v))
}

// CreationDateNEQ applies the NEQ predicate on the "creation_date" field.
func CreationDateNEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldCreationDate, v))
}

// CreationDateIn applies the In predicate on the "creation_date" field.
func CreationDateIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldCreationDate, vs...))
}

// CreationDateNotIn applies the NotIn predicate on the "creation_date" field.
func CreationDateNotIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldCreationDate, vs...))
}

// CreationDateGT applies the GT predicate on the "creation_date" field.
func CreationDateGT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldCreationDate, v))
}

// CreationDateGTE applies the GTE predicate on the "creation_date" field.
func CreationDateGTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldCreationDate, v))
}

// CreationDateLT applies the LT predicate on the "creation_date" field.
func CreationDateLT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldCreationDate, v))
}

// CreationDateLTE applies the LTE predicate on the "creation_date" field.
func CreationDateLTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldCreationDate, v))
}

// CardIDEQ applies the EQ predicate on the "card_id" field.
func CardIDEQ(v uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldCardID, v))
}

// CardIDNEQ applies the NEQ predicate on the "card_id" field.
func CardIDNEQ(v uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldCardID, v))
}

// CardIDIn applies the In predicate on the "card_id" field.
func CardIDIn(vs ...uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldCardID, vs...))
}

// CardIDNotIn applies the NotIn predicate on the "card_id" field.
func CardIDNotIn(vs ...uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldCardID, vs...))
}

// CardIDGT applies the GT predicate on the "card_id" field.
func CardIDGT(v uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldCardID, v))
}

// CardIDGTE applies the GTE predicate on the "card_id" field.
func CardIDGTE(v uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldCardID, v))
}

// CardIDLT applies the LT predicate on the "card_id" field.
func CardIDLT(v uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldCardID, v))
}

// CardIDLTE applies the LTE predicate on the "card_id" field.
func CardIDLTE(v uuid.UUID) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldCardID, v))
}

// CardIDIsNil applies the IsNil predicate on the "card_id" field.
func CardIDIsNil() predicate.FinContract {
	return predicate.FinContract(sql.FieldIsNull(FieldCardID))
}

// CardIDNotNil applies the NotNil predicate on the "card_id" field.
func CardIDNotNil() predicate.FinContract {
	return predicate.FinContract(sql.FieldNotNull(FieldCardID))
}

// ContractCodeEQ applies the EQ predicate on the "contract_code" field.
func ContractCodeEQ(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldContractCode, v))
}

// ContractCodeNEQ applies the NEQ predicate on the "contract_code" field.
func ContractCodeNEQ(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldContractCode, v))
}

// ContractCodeIn applies the In predicate on the "contract_code" field.
func ContractCodeIn(vs ...string) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldContractCode, vs...))
}

// ContractCodeNotIn applies the NotIn predicate on the "contract_code" field.
func ContractCodeNotIn(vs ...string) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldContractCode, vs...))
}

// ContractCodeGT applies the GT predicate on the "contract_code" field.
func ContractCodeGT(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldContractCode, v))
}

// ContractCodeGTE applies the GTE predicate on the "contract_code" field.
func ContractCodeGTE(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldContractCode, v))
}

// ContractCodeLT applies the LT predicate on the "contract_code" field.
func ContractCodeLT(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldContractCode, v))
}

// ContractCodeLTE applies the LTE predicate on the "contract_code" field.
func ContractCodeLTE(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldContractCode, v))
}

// ContractCodeContains applies the Contains predicate on the "contract_code" field.
func ContractCodeContains(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldContains(FieldContractCode, v))
}

// ContractCodeHasPrefix applies the HasPrefix predicate on the "contract_code" field.
func ContractCodeHasPrefix(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldHasPrefix(FieldContractCode, v))
}

// ContractCodeHasSuffix applies the HasSuffix predicate on the "contract_code" field.
func ContractCodeHasSuffix(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldHasSuffix(FieldContractCode, v))
}

// ContractCodeIsNil applies the IsNil predicate on the "contract_code" field.
func ContractCodeIsNil() predicate.FinContract {
	return predicate.FinContract(sql.FieldIsNull(FieldContractCode))
}

// ContractCodeNotNil applies the NotNil predicate on the "contract_code" field.
func ContractCodeNotNil() predicate.FinContract {
	return predicate.FinContract(sql.FieldNotNull(FieldContractCode))
}

// ContractCodeEqualFold applies the EqualFold predicate on the "contract_code" field.
func ContractCodeEqualFold(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEqualFold(FieldContractCode, v))
}

// ContractCodeContainsFold applies the ContainsFold predicate on the "contract_code" field.
func ContractCodeContainsFold(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldContainsFold(FieldContractCode, v))
}

// ContractDataOpenEQ applies the EQ predicate on the "contract_data_open" field.
func ContractDataOpenEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldContractDataOpen, v))
}

// ContractDataOpenNEQ applies the NEQ predicate on the "contract_data_open" field.
func ContractDataOpenNEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldContractDataOpen, v))
}

// ContractDataOpenIn applies the In predicate on the "contract_data_open" field.
func ContractDataOpenIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldContractDataOpen, vs...))
}

// ContractDataOpenNotIn applies the NotIn predicate on the "contract_data_open" field.
func ContractDataOpenNotIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldContractDataOpen, vs...))
}

// ContractDataOpenGT applies the GT predicate on the "contract_data_open" field.
func ContractDataOpenGT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldContractDataOpen, v))
}

// ContractDataOpenGTE applies the GTE predicate on the "contract_data_open" field.
func ContractDataOpenGTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldContractDataOpen, v))
}

// ContractDataOpenLT applies the LT predicate on the "contract_data_open" field.
func ContractDataOpenLT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldContractDataOpen, v))
}

// ContractDataOpenLTE applies the LTE predicate on the "contract_data_open" field.
func ContractDataOpenLTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldContractDataOpen, v))
}

// ContractDataCloseEQ applies the EQ predicate on the "contract_data_close" field.
func ContractDataCloseEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldContractDataClose, v))
}

// ContractDataCloseNEQ applies the NEQ predicate on the "contract_data_close" field.
func ContractDataCloseNEQ(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldContractDataClose, v))
}

// ContractDataCloseIn applies the In predicate on the "contract_data_close" field.
func ContractDataCloseIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldContractDataClose, vs...))
}

// ContractDataCloseNotIn applies the NotIn predicate on the "contract_data_close" field.
func ContractDataCloseNotIn(vs ...time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldContractDataClose, vs...))
}

// ContractDataCloseGT applies the GT predicate on the "contract_data_close" field.
func ContractDataCloseGT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldContractDataClose, v))
}

// ContractDataCloseGTE applies the GTE predicate on the "contract_data_close" field.
func ContractDataCloseGTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldContractDataClose, v))
}

// ContractDataCloseLT applies the LT predicate on the "contract_data_close" field.
func ContractDataCloseLT(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldContractDataClose, v))
}

// ContractDataCloseLTE applies the LTE predicate on the "contract_data_close" field.
func ContractDataCloseLTE(v time.Time) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldContractDataClose, v))
}

// ContractDataCloseIsNil applies the IsNil predicate on the "contract_data_close" field.
func ContractDataCloseIsNil() predicate.FinContract {
	return predicate.FinContract(sql.FieldIsNull(FieldContractDataClose))
}

// ContractDataCloseNotNil applies the NotNil predicate on the "contract_data_close" field.
func ContractDataCloseNotNil() predicate.FinContract {
	return predicate.FinContract(sql.FieldNotNull(FieldContractDataClose))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...string) predicate.FinContract {
	return predicate.FinContract(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...string) predicate.FinContract {
	return predicate.FinContract(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldLTE(FieldUserID, v))
}

// UserIDContains applies the Contains predicate on the "user_id" field.
func UserIDContains(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldContains(FieldUserID, v))
}

// UserIDHasPrefix applies the HasPrefix predicate on the "user_id" field.
func UserIDHasPrefix(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldHasPrefix(FieldUserID, v))
}

// UserIDHasSuffix applies the HasSuffix predicate on the "user_id" field.
func UserIDHasSuffix(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldHasSuffix(FieldUserID, v))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.FinContract {
	return predicate.FinContract(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.FinContract {
	return predicate.FinContract(sql.FieldNotNull(FieldUserID))
}

// UserIDEqualFold applies the EqualFold predicate on the "user_id" field.
func UserIDEqualFold(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldEqualFold(FieldUserID, v))
}

// UserIDContainsFold applies the ContainsFold predicate on the "user_id" field.
func UserIDContainsFold(v string) predicate.FinContract {
	return predicate.FinContract(sql.FieldContainsFold(FieldUserID, v))
}

// HasCards applies the HasEdge predicate on the "cards" edge.
func HasCards() predicate.FinContract {
	return predicate.FinContract(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, CardsTable, CardsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCardsWith applies the HasEdge predicate on the "cards" edge with a given conditions (other predicates).
func HasCardsWith(preds ...predicate.Cards) predicate.FinContract {
	return predicate.FinContract(func(s *sql.Selector) {
		step := newCardsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.FinContract) predicate.FinContract {
	return predicate.FinContract(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.FinContract) predicate.FinContract {
	return predicate.FinContract(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.FinContract) predicate.FinContract {
	return predicate.FinContract(sql.NotPredicates(p))
}
