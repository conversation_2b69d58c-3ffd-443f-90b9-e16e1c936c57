// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// ClientVerificationResultsQuery is the builder for querying ClientVerificationResults entities.
type ClientVerificationResultsQuery struct {
	config
	ctx        *QueryContext
	order      []clientverificationresults.OrderOption
	inters     []Interceptor
	predicates []predicate.ClientVerificationResults
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ClientVerificationResultsQuery builder.
func (cvrq *ClientVerificationResultsQuery) Where(ps ...predicate.ClientVerificationResults) *ClientVerificationResultsQuery {
	cvrq.predicates = append(cvrq.predicates, ps...)
	return cvrq
}

// Limit the number of records to be returned by this query.
func (cvrq *ClientVerificationResultsQuery) Limit(limit int) *ClientVerificationResultsQuery {
	cvrq.ctx.Limit = &limit
	return cvrq
}

// Offset to start from.
func (cvrq *ClientVerificationResultsQuery) Offset(offset int) *ClientVerificationResultsQuery {
	cvrq.ctx.Offset = &offset
	return cvrq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (cvrq *ClientVerificationResultsQuery) Unique(unique bool) *ClientVerificationResultsQuery {
	cvrq.ctx.Unique = &unique
	return cvrq
}

// Order specifies how the records should be ordered.
func (cvrq *ClientVerificationResultsQuery) Order(o ...clientverificationresults.OrderOption) *ClientVerificationResultsQuery {
	cvrq.order = append(cvrq.order, o...)
	return cvrq
}

// First returns the first ClientVerificationResults entity from the query.
// Returns a *NotFoundError when no ClientVerificationResults was found.
func (cvrq *ClientVerificationResultsQuery) First(ctx context.Context) (*ClientVerificationResults, error) {
	nodes, err := cvrq.Limit(1).All(setContextOp(ctx, cvrq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{clientverificationresults.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (cvrq *ClientVerificationResultsQuery) FirstX(ctx context.Context) *ClientVerificationResults {
	node, err := cvrq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first ClientVerificationResults ID from the query.
// Returns a *NotFoundError when no ClientVerificationResults ID was found.
func (cvrq *ClientVerificationResultsQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = cvrq.Limit(1).IDs(setContextOp(ctx, cvrq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{clientverificationresults.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (cvrq *ClientVerificationResultsQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := cvrq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single ClientVerificationResults entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one ClientVerificationResults entity is found.
// Returns a *NotFoundError when no ClientVerificationResults entities are found.
func (cvrq *ClientVerificationResultsQuery) Only(ctx context.Context) (*ClientVerificationResults, error) {
	nodes, err := cvrq.Limit(2).All(setContextOp(ctx, cvrq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{clientverificationresults.Label}
	default:
		return nil, &NotSingularError{clientverificationresults.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (cvrq *ClientVerificationResultsQuery) OnlyX(ctx context.Context) *ClientVerificationResults {
	node, err := cvrq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only ClientVerificationResults ID in the query.
// Returns a *NotSingularError when more than one ClientVerificationResults ID is found.
// Returns a *NotFoundError when no entities are found.
func (cvrq *ClientVerificationResultsQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = cvrq.Limit(2).IDs(setContextOp(ctx, cvrq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{clientverificationresults.Label}
	default:
		err = &NotSingularError{clientverificationresults.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (cvrq *ClientVerificationResultsQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := cvrq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of ClientVerificationResultsSlice.
func (cvrq *ClientVerificationResultsQuery) All(ctx context.Context) ([]*ClientVerificationResults, error) {
	ctx = setContextOp(ctx, cvrq.ctx, ent.OpQueryAll)
	if err := cvrq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*ClientVerificationResults, *ClientVerificationResultsQuery]()
	return withInterceptors[[]*ClientVerificationResults](ctx, cvrq, qr, cvrq.inters)
}

// AllX is like All, but panics if an error occurs.
func (cvrq *ClientVerificationResultsQuery) AllX(ctx context.Context) []*ClientVerificationResults {
	nodes, err := cvrq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of ClientVerificationResults IDs.
func (cvrq *ClientVerificationResultsQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if cvrq.ctx.Unique == nil && cvrq.path != nil {
		cvrq.Unique(true)
	}
	ctx = setContextOp(ctx, cvrq.ctx, ent.OpQueryIDs)
	if err = cvrq.Select(clientverificationresults.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (cvrq *ClientVerificationResultsQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := cvrq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (cvrq *ClientVerificationResultsQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, cvrq.ctx, ent.OpQueryCount)
	if err := cvrq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, cvrq, querierCount[*ClientVerificationResultsQuery](), cvrq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (cvrq *ClientVerificationResultsQuery) CountX(ctx context.Context) int {
	count, err := cvrq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (cvrq *ClientVerificationResultsQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, cvrq.ctx, ent.OpQueryExist)
	switch _, err := cvrq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (cvrq *ClientVerificationResultsQuery) ExistX(ctx context.Context) bool {
	exist, err := cvrq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ClientVerificationResultsQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (cvrq *ClientVerificationResultsQuery) Clone() *ClientVerificationResultsQuery {
	if cvrq == nil {
		return nil
	}
	return &ClientVerificationResultsQuery{
		config:     cvrq.config,
		ctx:        cvrq.ctx.Clone(),
		order:      append([]clientverificationresults.OrderOption{}, cvrq.order...),
		inters:     append([]Interceptor{}, cvrq.inters...),
		predicates: append([]predicate.ClientVerificationResults{}, cvrq.predicates...),
		// clone intermediate query.
		sql:  cvrq.sql.Clone(),
		path: cvrq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.ClientVerificationResults.Query().
//		GroupBy(clientverificationresults.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (cvrq *ClientVerificationResultsQuery) GroupBy(field string, fields ...string) *ClientVerificationResultsGroupBy {
	cvrq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ClientVerificationResultsGroupBy{build: cvrq}
	grbuild.flds = &cvrq.ctx.Fields
	grbuild.label = clientverificationresults.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.ClientVerificationResults.Query().
//		Select(clientverificationresults.FieldCreateTime).
//		Scan(ctx, &v)
func (cvrq *ClientVerificationResultsQuery) Select(fields ...string) *ClientVerificationResultsSelect {
	cvrq.ctx.Fields = append(cvrq.ctx.Fields, fields...)
	sbuild := &ClientVerificationResultsSelect{ClientVerificationResultsQuery: cvrq}
	sbuild.label = clientverificationresults.Label
	sbuild.flds, sbuild.scan = &cvrq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ClientVerificationResultsSelect configured with the given aggregations.
func (cvrq *ClientVerificationResultsQuery) Aggregate(fns ...AggregateFunc) *ClientVerificationResultsSelect {
	return cvrq.Select().Aggregate(fns...)
}

func (cvrq *ClientVerificationResultsQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range cvrq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, cvrq); err != nil {
				return err
			}
		}
	}
	for _, f := range cvrq.ctx.Fields {
		if !clientverificationresults.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if cvrq.path != nil {
		prev, err := cvrq.path(ctx)
		if err != nil {
			return err
		}
		cvrq.sql = prev
	}
	return nil
}

func (cvrq *ClientVerificationResultsQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*ClientVerificationResults, error) {
	var (
		nodes = []*ClientVerificationResults{}
		_spec = cvrq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*ClientVerificationResults).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &ClientVerificationResults{config: cvrq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, cvrq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (cvrq *ClientVerificationResultsQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := cvrq.querySpec()
	_spec.Node.Columns = cvrq.ctx.Fields
	if len(cvrq.ctx.Fields) > 0 {
		_spec.Unique = cvrq.ctx.Unique != nil && *cvrq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, cvrq.driver, _spec)
}

func (cvrq *ClientVerificationResultsQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(clientverificationresults.Table, clientverificationresults.Columns, sqlgraph.NewFieldSpec(clientverificationresults.FieldID, field.TypeUUID))
	_spec.From = cvrq.sql
	if unique := cvrq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if cvrq.path != nil {
		_spec.Unique = true
	}
	if fields := cvrq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, clientverificationresults.FieldID)
		for i := range fields {
			if fields[i] != clientverificationresults.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := cvrq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := cvrq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := cvrq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := cvrq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (cvrq *ClientVerificationResultsQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(cvrq.driver.Dialect())
	t1 := builder.Table(clientverificationresults.Table)
	columns := cvrq.ctx.Fields
	if len(columns) == 0 {
		columns = clientverificationresults.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if cvrq.sql != nil {
		selector = cvrq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if cvrq.ctx.Unique != nil && *cvrq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range cvrq.predicates {
		p(selector)
	}
	for _, p := range cvrq.order {
		p(selector)
	}
	if offset := cvrq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := cvrq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ClientVerificationResultsGroupBy is the group-by builder for ClientVerificationResults entities.
type ClientVerificationResultsGroupBy struct {
	selector
	build *ClientVerificationResultsQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (cvrgb *ClientVerificationResultsGroupBy) Aggregate(fns ...AggregateFunc) *ClientVerificationResultsGroupBy {
	cvrgb.fns = append(cvrgb.fns, fns...)
	return cvrgb
}

// Scan applies the selector query and scans the result into the given value.
func (cvrgb *ClientVerificationResultsGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cvrgb.build.ctx, ent.OpQueryGroupBy)
	if err := cvrgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ClientVerificationResultsQuery, *ClientVerificationResultsGroupBy](ctx, cvrgb.build, cvrgb, cvrgb.build.inters, v)
}

func (cvrgb *ClientVerificationResultsGroupBy) sqlScan(ctx context.Context, root *ClientVerificationResultsQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(cvrgb.fns))
	for _, fn := range cvrgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*cvrgb.flds)+len(cvrgb.fns))
		for _, f := range *cvrgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*cvrgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cvrgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ClientVerificationResultsSelect is the builder for selecting fields of ClientVerificationResults entities.
type ClientVerificationResultsSelect struct {
	*ClientVerificationResultsQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cvrs *ClientVerificationResultsSelect) Aggregate(fns ...AggregateFunc) *ClientVerificationResultsSelect {
	cvrs.fns = append(cvrs.fns, fns...)
	return cvrs
}

// Scan applies the selector query and scans the result into the given value.
func (cvrs *ClientVerificationResultsSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cvrs.ctx, ent.OpQuerySelect)
	if err := cvrs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ClientVerificationResultsQuery, *ClientVerificationResultsSelect](ctx, cvrs.ClientVerificationResultsQuery, cvrs, cvrs.inters, v)
}

func (cvrs *ClientVerificationResultsSelect) sqlScan(ctx context.Context, root *ClientVerificationResultsQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cvrs.fns))
	for _, fn := range cvrs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cvrs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cvrs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
