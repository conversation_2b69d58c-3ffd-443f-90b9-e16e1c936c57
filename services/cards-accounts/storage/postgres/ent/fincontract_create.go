// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
)

// FinContractCreate is the builder for creating a FinContract entity.
type FinContractCreate struct {
	config
	mutation *FinContractMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *FinContractCreate) SetCreateTime(v time.Time) *FinContractCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableCreateTime(v *time.Time) *FinContractCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *FinContractCreate) SetUpdateTime(v time.Time) *FinContractCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableUpdateTime(v *time.Time) *FinContractCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetContractType sets the "contract_type" field.
func (_c *FinContractCreate) SetContractType(v fincontract.ContractType) *FinContractCreate {
	_c.mutation.SetContractType(v)
	return _c
}

// SetNillableContractType sets the "contract_type" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableContractType(v *fincontract.ContractType) *FinContractCreate {
	if v != nil {
		_c.SetContractType(*v)
	}
	return _c
}

// SetContractCurrency sets the "contract_currency" field.
func (_c *FinContractCreate) SetContractCurrency(v fincontract.ContractCurrency) *FinContractCreate {
	_c.mutation.SetContractCurrency(v)
	return _c
}

// SetNillableContractCurrency sets the "contract_currency" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableContractCurrency(v *fincontract.ContractCurrency) *FinContractCreate {
	if v != nil {
		_c.SetContractCurrency(*v)
	}
	return _c
}

// SetIban sets the "iban" field.
func (_c *FinContractCreate) SetIban(v string) *FinContractCreate {
	_c.mutation.SetIban(v)
	return _c
}

// SetStatus sets the "status" field.
func (_c *FinContractCreate) SetStatus(v fincontract.Status) *FinContractCreate {
	_c.mutation.SetStatus(v)
	return _c
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableStatus(v *fincontract.Status) *FinContractCreate {
	if v != nil {
		_c.SetStatus(*v)
	}
	return _c
}

// SetCreationDate sets the "creation_date" field.
func (_c *FinContractCreate) SetCreationDate(v time.Time) *FinContractCreate {
	_c.mutation.SetCreationDate(v)
	return _c
}

// SetNillableCreationDate sets the "creation_date" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableCreationDate(v *time.Time) *FinContractCreate {
	if v != nil {
		_c.SetCreationDate(*v)
	}
	return _c
}

// SetCardID sets the "card_id" field.
func (_c *FinContractCreate) SetCardID(v uuid.UUID) *FinContractCreate {
	_c.mutation.SetCardID(v)
	return _c
}

// SetNillableCardID sets the "card_id" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableCardID(v *uuid.UUID) *FinContractCreate {
	if v != nil {
		_c.SetCardID(*v)
	}
	return _c
}

// SetContractCode sets the "contract_code" field.
func (_c *FinContractCreate) SetContractCode(v string) *FinContractCreate {
	_c.mutation.SetContractCode(v)
	return _c
}

// SetNillableContractCode sets the "contract_code" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableContractCode(v *string) *FinContractCreate {
	if v != nil {
		_c.SetContractCode(*v)
	}
	return _c
}

// SetContractDataOpen sets the "contract_data_open" field.
func (_c *FinContractCreate) SetContractDataOpen(v time.Time) *FinContractCreate {
	_c.mutation.SetContractDataOpen(v)
	return _c
}

// SetContractDataClose sets the "contract_data_close" field.
func (_c *FinContractCreate) SetContractDataClose(v time.Time) *FinContractCreate {
	_c.mutation.SetContractDataClose(v)
	return _c
}

// SetNillableContractDataClose sets the "contract_data_close" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableContractDataClose(v *time.Time) *FinContractCreate {
	if v != nil {
		_c.SetContractDataClose(*v)
	}
	return _c
}

// SetUserID sets the "user_id" field.
func (_c *FinContractCreate) SetUserID(v string) *FinContractCreate {
	_c.mutation.SetUserID(v)
	return _c
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableUserID(v *string) *FinContractCreate {
	if v != nil {
		_c.SetUserID(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *FinContractCreate) SetID(v uuid.UUID) *FinContractCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *FinContractCreate) SetNillableID(v *uuid.UUID) *FinContractCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// SetCardsID sets the "cards" edge to the Cards entity by ID.
func (_c *FinContractCreate) SetCardsID(id uuid.UUID) *FinContractCreate {
	_c.mutation.SetCardsID(id)
	return _c
}

// SetNillableCardsID sets the "cards" edge to the Cards entity by ID if the given value is not nil.
func (_c *FinContractCreate) SetNillableCardsID(id *uuid.UUID) *FinContractCreate {
	if id != nil {
		_c = _c.SetCardsID(*id)
	}
	return _c
}

// SetCards sets the "cards" edge to the Cards entity.
func (_c *FinContractCreate) SetCards(v *Cards) *FinContractCreate {
	return _c.SetCardsID(v.ID)
}

// Mutation returns the FinContractMutation object of the builder.
func (_c *FinContractCreate) Mutation() *FinContractMutation {
	return _c.mutation
}

// Save creates the FinContract in the database.
func (_c *FinContractCreate) Save(ctx context.Context) (*FinContract, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *FinContractCreate) SaveX(ctx context.Context) *FinContract {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *FinContractCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *FinContractCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *FinContractCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := fincontract.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := fincontract.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.ContractType(); !ok {
		v := fincontract.DefaultContractType
		_c.mutation.SetContractType(v)
	}
	if _, ok := _c.mutation.ContractCurrency(); !ok {
		v := fincontract.DefaultContractCurrency
		_c.mutation.SetContractCurrency(v)
	}
	if _, ok := _c.mutation.Status(); !ok {
		v := fincontract.DefaultStatus
		_c.mutation.SetStatus(v)
	}
	if _, ok := _c.mutation.CreationDate(); !ok {
		v := fincontract.DefaultCreationDate()
		_c.mutation.SetCreationDate(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := fincontract.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *FinContractCreate) check() error {
	if _, ok := _c.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "FinContract.create_time"`)}
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "FinContract.update_time"`)}
	}
	if _, ok := _c.mutation.ContractType(); !ok {
		return &ValidationError{Name: "contract_type", err: errors.New(`ent: missing required field "FinContract.contract_type"`)}
	}
	if v, ok := _c.mutation.ContractType(); ok {
		if err := fincontract.ContractTypeValidator(v); err != nil {
			return &ValidationError{Name: "contract_type", err: fmt.Errorf(`ent: validator failed for field "FinContract.contract_type": %w`, err)}
		}
	}
	if _, ok := _c.mutation.ContractCurrency(); !ok {
		return &ValidationError{Name: "contract_currency", err: errors.New(`ent: missing required field "FinContract.contract_currency"`)}
	}
	if v, ok := _c.mutation.ContractCurrency(); ok {
		if err := fincontract.ContractCurrencyValidator(v); err != nil {
			return &ValidationError{Name: "contract_currency", err: fmt.Errorf(`ent: validator failed for field "FinContract.contract_currency": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Iban(); !ok {
		return &ValidationError{Name: "iban", err: errors.New(`ent: missing required field "FinContract.iban"`)}
	}
	if _, ok := _c.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "FinContract.status"`)}
	}
	if v, ok := _c.mutation.Status(); ok {
		if err := fincontract.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "FinContract.status": %w`, err)}
		}
	}
	if _, ok := _c.mutation.CreationDate(); !ok {
		return &ValidationError{Name: "creation_date", err: errors.New(`ent: missing required field "FinContract.creation_date"`)}
	}
	if _, ok := _c.mutation.ContractDataOpen(); !ok {
		return &ValidationError{Name: "contract_data_open", err: errors.New(`ent: missing required field "FinContract.contract_data_open"`)}
	}
	return nil
}

func (_c *FinContractCreate) sqlSave(ctx context.Context) (*FinContract, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *FinContractCreate) createSpec() (*FinContract, *sqlgraph.CreateSpec) {
	var (
		_node = &FinContract{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(fincontract.Table, sqlgraph.NewFieldSpec(fincontract.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(fincontract.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(fincontract.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.ContractType(); ok {
		_spec.SetField(fincontract.FieldContractType, field.TypeEnum, value)
		_node.ContractType = value
	}
	if value, ok := _c.mutation.ContractCurrency(); ok {
		_spec.SetField(fincontract.FieldContractCurrency, field.TypeEnum, value)
		_node.ContractCurrency = value
	}
	if value, ok := _c.mutation.Iban(); ok {
		_spec.SetField(fincontract.FieldIban, field.TypeString, value)
		_node.Iban = value
	}
	if value, ok := _c.mutation.Status(); ok {
		_spec.SetField(fincontract.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := _c.mutation.CreationDate(); ok {
		_spec.SetField(fincontract.FieldCreationDate, field.TypeTime, value)
		_node.CreationDate = value
	}
	if value, ok := _c.mutation.CardID(); ok {
		_spec.SetField(fincontract.FieldCardID, field.TypeUUID, value)
		_node.CardID = &value
	}
	if value, ok := _c.mutation.ContractCode(); ok {
		_spec.SetField(fincontract.FieldContractCode, field.TypeString, value)
		_node.ContractCode = &value
	}
	if value, ok := _c.mutation.ContractDataOpen(); ok {
		_spec.SetField(fincontract.FieldContractDataOpen, field.TypeTime, value)
		_node.ContractDataOpen = value
	}
	if value, ok := _c.mutation.ContractDataClose(); ok {
		_spec.SetField(fincontract.FieldContractDataClose, field.TypeTime, value)
		_node.ContractDataClose = &value
	}
	if value, ok := _c.mutation.UserID(); ok {
		_spec.SetField(fincontract.FieldUserID, field.TypeString, value)
		_node.UserID = &value
	}
	if nodes := _c.mutation.CardsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   fincontract.CardsTable,
			Columns: []string{fincontract.CardsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cards.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.fin_contract_cards = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.FinContract.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.FinContractUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *FinContractCreate) OnConflict(opts ...sql.ConflictOption) *FinContractUpsertOne {
	_c.conflict = opts
	return &FinContractUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.FinContract.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *FinContractCreate) OnConflictColumns(columns ...string) *FinContractUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &FinContractUpsertOne{
		create: _c,
	}
}

type (
	// FinContractUpsertOne is the builder for "upsert"-ing
	//  one FinContract node.
	FinContractUpsertOne struct {
		create *FinContractCreate
	}

	// FinContractUpsert is the "OnConflict" setter.
	FinContractUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *FinContractUpsert) SetUpdateTime(v time.Time) *FinContractUpsert {
	u.Set(fincontract.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateUpdateTime() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldUpdateTime)
	return u
}

// SetContractType sets the "contract_type" field.
func (u *FinContractUpsert) SetContractType(v fincontract.ContractType) *FinContractUpsert {
	u.Set(fincontract.FieldContractType, v)
	return u
}

// UpdateContractType sets the "contract_type" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateContractType() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldContractType)
	return u
}

// SetContractCurrency sets the "contract_currency" field.
func (u *FinContractUpsert) SetContractCurrency(v fincontract.ContractCurrency) *FinContractUpsert {
	u.Set(fincontract.FieldContractCurrency, v)
	return u
}

// UpdateContractCurrency sets the "contract_currency" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateContractCurrency() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldContractCurrency)
	return u
}

// SetIban sets the "iban" field.
func (u *FinContractUpsert) SetIban(v string) *FinContractUpsert {
	u.Set(fincontract.FieldIban, v)
	return u
}

// UpdateIban sets the "iban" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateIban() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldIban)
	return u
}

// SetStatus sets the "status" field.
func (u *FinContractUpsert) SetStatus(v fincontract.Status) *FinContractUpsert {
	u.Set(fincontract.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateStatus() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldStatus)
	return u
}

// SetCardID sets the "card_id" field.
func (u *FinContractUpsert) SetCardID(v uuid.UUID) *FinContractUpsert {
	u.Set(fincontract.FieldCardID, v)
	return u
}

// UpdateCardID sets the "card_id" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateCardID() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldCardID)
	return u
}

// ClearCardID clears the value of the "card_id" field.
func (u *FinContractUpsert) ClearCardID() *FinContractUpsert {
	u.SetNull(fincontract.FieldCardID)
	return u
}

// SetContractCode sets the "contract_code" field.
func (u *FinContractUpsert) SetContractCode(v string) *FinContractUpsert {
	u.Set(fincontract.FieldContractCode, v)
	return u
}

// UpdateContractCode sets the "contract_code" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateContractCode() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldContractCode)
	return u
}

// ClearContractCode clears the value of the "contract_code" field.
func (u *FinContractUpsert) ClearContractCode() *FinContractUpsert {
	u.SetNull(fincontract.FieldContractCode)
	return u
}

// SetContractDataOpen sets the "contract_data_open" field.
func (u *FinContractUpsert) SetContractDataOpen(v time.Time) *FinContractUpsert {
	u.Set(fincontract.FieldContractDataOpen, v)
	return u
}

// UpdateContractDataOpen sets the "contract_data_open" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateContractDataOpen() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldContractDataOpen)
	return u
}

// SetContractDataClose sets the "contract_data_close" field.
func (u *FinContractUpsert) SetContractDataClose(v time.Time) *FinContractUpsert {
	u.Set(fincontract.FieldContractDataClose, v)
	return u
}

// UpdateContractDataClose sets the "contract_data_close" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateContractDataClose() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldContractDataClose)
	return u
}

// ClearContractDataClose clears the value of the "contract_data_close" field.
func (u *FinContractUpsert) ClearContractDataClose() *FinContractUpsert {
	u.SetNull(fincontract.FieldContractDataClose)
	return u
}

// SetUserID sets the "user_id" field.
func (u *FinContractUpsert) SetUserID(v string) *FinContractUpsert {
	u.Set(fincontract.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *FinContractUpsert) UpdateUserID() *FinContractUpsert {
	u.SetExcluded(fincontract.FieldUserID)
	return u
}

// ClearUserID clears the value of the "user_id" field.
func (u *FinContractUpsert) ClearUserID() *FinContractUpsert {
	u.SetNull(fincontract.FieldUserID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.FinContract.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(fincontract.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *FinContractUpsertOne) UpdateNewValues() *FinContractUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(fincontract.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(fincontract.FieldCreateTime)
		}
		if _, exists := u.create.mutation.CreationDate(); exists {
			s.SetIgnore(fincontract.FieldCreationDate)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.FinContract.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *FinContractUpsertOne) Ignore() *FinContractUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *FinContractUpsertOne) DoNothing() *FinContractUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the FinContractCreate.OnConflict
// documentation for more info.
func (u *FinContractUpsertOne) Update(set func(*FinContractUpsert)) *FinContractUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&FinContractUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *FinContractUpsertOne) SetUpdateTime(v time.Time) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateUpdateTime() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateUpdateTime()
	})
}

// SetContractType sets the "contract_type" field.
func (u *FinContractUpsertOne) SetContractType(v fincontract.ContractType) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractType(v)
	})
}

// UpdateContractType sets the "contract_type" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateContractType() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractType()
	})
}

// SetContractCurrency sets the "contract_currency" field.
func (u *FinContractUpsertOne) SetContractCurrency(v fincontract.ContractCurrency) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractCurrency(v)
	})
}

// UpdateContractCurrency sets the "contract_currency" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateContractCurrency() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractCurrency()
	})
}

// SetIban sets the "iban" field.
func (u *FinContractUpsertOne) SetIban(v string) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetIban(v)
	})
}

// UpdateIban sets the "iban" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateIban() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateIban()
	})
}

// SetStatus sets the "status" field.
func (u *FinContractUpsertOne) SetStatus(v fincontract.Status) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateStatus() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateStatus()
	})
}

// SetCardID sets the "card_id" field.
func (u *FinContractUpsertOne) SetCardID(v uuid.UUID) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetCardID(v)
	})
}

// UpdateCardID sets the "card_id" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateCardID() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateCardID()
	})
}

// ClearCardID clears the value of the "card_id" field.
func (u *FinContractUpsertOne) ClearCardID() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.ClearCardID()
	})
}

// SetContractCode sets the "contract_code" field.
func (u *FinContractUpsertOne) SetContractCode(v string) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractCode(v)
	})
}

// UpdateContractCode sets the "contract_code" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateContractCode() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractCode()
	})
}

// ClearContractCode clears the value of the "contract_code" field.
func (u *FinContractUpsertOne) ClearContractCode() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.ClearContractCode()
	})
}

// SetContractDataOpen sets the "contract_data_open" field.
func (u *FinContractUpsertOne) SetContractDataOpen(v time.Time) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractDataOpen(v)
	})
}

// UpdateContractDataOpen sets the "contract_data_open" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateContractDataOpen() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractDataOpen()
	})
}

// SetContractDataClose sets the "contract_data_close" field.
func (u *FinContractUpsertOne) SetContractDataClose(v time.Time) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractDataClose(v)
	})
}

// UpdateContractDataClose sets the "contract_data_close" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateContractDataClose() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractDataClose()
	})
}

// ClearContractDataClose clears the value of the "contract_data_close" field.
func (u *FinContractUpsertOne) ClearContractDataClose() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.ClearContractDataClose()
	})
}

// SetUserID sets the "user_id" field.
func (u *FinContractUpsertOne) SetUserID(v string) *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *FinContractUpsertOne) UpdateUserID() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateUserID()
	})
}

// ClearUserID clears the value of the "user_id" field.
func (u *FinContractUpsertOne) ClearUserID() *FinContractUpsertOne {
	return u.Update(func(s *FinContractUpsert) {
		s.ClearUserID()
	})
}

// Exec executes the query.
func (u *FinContractUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for FinContractCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *FinContractUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *FinContractUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: FinContractUpsertOne.ID is not supported by MySQL driver. Use FinContractUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *FinContractUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// FinContractCreateBulk is the builder for creating many FinContract entities in bulk.
type FinContractCreateBulk struct {
	config
	err      error
	builders []*FinContractCreate
	conflict []sql.ConflictOption
}

// Save creates the FinContract entities in the database.
func (_c *FinContractCreateBulk) Save(ctx context.Context) ([]*FinContract, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*FinContract, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*FinContractMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *FinContractCreateBulk) SaveX(ctx context.Context) []*FinContract {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *FinContractCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *FinContractCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.FinContract.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.FinContractUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *FinContractCreateBulk) OnConflict(opts ...sql.ConflictOption) *FinContractUpsertBulk {
	_c.conflict = opts
	return &FinContractUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.FinContract.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *FinContractCreateBulk) OnConflictColumns(columns ...string) *FinContractUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &FinContractUpsertBulk{
		create: _c,
	}
}

// FinContractUpsertBulk is the builder for "upsert"-ing
// a bulk of FinContract nodes.
type FinContractUpsertBulk struct {
	create *FinContractCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.FinContract.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(fincontract.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *FinContractUpsertBulk) UpdateNewValues() *FinContractUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(fincontract.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(fincontract.FieldCreateTime)
			}
			if _, exists := b.mutation.CreationDate(); exists {
				s.SetIgnore(fincontract.FieldCreationDate)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.FinContract.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *FinContractUpsertBulk) Ignore() *FinContractUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *FinContractUpsertBulk) DoNothing() *FinContractUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the FinContractCreateBulk.OnConflict
// documentation for more info.
func (u *FinContractUpsertBulk) Update(set func(*FinContractUpsert)) *FinContractUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&FinContractUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *FinContractUpsertBulk) SetUpdateTime(v time.Time) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateUpdateTime() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateUpdateTime()
	})
}

// SetContractType sets the "contract_type" field.
func (u *FinContractUpsertBulk) SetContractType(v fincontract.ContractType) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractType(v)
	})
}

// UpdateContractType sets the "contract_type" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateContractType() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractType()
	})
}

// SetContractCurrency sets the "contract_currency" field.
func (u *FinContractUpsertBulk) SetContractCurrency(v fincontract.ContractCurrency) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractCurrency(v)
	})
}

// UpdateContractCurrency sets the "contract_currency" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateContractCurrency() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractCurrency()
	})
}

// SetIban sets the "iban" field.
func (u *FinContractUpsertBulk) SetIban(v string) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetIban(v)
	})
}

// UpdateIban sets the "iban" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateIban() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateIban()
	})
}

// SetStatus sets the "status" field.
func (u *FinContractUpsertBulk) SetStatus(v fincontract.Status) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateStatus() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateStatus()
	})
}

// SetCardID sets the "card_id" field.
func (u *FinContractUpsertBulk) SetCardID(v uuid.UUID) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetCardID(v)
	})
}

// UpdateCardID sets the "card_id" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateCardID() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateCardID()
	})
}

// ClearCardID clears the value of the "card_id" field.
func (u *FinContractUpsertBulk) ClearCardID() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.ClearCardID()
	})
}

// SetContractCode sets the "contract_code" field.
func (u *FinContractUpsertBulk) SetContractCode(v string) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractCode(v)
	})
}

// UpdateContractCode sets the "contract_code" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateContractCode() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractCode()
	})
}

// ClearContractCode clears the value of the "contract_code" field.
func (u *FinContractUpsertBulk) ClearContractCode() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.ClearContractCode()
	})
}

// SetContractDataOpen sets the "contract_data_open" field.
func (u *FinContractUpsertBulk) SetContractDataOpen(v time.Time) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractDataOpen(v)
	})
}

// UpdateContractDataOpen sets the "contract_data_open" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateContractDataOpen() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractDataOpen()
	})
}

// SetContractDataClose sets the "contract_data_close" field.
func (u *FinContractUpsertBulk) SetContractDataClose(v time.Time) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetContractDataClose(v)
	})
}

// UpdateContractDataClose sets the "contract_data_close" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateContractDataClose() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateContractDataClose()
	})
}

// ClearContractDataClose clears the value of the "contract_data_close" field.
func (u *FinContractUpsertBulk) ClearContractDataClose() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.ClearContractDataClose()
	})
}

// SetUserID sets the "user_id" field.
func (u *FinContractUpsertBulk) SetUserID(v string) *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *FinContractUpsertBulk) UpdateUserID() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.UpdateUserID()
	})
}

// ClearUserID clears the value of the "user_id" field.
func (u *FinContractUpsertBulk) ClearUserID() *FinContractUpsertBulk {
	return u.Update(func(s *FinContractUpsert) {
		s.ClearUserID()
	})
}

// Exec executes the query.
func (u *FinContractUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the FinContractCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for FinContractCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *FinContractUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
