// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/health"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Accounts is the client for interacting with the Accounts builders.
	Accounts *AccountsClient
	// Cards is the client for interacting with the Cards builders.
	Cards *CardsClient
	// ClientVerificationResults is the client for interacting with the ClientVerificationResults builders.
	ClientVerificationResults *ClientVerificationResultsClient
	// FinContract is the client for interacting with the FinContract builders.
	FinContract *FinContractClient
	// Health is the client for interacting with the Health builders.
	Health *HealthClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Accounts = NewAccountsClient(c.config)
	c.Cards = NewCardsClient(c.config)
	c.ClientVerificationResults = NewClientVerificationResultsClient(c.config)
	c.FinContract = NewFinContractClient(c.config)
	c.Health = NewHealthClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                       ctx,
		config:                    cfg,
		Accounts:                  NewAccountsClient(cfg),
		Cards:                     NewCardsClient(cfg),
		ClientVerificationResults: NewClientVerificationResultsClient(cfg),
		FinContract:               NewFinContractClient(cfg),
		Health:                    NewHealthClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                       ctx,
		config:                    cfg,
		Accounts:                  NewAccountsClient(cfg),
		Cards:                     NewCardsClient(cfg),
		ClientVerificationResults: NewClientVerificationResultsClient(cfg),
		FinContract:               NewFinContractClient(cfg),
		Health:                    NewHealthClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Accounts.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.Accounts.Use(hooks...)
	c.Cards.Use(hooks...)
	c.ClientVerificationResults.Use(hooks...)
	c.FinContract.Use(hooks...)
	c.Health.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.Accounts.Intercept(interceptors...)
	c.Cards.Intercept(interceptors...)
	c.ClientVerificationResults.Intercept(interceptors...)
	c.FinContract.Intercept(interceptors...)
	c.Health.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AccountsMutation:
		return c.Accounts.mutate(ctx, m)
	case *CardsMutation:
		return c.Cards.mutate(ctx, m)
	case *ClientVerificationResultsMutation:
		return c.ClientVerificationResults.mutate(ctx, m)
	case *FinContractMutation:
		return c.FinContract.mutate(ctx, m)
	case *HealthMutation:
		return c.Health.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// AccountsClient is a client for the Accounts schema.
type AccountsClient struct {
	config
}

// NewAccountsClient returns a client for the Accounts from the given config.
func NewAccountsClient(c config) *AccountsClient {
	return &AccountsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `accounts.Hooks(f(g(h())))`.
func (c *AccountsClient) Use(hooks ...Hook) {
	c.hooks.Accounts = append(c.hooks.Accounts, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `accounts.Intercept(f(g(h())))`.
func (c *AccountsClient) Intercept(interceptors ...Interceptor) {
	c.inters.Accounts = append(c.inters.Accounts, interceptors...)
}

// Create returns a builder for creating a Accounts entity.
func (c *AccountsClient) Create() *AccountsCreate {
	mutation := newAccountsMutation(c.config, OpCreate)
	return &AccountsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Accounts entities.
func (c *AccountsClient) CreateBulk(builders ...*AccountsCreate) *AccountsCreateBulk {
	return &AccountsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AccountsClient) MapCreateBulk(slice any, setFunc func(*AccountsCreate, int)) *AccountsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AccountsCreateBulk{err: fmt.Errorf("calling to AccountsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AccountsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AccountsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Accounts.
func (c *AccountsClient) Update() *AccountsUpdate {
	mutation := newAccountsMutation(c.config, OpUpdate)
	return &AccountsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AccountsClient) UpdateOne(_m *Accounts) *AccountsUpdateOne {
	mutation := newAccountsMutation(c.config, OpUpdateOne, withAccounts(_m))
	return &AccountsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AccountsClient) UpdateOneID(id uuid.UUID) *AccountsUpdateOne {
	mutation := newAccountsMutation(c.config, OpUpdateOne, withAccountsID(id))
	return &AccountsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Accounts.
func (c *AccountsClient) Delete() *AccountsDelete {
	mutation := newAccountsMutation(c.config, OpDelete)
	return &AccountsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AccountsClient) DeleteOne(_m *Accounts) *AccountsDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AccountsClient) DeleteOneID(id uuid.UUID) *AccountsDeleteOne {
	builder := c.Delete().Where(accounts.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AccountsDeleteOne{builder}
}

// Query returns a query builder for Accounts.
func (c *AccountsClient) Query() *AccountsQuery {
	return &AccountsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAccounts},
		inters: c.Interceptors(),
	}
}

// Get returns a Accounts entity by its id.
func (c *AccountsClient) Get(ctx context.Context, id uuid.UUID) (*Accounts, error) {
	return c.Query().Where(accounts.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AccountsClient) GetX(ctx context.Context, id uuid.UUID) *Accounts {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AccountsClient) Hooks() []Hook {
	return c.hooks.Accounts
}

// Interceptors returns the client interceptors.
func (c *AccountsClient) Interceptors() []Interceptor {
	return c.inters.Accounts
}

func (c *AccountsClient) mutate(ctx context.Context, m *AccountsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AccountsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AccountsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AccountsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AccountsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Accounts mutation op: %q", m.Op())
	}
}

// CardsClient is a client for the Cards schema.
type CardsClient struct {
	config
}

// NewCardsClient returns a client for the Cards from the given config.
func NewCardsClient(c config) *CardsClient {
	return &CardsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `cards.Hooks(f(g(h())))`.
func (c *CardsClient) Use(hooks ...Hook) {
	c.hooks.Cards = append(c.hooks.Cards, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `cards.Intercept(f(g(h())))`.
func (c *CardsClient) Intercept(interceptors ...Interceptor) {
	c.inters.Cards = append(c.inters.Cards, interceptors...)
}

// Create returns a builder for creating a Cards entity.
func (c *CardsClient) Create() *CardsCreate {
	mutation := newCardsMutation(c.config, OpCreate)
	return &CardsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Cards entities.
func (c *CardsClient) CreateBulk(builders ...*CardsCreate) *CardsCreateBulk {
	return &CardsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CardsClient) MapCreateBulk(slice any, setFunc func(*CardsCreate, int)) *CardsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CardsCreateBulk{err: fmt.Errorf("calling to CardsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CardsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CardsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Cards.
func (c *CardsClient) Update() *CardsUpdate {
	mutation := newCardsMutation(c.config, OpUpdate)
	return &CardsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CardsClient) UpdateOne(_m *Cards) *CardsUpdateOne {
	mutation := newCardsMutation(c.config, OpUpdateOne, withCards(_m))
	return &CardsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CardsClient) UpdateOneID(id uuid.UUID) *CardsUpdateOne {
	mutation := newCardsMutation(c.config, OpUpdateOne, withCardsID(id))
	return &CardsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Cards.
func (c *CardsClient) Delete() *CardsDelete {
	mutation := newCardsMutation(c.config, OpDelete)
	return &CardsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CardsClient) DeleteOne(_m *Cards) *CardsDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CardsClient) DeleteOneID(id uuid.UUID) *CardsDeleteOne {
	builder := c.Delete().Where(cards.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CardsDeleteOne{builder}
}

// Query returns a query builder for Cards.
func (c *CardsClient) Query() *CardsQuery {
	return &CardsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCards},
		inters: c.Interceptors(),
	}
}

// Get returns a Cards entity by its id.
func (c *CardsClient) Get(ctx context.Context, id uuid.UUID) (*Cards, error) {
	return c.Query().Where(cards.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CardsClient) GetX(ctx context.Context, id uuid.UUID) *Cards {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryAccount queries the account edge of a Cards.
func (c *CardsClient) QueryAccount(_m *Cards) *AccountsQuery {
	query := (&AccountsClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cards.Table, cards.FieldID, id),
			sqlgraph.To(accounts.Table, accounts.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cards.AccountTable, cards.AccountColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CardsClient) Hooks() []Hook {
	return c.hooks.Cards
}

// Interceptors returns the client interceptors.
func (c *CardsClient) Interceptors() []Interceptor {
	return c.inters.Cards
}

func (c *CardsClient) mutate(ctx context.Context, m *CardsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CardsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CardsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CardsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CardsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Cards mutation op: %q", m.Op())
	}
}

// ClientVerificationResultsClient is a client for the ClientVerificationResults schema.
type ClientVerificationResultsClient struct {
	config
}

// NewClientVerificationResultsClient returns a client for the ClientVerificationResults from the given config.
func NewClientVerificationResultsClient(c config) *ClientVerificationResultsClient {
	return &ClientVerificationResultsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `clientverificationresults.Hooks(f(g(h())))`.
func (c *ClientVerificationResultsClient) Use(hooks ...Hook) {
	c.hooks.ClientVerificationResults = append(c.hooks.ClientVerificationResults, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `clientverificationresults.Intercept(f(g(h())))`.
func (c *ClientVerificationResultsClient) Intercept(interceptors ...Interceptor) {
	c.inters.ClientVerificationResults = append(c.inters.ClientVerificationResults, interceptors...)
}

// Create returns a builder for creating a ClientVerificationResults entity.
func (c *ClientVerificationResultsClient) Create() *ClientVerificationResultsCreate {
	mutation := newClientVerificationResultsMutation(c.config, OpCreate)
	return &ClientVerificationResultsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ClientVerificationResults entities.
func (c *ClientVerificationResultsClient) CreateBulk(builders ...*ClientVerificationResultsCreate) *ClientVerificationResultsCreateBulk {
	return &ClientVerificationResultsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ClientVerificationResultsClient) MapCreateBulk(slice any, setFunc func(*ClientVerificationResultsCreate, int)) *ClientVerificationResultsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ClientVerificationResultsCreateBulk{err: fmt.Errorf("calling to ClientVerificationResultsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ClientVerificationResultsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ClientVerificationResultsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ClientVerificationResults.
func (c *ClientVerificationResultsClient) Update() *ClientVerificationResultsUpdate {
	mutation := newClientVerificationResultsMutation(c.config, OpUpdate)
	return &ClientVerificationResultsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ClientVerificationResultsClient) UpdateOne(cvr *ClientVerificationResults) *ClientVerificationResultsUpdateOne {
	mutation := newClientVerificationResultsMutation(c.config, OpUpdateOne, withClientVerificationResults(cvr))
	return &ClientVerificationResultsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ClientVerificationResultsClient) UpdateOneID(id uuid.UUID) *ClientVerificationResultsUpdateOne {
	mutation := newClientVerificationResultsMutation(c.config, OpUpdateOne, withClientVerificationResultsID(id))
	return &ClientVerificationResultsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ClientVerificationResults.
func (c *ClientVerificationResultsClient) Delete() *ClientVerificationResultsDelete {
	mutation := newClientVerificationResultsMutation(c.config, OpDelete)
	return &ClientVerificationResultsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ClientVerificationResultsClient) DeleteOne(cvr *ClientVerificationResults) *ClientVerificationResultsDeleteOne {
	return c.DeleteOneID(cvr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ClientVerificationResultsClient) DeleteOneID(id uuid.UUID) *ClientVerificationResultsDeleteOne {
	builder := c.Delete().Where(clientverificationresults.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ClientVerificationResultsDeleteOne{builder}
}

// Query returns a query builder for ClientVerificationResults.
func (c *ClientVerificationResultsClient) Query() *ClientVerificationResultsQuery {
	return &ClientVerificationResultsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeClientVerificationResults},
		inters: c.Interceptors(),
	}
}

// Get returns a ClientVerificationResults entity by its id.
func (c *ClientVerificationResultsClient) Get(ctx context.Context, id uuid.UUID) (*ClientVerificationResults, error) {
	return c.Query().Where(clientverificationresults.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ClientVerificationResultsClient) GetX(ctx context.Context, id uuid.UUID) *ClientVerificationResults {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ClientVerificationResultsClient) Hooks() []Hook {
	return c.hooks.ClientVerificationResults
}

// Interceptors returns the client interceptors.
func (c *ClientVerificationResultsClient) Interceptors() []Interceptor {
	return c.inters.ClientVerificationResults
}

func (c *ClientVerificationResultsClient) mutate(ctx context.Context, m *ClientVerificationResultsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ClientVerificationResultsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ClientVerificationResultsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ClientVerificationResultsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ClientVerificationResultsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ClientVerificationResults mutation op: %q", m.Op())
	}
}

// FinContractClient is a client for the FinContract schema.
type FinContractClient struct {
	config
}

// NewFinContractClient returns a client for the FinContract from the given config.
func NewFinContractClient(c config) *FinContractClient {
	return &FinContractClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `fincontract.Hooks(f(g(h())))`.
func (c *FinContractClient) Use(hooks ...Hook) {
	c.hooks.FinContract = append(c.hooks.FinContract, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `fincontract.Intercept(f(g(h())))`.
func (c *FinContractClient) Intercept(interceptors ...Interceptor) {
	c.inters.FinContract = append(c.inters.FinContract, interceptors...)
}

// Create returns a builder for creating a FinContract entity.
func (c *FinContractClient) Create() *FinContractCreate {
	mutation := newFinContractMutation(c.config, OpCreate)
	return &FinContractCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of FinContract entities.
func (c *FinContractClient) CreateBulk(builders ...*FinContractCreate) *FinContractCreateBulk {
	return &FinContractCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FinContractClient) MapCreateBulk(slice any, setFunc func(*FinContractCreate, int)) *FinContractCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FinContractCreateBulk{err: fmt.Errorf("calling to FinContractClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FinContractCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FinContractCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for FinContract.
func (c *FinContractClient) Update() *FinContractUpdate {
	mutation := newFinContractMutation(c.config, OpUpdate)
	return &FinContractUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FinContractClient) UpdateOne(_m *FinContract) *FinContractUpdateOne {
	mutation := newFinContractMutation(c.config, OpUpdateOne, withFinContract(_m))
	return &FinContractUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FinContractClient) UpdateOneID(id uuid.UUID) *FinContractUpdateOne {
	mutation := newFinContractMutation(c.config, OpUpdateOne, withFinContractID(id))
	return &FinContractUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for FinContract.
func (c *FinContractClient) Delete() *FinContractDelete {
	mutation := newFinContractMutation(c.config, OpDelete)
	return &FinContractDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FinContractClient) DeleteOne(_m *FinContract) *FinContractDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FinContractClient) DeleteOneID(id uuid.UUID) *FinContractDeleteOne {
	builder := c.Delete().Where(fincontract.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FinContractDeleteOne{builder}
}

// Query returns a query builder for FinContract.
func (c *FinContractClient) Query() *FinContractQuery {
	return &FinContractQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFinContract},
		inters: c.Interceptors(),
	}
}

// Get returns a FinContract entity by its id.
func (c *FinContractClient) Get(ctx context.Context, id uuid.UUID) (*FinContract, error) {
	return c.Query().Where(fincontract.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FinContractClient) GetX(ctx context.Context, id uuid.UUID) *FinContract {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCards queries the cards edge of a FinContract.
func (c *FinContractClient) QueryCards(_m *FinContract) *CardsQuery {
	query := (&CardsClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(fincontract.Table, fincontract.FieldID, id),
			sqlgraph.To(cards.Table, cards.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, fincontract.CardsTable, fincontract.CardsColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FinContractClient) Hooks() []Hook {
	return c.hooks.FinContract
}

// Interceptors returns the client interceptors.
func (c *FinContractClient) Interceptors() []Interceptor {
	return c.inters.FinContract
}

func (c *FinContractClient) mutate(ctx context.Context, m *FinContractMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FinContractCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FinContractUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FinContractUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FinContractDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown FinContract mutation op: %q", m.Op())
	}
}

// HealthClient is a client for the Health schema.
type HealthClient struct {
	config
}

// NewHealthClient returns a client for the Health from the given config.
func NewHealthClient(c config) *HealthClient {
	return &HealthClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `health.Hooks(f(g(h())))`.
func (c *HealthClient) Use(hooks ...Hook) {
	c.hooks.Health = append(c.hooks.Health, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `health.Intercept(f(g(h())))`.
func (c *HealthClient) Intercept(interceptors ...Interceptor) {
	c.inters.Health = append(c.inters.Health, interceptors...)
}

// Create returns a builder for creating a Health entity.
func (c *HealthClient) Create() *HealthCreate {
	mutation := newHealthMutation(c.config, OpCreate)
	return &HealthCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Health entities.
func (c *HealthClient) CreateBulk(builders ...*HealthCreate) *HealthCreateBulk {
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *HealthClient) MapCreateBulk(slice any, setFunc func(*HealthCreate, int)) *HealthCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &HealthCreateBulk{err: fmt.Errorf("calling to HealthClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*HealthCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Health.
func (c *HealthClient) Update() *HealthUpdate {
	mutation := newHealthMutation(c.config, OpUpdate)
	return &HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *HealthClient) UpdateOne(_m *Health) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealth(_m))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *HealthClient) UpdateOneID(id uuid.UUID) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealthID(id))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Health.
func (c *HealthClient) Delete() *HealthDelete {
	mutation := newHealthMutation(c.config, OpDelete)
	return &HealthDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *HealthClient) DeleteOne(_m *Health) *HealthDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *HealthClient) DeleteOneID(id uuid.UUID) *HealthDeleteOne {
	builder := c.Delete().Where(health.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &HealthDeleteOne{builder}
}

// Query returns a query builder for Health.
func (c *HealthClient) Query() *HealthQuery {
	return &HealthQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeHealth},
		inters: c.Interceptors(),
	}
}

// Get returns a Health entity by its id.
func (c *HealthClient) Get(ctx context.Context, id uuid.UUID) (*Health, error) {
	return c.Query().Where(health.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *HealthClient) GetX(ctx context.Context, id uuid.UUID) *Health {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *HealthClient) Hooks() []Hook {
	return c.hooks.Health
}

// Interceptors returns the client interceptors.
func (c *HealthClient) Interceptors() []Interceptor {
	return c.inters.Health
}

func (c *HealthClient) mutate(ctx context.Context, m *HealthMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&HealthCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&HealthDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Health mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Accounts, Cards, ClientVerificationResults, FinContract, Health []ent.Hook
	}
	inters struct {
		Accounts, Cards, ClientVerificationResults, FinContract,
		Health []ent.Interceptor
	}
)
