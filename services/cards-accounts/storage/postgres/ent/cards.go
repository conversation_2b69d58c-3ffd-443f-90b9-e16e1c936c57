// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
)

// Cards is the model entity for the Cards schema.
type Cards struct {
	config `json:"-"`
	// ID of the ent.
	// идентификатор карты
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// идентификатор клиента в БД Users (FK)
	ClientID *uuid.UUID `json:"client_id,omitempty"`
	// идентификатор текущего счета, к которому привязана карта (FK O2O)
	AttachedAccountID *uuid.UUID `json:"attached_account_id,omitempty"`
	// Наименование держателя карты
	EmbossingName *string `json:"embossing_name,omitempty"`
	// маскированный номер карты (поле обновляется после создания карты в ПЦ)
	MaskedPan *string `json:"masked_pan,omitempty"`
	// тип карты
	CardType cards.CardType `json:"card_type,omitempty"`
	// тип карточного продукта
	ProductType cards.ProductType `json:"product_type,omitempty"`
	// платежная система
	PaymentSystem cards.PaymentSystem `json:"payment_system,omitempty"`
	// класс карты
	CardClass cards.CardClass `json:"card_class,omitempty"`
	// статус карты (поле обновляется после создания карты в ПЦ)
	Status cards.Status `json:"status,omitempty"`
	// статус токенизации карты
	TokenizationStatus cards.TokenizationStatus `json:"tokenization_status,omitempty"`
	// кошелек
	Wallet *string `json:"wallet,omitempty"`
	// дата и время создания карты
	CreationDate time.Time `json:"creation_date,omitempty"`
	// срок действия карты (поле обновляется после создания карты в ПЦ)
	ExpireDate *time.Time `json:"expire_date,omitempty"`
	// дата и время последнего изменения данных карты
	ModificationDate time.Time `json:"modification_date,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the CardsQuery when eager-loading is set.
	Edges        CardsEdges `json:"edges"`
	selectValues sql.SelectValues
}

// CardsEdges holds the relations/edges for other nodes in the graph.
type CardsEdges struct {
	// Account holds the value of the account edge.
	Account *Accounts `json:"account,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// AccountOrErr returns the Account value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CardsEdges) AccountOrErr() (*Accounts, error) {
	if e.Account != nil {
		return e.Account, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: accounts.Label}
	}
	return nil, &NotLoadedError{edge: "account"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Cards) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case cards.FieldClientID, cards.FieldAttachedAccountID:
			values[i] = &sql.NullScanner{S: new(uuid.UUID)}
		case cards.FieldEmbossingName, cards.FieldMaskedPan, cards.FieldCardType, cards.FieldProductType, cards.FieldPaymentSystem, cards.FieldCardClass, cards.FieldStatus, cards.FieldTokenizationStatus, cards.FieldWallet:
			values[i] = new(sql.NullString)
		case cards.FieldCreateTime, cards.FieldUpdateTime, cards.FieldCreationDate, cards.FieldExpireDate, cards.FieldModificationDate:
			values[i] = new(sql.NullTime)
		case cards.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Cards fields.
func (_m *Cards) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case cards.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case cards.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case cards.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case cards.FieldClientID:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field client_id", values[i])
			} else if value.Valid {
				_m.ClientID = new(uuid.UUID)
				*_m.ClientID = *value.S.(*uuid.UUID)
			}
		case cards.FieldAttachedAccountID:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field attached_account_id", values[i])
			} else if value.Valid {
				_m.AttachedAccountID = new(uuid.UUID)
				*_m.AttachedAccountID = *value.S.(*uuid.UUID)
			}
		case cards.FieldEmbossingName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field embossing_name", values[i])
			} else if value.Valid {
				_m.EmbossingName = new(string)
				*_m.EmbossingName = value.String
			}
		case cards.FieldMaskedPan:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field masked_pan", values[i])
			} else if value.Valid {
				_m.MaskedPan = new(string)
				*_m.MaskedPan = value.String
			}
		case cards.FieldCardType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field card_type", values[i])
			} else if value.Valid {
				_m.CardType = cards.CardType(value.String)
			}
		case cards.FieldProductType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field product_type", values[i])
			} else if value.Valid {
				_m.ProductType = cards.ProductType(value.String)
			}
		case cards.FieldPaymentSystem:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_system", values[i])
			} else if value.Valid {
				_m.PaymentSystem = cards.PaymentSystem(value.String)
			}
		case cards.FieldCardClass:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field card_class", values[i])
			} else if value.Valid {
				_m.CardClass = cards.CardClass(value.String)
			}
		case cards.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				_m.Status = cards.Status(value.String)
			}
		case cards.FieldTokenizationStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tokenization_status", values[i])
			} else if value.Valid {
				_m.TokenizationStatus = cards.TokenizationStatus(value.String)
			}
		case cards.FieldWallet:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field wallet", values[i])
			} else if value.Valid {
				_m.Wallet = new(string)
				*_m.Wallet = value.String
			}
		case cards.FieldCreationDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field creation_date", values[i])
			} else if value.Valid {
				_m.CreationDate = value.Time
			}
		case cards.FieldExpireDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expire_date", values[i])
			} else if value.Valid {
				_m.ExpireDate = new(time.Time)
				*_m.ExpireDate = value.Time
			}
		case cards.FieldModificationDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field modification_date", values[i])
			} else if value.Valid {
				_m.ModificationDate = value.Time
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Cards.
// This includes values selected through modifiers, order, etc.
func (_m *Cards) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// QueryAccount queries the "account" edge of the Cards entity.
func (_m *Cards) QueryAccount() *AccountsQuery {
	return NewCardsClient(_m.config).QueryAccount(_m)
}

// Update returns a builder for updating this Cards.
// Note that you need to call Cards.Unwrap() before calling this method if this Cards
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Cards) Update() *CardsUpdateOne {
	return NewCardsClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Cards entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Cards) Unwrap() *Cards {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Cards is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Cards) String() string {
	var builder strings.Builder
	builder.WriteString("Cards(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := _m.ClientID; v != nil {
		builder.WriteString("client_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.AttachedAccountID; v != nil {
		builder.WriteString("attached_account_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.EmbossingName; v != nil {
		builder.WriteString("embossing_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.MaskedPan; v != nil {
		builder.WriteString("masked_pan=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("card_type=")
	builder.WriteString(fmt.Sprintf("%v", _m.CardType))
	builder.WriteString(", ")
	builder.WriteString("product_type=")
	builder.WriteString(fmt.Sprintf("%v", _m.ProductType))
	builder.WriteString(", ")
	builder.WriteString("payment_system=")
	builder.WriteString(fmt.Sprintf("%v", _m.PaymentSystem))
	builder.WriteString(", ")
	builder.WriteString("card_class=")
	builder.WriteString(fmt.Sprintf("%v", _m.CardClass))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", _m.Status))
	builder.WriteString(", ")
	builder.WriteString("tokenization_status=")
	builder.WriteString(fmt.Sprintf("%v", _m.TokenizationStatus))
	builder.WriteString(", ")
	if v := _m.Wallet; v != nil {
		builder.WriteString("wallet=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("creation_date=")
	builder.WriteString(_m.CreationDate.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := _m.ExpireDate; v != nil {
		builder.WriteString("expire_date=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("modification_date=")
	builder.WriteString(_m.ModificationDate.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// CardsSlice is a parsable slice of Cards.
type CardsSlice []*Cards
