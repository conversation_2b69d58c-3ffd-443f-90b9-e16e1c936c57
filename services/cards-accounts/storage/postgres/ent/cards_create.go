// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
)

// CardsCreate is the builder for creating a Cards entity.
type CardsCreate struct {
	config
	mutation *CardsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *CardsCreate) SetCreateTime(v time.Time) *CardsCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *CardsCreate) SetNillableCreateTime(v *time.Time) *CardsCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *CardsCreate) SetUpdateTime(v time.Time) *CardsCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *CardsCreate) SetNillableUpdateTime(v *time.Time) *CardsCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetClientID sets the "client_id" field.
func (_c *CardsCreate) SetClientID(v uuid.UUID) *CardsCreate {
	_c.mutation.SetClientID(v)
	return _c
}

// SetNillableClientID sets the "client_id" field if the given value is not nil.
func (_c *CardsCreate) SetNillableClientID(v *uuid.UUID) *CardsCreate {
	if v != nil {
		_c.SetClientID(*v)
	}
	return _c
}

// SetAttachedAccountID sets the "attached_account_id" field.
func (_c *CardsCreate) SetAttachedAccountID(v uuid.UUID) *CardsCreate {
	_c.mutation.SetAttachedAccountID(v)
	return _c
}

// SetNillableAttachedAccountID sets the "attached_account_id" field if the given value is not nil.
func (_c *CardsCreate) SetNillableAttachedAccountID(v *uuid.UUID) *CardsCreate {
	if v != nil {
		_c.SetAttachedAccountID(*v)
	}
	return _c
}

// SetEmbossingName sets the "embossing_name" field.
func (_c *CardsCreate) SetEmbossingName(v string) *CardsCreate {
	_c.mutation.SetEmbossingName(v)
	return _c
}

// SetNillableEmbossingName sets the "embossing_name" field if the given value is not nil.
func (_c *CardsCreate) SetNillableEmbossingName(v *string) *CardsCreate {
	if v != nil {
		_c.SetEmbossingName(*v)
	}
	return _c
}

// SetMaskedPan sets the "masked_pan" field.
func (_c *CardsCreate) SetMaskedPan(v string) *CardsCreate {
	_c.mutation.SetMaskedPan(v)
	return _c
}

// SetNillableMaskedPan sets the "masked_pan" field if the given value is not nil.
func (_c *CardsCreate) SetNillableMaskedPan(v *string) *CardsCreate {
	if v != nil {
		_c.SetMaskedPan(*v)
	}
	return _c
}

// SetCardType sets the "card_type" field.
func (_c *CardsCreate) SetCardType(v cards.CardType) *CardsCreate {
	_c.mutation.SetCardType(v)
	return _c
}

// SetNillableCardType sets the "card_type" field if the given value is not nil.
func (_c *CardsCreate) SetNillableCardType(v *cards.CardType) *CardsCreate {
	if v != nil {
		_c.SetCardType(*v)
	}
	return _c
}

// SetProductType sets the "product_type" field.
func (_c *CardsCreate) SetProductType(v cards.ProductType) *CardsCreate {
	_c.mutation.SetProductType(v)
	return _c
}

// SetNillableProductType sets the "product_type" field if the given value is not nil.
func (_c *CardsCreate) SetNillableProductType(v *cards.ProductType) *CardsCreate {
	if v != nil {
		_c.SetProductType(*v)
	}
	return _c
}

// SetPaymentSystem sets the "payment_system" field.
func (_c *CardsCreate) SetPaymentSystem(v cards.PaymentSystem) *CardsCreate {
	_c.mutation.SetPaymentSystem(v)
	return _c
}

// SetNillablePaymentSystem sets the "payment_system" field if the given value is not nil.
func (_c *CardsCreate) SetNillablePaymentSystem(v *cards.PaymentSystem) *CardsCreate {
	if v != nil {
		_c.SetPaymentSystem(*v)
	}
	return _c
}

// SetCardClass sets the "card_class" field.
func (_c *CardsCreate) SetCardClass(v cards.CardClass) *CardsCreate {
	_c.mutation.SetCardClass(v)
	return _c
}

// SetNillableCardClass sets the "card_class" field if the given value is not nil.
func (_c *CardsCreate) SetNillableCardClass(v *cards.CardClass) *CardsCreate {
	if v != nil {
		_c.SetCardClass(*v)
	}
	return _c
}

// SetStatus sets the "status" field.
func (_c *CardsCreate) SetStatus(v cards.Status) *CardsCreate {
	_c.mutation.SetStatus(v)
	return _c
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_c *CardsCreate) SetNillableStatus(v *cards.Status) *CardsCreate {
	if v != nil {
		_c.SetStatus(*v)
	}
	return _c
}

// SetTokenizationStatus sets the "tokenization_status" field.
func (_c *CardsCreate) SetTokenizationStatus(v cards.TokenizationStatus) *CardsCreate {
	_c.mutation.SetTokenizationStatus(v)
	return _c
}

// SetNillableTokenizationStatus sets the "tokenization_status" field if the given value is not nil.
func (_c *CardsCreate) SetNillableTokenizationStatus(v *cards.TokenizationStatus) *CardsCreate {
	if v != nil {
		_c.SetTokenizationStatus(*v)
	}
	return _c
}

// SetWallet sets the "wallet" field.
func (_c *CardsCreate) SetWallet(v string) *CardsCreate {
	_c.mutation.SetWallet(v)
	return _c
}

// SetNillableWallet sets the "wallet" field if the given value is not nil.
func (_c *CardsCreate) SetNillableWallet(v *string) *CardsCreate {
	if v != nil {
		_c.SetWallet(*v)
	}
	return _c
}

// SetCreationDate sets the "creation_date" field.
func (_c *CardsCreate) SetCreationDate(v time.Time) *CardsCreate {
	_c.mutation.SetCreationDate(v)
	return _c
}

// SetNillableCreationDate sets the "creation_date" field if the given value is not nil.
func (_c *CardsCreate) SetNillableCreationDate(v *time.Time) *CardsCreate {
	if v != nil {
		_c.SetCreationDate(*v)
	}
	return _c
}

// SetExpireDate sets the "expire_date" field.
func (_c *CardsCreate) SetExpireDate(v time.Time) *CardsCreate {
	_c.mutation.SetExpireDate(v)
	return _c
}

// SetNillableExpireDate sets the "expire_date" field if the given value is not nil.
func (_c *CardsCreate) SetNillableExpireDate(v *time.Time) *CardsCreate {
	if v != nil {
		_c.SetExpireDate(*v)
	}
	return _c
}

// SetModificationDate sets the "modification_date" field.
func (_c *CardsCreate) SetModificationDate(v time.Time) *CardsCreate {
	_c.mutation.SetModificationDate(v)
	return _c
}

// SetNillableModificationDate sets the "modification_date" field if the given value is not nil.
func (_c *CardsCreate) SetNillableModificationDate(v *time.Time) *CardsCreate {
	if v != nil {
		_c.SetModificationDate(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *CardsCreate) SetID(v uuid.UUID) *CardsCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *CardsCreate) SetNillableID(v *uuid.UUID) *CardsCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// SetAccountID sets the "account" edge to the Accounts entity by ID.
func (_c *CardsCreate) SetAccountID(id uuid.UUID) *CardsCreate {
	_c.mutation.SetAccountID(id)
	return _c
}

// SetNillableAccountID sets the "account" edge to the Accounts entity by ID if the given value is not nil.
func (_c *CardsCreate) SetNillableAccountID(id *uuid.UUID) *CardsCreate {
	if id != nil {
		_c = _c.SetAccountID(*id)
	}
	return _c
}

// SetAccount sets the "account" edge to the Accounts entity.
func (_c *CardsCreate) SetAccount(v *Accounts) *CardsCreate {
	return _c.SetAccountID(v.ID)
}

// Mutation returns the CardsMutation object of the builder.
func (_c *CardsCreate) Mutation() *CardsMutation {
	return _c.mutation
}

// Save creates the Cards in the database.
func (_c *CardsCreate) Save(ctx context.Context) (*Cards, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *CardsCreate) SaveX(ctx context.Context) *Cards {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *CardsCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *CardsCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *CardsCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := cards.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := cards.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.CardType(); !ok {
		v := cards.DefaultCardType
		_c.mutation.SetCardType(v)
	}
	if _, ok := _c.mutation.ProductType(); !ok {
		v := cards.DefaultProductType
		_c.mutation.SetProductType(v)
	}
	if _, ok := _c.mutation.PaymentSystem(); !ok {
		v := cards.DefaultPaymentSystem
		_c.mutation.SetPaymentSystem(v)
	}
	if _, ok := _c.mutation.CardClass(); !ok {
		v := cards.DefaultCardClass
		_c.mutation.SetCardClass(v)
	}
	if _, ok := _c.mutation.Status(); !ok {
		v := cards.DefaultStatus
		_c.mutation.SetStatus(v)
	}
	if _, ok := _c.mutation.TokenizationStatus(); !ok {
		v := cards.DefaultTokenizationStatus
		_c.mutation.SetTokenizationStatus(v)
	}
	if _, ok := _c.mutation.CreationDate(); !ok {
		v := cards.DefaultCreationDate()
		_c.mutation.SetCreationDate(v)
	}
	if _, ok := _c.mutation.ModificationDate(); !ok {
		v := cards.DefaultModificationDate()
		_c.mutation.SetModificationDate(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := cards.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *CardsCreate) check() error {
	if _, ok := _c.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "Cards.create_time"`)}
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "Cards.update_time"`)}
	}
	if v, ok := _c.mutation.EmbossingName(); ok {
		if err := cards.EmbossingNameValidator(v); err != nil {
			return &ValidationError{Name: "embossing_name", err: fmt.Errorf(`ent: validator failed for field "Cards.embossing_name": %w`, err)}
		}
	}
	if v, ok := _c.mutation.MaskedPan(); ok {
		if err := cards.MaskedPanValidator(v); err != nil {
			return &ValidationError{Name: "masked_pan", err: fmt.Errorf(`ent: validator failed for field "Cards.masked_pan": %w`, err)}
		}
	}
	if _, ok := _c.mutation.CardType(); !ok {
		return &ValidationError{Name: "card_type", err: errors.New(`ent: missing required field "Cards.card_type"`)}
	}
	if v, ok := _c.mutation.CardType(); ok {
		if err := cards.CardTypeValidator(v); err != nil {
			return &ValidationError{Name: "card_type", err: fmt.Errorf(`ent: validator failed for field "Cards.card_type": %w`, err)}
		}
	}
	if _, ok := _c.mutation.ProductType(); !ok {
		return &ValidationError{Name: "product_type", err: errors.New(`ent: missing required field "Cards.product_type"`)}
	}
	if v, ok := _c.mutation.ProductType(); ok {
		if err := cards.ProductTypeValidator(v); err != nil {
			return &ValidationError{Name: "product_type", err: fmt.Errorf(`ent: validator failed for field "Cards.product_type": %w`, err)}
		}
	}
	if _, ok := _c.mutation.PaymentSystem(); !ok {
		return &ValidationError{Name: "payment_system", err: errors.New(`ent: missing required field "Cards.payment_system"`)}
	}
	if v, ok := _c.mutation.PaymentSystem(); ok {
		if err := cards.PaymentSystemValidator(v); err != nil {
			return &ValidationError{Name: "payment_system", err: fmt.Errorf(`ent: validator failed for field "Cards.payment_system": %w`, err)}
		}
	}
	if _, ok := _c.mutation.CardClass(); !ok {
		return &ValidationError{Name: "card_class", err: errors.New(`ent: missing required field "Cards.card_class"`)}
	}
	if v, ok := _c.mutation.CardClass(); ok {
		if err := cards.CardClassValidator(v); err != nil {
			return &ValidationError{Name: "card_class", err: fmt.Errorf(`ent: validator failed for field "Cards.card_class": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Cards.status"`)}
	}
	if v, ok := _c.mutation.Status(); ok {
		if err := cards.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Cards.status": %w`, err)}
		}
	}
	if _, ok := _c.mutation.TokenizationStatus(); !ok {
		return &ValidationError{Name: "tokenization_status", err: errors.New(`ent: missing required field "Cards.tokenization_status"`)}
	}
	if v, ok := _c.mutation.TokenizationStatus(); ok {
		if err := cards.TokenizationStatusValidator(v); err != nil {
			return &ValidationError{Name: "tokenization_status", err: fmt.Errorf(`ent: validator failed for field "Cards.tokenization_status": %w`, err)}
		}
	}
	if _, ok := _c.mutation.CreationDate(); !ok {
		return &ValidationError{Name: "creation_date", err: errors.New(`ent: missing required field "Cards.creation_date"`)}
	}
	if _, ok := _c.mutation.ModificationDate(); !ok {
		return &ValidationError{Name: "modification_date", err: errors.New(`ent: missing required field "Cards.modification_date"`)}
	}
	return nil
}

func (_c *CardsCreate) sqlSave(ctx context.Context) (*Cards, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *CardsCreate) createSpec() (*Cards, *sqlgraph.CreateSpec) {
	var (
		_node = &Cards{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(cards.Table, sqlgraph.NewFieldSpec(cards.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(cards.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(cards.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.ClientID(); ok {
		_spec.SetField(cards.FieldClientID, field.TypeUUID, value)
		_node.ClientID = &value
	}
	if value, ok := _c.mutation.EmbossingName(); ok {
		_spec.SetField(cards.FieldEmbossingName, field.TypeString, value)
		_node.EmbossingName = &value
	}
	if value, ok := _c.mutation.MaskedPan(); ok {
		_spec.SetField(cards.FieldMaskedPan, field.TypeString, value)
		_node.MaskedPan = &value
	}
	if value, ok := _c.mutation.CardType(); ok {
		_spec.SetField(cards.FieldCardType, field.TypeEnum, value)
		_node.CardType = value
	}
	if value, ok := _c.mutation.ProductType(); ok {
		_spec.SetField(cards.FieldProductType, field.TypeEnum, value)
		_node.ProductType = value
	}
	if value, ok := _c.mutation.PaymentSystem(); ok {
		_spec.SetField(cards.FieldPaymentSystem, field.TypeEnum, value)
		_node.PaymentSystem = value
	}
	if value, ok := _c.mutation.CardClass(); ok {
		_spec.SetField(cards.FieldCardClass, field.TypeEnum, value)
		_node.CardClass = value
	}
	if value, ok := _c.mutation.Status(); ok {
		_spec.SetField(cards.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := _c.mutation.TokenizationStatus(); ok {
		_spec.SetField(cards.FieldTokenizationStatus, field.TypeEnum, value)
		_node.TokenizationStatus = value
	}
	if value, ok := _c.mutation.Wallet(); ok {
		_spec.SetField(cards.FieldWallet, field.TypeString, value)
		_node.Wallet = &value
	}
	if value, ok := _c.mutation.CreationDate(); ok {
		_spec.SetField(cards.FieldCreationDate, field.TypeTime, value)
		_node.CreationDate = value
	}
	if value, ok := _c.mutation.ExpireDate(); ok {
		_spec.SetField(cards.FieldExpireDate, field.TypeTime, value)
		_node.ExpireDate = &value
	}
	if value, ok := _c.mutation.ModificationDate(); ok {
		_spec.SetField(cards.FieldModificationDate, field.TypeTime, value)
		_node.ModificationDate = value
	}
	if nodes := _c.mutation.AccountIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cards.AccountTable,
			Columns: []string{cards.AccountColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.AttachedAccountID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Cards.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.CardsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *CardsCreate) OnConflict(opts ...sql.ConflictOption) *CardsUpsertOne {
	_c.conflict = opts
	return &CardsUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Cards.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *CardsCreate) OnConflictColumns(columns ...string) *CardsUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &CardsUpsertOne{
		create: _c,
	}
}

type (
	// CardsUpsertOne is the builder for "upsert"-ing
	//  one Cards node.
	CardsUpsertOne struct {
		create *CardsCreate
	}

	// CardsUpsert is the "OnConflict" setter.
	CardsUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdateTime sets the "update_time" field.
func (u *CardsUpsert) SetUpdateTime(v time.Time) *CardsUpsert {
	u.Set(cards.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *CardsUpsert) UpdateUpdateTime() *CardsUpsert {
	u.SetExcluded(cards.FieldUpdateTime)
	return u
}

// SetClientID sets the "client_id" field.
func (u *CardsUpsert) SetClientID(v uuid.UUID) *CardsUpsert {
	u.Set(cards.FieldClientID, v)
	return u
}

// UpdateClientID sets the "client_id" field to the value that was provided on create.
func (u *CardsUpsert) UpdateClientID() *CardsUpsert {
	u.SetExcluded(cards.FieldClientID)
	return u
}

// ClearClientID clears the value of the "client_id" field.
func (u *CardsUpsert) ClearClientID() *CardsUpsert {
	u.SetNull(cards.FieldClientID)
	return u
}

// SetAttachedAccountID sets the "attached_account_id" field.
func (u *CardsUpsert) SetAttachedAccountID(v uuid.UUID) *CardsUpsert {
	u.Set(cards.FieldAttachedAccountID, v)
	return u
}

// UpdateAttachedAccountID sets the "attached_account_id" field to the value that was provided on create.
func (u *CardsUpsert) UpdateAttachedAccountID() *CardsUpsert {
	u.SetExcluded(cards.FieldAttachedAccountID)
	return u
}

// ClearAttachedAccountID clears the value of the "attached_account_id" field.
func (u *CardsUpsert) ClearAttachedAccountID() *CardsUpsert {
	u.SetNull(cards.FieldAttachedAccountID)
	return u
}

// SetEmbossingName sets the "embossing_name" field.
func (u *CardsUpsert) SetEmbossingName(v string) *CardsUpsert {
	u.Set(cards.FieldEmbossingName, v)
	return u
}

// UpdateEmbossingName sets the "embossing_name" field to the value that was provided on create.
func (u *CardsUpsert) UpdateEmbossingName() *CardsUpsert {
	u.SetExcluded(cards.FieldEmbossingName)
	return u
}

// ClearEmbossingName clears the value of the "embossing_name" field.
func (u *CardsUpsert) ClearEmbossingName() *CardsUpsert {
	u.SetNull(cards.FieldEmbossingName)
	return u
}

// SetMaskedPan sets the "masked_pan" field.
func (u *CardsUpsert) SetMaskedPan(v string) *CardsUpsert {
	u.Set(cards.FieldMaskedPan, v)
	return u
}

// UpdateMaskedPan sets the "masked_pan" field to the value that was provided on create.
func (u *CardsUpsert) UpdateMaskedPan() *CardsUpsert {
	u.SetExcluded(cards.FieldMaskedPan)
	return u
}

// ClearMaskedPan clears the value of the "masked_pan" field.
func (u *CardsUpsert) ClearMaskedPan() *CardsUpsert {
	u.SetNull(cards.FieldMaskedPan)
	return u
}

// SetCardType sets the "card_type" field.
func (u *CardsUpsert) SetCardType(v cards.CardType) *CardsUpsert {
	u.Set(cards.FieldCardType, v)
	return u
}

// UpdateCardType sets the "card_type" field to the value that was provided on create.
func (u *CardsUpsert) UpdateCardType() *CardsUpsert {
	u.SetExcluded(cards.FieldCardType)
	return u
}

// SetProductType sets the "product_type" field.
func (u *CardsUpsert) SetProductType(v cards.ProductType) *CardsUpsert {
	u.Set(cards.FieldProductType, v)
	return u
}

// UpdateProductType sets the "product_type" field to the value that was provided on create.
func (u *CardsUpsert) UpdateProductType() *CardsUpsert {
	u.SetExcluded(cards.FieldProductType)
	return u
}

// SetPaymentSystem sets the "payment_system" field.
func (u *CardsUpsert) SetPaymentSystem(v cards.PaymentSystem) *CardsUpsert {
	u.Set(cards.FieldPaymentSystem, v)
	return u
}

// UpdatePaymentSystem sets the "payment_system" field to the value that was provided on create.
func (u *CardsUpsert) UpdatePaymentSystem() *CardsUpsert {
	u.SetExcluded(cards.FieldPaymentSystem)
	return u
}

// SetCardClass sets the "card_class" field.
func (u *CardsUpsert) SetCardClass(v cards.CardClass) *CardsUpsert {
	u.Set(cards.FieldCardClass, v)
	return u
}

// UpdateCardClass sets the "card_class" field to the value that was provided on create.
func (u *CardsUpsert) UpdateCardClass() *CardsUpsert {
	u.SetExcluded(cards.FieldCardClass)
	return u
}

// SetStatus sets the "status" field.
func (u *CardsUpsert) SetStatus(v cards.Status) *CardsUpsert {
	u.Set(cards.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *CardsUpsert) UpdateStatus() *CardsUpsert {
	u.SetExcluded(cards.FieldStatus)
	return u
}

// SetTokenizationStatus sets the "tokenization_status" field.
func (u *CardsUpsert) SetTokenizationStatus(v cards.TokenizationStatus) *CardsUpsert {
	u.Set(cards.FieldTokenizationStatus, v)
	return u
}

// UpdateTokenizationStatus sets the "tokenization_status" field to the value that was provided on create.
func (u *CardsUpsert) UpdateTokenizationStatus() *CardsUpsert {
	u.SetExcluded(cards.FieldTokenizationStatus)
	return u
}

// SetWallet sets the "wallet" field.
func (u *CardsUpsert) SetWallet(v string) *CardsUpsert {
	u.Set(cards.FieldWallet, v)
	return u
}

// UpdateWallet sets the "wallet" field to the value that was provided on create.
func (u *CardsUpsert) UpdateWallet() *CardsUpsert {
	u.SetExcluded(cards.FieldWallet)
	return u
}

// ClearWallet clears the value of the "wallet" field.
func (u *CardsUpsert) ClearWallet() *CardsUpsert {
	u.SetNull(cards.FieldWallet)
	return u
}

// SetExpireDate sets the "expire_date" field.
func (u *CardsUpsert) SetExpireDate(v time.Time) *CardsUpsert {
	u.Set(cards.FieldExpireDate, v)
	return u
}

// UpdateExpireDate sets the "expire_date" field to the value that was provided on create.
func (u *CardsUpsert) UpdateExpireDate() *CardsUpsert {
	u.SetExcluded(cards.FieldExpireDate)
	return u
}

// ClearExpireDate clears the value of the "expire_date" field.
func (u *CardsUpsert) ClearExpireDate() *CardsUpsert {
	u.SetNull(cards.FieldExpireDate)
	return u
}

// SetModificationDate sets the "modification_date" field.
func (u *CardsUpsert) SetModificationDate(v time.Time) *CardsUpsert {
	u.Set(cards.FieldModificationDate, v)
	return u
}

// UpdateModificationDate sets the "modification_date" field to the value that was provided on create.
func (u *CardsUpsert) UpdateModificationDate() *CardsUpsert {
	u.SetExcluded(cards.FieldModificationDate)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Cards.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(cards.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *CardsUpsertOne) UpdateNewValues() *CardsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(cards.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(cards.FieldCreateTime)
		}
		if _, exists := u.create.mutation.CreationDate(); exists {
			s.SetIgnore(cards.FieldCreationDate)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Cards.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *CardsUpsertOne) Ignore() *CardsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *CardsUpsertOne) DoNothing() *CardsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the CardsCreate.OnConflict
// documentation for more info.
func (u *CardsUpsertOne) Update(set func(*CardsUpsert)) *CardsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&CardsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *CardsUpsertOne) SetUpdateTime(v time.Time) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateUpdateTime() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateUpdateTime()
	})
}

// SetClientID sets the "client_id" field.
func (u *CardsUpsertOne) SetClientID(v uuid.UUID) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetClientID(v)
	})
}

// UpdateClientID sets the "client_id" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateClientID() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateClientID()
	})
}

// ClearClientID clears the value of the "client_id" field.
func (u *CardsUpsertOne) ClearClientID() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.ClearClientID()
	})
}

// SetAttachedAccountID sets the "attached_account_id" field.
func (u *CardsUpsertOne) SetAttachedAccountID(v uuid.UUID) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetAttachedAccountID(v)
	})
}

// UpdateAttachedAccountID sets the "attached_account_id" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateAttachedAccountID() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateAttachedAccountID()
	})
}

// ClearAttachedAccountID clears the value of the "attached_account_id" field.
func (u *CardsUpsertOne) ClearAttachedAccountID() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.ClearAttachedAccountID()
	})
}

// SetEmbossingName sets the "embossing_name" field.
func (u *CardsUpsertOne) SetEmbossingName(v string) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetEmbossingName(v)
	})
}

// UpdateEmbossingName sets the "embossing_name" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateEmbossingName() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateEmbossingName()
	})
}

// ClearEmbossingName clears the value of the "embossing_name" field.
func (u *CardsUpsertOne) ClearEmbossingName() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.ClearEmbossingName()
	})
}

// SetMaskedPan sets the "masked_pan" field.
func (u *CardsUpsertOne) SetMaskedPan(v string) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetMaskedPan(v)
	})
}

// UpdateMaskedPan sets the "masked_pan" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateMaskedPan() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateMaskedPan()
	})
}

// ClearMaskedPan clears the value of the "masked_pan" field.
func (u *CardsUpsertOne) ClearMaskedPan() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.ClearMaskedPan()
	})
}

// SetCardType sets the "card_type" field.
func (u *CardsUpsertOne) SetCardType(v cards.CardType) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetCardType(v)
	})
}

// UpdateCardType sets the "card_type" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateCardType() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateCardType()
	})
}

// SetProductType sets the "product_type" field.
func (u *CardsUpsertOne) SetProductType(v cards.ProductType) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetProductType(v)
	})
}

// UpdateProductType sets the "product_type" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateProductType() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateProductType()
	})
}

// SetPaymentSystem sets the "payment_system" field.
func (u *CardsUpsertOne) SetPaymentSystem(v cards.PaymentSystem) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetPaymentSystem(v)
	})
}

// UpdatePaymentSystem sets the "payment_system" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdatePaymentSystem() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdatePaymentSystem()
	})
}

// SetCardClass sets the "card_class" field.
func (u *CardsUpsertOne) SetCardClass(v cards.CardClass) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetCardClass(v)
	})
}

// UpdateCardClass sets the "card_class" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateCardClass() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateCardClass()
	})
}

// SetStatus sets the "status" field.
func (u *CardsUpsertOne) SetStatus(v cards.Status) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateStatus() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateStatus()
	})
}

// SetTokenizationStatus sets the "tokenization_status" field.
func (u *CardsUpsertOne) SetTokenizationStatus(v cards.TokenizationStatus) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetTokenizationStatus(v)
	})
}

// UpdateTokenizationStatus sets the "tokenization_status" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateTokenizationStatus() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateTokenizationStatus()
	})
}

// SetWallet sets the "wallet" field.
func (u *CardsUpsertOne) SetWallet(v string) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetWallet(v)
	})
}

// UpdateWallet sets the "wallet" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateWallet() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateWallet()
	})
}

// ClearWallet clears the value of the "wallet" field.
func (u *CardsUpsertOne) ClearWallet() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.ClearWallet()
	})
}

// SetExpireDate sets the "expire_date" field.
func (u *CardsUpsertOne) SetExpireDate(v time.Time) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetExpireDate(v)
	})
}

// UpdateExpireDate sets the "expire_date" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateExpireDate() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateExpireDate()
	})
}

// ClearExpireDate clears the value of the "expire_date" field.
func (u *CardsUpsertOne) ClearExpireDate() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.ClearExpireDate()
	})
}

// SetModificationDate sets the "modification_date" field.
func (u *CardsUpsertOne) SetModificationDate(v time.Time) *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.SetModificationDate(v)
	})
}

// UpdateModificationDate sets the "modification_date" field to the value that was provided on create.
func (u *CardsUpsertOne) UpdateModificationDate() *CardsUpsertOne {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateModificationDate()
	})
}

// Exec executes the query.
func (u *CardsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for CardsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *CardsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *CardsUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: CardsUpsertOne.ID is not supported by MySQL driver. Use CardsUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *CardsUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// CardsCreateBulk is the builder for creating many Cards entities in bulk.
type CardsCreateBulk struct {
	config
	err      error
	builders []*CardsCreate
	conflict []sql.ConflictOption
}

// Save creates the Cards entities in the database.
func (_c *CardsCreateBulk) Save(ctx context.Context) ([]*Cards, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Cards, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CardsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *CardsCreateBulk) SaveX(ctx context.Context) []*Cards {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *CardsCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *CardsCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Cards.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.CardsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *CardsCreateBulk) OnConflict(opts ...sql.ConflictOption) *CardsUpsertBulk {
	_c.conflict = opts
	return &CardsUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Cards.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *CardsCreateBulk) OnConflictColumns(columns ...string) *CardsUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &CardsUpsertBulk{
		create: _c,
	}
}

// CardsUpsertBulk is the builder for "upsert"-ing
// a bulk of Cards nodes.
type CardsUpsertBulk struct {
	create *CardsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Cards.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(cards.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *CardsUpsertBulk) UpdateNewValues() *CardsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(cards.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(cards.FieldCreateTime)
			}
			if _, exists := b.mutation.CreationDate(); exists {
				s.SetIgnore(cards.FieldCreationDate)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Cards.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *CardsUpsertBulk) Ignore() *CardsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *CardsUpsertBulk) DoNothing() *CardsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the CardsCreateBulk.OnConflict
// documentation for more info.
func (u *CardsUpsertBulk) Update(set func(*CardsUpsert)) *CardsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&CardsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *CardsUpsertBulk) SetUpdateTime(v time.Time) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateUpdateTime() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateUpdateTime()
	})
}

// SetClientID sets the "client_id" field.
func (u *CardsUpsertBulk) SetClientID(v uuid.UUID) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetClientID(v)
	})
}

// UpdateClientID sets the "client_id" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateClientID() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateClientID()
	})
}

// ClearClientID clears the value of the "client_id" field.
func (u *CardsUpsertBulk) ClearClientID() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.ClearClientID()
	})
}

// SetAttachedAccountID sets the "attached_account_id" field.
func (u *CardsUpsertBulk) SetAttachedAccountID(v uuid.UUID) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetAttachedAccountID(v)
	})
}

// UpdateAttachedAccountID sets the "attached_account_id" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateAttachedAccountID() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateAttachedAccountID()
	})
}

// ClearAttachedAccountID clears the value of the "attached_account_id" field.
func (u *CardsUpsertBulk) ClearAttachedAccountID() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.ClearAttachedAccountID()
	})
}

// SetEmbossingName sets the "embossing_name" field.
func (u *CardsUpsertBulk) SetEmbossingName(v string) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetEmbossingName(v)
	})
}

// UpdateEmbossingName sets the "embossing_name" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateEmbossingName() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateEmbossingName()
	})
}

// ClearEmbossingName clears the value of the "embossing_name" field.
func (u *CardsUpsertBulk) ClearEmbossingName() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.ClearEmbossingName()
	})
}

// SetMaskedPan sets the "masked_pan" field.
func (u *CardsUpsertBulk) SetMaskedPan(v string) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetMaskedPan(v)
	})
}

// UpdateMaskedPan sets the "masked_pan" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateMaskedPan() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateMaskedPan()
	})
}

// ClearMaskedPan clears the value of the "masked_pan" field.
func (u *CardsUpsertBulk) ClearMaskedPan() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.ClearMaskedPan()
	})
}

// SetCardType sets the "card_type" field.
func (u *CardsUpsertBulk) SetCardType(v cards.CardType) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetCardType(v)
	})
}

// UpdateCardType sets the "card_type" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateCardType() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateCardType()
	})
}

// SetProductType sets the "product_type" field.
func (u *CardsUpsertBulk) SetProductType(v cards.ProductType) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetProductType(v)
	})
}

// UpdateProductType sets the "product_type" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateProductType() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateProductType()
	})
}

// SetPaymentSystem sets the "payment_system" field.
func (u *CardsUpsertBulk) SetPaymentSystem(v cards.PaymentSystem) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetPaymentSystem(v)
	})
}

// UpdatePaymentSystem sets the "payment_system" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdatePaymentSystem() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdatePaymentSystem()
	})
}

// SetCardClass sets the "card_class" field.
func (u *CardsUpsertBulk) SetCardClass(v cards.CardClass) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetCardClass(v)
	})
}

// UpdateCardClass sets the "card_class" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateCardClass() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateCardClass()
	})
}

// SetStatus sets the "status" field.
func (u *CardsUpsertBulk) SetStatus(v cards.Status) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateStatus() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateStatus()
	})
}

// SetTokenizationStatus sets the "tokenization_status" field.
func (u *CardsUpsertBulk) SetTokenizationStatus(v cards.TokenizationStatus) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetTokenizationStatus(v)
	})
}

// UpdateTokenizationStatus sets the "tokenization_status" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateTokenizationStatus() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateTokenizationStatus()
	})
}

// SetWallet sets the "wallet" field.
func (u *CardsUpsertBulk) SetWallet(v string) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetWallet(v)
	})
}

// UpdateWallet sets the "wallet" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateWallet() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateWallet()
	})
}

// ClearWallet clears the value of the "wallet" field.
func (u *CardsUpsertBulk) ClearWallet() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.ClearWallet()
	})
}

// SetExpireDate sets the "expire_date" field.
func (u *CardsUpsertBulk) SetExpireDate(v time.Time) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetExpireDate(v)
	})
}

// UpdateExpireDate sets the "expire_date" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateExpireDate() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateExpireDate()
	})
}

// ClearExpireDate clears the value of the "expire_date" field.
func (u *CardsUpsertBulk) ClearExpireDate() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.ClearExpireDate()
	})
}

// SetModificationDate sets the "modification_date" field.
func (u *CardsUpsertBulk) SetModificationDate(v time.Time) *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.SetModificationDate(v)
	})
}

// UpdateModificationDate sets the "modification_date" field to the value that was provided on create.
func (u *CardsUpsertBulk) UpdateModificationDate() *CardsUpsertBulk {
	return u.Update(func(s *CardsUpsert) {
		s.UpdateModificationDate()
	})
}

// Exec executes the query.
func (u *CardsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the CardsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for CardsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *CardsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
