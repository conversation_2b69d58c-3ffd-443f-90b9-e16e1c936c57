// Code generated by ent, DO NOT EDIT.

package accounts

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the accounts type in the database.
	Label = "accounts"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldClientIin holds the string denoting the client_iin field in the database.
	FieldClientIin = "client_iin"
	// FieldClientCode holds the string denoting the client_code field in the database.
	FieldClientCode = "client_code"
	// FieldClientName holds the string denoting the client_name field in the database.
	FieldClientName = "client_name"
	// FieldTitle holds the string denoting the title field in the database.
	FieldTitle = "title"
	// FieldShortName holds the string denoting the short_name field in the database.
	FieldShortName = "short_name"
	// FieldCurrency holds the string denoting the currency field in the database.
	FieldCurrency = "currency"
	// FieldIban holds the string denoting the iban field in the database.
	FieldIban = "iban"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldDateOpened holds the string denoting the date_opened field in the database.
	FieldDateOpened = "date_opened"
	// FieldBalance holds the string denoting the balance field in the database.
	FieldBalance = "balance"
	// FieldBalanceNatival holds the string denoting the balance_natival field in the database.
	FieldBalanceNatival = "balance_natival"
	// FieldBlockedBalance holds the string denoting the blocked_balance field in the database.
	FieldBlockedBalance = "blocked_balance"
	// FieldAvailableBalance holds the string denoting the available_balance field in the database.
	FieldAvailableBalance = "available_balance"
	// FieldDateClosed holds the string denoting the date_closed field in the database.
	FieldDateClosed = "date_closed"
	// FieldArrestBlocking holds the string denoting the arrest_blocking field in the database.
	FieldArrestBlocking = "arrest_blocking"
	// FieldArrestDebtAmount holds the string denoting the arrest_debt_amount field in the database.
	FieldArrestDebtAmount = "arrest_debt_amount"
	// FieldHasArrest holds the string denoting the has_arrest field in the database.
	FieldHasArrest = "has_arrest"
	// FieldOrigin holds the string denoting the origin field in the database.
	FieldOrigin = "origin"
	// FieldDeaReferenceID holds the string denoting the dea_reference_id field in the database.
	FieldDeaReferenceID = "dea_reference_id"
	// FieldClientType holds the string denoting the client_type field in the database.
	FieldClientType = "client_type"
	// FieldIsMain holds the string denoting the is_main field in the database.
	FieldIsMain = "is_main"
	// FieldAccessionAccount holds the string denoting the accession_account field in the database.
	FieldAccessionAccount = "accession_account"
	// FieldIsArrested holds the string denoting the is_arrested field in the database.
	FieldIsArrested = "is_arrested"
	// Table holds the table name of the accounts in the database.
	Table = "accounts"
)

// Columns holds all SQL columns for accounts fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldClientIin,
	FieldClientCode,
	FieldClientName,
	FieldTitle,
	FieldShortName,
	FieldCurrency,
	FieldIban,
	FieldType,
	FieldStatus,
	FieldDateOpened,
	FieldBalance,
	FieldBalanceNatival,
	FieldBlockedBalance,
	FieldAvailableBalance,
	FieldDateClosed,
	FieldArrestBlocking,
	FieldArrestDebtAmount,
	FieldOrigin,
	FieldDeaReferenceID,
	FieldClientType,
	FieldIsMain,
	FieldAccessionAccount,
	FieldIsArrested,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	for _, f := range [...]string{FieldHasArrest} {
		if column == f {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// DefaultDeaReferenceID holds the default value on creation for the "dea_reference_id" field.
	DefaultDeaReferenceID string
	// DefaultClientType holds the default value on creation for the "client_type" field.
	DefaultClientType string
	// DefaultIsMain holds the default value on creation for the "is_main" field.
	DefaultIsMain bool
	// DefaultAccessionAccount holds the default value on creation for the "accession_account" field.
	DefaultAccessionAccount bool
	// DefaultIsArrested holds the default value on creation for the "is_arrested" field.
	DefaultIsArrested bool
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the Accounts queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByClientIin orders the results by the client_iin field.
func ByClientIin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldClientIin, opts...).ToFunc()
}

// ByClientCode orders the results by the client_code field.
func ByClientCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldClientCode, opts...).ToFunc()
}

// ByClientName orders the results by the client_name field.
func ByClientName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldClientName, opts...).ToFunc()
}

// ByTitle orders the results by the title field.
func ByTitle(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTitle, opts...).ToFunc()
}

// ByShortName orders the results by the short_name field.
func ByShortName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldShortName, opts...).ToFunc()
}

// ByCurrency orders the results by the currency field.
func ByCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCurrency, opts...).ToFunc()
}

// ByIban orders the results by the iban field.
func ByIban(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIban, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByDateOpened orders the results by the date_opened field.
func ByDateOpened(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDateOpened, opts...).ToFunc()
}

// ByBalance orders the results by the balance field.
func ByBalance(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBalance, opts...).ToFunc()
}

// ByBalanceNatival orders the results by the balance_natival field.
func ByBalanceNatival(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBalanceNatival, opts...).ToFunc()
}

// ByBlockedBalance orders the results by the blocked_balance field.
func ByBlockedBalance(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBlockedBalance, opts...).ToFunc()
}

// ByAvailableBalance orders the results by the available_balance field.
func ByAvailableBalance(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAvailableBalance, opts...).ToFunc()
}

// ByDateClosed orders the results by the date_closed field.
func ByDateClosed(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDateClosed, opts...).ToFunc()
}

// ByArrestBlocking orders the results by the arrest_blocking field.
func ByArrestBlocking(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldArrestBlocking, opts...).ToFunc()
}

// ByArrestDebtAmount orders the results by the arrest_debt_amount field.
func ByArrestDebtAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldArrestDebtAmount, opts...).ToFunc()
}

// ByHasArrest orders the results by the has_arrest field.
func ByHasArrest(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHasArrest, opts...).ToFunc()
}

// ByOrigin orders the results by the origin field.
func ByOrigin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrigin, opts...).ToFunc()
}

// ByDeaReferenceID orders the results by the dea_reference_id field.
func ByDeaReferenceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeaReferenceID, opts...).ToFunc()
}

// ByClientType orders the results by the client_type field.
func ByClientType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldClientType, opts...).ToFunc()
}

// ByIsMain orders the results by the is_main field.
func ByIsMain(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsMain, opts...).ToFunc()
}

// ByAccessionAccount orders the results by the accession_account field.
func ByAccessionAccount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccessionAccount, opts...).ToFunc()
}

// ByIsArrested orders the results by the is_arrested field.
func ByIsArrested(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsArrested, opts...).ToFunc()
}
