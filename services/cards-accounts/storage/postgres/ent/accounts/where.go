// Code generated by ent, DO NOT EDIT.

package accounts

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldUpdateTime, v))
}

// ClientIin applies equality check predicate on the "client_iin" field. It's identical to ClientIinEQ.
func ClientIin(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldClientIin, v))
}

// ClientCode applies equality check predicate on the "client_code" field. It's identical to ClientCodeEQ.
func ClientCode(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldClientCode, v))
}

// ClientName applies equality check predicate on the "client_name" field. It's identical to ClientNameEQ.
func ClientName(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldClientName, v))
}

// Title applies equality check predicate on the "title" field. It's identical to TitleEQ.
func Title(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldTitle, v))
}

// ShortName applies equality check predicate on the "short_name" field. It's identical to ShortNameEQ.
func ShortName(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldShortName, v))
}

// Currency applies equality check predicate on the "currency" field. It's identical to CurrencyEQ.
func Currency(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldCurrency, v))
}

// Iban applies equality check predicate on the "iban" field. It's identical to IbanEQ.
func Iban(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldIban, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldType, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldStatus, v))
}

// DateOpened applies equality check predicate on the "date_opened" field. It's identical to DateOpenedEQ.
func DateOpened(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldDateOpened, v))
}

// Balance applies equality check predicate on the "balance" field. It's identical to BalanceEQ.
func Balance(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldBalance, v))
}

// BalanceNatival applies equality check predicate on the "balance_natival" field. It's identical to BalanceNativalEQ.
func BalanceNatival(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldBalanceNatival, v))
}

// BlockedBalance applies equality check predicate on the "blocked_balance" field. It's identical to BlockedBalanceEQ.
func BlockedBalance(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldBlockedBalance, v))
}

// AvailableBalance applies equality check predicate on the "available_balance" field. It's identical to AvailableBalanceEQ.
func AvailableBalance(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldAvailableBalance, v))
}

// DateClosed applies equality check predicate on the "date_closed" field. It's identical to DateClosedEQ.
func DateClosed(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldDateClosed, v))
}

// ArrestBlocking applies equality check predicate on the "arrest_blocking" field. It's identical to ArrestBlockingEQ.
func ArrestBlocking(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldArrestBlocking, v))
}

// ArrestDebtAmount applies equality check predicate on the "arrest_debt_amount" field. It's identical to ArrestDebtAmountEQ.
func ArrestDebtAmount(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldArrestDebtAmount, v))
}

// HasArrest applies equality check predicate on the "has_arrest" field. It's identical to HasArrestEQ.
func HasArrest(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldHasArrest, v))
}

// Origin applies equality check predicate on the "origin" field. It's identical to OriginEQ.
func Origin(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldOrigin, v))
}

// DeaReferenceID applies equality check predicate on the "dea_reference_id" field. It's identical to DeaReferenceIDEQ.
func DeaReferenceID(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldDeaReferenceID, v))
}

// ClientType applies equality check predicate on the "client_type" field. It's identical to ClientTypeEQ.
func ClientType(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldClientType, v))
}

// IsMain applies equality check predicate on the "is_main" field. It's identical to IsMainEQ.
func IsMain(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldIsMain, v))
}

// AccessionAccount applies equality check predicate on the "accession_account" field. It's identical to AccessionAccountEQ.
func AccessionAccount(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldAccessionAccount, v))
}

// IsArrested applies equality check predicate on the "is_arrested" field. It's identical to IsArrestedEQ.
func IsArrested(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldIsArrested, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldUpdateTime, v))
}

// ClientIinEQ applies the EQ predicate on the "client_iin" field.
func ClientIinEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldClientIin, v))
}

// ClientIinNEQ applies the NEQ predicate on the "client_iin" field.
func ClientIinNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldClientIin, v))
}

// ClientIinIn applies the In predicate on the "client_iin" field.
func ClientIinIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldClientIin, vs...))
}

// ClientIinNotIn applies the NotIn predicate on the "client_iin" field.
func ClientIinNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldClientIin, vs...))
}

// ClientIinGT applies the GT predicate on the "client_iin" field.
func ClientIinGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldClientIin, v))
}

// ClientIinGTE applies the GTE predicate on the "client_iin" field.
func ClientIinGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldClientIin, v))
}

// ClientIinLT applies the LT predicate on the "client_iin" field.
func ClientIinLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldClientIin, v))
}

// ClientIinLTE applies the LTE predicate on the "client_iin" field.
func ClientIinLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldClientIin, v))
}

// ClientIinContains applies the Contains predicate on the "client_iin" field.
func ClientIinContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldClientIin, v))
}

// ClientIinHasPrefix applies the HasPrefix predicate on the "client_iin" field.
func ClientIinHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldClientIin, v))
}

// ClientIinHasSuffix applies the HasSuffix predicate on the "client_iin" field.
func ClientIinHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldClientIin, v))
}

// ClientIinEqualFold applies the EqualFold predicate on the "client_iin" field.
func ClientIinEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldClientIin, v))
}

// ClientIinContainsFold applies the ContainsFold predicate on the "client_iin" field.
func ClientIinContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldClientIin, v))
}

// ClientCodeEQ applies the EQ predicate on the "client_code" field.
func ClientCodeEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldClientCode, v))
}

// ClientCodeNEQ applies the NEQ predicate on the "client_code" field.
func ClientCodeNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldClientCode, v))
}

// ClientCodeIn applies the In predicate on the "client_code" field.
func ClientCodeIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldClientCode, vs...))
}

// ClientCodeNotIn applies the NotIn predicate on the "client_code" field.
func ClientCodeNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldClientCode, vs...))
}

// ClientCodeGT applies the GT predicate on the "client_code" field.
func ClientCodeGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldClientCode, v))
}

// ClientCodeGTE applies the GTE predicate on the "client_code" field.
func ClientCodeGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldClientCode, v))
}

// ClientCodeLT applies the LT predicate on the "client_code" field.
func ClientCodeLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldClientCode, v))
}

// ClientCodeLTE applies the LTE predicate on the "client_code" field.
func ClientCodeLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldClientCode, v))
}

// ClientCodeContains applies the Contains predicate on the "client_code" field.
func ClientCodeContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldClientCode, v))
}

// ClientCodeHasPrefix applies the HasPrefix predicate on the "client_code" field.
func ClientCodeHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldClientCode, v))
}

// ClientCodeHasSuffix applies the HasSuffix predicate on the "client_code" field.
func ClientCodeHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldClientCode, v))
}

// ClientCodeEqualFold applies the EqualFold predicate on the "client_code" field.
func ClientCodeEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldClientCode, v))
}

// ClientCodeContainsFold applies the ContainsFold predicate on the "client_code" field.
func ClientCodeContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldClientCode, v))
}

// ClientNameEQ applies the EQ predicate on the "client_name" field.
func ClientNameEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldClientName, v))
}

// ClientNameNEQ applies the NEQ predicate on the "client_name" field.
func ClientNameNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldClientName, v))
}

// ClientNameIn applies the In predicate on the "client_name" field.
func ClientNameIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldClientName, vs...))
}

// ClientNameNotIn applies the NotIn predicate on the "client_name" field.
func ClientNameNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldClientName, vs...))
}

// ClientNameGT applies the GT predicate on the "client_name" field.
func ClientNameGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldClientName, v))
}

// ClientNameGTE applies the GTE predicate on the "client_name" field.
func ClientNameGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldClientName, v))
}

// ClientNameLT applies the LT predicate on the "client_name" field.
func ClientNameLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldClientName, v))
}

// ClientNameLTE applies the LTE predicate on the "client_name" field.
func ClientNameLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldClientName, v))
}

// ClientNameContains applies the Contains predicate on the "client_name" field.
func ClientNameContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldClientName, v))
}

// ClientNameHasPrefix applies the HasPrefix predicate on the "client_name" field.
func ClientNameHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldClientName, v))
}

// ClientNameHasSuffix applies the HasSuffix predicate on the "client_name" field.
func ClientNameHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldClientName, v))
}

// ClientNameEqualFold applies the EqualFold predicate on the "client_name" field.
func ClientNameEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldClientName, v))
}

// ClientNameContainsFold applies the ContainsFold predicate on the "client_name" field.
func ClientNameContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldClientName, v))
}

// TitleEQ applies the EQ predicate on the "title" field.
func TitleEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldTitle, v))
}

// TitleNEQ applies the NEQ predicate on the "title" field.
func TitleNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldTitle, v))
}

// TitleIn applies the In predicate on the "title" field.
func TitleIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldTitle, vs...))
}

// TitleNotIn applies the NotIn predicate on the "title" field.
func TitleNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldTitle, vs...))
}

// TitleGT applies the GT predicate on the "title" field.
func TitleGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldTitle, v))
}

// TitleGTE applies the GTE predicate on the "title" field.
func TitleGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldTitle, v))
}

// TitleLT applies the LT predicate on the "title" field.
func TitleLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldTitle, v))
}

// TitleLTE applies the LTE predicate on the "title" field.
func TitleLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldTitle, v))
}

// TitleContains applies the Contains predicate on the "title" field.
func TitleContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldTitle, v))
}

// TitleHasPrefix applies the HasPrefix predicate on the "title" field.
func TitleHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldTitle, v))
}

// TitleHasSuffix applies the HasSuffix predicate on the "title" field.
func TitleHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldTitle, v))
}

// TitleIsNil applies the IsNil predicate on the "title" field.
func TitleIsNil() predicate.Accounts {
	return predicate.Accounts(sql.FieldIsNull(FieldTitle))
}

// TitleNotNil applies the NotNil predicate on the "title" field.
func TitleNotNil() predicate.Accounts {
	return predicate.Accounts(sql.FieldNotNull(FieldTitle))
}

// TitleEqualFold applies the EqualFold predicate on the "title" field.
func TitleEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldTitle, v))
}

// TitleContainsFold applies the ContainsFold predicate on the "title" field.
func TitleContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldTitle, v))
}

// ShortNameEQ applies the EQ predicate on the "short_name" field.
func ShortNameEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldShortName, v))
}

// ShortNameNEQ applies the NEQ predicate on the "short_name" field.
func ShortNameNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldShortName, v))
}

// ShortNameIn applies the In predicate on the "short_name" field.
func ShortNameIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldShortName, vs...))
}

// ShortNameNotIn applies the NotIn predicate on the "short_name" field.
func ShortNameNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldShortName, vs...))
}

// ShortNameGT applies the GT predicate on the "short_name" field.
func ShortNameGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldShortName, v))
}

// ShortNameGTE applies the GTE predicate on the "short_name" field.
func ShortNameGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldShortName, v))
}

// ShortNameLT applies the LT predicate on the "short_name" field.
func ShortNameLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldShortName, v))
}

// ShortNameLTE applies the LTE predicate on the "short_name" field.
func ShortNameLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldShortName, v))
}

// ShortNameContains applies the Contains predicate on the "short_name" field.
func ShortNameContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldShortName, v))
}

// ShortNameHasPrefix applies the HasPrefix predicate on the "short_name" field.
func ShortNameHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldShortName, v))
}

// ShortNameHasSuffix applies the HasSuffix predicate on the "short_name" field.
func ShortNameHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldShortName, v))
}

// ShortNameIsNil applies the IsNil predicate on the "short_name" field.
func ShortNameIsNil() predicate.Accounts {
	return predicate.Accounts(sql.FieldIsNull(FieldShortName))
}

// ShortNameNotNil applies the NotNil predicate on the "short_name" field.
func ShortNameNotNil() predicate.Accounts {
	return predicate.Accounts(sql.FieldNotNull(FieldShortName))
}

// ShortNameEqualFold applies the EqualFold predicate on the "short_name" field.
func ShortNameEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldShortName, v))
}

// ShortNameContainsFold applies the ContainsFold predicate on the "short_name" field.
func ShortNameContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldShortName, v))
}

// CurrencyEQ applies the EQ predicate on the "currency" field.
func CurrencyEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldCurrency, v))
}

// CurrencyNEQ applies the NEQ predicate on the "currency" field.
func CurrencyNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldCurrency, v))
}

// CurrencyIn applies the In predicate on the "currency" field.
func CurrencyIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldCurrency, vs...))
}

// CurrencyNotIn applies the NotIn predicate on the "currency" field.
func CurrencyNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldCurrency, vs...))
}

// CurrencyGT applies the GT predicate on the "currency" field.
func CurrencyGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldCurrency, v))
}

// CurrencyGTE applies the GTE predicate on the "currency" field.
func CurrencyGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldCurrency, v))
}

// CurrencyLT applies the LT predicate on the "currency" field.
func CurrencyLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldCurrency, v))
}

// CurrencyLTE applies the LTE predicate on the "currency" field.
func CurrencyLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldCurrency, v))
}

// CurrencyContains applies the Contains predicate on the "currency" field.
func CurrencyContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldCurrency, v))
}

// CurrencyHasPrefix applies the HasPrefix predicate on the "currency" field.
func CurrencyHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldCurrency, v))
}

// CurrencyHasSuffix applies the HasSuffix predicate on the "currency" field.
func CurrencyHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldCurrency, v))
}

// CurrencyEqualFold applies the EqualFold predicate on the "currency" field.
func CurrencyEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldCurrency, v))
}

// CurrencyContainsFold applies the ContainsFold predicate on the "currency" field.
func CurrencyContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldCurrency, v))
}

// IbanEQ applies the EQ predicate on the "iban" field.
func IbanEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldIban, v))
}

// IbanNEQ applies the NEQ predicate on the "iban" field.
func IbanNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldIban, v))
}

// IbanIn applies the In predicate on the "iban" field.
func IbanIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldIban, vs...))
}

// IbanNotIn applies the NotIn predicate on the "iban" field.
func IbanNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldIban, vs...))
}

// IbanGT applies the GT predicate on the "iban" field.
func IbanGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldIban, v))
}

// IbanGTE applies the GTE predicate on the "iban" field.
func IbanGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldIban, v))
}

// IbanLT applies the LT predicate on the "iban" field.
func IbanLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldIban, v))
}

// IbanLTE applies the LTE predicate on the "iban" field.
func IbanLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldIban, v))
}

// IbanContains applies the Contains predicate on the "iban" field.
func IbanContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldIban, v))
}

// IbanHasPrefix applies the HasPrefix predicate on the "iban" field.
func IbanHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldIban, v))
}

// IbanHasSuffix applies the HasSuffix predicate on the "iban" field.
func IbanHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldIban, v))
}

// IbanEqualFold applies the EqualFold predicate on the "iban" field.
func IbanEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldIban, v))
}

// IbanContainsFold applies the ContainsFold predicate on the "iban" field.
func IbanContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldIban, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldType, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldStatus, v))
}

// DateOpenedEQ applies the EQ predicate on the "date_opened" field.
func DateOpenedEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldDateOpened, v))
}

// DateOpenedNEQ applies the NEQ predicate on the "date_opened" field.
func DateOpenedNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldDateOpened, v))
}

// DateOpenedIn applies the In predicate on the "date_opened" field.
func DateOpenedIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldDateOpened, vs...))
}

// DateOpenedNotIn applies the NotIn predicate on the "date_opened" field.
func DateOpenedNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldDateOpened, vs...))
}

// DateOpenedGT applies the GT predicate on the "date_opened" field.
func DateOpenedGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldDateOpened, v))
}

// DateOpenedGTE applies the GTE predicate on the "date_opened" field.
func DateOpenedGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldDateOpened, v))
}

// DateOpenedLT applies the LT predicate on the "date_opened" field.
func DateOpenedLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldDateOpened, v))
}

// DateOpenedLTE applies the LTE predicate on the "date_opened" field.
func DateOpenedLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldDateOpened, v))
}

// DateOpenedContains applies the Contains predicate on the "date_opened" field.
func DateOpenedContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldDateOpened, v))
}

// DateOpenedHasPrefix applies the HasPrefix predicate on the "date_opened" field.
func DateOpenedHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldDateOpened, v))
}

// DateOpenedHasSuffix applies the HasSuffix predicate on the "date_opened" field.
func DateOpenedHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldDateOpened, v))
}

// DateOpenedEqualFold applies the EqualFold predicate on the "date_opened" field.
func DateOpenedEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldDateOpened, v))
}

// DateOpenedContainsFold applies the ContainsFold predicate on the "date_opened" field.
func DateOpenedContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldDateOpened, v))
}

// BalanceEQ applies the EQ predicate on the "balance" field.
func BalanceEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldBalance, v))
}

// BalanceNEQ applies the NEQ predicate on the "balance" field.
func BalanceNEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldBalance, v))
}

// BalanceIn applies the In predicate on the "balance" field.
func BalanceIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldBalance, vs...))
}

// BalanceNotIn applies the NotIn predicate on the "balance" field.
func BalanceNotIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldBalance, vs...))
}

// BalanceGT applies the GT predicate on the "balance" field.
func BalanceGT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldBalance, v))
}

// BalanceGTE applies the GTE predicate on the "balance" field.
func BalanceGTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldBalance, v))
}

// BalanceLT applies the LT predicate on the "balance" field.
func BalanceLT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldBalance, v))
}

// BalanceLTE applies the LTE predicate on the "balance" field.
func BalanceLTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldBalance, v))
}

// BalanceNativalEQ applies the EQ predicate on the "balance_natival" field.
func BalanceNativalEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldBalanceNatival, v))
}

// BalanceNativalNEQ applies the NEQ predicate on the "balance_natival" field.
func BalanceNativalNEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldBalanceNatival, v))
}

// BalanceNativalIn applies the In predicate on the "balance_natival" field.
func BalanceNativalIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldBalanceNatival, vs...))
}

// BalanceNativalNotIn applies the NotIn predicate on the "balance_natival" field.
func BalanceNativalNotIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldBalanceNatival, vs...))
}

// BalanceNativalGT applies the GT predicate on the "balance_natival" field.
func BalanceNativalGT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldBalanceNatival, v))
}

// BalanceNativalGTE applies the GTE predicate on the "balance_natival" field.
func BalanceNativalGTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldBalanceNatival, v))
}

// BalanceNativalLT applies the LT predicate on the "balance_natival" field.
func BalanceNativalLT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldBalanceNatival, v))
}

// BalanceNativalLTE applies the LTE predicate on the "balance_natival" field.
func BalanceNativalLTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldBalanceNatival, v))
}

// BlockedBalanceEQ applies the EQ predicate on the "blocked_balance" field.
func BlockedBalanceEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldBlockedBalance, v))
}

// BlockedBalanceNEQ applies the NEQ predicate on the "blocked_balance" field.
func BlockedBalanceNEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldBlockedBalance, v))
}

// BlockedBalanceIn applies the In predicate on the "blocked_balance" field.
func BlockedBalanceIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldBlockedBalance, vs...))
}

// BlockedBalanceNotIn applies the NotIn predicate on the "blocked_balance" field.
func BlockedBalanceNotIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldBlockedBalance, vs...))
}

// BlockedBalanceGT applies the GT predicate on the "blocked_balance" field.
func BlockedBalanceGT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldBlockedBalance, v))
}

// BlockedBalanceGTE applies the GTE predicate on the "blocked_balance" field.
func BlockedBalanceGTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldBlockedBalance, v))
}

// BlockedBalanceLT applies the LT predicate on the "blocked_balance" field.
func BlockedBalanceLT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldBlockedBalance, v))
}

// BlockedBalanceLTE applies the LTE predicate on the "blocked_balance" field.
func BlockedBalanceLTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldBlockedBalance, v))
}

// AvailableBalanceEQ applies the EQ predicate on the "available_balance" field.
func AvailableBalanceEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldAvailableBalance, v))
}

// AvailableBalanceNEQ applies the NEQ predicate on the "available_balance" field.
func AvailableBalanceNEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldAvailableBalance, v))
}

// AvailableBalanceIn applies the In predicate on the "available_balance" field.
func AvailableBalanceIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldAvailableBalance, vs...))
}

// AvailableBalanceNotIn applies the NotIn predicate on the "available_balance" field.
func AvailableBalanceNotIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldAvailableBalance, vs...))
}

// AvailableBalanceGT applies the GT predicate on the "available_balance" field.
func AvailableBalanceGT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldAvailableBalance, v))
}

// AvailableBalanceGTE applies the GTE predicate on the "available_balance" field.
func AvailableBalanceGTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldAvailableBalance, v))
}

// AvailableBalanceLT applies the LT predicate on the "available_balance" field.
func AvailableBalanceLT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldAvailableBalance, v))
}

// AvailableBalanceLTE applies the LTE predicate on the "available_balance" field.
func AvailableBalanceLTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldAvailableBalance, v))
}

// DateClosedEQ applies the EQ predicate on the "date_closed" field.
func DateClosedEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldDateClosed, v))
}

// DateClosedNEQ applies the NEQ predicate on the "date_closed" field.
func DateClosedNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldDateClosed, v))
}

// DateClosedIn applies the In predicate on the "date_closed" field.
func DateClosedIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldDateClosed, vs...))
}

// DateClosedNotIn applies the NotIn predicate on the "date_closed" field.
func DateClosedNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldDateClosed, vs...))
}

// DateClosedGT applies the GT predicate on the "date_closed" field.
func DateClosedGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldDateClosed, v))
}

// DateClosedGTE applies the GTE predicate on the "date_closed" field.
func DateClosedGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldDateClosed, v))
}

// DateClosedLT applies the LT predicate on the "date_closed" field.
func DateClosedLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldDateClosed, v))
}

// DateClosedLTE applies the LTE predicate on the "date_closed" field.
func DateClosedLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldDateClosed, v))
}

// DateClosedContains applies the Contains predicate on the "date_closed" field.
func DateClosedContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldDateClosed, v))
}

// DateClosedHasPrefix applies the HasPrefix predicate on the "date_closed" field.
func DateClosedHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldDateClosed, v))
}

// DateClosedHasSuffix applies the HasSuffix predicate on the "date_closed" field.
func DateClosedHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldDateClosed, v))
}

// DateClosedEqualFold applies the EqualFold predicate on the "date_closed" field.
func DateClosedEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldDateClosed, v))
}

// DateClosedContainsFold applies the ContainsFold predicate on the "date_closed" field.
func DateClosedContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldDateClosed, v))
}

// ArrestBlockingEQ applies the EQ predicate on the "arrest_blocking" field.
func ArrestBlockingEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldArrestBlocking, v))
}

// ArrestBlockingNEQ applies the NEQ predicate on the "arrest_blocking" field.
func ArrestBlockingNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldArrestBlocking, v))
}

// ArrestBlockingIn applies the In predicate on the "arrest_blocking" field.
func ArrestBlockingIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldArrestBlocking, vs...))
}

// ArrestBlockingNotIn applies the NotIn predicate on the "arrest_blocking" field.
func ArrestBlockingNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldArrestBlocking, vs...))
}

// ArrestBlockingGT applies the GT predicate on the "arrest_blocking" field.
func ArrestBlockingGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldArrestBlocking, v))
}

// ArrestBlockingGTE applies the GTE predicate on the "arrest_blocking" field.
func ArrestBlockingGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldArrestBlocking, v))
}

// ArrestBlockingLT applies the LT predicate on the "arrest_blocking" field.
func ArrestBlockingLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldArrestBlocking, v))
}

// ArrestBlockingLTE applies the LTE predicate on the "arrest_blocking" field.
func ArrestBlockingLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldArrestBlocking, v))
}

// ArrestBlockingContains applies the Contains predicate on the "arrest_blocking" field.
func ArrestBlockingContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldArrestBlocking, v))
}

// ArrestBlockingHasPrefix applies the HasPrefix predicate on the "arrest_blocking" field.
func ArrestBlockingHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldArrestBlocking, v))
}

// ArrestBlockingHasSuffix applies the HasSuffix predicate on the "arrest_blocking" field.
func ArrestBlockingHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldArrestBlocking, v))
}

// ArrestBlockingEqualFold applies the EqualFold predicate on the "arrest_blocking" field.
func ArrestBlockingEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldArrestBlocking, v))
}

// ArrestBlockingContainsFold applies the ContainsFold predicate on the "arrest_blocking" field.
func ArrestBlockingContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldArrestBlocking, v))
}

// ArrestDebtAmountEQ applies the EQ predicate on the "arrest_debt_amount" field.
func ArrestDebtAmountEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldArrestDebtAmount, v))
}

// ArrestDebtAmountNEQ applies the NEQ predicate on the "arrest_debt_amount" field.
func ArrestDebtAmountNEQ(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldArrestDebtAmount, v))
}

// ArrestDebtAmountIn applies the In predicate on the "arrest_debt_amount" field.
func ArrestDebtAmountIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldArrestDebtAmount, vs...))
}

// ArrestDebtAmountNotIn applies the NotIn predicate on the "arrest_debt_amount" field.
func ArrestDebtAmountNotIn(vs ...decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldArrestDebtAmount, vs...))
}

// ArrestDebtAmountGT applies the GT predicate on the "arrest_debt_amount" field.
func ArrestDebtAmountGT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldArrestDebtAmount, v))
}

// ArrestDebtAmountGTE applies the GTE predicate on the "arrest_debt_amount" field.
func ArrestDebtAmountGTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldArrestDebtAmount, v))
}

// ArrestDebtAmountLT applies the LT predicate on the "arrest_debt_amount" field.
func ArrestDebtAmountLT(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldArrestDebtAmount, v))
}

// ArrestDebtAmountLTE applies the LTE predicate on the "arrest_debt_amount" field.
func ArrestDebtAmountLTE(v decimal.Decimal) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldArrestDebtAmount, v))
}

// HasArrestEQ applies the EQ predicate on the "has_arrest" field.
func HasArrestEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldHasArrest, v))
}

// HasArrestNEQ applies the NEQ predicate on the "has_arrest" field.
func HasArrestNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldHasArrest, v))
}

// HasArrestIn applies the In predicate on the "has_arrest" field.
func HasArrestIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldHasArrest, vs...))
}

// HasArrestNotIn applies the NotIn predicate on the "has_arrest" field.
func HasArrestNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldHasArrest, vs...))
}

// HasArrestGT applies the GT predicate on the "has_arrest" field.
func HasArrestGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldHasArrest, v))
}

// HasArrestGTE applies the GTE predicate on the "has_arrest" field.
func HasArrestGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldHasArrest, v))
}

// HasArrestLT applies the LT predicate on the "has_arrest" field.
func HasArrestLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldHasArrest, v))
}

// HasArrestLTE applies the LTE predicate on the "has_arrest" field.
func HasArrestLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldHasArrest, v))
}

// HasArrestContains applies the Contains predicate on the "has_arrest" field.
func HasArrestContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldHasArrest, v))
}

// HasArrestHasPrefix applies the HasPrefix predicate on the "has_arrest" field.
func HasArrestHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldHasArrest, v))
}

// HasArrestHasSuffix applies the HasSuffix predicate on the "has_arrest" field.
func HasArrestHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldHasArrest, v))
}

// HasArrestEqualFold applies the EqualFold predicate on the "has_arrest" field.
func HasArrestEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldHasArrest, v))
}

// HasArrestContainsFold applies the ContainsFold predicate on the "has_arrest" field.
func HasArrestContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldHasArrest, v))
}

// OriginEQ applies the EQ predicate on the "origin" field.
func OriginEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldOrigin, v))
}

// OriginNEQ applies the NEQ predicate on the "origin" field.
func OriginNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldOrigin, v))
}

// OriginIn applies the In predicate on the "origin" field.
func OriginIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldOrigin, vs...))
}

// OriginNotIn applies the NotIn predicate on the "origin" field.
func OriginNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldOrigin, vs...))
}

// OriginGT applies the GT predicate on the "origin" field.
func OriginGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldOrigin, v))
}

// OriginGTE applies the GTE predicate on the "origin" field.
func OriginGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldOrigin, v))
}

// OriginLT applies the LT predicate on the "origin" field.
func OriginLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldOrigin, v))
}

// OriginLTE applies the LTE predicate on the "origin" field.
func OriginLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldOrigin, v))
}

// OriginContains applies the Contains predicate on the "origin" field.
func OriginContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldOrigin, v))
}

// OriginHasPrefix applies the HasPrefix predicate on the "origin" field.
func OriginHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldOrigin, v))
}

// OriginHasSuffix applies the HasSuffix predicate on the "origin" field.
func OriginHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldOrigin, v))
}

// OriginEqualFold applies the EqualFold predicate on the "origin" field.
func OriginEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldOrigin, v))
}

// OriginContainsFold applies the ContainsFold predicate on the "origin" field.
func OriginContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldOrigin, v))
}

// DeaReferenceIDEQ applies the EQ predicate on the "dea_reference_id" field.
func DeaReferenceIDEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldDeaReferenceID, v))
}

// DeaReferenceIDNEQ applies the NEQ predicate on the "dea_reference_id" field.
func DeaReferenceIDNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldDeaReferenceID, v))
}

// DeaReferenceIDIn applies the In predicate on the "dea_reference_id" field.
func DeaReferenceIDIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldDeaReferenceID, vs...))
}

// DeaReferenceIDNotIn applies the NotIn predicate on the "dea_reference_id" field.
func DeaReferenceIDNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldDeaReferenceID, vs...))
}

// DeaReferenceIDGT applies the GT predicate on the "dea_reference_id" field.
func DeaReferenceIDGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldDeaReferenceID, v))
}

// DeaReferenceIDGTE applies the GTE predicate on the "dea_reference_id" field.
func DeaReferenceIDGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldDeaReferenceID, v))
}

// DeaReferenceIDLT applies the LT predicate on the "dea_reference_id" field.
func DeaReferenceIDLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldDeaReferenceID, v))
}

// DeaReferenceIDLTE applies the LTE predicate on the "dea_reference_id" field.
func DeaReferenceIDLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldDeaReferenceID, v))
}

// DeaReferenceIDContains applies the Contains predicate on the "dea_reference_id" field.
func DeaReferenceIDContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldDeaReferenceID, v))
}

// DeaReferenceIDHasPrefix applies the HasPrefix predicate on the "dea_reference_id" field.
func DeaReferenceIDHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldDeaReferenceID, v))
}

// DeaReferenceIDHasSuffix applies the HasSuffix predicate on the "dea_reference_id" field.
func DeaReferenceIDHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldDeaReferenceID, v))
}

// DeaReferenceIDEqualFold applies the EqualFold predicate on the "dea_reference_id" field.
func DeaReferenceIDEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldDeaReferenceID, v))
}

// DeaReferenceIDContainsFold applies the ContainsFold predicate on the "dea_reference_id" field.
func DeaReferenceIDContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldDeaReferenceID, v))
}

// ClientTypeEQ applies the EQ predicate on the "client_type" field.
func ClientTypeEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldClientType, v))
}

// ClientTypeNEQ applies the NEQ predicate on the "client_type" field.
func ClientTypeNEQ(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldClientType, v))
}

// ClientTypeIn applies the In predicate on the "client_type" field.
func ClientTypeIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldIn(FieldClientType, vs...))
}

// ClientTypeNotIn applies the NotIn predicate on the "client_type" field.
func ClientTypeNotIn(vs ...string) predicate.Accounts {
	return predicate.Accounts(sql.FieldNotIn(FieldClientType, vs...))
}

// ClientTypeGT applies the GT predicate on the "client_type" field.
func ClientTypeGT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGT(FieldClientType, v))
}

// ClientTypeGTE applies the GTE predicate on the "client_type" field.
func ClientTypeGTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldGTE(FieldClientType, v))
}

// ClientTypeLT applies the LT predicate on the "client_type" field.
func ClientTypeLT(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLT(FieldClientType, v))
}

// ClientTypeLTE applies the LTE predicate on the "client_type" field.
func ClientTypeLTE(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldLTE(FieldClientType, v))
}

// ClientTypeContains applies the Contains predicate on the "client_type" field.
func ClientTypeContains(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContains(FieldClientType, v))
}

// ClientTypeHasPrefix applies the HasPrefix predicate on the "client_type" field.
func ClientTypeHasPrefix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasPrefix(FieldClientType, v))
}

// ClientTypeHasSuffix applies the HasSuffix predicate on the "client_type" field.
func ClientTypeHasSuffix(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldHasSuffix(FieldClientType, v))
}

// ClientTypeEqualFold applies the EqualFold predicate on the "client_type" field.
func ClientTypeEqualFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldEqualFold(FieldClientType, v))
}

// ClientTypeContainsFold applies the ContainsFold predicate on the "client_type" field.
func ClientTypeContainsFold(v string) predicate.Accounts {
	return predicate.Accounts(sql.FieldContainsFold(FieldClientType, v))
}

// IsMainEQ applies the EQ predicate on the "is_main" field.
func IsMainEQ(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldIsMain, v))
}

// IsMainNEQ applies the NEQ predicate on the "is_main" field.
func IsMainNEQ(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldIsMain, v))
}

// AccessionAccountEQ applies the EQ predicate on the "accession_account" field.
func AccessionAccountEQ(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldAccessionAccount, v))
}

// AccessionAccountNEQ applies the NEQ predicate on the "accession_account" field.
func AccessionAccountNEQ(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldAccessionAccount, v))
}

// IsArrestedEQ applies the EQ predicate on the "is_arrested" field.
func IsArrestedEQ(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldEQ(FieldIsArrested, v))
}

// IsArrestedNEQ applies the NEQ predicate on the "is_arrested" field.
func IsArrestedNEQ(v bool) predicate.Accounts {
	return predicate.Accounts(sql.FieldNEQ(FieldIsArrested, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Accounts) predicate.Accounts {
	return predicate.Accounts(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Accounts) predicate.Accounts {
	return predicate.Accounts(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Accounts) predicate.Accounts {
	return predicate.Accounts(sql.NotPredicates(p))
}
