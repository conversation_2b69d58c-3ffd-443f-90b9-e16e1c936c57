// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
)

// Accounts is the model entity for the Accounts schema.
type Accounts struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// ClientIin holds the value of the "client_iin" field.
	ClientIin string `json:"client_iin,omitempty"`
	// ClientCode holds the value of the "client_code" field.
	ClientCode string `json:"client_code,omitempty"`
	// ClientName holds the value of the "client_name" field.
	ClientName string `json:"client_name,omitempty"`
	// Title holds the value of the "title" field.
	Title string `json:"title,omitempty"`
	// ShortName holds the value of the "short_name" field.
	ShortName string `json:"short_name,omitempty"`
	// Currency holds the value of the "currency" field.
	Currency string `json:"currency,omitempty"`
	// Iban holds the value of the "iban" field.
	Iban string `json:"iban,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Status holds the value of the "status" field.
	Status string `json:"status,omitempty"`
	// DateOpened holds the value of the "date_opened" field.
	DateOpened string `json:"date_opened,omitempty"`
	// Balance holds the value of the "balance" field.
	Balance decimal.Decimal `json:"balance,omitempty"`
	// BalanceNatival holds the value of the "balance_natival" field.
	BalanceNatival decimal.Decimal `json:"balance_natival,omitempty"`
	// BlockedBalance holds the value of the "blocked_balance" field.
	BlockedBalance decimal.Decimal `json:"blocked_balance,omitempty"`
	// AvailableBalance holds the value of the "available_balance" field.
	AvailableBalance decimal.Decimal `json:"available_balance,omitempty"`
	// DateClosed holds the value of the "date_closed" field.
	DateClosed string `json:"date_closed,omitempty"`
	// ArrestBlocking holds the value of the "arrest_blocking" field.
	ArrestBlocking string `json:"arrest_blocking,omitempty"`
	// ArrestDebtAmount holds the value of the "arrest_debt_amount" field.
	ArrestDebtAmount decimal.Decimal `json:"arrest_debt_amount,omitempty"`
	// HasArrest holds the value of the "has_arrest" field.
	//
	// Deprecated: использовать is_arrested
	HasArrest string `json:"has_arrest,omitempty"`
	// Origin holds the value of the "origin" field.
	Origin string `json:"origin,omitempty"`
	// DeaReferenceID holds the value of the "dea_reference_id" field.
	DeaReferenceID string `json:"dea_reference_id,omitempty"`
	// ClientType holds the value of the "client_type" field.
	ClientType string `json:"client_type,omitempty"`
	// IsMain holds the value of the "is_main" field.
	IsMain bool `json:"is_main,omitempty"`
	// AccessionAccount holds the value of the "accession_account" field.
	AccessionAccount bool `json:"accession_account,omitempty"`
	// IsArrested holds the value of the "is_arrested" field.
	IsArrested   bool `json:"is_arrested,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Accounts) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case accounts.FieldBalance, accounts.FieldBalanceNatival, accounts.FieldBlockedBalance, accounts.FieldAvailableBalance, accounts.FieldArrestDebtAmount:
			values[i] = new(decimal.Decimal)
		case accounts.FieldIsMain, accounts.FieldAccessionAccount, accounts.FieldIsArrested:
			values[i] = new(sql.NullBool)
		case accounts.FieldClientIin, accounts.FieldClientCode, accounts.FieldClientName, accounts.FieldTitle, accounts.FieldShortName, accounts.FieldCurrency, accounts.FieldIban, accounts.FieldType, accounts.FieldStatus, accounts.FieldDateOpened, accounts.FieldDateClosed, accounts.FieldArrestBlocking, accounts.FieldHasArrest, accounts.FieldOrigin, accounts.FieldDeaReferenceID, accounts.FieldClientType:
			values[i] = new(sql.NullString)
		case accounts.FieldCreateTime, accounts.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case accounts.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Accounts fields.
func (_m *Accounts) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case accounts.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case accounts.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case accounts.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case accounts.FieldClientIin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_iin", values[i])
			} else if value.Valid {
				_m.ClientIin = value.String
			}
		case accounts.FieldClientCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_code", values[i])
			} else if value.Valid {
				_m.ClientCode = value.String
			}
		case accounts.FieldClientName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_name", values[i])
			} else if value.Valid {
				_m.ClientName = value.String
			}
		case accounts.FieldTitle:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field title", values[i])
			} else if value.Valid {
				_m.Title = value.String
			}
		case accounts.FieldShortName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field short_name", values[i])
			} else if value.Valid {
				_m.ShortName = value.String
			}
		case accounts.FieldCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field currency", values[i])
			} else if value.Valid {
				_m.Currency = value.String
			}
		case accounts.FieldIban:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field iban", values[i])
			} else if value.Valid {
				_m.Iban = value.String
			}
		case accounts.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				_m.Type = value.String
			}
		case accounts.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				_m.Status = value.String
			}
		case accounts.FieldDateOpened:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field date_opened", values[i])
			} else if value.Valid {
				_m.DateOpened = value.String
			}
		case accounts.FieldBalance:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field balance", values[i])
			} else if value != nil {
				_m.Balance = *value
			}
		case accounts.FieldBalanceNatival:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field balance_natival", values[i])
			} else if value != nil {
				_m.BalanceNatival = *value
			}
		case accounts.FieldBlockedBalance:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field blocked_balance", values[i])
			} else if value != nil {
				_m.BlockedBalance = *value
			}
		case accounts.FieldAvailableBalance:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field available_balance", values[i])
			} else if value != nil {
				_m.AvailableBalance = *value
			}
		case accounts.FieldDateClosed:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field date_closed", values[i])
			} else if value.Valid {
				_m.DateClosed = value.String
			}
		case accounts.FieldArrestBlocking:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field arrest_blocking", values[i])
			} else if value.Valid {
				_m.ArrestBlocking = value.String
			}
		case accounts.FieldArrestDebtAmount:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field arrest_debt_amount", values[i])
			} else if value != nil {
				_m.ArrestDebtAmount = *value
			}
		case accounts.FieldHasArrest:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field has_arrest", values[i])
			} else if value.Valid {
				_m.HasArrest = value.String
			}
		case accounts.FieldOrigin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field origin", values[i])
			} else if value.Valid {
				_m.Origin = value.String
			}
		case accounts.FieldDeaReferenceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field dea_reference_id", values[i])
			} else if value.Valid {
				_m.DeaReferenceID = value.String
			}
		case accounts.FieldClientType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field client_type", values[i])
			} else if value.Valid {
				_m.ClientType = value.String
			}
		case accounts.FieldIsMain:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_main", values[i])
			} else if value.Valid {
				_m.IsMain = value.Bool
			}
		case accounts.FieldAccessionAccount:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field accession_account", values[i])
			} else if value.Valid {
				_m.AccessionAccount = value.Bool
			}
		case accounts.FieldIsArrested:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_arrested", values[i])
			} else if value.Valid {
				_m.IsArrested = value.Bool
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Accounts.
// This includes values selected through modifiers, order, etc.
func (_m *Accounts) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Accounts.
// Note that you need to call Accounts.Unwrap() before calling this method if this Accounts
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Accounts) Update() *AccountsUpdateOne {
	return NewAccountsClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Accounts entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Accounts) Unwrap() *Accounts {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Accounts is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Accounts) String() string {
	var builder strings.Builder
	builder.WriteString("Accounts(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("client_iin=")
	builder.WriteString(_m.ClientIin)
	builder.WriteString(", ")
	builder.WriteString("client_code=")
	builder.WriteString(_m.ClientCode)
	builder.WriteString(", ")
	builder.WriteString("client_name=")
	builder.WriteString(_m.ClientName)
	builder.WriteString(", ")
	builder.WriteString("title=")
	builder.WriteString(_m.Title)
	builder.WriteString(", ")
	builder.WriteString("short_name=")
	builder.WriteString(_m.ShortName)
	builder.WriteString(", ")
	builder.WriteString("currency=")
	builder.WriteString(_m.Currency)
	builder.WriteString(", ")
	builder.WriteString("iban=")
	builder.WriteString(_m.Iban)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(_m.Type)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(_m.Status)
	builder.WriteString(", ")
	builder.WriteString("date_opened=")
	builder.WriteString(_m.DateOpened)
	builder.WriteString(", ")
	builder.WriteString("balance=")
	builder.WriteString(fmt.Sprintf("%v", _m.Balance))
	builder.WriteString(", ")
	builder.WriteString("balance_natival=")
	builder.WriteString(fmt.Sprintf("%v", _m.BalanceNatival))
	builder.WriteString(", ")
	builder.WriteString("blocked_balance=")
	builder.WriteString(fmt.Sprintf("%v", _m.BlockedBalance))
	builder.WriteString(", ")
	builder.WriteString("available_balance=")
	builder.WriteString(fmt.Sprintf("%v", _m.AvailableBalance))
	builder.WriteString(", ")
	builder.WriteString("date_closed=")
	builder.WriteString(_m.DateClosed)
	builder.WriteString(", ")
	builder.WriteString("arrest_blocking=")
	builder.WriteString(_m.ArrestBlocking)
	builder.WriteString(", ")
	builder.WriteString("arrest_debt_amount=")
	builder.WriteString(fmt.Sprintf("%v", _m.ArrestDebtAmount))
	builder.WriteString(", ")
	builder.WriteString("has_arrest=")
	builder.WriteString(_m.HasArrest)
	builder.WriteString(", ")
	builder.WriteString("origin=")
	builder.WriteString(_m.Origin)
	builder.WriteString(", ")
	builder.WriteString("dea_reference_id=")
	builder.WriteString(_m.DeaReferenceID)
	builder.WriteString(", ")
	builder.WriteString("client_type=")
	builder.WriteString(_m.ClientType)
	builder.WriteString(", ")
	builder.WriteString("is_main=")
	builder.WriteString(fmt.Sprintf("%v", _m.IsMain))
	builder.WriteString(", ")
	builder.WriteString("accession_account=")
	builder.WriteString(fmt.Sprintf("%v", _m.AccessionAccount))
	builder.WriteString(", ")
	builder.WriteString("is_arrested=")
	builder.WriteString(fmt.Sprintf("%v", _m.IsArrested))
	builder.WriteByte(')')
	return builder.String()
}

// AccountsSlice is a parsable slice of Accounts.
type AccountsSlice []*Accounts
