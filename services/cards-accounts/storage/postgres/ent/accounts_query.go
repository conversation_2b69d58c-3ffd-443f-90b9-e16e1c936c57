// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// AccountsQuery is the builder for querying Accounts entities.
type AccountsQuery struct {
	config
	ctx        *QueryContext
	order      []accounts.OrderOption
	inters     []Interceptor
	predicates []predicate.Accounts
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AccountsQuery builder.
func (_q *AccountsQuery) Where(ps ...predicate.Accounts) *AccountsQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *AccountsQuery) Limit(limit int) *AccountsQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *AccountsQuery) Offset(offset int) *AccountsQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *AccountsQuery) Unique(unique bool) *AccountsQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *AccountsQuery) Order(o ...accounts.OrderOption) *AccountsQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// First returns the first Accounts entity from the query.
// Returns a *NotFoundError when no Accounts was found.
func (_q *AccountsQuery) First(ctx context.Context) (*Accounts, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{accounts.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *AccountsQuery) FirstX(ctx context.Context) *Accounts {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Accounts ID from the query.
// Returns a *NotFoundError when no Accounts ID was found.
func (_q *AccountsQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{accounts.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *AccountsQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Accounts entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Accounts entity is found.
// Returns a *NotFoundError when no Accounts entities are found.
func (_q *AccountsQuery) Only(ctx context.Context) (*Accounts, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{accounts.Label}
	default:
		return nil, &NotSingularError{accounts.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *AccountsQuery) OnlyX(ctx context.Context) *Accounts {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Accounts ID in the query.
// Returns a *NotSingularError when more than one Accounts ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *AccountsQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{accounts.Label}
	default:
		err = &NotSingularError{accounts.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *AccountsQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AccountsSlice.
func (_q *AccountsQuery) All(ctx context.Context) ([]*Accounts, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Accounts, *AccountsQuery]()
	return withInterceptors[[]*Accounts](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *AccountsQuery) AllX(ctx context.Context) []*Accounts {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Accounts IDs.
func (_q *AccountsQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(accounts.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *AccountsQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *AccountsQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*AccountsQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *AccountsQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *AccountsQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *AccountsQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AccountsQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *AccountsQuery) Clone() *AccountsQuery {
	if _q == nil {
		return nil
	}
	return &AccountsQuery{
		config:     _q.config,
		ctx:        _q.ctx.Clone(),
		order:      append([]accounts.OrderOption{}, _q.order...),
		inters:     append([]Interceptor{}, _q.inters...),
		predicates: append([]predicate.Accounts{}, _q.predicates...),
		// clone intermediate query.
		sql:  _q.sql.Clone(),
		path: _q.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Accounts.Query().
//		GroupBy(accounts.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (_q *AccountsQuery) GroupBy(field string, fields ...string) *AccountsGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AccountsGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = accounts.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.Accounts.Query().
//		Select(accounts.FieldCreateTime).
//		Scan(ctx, &v)
func (_q *AccountsQuery) Select(fields ...string) *AccountsSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &AccountsSelect{AccountsQuery: _q}
	sbuild.label = accounts.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AccountsSelect configured with the given aggregations.
func (_q *AccountsQuery) Aggregate(fns ...AggregateFunc) *AccountsSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *AccountsQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !accounts.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *AccountsQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Accounts, error) {
	var (
		nodes = []*Accounts{}
		_spec = _q.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Accounts).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Accounts{config: _q.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (_q *AccountsQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *AccountsQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(accounts.Table, accounts.Columns, sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, accounts.FieldID)
		for i := range fields {
			if fields[i] != accounts.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *AccountsQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(accounts.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = accounts.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// AccountsGroupBy is the group-by builder for Accounts entities.
type AccountsGroupBy struct {
	selector
	build *AccountsQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *AccountsGroupBy) Aggregate(fns ...AggregateFunc) *AccountsGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *AccountsGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AccountsQuery, *AccountsGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *AccountsGroupBy) sqlScan(ctx context.Context, root *AccountsQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AccountsSelect is the builder for selecting fields of Accounts entities.
type AccountsSelect struct {
	*AccountsQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *AccountsSelect) Aggregate(fns ...AggregateFunc) *AccountsSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *AccountsSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AccountsQuery, *AccountsSelect](ctx, _s.AccountsQuery, _s, _s.inters, v)
}

func (_s *AccountsSelect) sqlScan(ctx context.Context, root *AccountsQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
