// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AccountsColumns holds the columns for the "accounts" table.
	AccountsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime},
		{Name: "update_time", Type: field.TypeTime},
		{Name: "client_iin", Type: field.TypeString},
		{Name: "client_code", Type: field.TypeString},
		{Name: "client_name", Type: field.TypeString},
		{Name: "title", Type: field.TypeString, Nullable: true},
		{Name: "short_name", Type: field.TypeString, Nullable: true},
		{Name: "currency", Type: field.TypeString},
		{Name: "iban", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "status", Type: field.TypeString},
		{Name: "date_opened", Type: field.TypeString},
		{Name: "balance", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "decimal(18, 2)"}},
		{Name: "balance_natival", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "decimal(18, 2)"}},
		{Name: "blocked_balance", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "decimal(18, 2)"}},
		{Name: "available_balance", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "decimal(18, 2)"}},
		{Name: "date_closed", Type: field.TypeString},
		{Name: "arrest_blocking", Type: field.TypeString},
		{Name: "arrest_debt_amount", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "decimal(18, 2)"}},
		{Name: "has_arrest", Type: field.TypeString},
		{Name: "origin", Type: field.TypeString},
		{Name: "dea_reference_id", Type: field.TypeString, Default: ""},
		{Name: "client_type", Type: field.TypeString, Default: ""},
		{Name: "is_main", Type: field.TypeBool, Default: false},
		{Name: "accession_account", Type: field.TypeBool, Default: false},
		{Name: "is_arrested", Type: field.TypeBool, Default: false},
	}
	// AccountsTable holds the schema information for the "accounts" table.
	AccountsTable = &schema.Table{
		Name:       "accounts",
		Columns:    AccountsColumns,
		PrimaryKey: []*schema.Column{AccountsColumns[0]},
	}
	// CardsColumns holds the columns for the "cards" table.
	CardsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID, Unique: true},
		{Name: "create_time", Type: field.TypeTime},
		{Name: "update_time", Type: field.TypeTime},
		{Name: "client_id", Type: field.TypeUUID, Nullable: true},
		{Name: "embossing_name", Type: field.TypeString, Nullable: true, Size: 25},
		{Name: "masked_pan", Type: field.TypeString, Nullable: true, Size: 16},
		{Name: "card_type", Type: field.TypeEnum, Enums: []string{"PHYSICAL", "VIRTUAL"}, Default: "VIRTUAL"},
		{Name: "product_type", Type: field.TypeEnum, Enums: []string{"DEBIT_CARD"}, Default: "DEBIT_CARD"},
		{Name: "payment_system", Type: field.TypeEnum, Enums: []string{"MASTERCARD"}, Default: "MASTERCARD"},
		{Name: "card_class", Type: field.TypeEnum, Enums: []string{"Mastercard Platinum"}, Default: "Mastercard Platinum"},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"IN_OPENING", "ERROR", "ACTIVE", "BLOCKED"}, Default: "IN_OPENING"},
		{Name: "tokenization_status", Type: field.TypeEnum, Enums: []string{"tokenized", "need_verify", "not_tokenized"}, Default: "not_tokenized"},
		{Name: "wallet", Type: field.TypeString, Nullable: true},
		{Name: "creation_date", Type: field.TypeTime},
		{Name: "expire_date", Type: field.TypeTime, Nullable: true},
		{Name: "modification_date", Type: field.TypeTime},
		{Name: "attached_account_id", Type: field.TypeUUID, Nullable: true},
	}
	// CardsTable holds the schema information for the "cards" table.
	CardsTable = &schema.Table{
		Name:       "cards",
		Columns:    CardsColumns,
		PrimaryKey: []*schema.Column{CardsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "cards_accounts_account",
				Columns:    []*schema.Column{CardsColumns[16]},
				RefColumns: []*schema.Column{AccountsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "cards_client_id_status_tokenization_status_expire_date_modification_date_creation_date",
				Unique:  false,
				Columns: []*schema.Column{CardsColumns[3], CardsColumns[10], CardsColumns[11], CardsColumns[14], CardsColumns[15], CardsColumns[13]},
			},
		},
	}
	// ClientVerificationResultsColumns holds the columns for the "client_verification_results" table.
	ClientVerificationResultsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime},
		{Name: "update_time", Type: field.TypeTime},
		{Name: "user_id", Type: field.TypeUUID},
		{Name: "status", Type: field.TypeString},
		{Name: "date", Type: field.TypeTime},
		{Name: "verification_result", Type: field.TypeString},
		{Name: "rejection_reason", Type: field.TypeString, Nullable: true},
	}
	// ClientVerificationResultsTable holds the schema information for the "client_verification_results" table.
	ClientVerificationResultsTable = &schema.Table{
		Name:       "client_verification_results",
		Columns:    ClientVerificationResultsColumns,
		PrimaryKey: []*schema.Column{ClientVerificationResultsColumns[0]},
	}
	// FinContractsColumns holds the columns for the "fin_contracts" table.
	FinContractsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID, Unique: true},
		{Name: "create_time", Type: field.TypeTime},
		{Name: "update_time", Type: field.TypeTime},
		{Name: "contract_type", Type: field.TypeEnum, Enums: []string{"Z_FIN_DEF_KZT", "Z_FIN_DEF_USD", "Z_FIN_DEF_EUR"}, Default: "Z_FIN_DEF_KZT"},
		{Name: "contract_currency", Type: field.TypeEnum, Enums: []string{"KZT", "USD", "EUR", "RUB", "CNY"}, Default: "KZT"},
		{Name: "iban", Type: field.TypeString},
		{Name: "status", Type: field.TypeEnum, Enums: []string{"IN_OPENING", "ERROR", "ACTIVE", "BLOCKED"}, Default: "IN_OPENING"},
		{Name: "creation_date", Type: field.TypeTime},
		{Name: "card_id", Type: field.TypeUUID, Nullable: true},
		{Name: "contract_code", Type: field.TypeString, Nullable: true},
		{Name: "contract_data_open", Type: field.TypeTime},
		{Name: "contract_data_close", Type: field.TypeTime, Nullable: true},
		{Name: "user_id", Type: field.TypeString, Nullable: true},
		{Name: "fin_contract_cards", Type: field.TypeUUID, Nullable: true},
	}
	// FinContractsTable holds the schema information for the "fin_contracts" table.
	FinContractsTable = &schema.Table{
		Name:       "fin_contracts",
		Columns:    FinContractsColumns,
		PrimaryKey: []*schema.Column{FinContractsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "fin_contracts_cards_cards",
				Columns:    []*schema.Column{FinContractsColumns[13]},
				RefColumns: []*schema.Column{CardsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "fincontract_iban",
				Unique:  true,
				Columns: []*schema.Column{FinContractsColumns[5]},
			},
			{
				Name:    "fincontract_status",
				Unique:  false,
				Columns: []*schema.Column{FinContractsColumns[6]},
			},
		},
	}
	// HealthsColumns holds the columns for the "healths" table.
	HealthsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
	}
	// HealthsTable holds the schema information for the "healths" table.
	HealthsTable = &schema.Table{
		Name:       "healths",
		Columns:    HealthsColumns,
		PrimaryKey: []*schema.Column{HealthsColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AccountsTable,
		CardsTable,
		ClientVerificationResultsTable,
		FinContractsTable,
		HealthsTable,
	}
)

func init() {
	CardsTable.ForeignKeys[0].RefTable = AccountsTable
	FinContractsTable.ForeignKeys[0].RefTable = CardsTable
}
