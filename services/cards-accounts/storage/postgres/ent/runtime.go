// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	accountsMixin := schema.Accounts{}.Mixin()
	accountsMixinFields0 := accountsMixin[0].Fields()
	_ = accountsMixinFields0
	accountsFields := schema.Accounts{}.Fields()
	_ = accountsFields
	// accountsDescCreateTime is the schema descriptor for create_time field.
	accountsDescCreateTime := accountsMixinFields0[0].Descriptor()
	// accounts.DefaultCreateTime holds the default value on creation for the create_time field.
	accounts.DefaultCreateTime = accountsDescCreateTime.Default.(func() time.Time)
	// accountsDescUpdateTime is the schema descriptor for update_time field.
	accountsDescUpdateTime := accountsMixinFields0[1].Descriptor()
	// accounts.DefaultUpdateTime holds the default value on creation for the update_time field.
	accounts.DefaultUpdateTime = accountsDescUpdateTime.Default.(func() time.Time)
	// accounts.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	accounts.UpdateDefaultUpdateTime = accountsDescUpdateTime.UpdateDefault.(func() time.Time)
	// accountsDescDeaReferenceID is the schema descriptor for dea_reference_id field.
	accountsDescDeaReferenceID := accountsFields[20].Descriptor()
	// accounts.DefaultDeaReferenceID holds the default value on creation for the dea_reference_id field.
	accounts.DefaultDeaReferenceID = accountsDescDeaReferenceID.Default.(string)
	// accountsDescClientType is the schema descriptor for client_type field.
	accountsDescClientType := accountsFields[21].Descriptor()
	// accounts.DefaultClientType holds the default value on creation for the client_type field.
	accounts.DefaultClientType = accountsDescClientType.Default.(string)
	// accountsDescIsMain is the schema descriptor for is_main field.
	accountsDescIsMain := accountsFields[22].Descriptor()
	// accounts.DefaultIsMain holds the default value on creation for the is_main field.
	accounts.DefaultIsMain = accountsDescIsMain.Default.(bool)
	// accountsDescAccessionAccount is the schema descriptor for accession_account field.
	accountsDescAccessionAccount := accountsFields[23].Descriptor()
	// accounts.DefaultAccessionAccount holds the default value on creation for the accession_account field.
	accounts.DefaultAccessionAccount = accountsDescAccessionAccount.Default.(bool)
	// accountsDescIsArrested is the schema descriptor for is_arrested field.
	accountsDescIsArrested := accountsFields[24].Descriptor()
	// accounts.DefaultIsArrested holds the default value on creation for the is_arrested field.
	accounts.DefaultIsArrested = accountsDescIsArrested.Default.(bool)
	// accountsDescID is the schema descriptor for id field.
	accountsDescID := accountsFields[0].Descriptor()
	// accounts.DefaultID holds the default value on creation for the id field.
	accounts.DefaultID = accountsDescID.Default.(func() uuid.UUID)
	cardsMixin := schema.Cards{}.Mixin()
	cardsMixinFields0 := cardsMixin[0].Fields()
	_ = cardsMixinFields0
	cardsFields := schema.Cards{}.Fields()
	_ = cardsFields
	// cardsDescCreateTime is the schema descriptor for create_time field.
	cardsDescCreateTime := cardsMixinFields0[0].Descriptor()
	// cards.DefaultCreateTime holds the default value on creation for the create_time field.
	cards.DefaultCreateTime = cardsDescCreateTime.Default.(func() time.Time)
	// cardsDescUpdateTime is the schema descriptor for update_time field.
	cardsDescUpdateTime := cardsMixinFields0[1].Descriptor()
	// cards.DefaultUpdateTime holds the default value on creation for the update_time field.
	cards.DefaultUpdateTime = cardsDescUpdateTime.Default.(func() time.Time)
	// cards.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	cards.UpdateDefaultUpdateTime = cardsDescUpdateTime.UpdateDefault.(func() time.Time)
	// cardsDescEmbossingName is the schema descriptor for embossing_name field.
	cardsDescEmbossingName := cardsFields[3].Descriptor()
	// cards.EmbossingNameValidator is a validator for the "embossing_name" field. It is called by the builders before save.
	cards.EmbossingNameValidator = cardsDescEmbossingName.Validators[0].(func(string) error)
	// cardsDescMaskedPan is the schema descriptor for masked_pan field.
	cardsDescMaskedPan := cardsFields[4].Descriptor()
	// cards.MaskedPanValidator is a validator for the "masked_pan" field. It is called by the builders before save.
	cards.MaskedPanValidator = cardsDescMaskedPan.Validators[0].(func(string) error)
	// cardsDescCreationDate is the schema descriptor for creation_date field.
	cardsDescCreationDate := cardsFields[12].Descriptor()
	// cards.DefaultCreationDate holds the default value on creation for the creation_date field.
	cards.DefaultCreationDate = cardsDescCreationDate.Default.(func() time.Time)
	// cardsDescModificationDate is the schema descriptor for modification_date field.
	cardsDescModificationDate := cardsFields[14].Descriptor()
	// cards.DefaultModificationDate holds the default value on creation for the modification_date field.
	cards.DefaultModificationDate = cardsDescModificationDate.Default.(func() time.Time)
	// cards.UpdateDefaultModificationDate holds the default value on update for the modification_date field.
	cards.UpdateDefaultModificationDate = cardsDescModificationDate.UpdateDefault.(func() time.Time)
	// cardsDescID is the schema descriptor for id field.
	cardsDescID := cardsFields[0].Descriptor()
	// cards.DefaultID holds the default value on creation for the id field.
	cards.DefaultID = cardsDescID.Default.(func() uuid.UUID)
	clientverificationresultsMixin := schema.ClientVerificationResults{}.Mixin()
	clientverificationresultsMixinFields0 := clientverificationresultsMixin[0].Fields()
	_ = clientverificationresultsMixinFields0
	clientverificationresultsFields := schema.ClientVerificationResults{}.Fields()
	_ = clientverificationresultsFields
	// clientverificationresultsDescCreateTime is the schema descriptor for create_time field.
	clientverificationresultsDescCreateTime := clientverificationresultsMixinFields0[0].Descriptor()
	// clientverificationresults.DefaultCreateTime holds the default value on creation for the create_time field.
	clientverificationresults.DefaultCreateTime = clientverificationresultsDescCreateTime.Default.(func() time.Time)
	// clientverificationresultsDescUpdateTime is the schema descriptor for update_time field.
	clientverificationresultsDescUpdateTime := clientverificationresultsMixinFields0[1].Descriptor()
	// clientverificationresults.DefaultUpdateTime holds the default value on creation for the update_time field.
	clientverificationresults.DefaultUpdateTime = clientverificationresultsDescUpdateTime.Default.(func() time.Time)
	// clientverificationresults.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	clientverificationresults.UpdateDefaultUpdateTime = clientverificationresultsDescUpdateTime.UpdateDefault.(func() time.Time)
	// clientverificationresultsDescID is the schema descriptor for id field.
	clientverificationresultsDescID := clientverificationresultsFields[0].Descriptor()
	// clientverificationresults.DefaultID holds the default value on creation for the id field.
	clientverificationresults.DefaultID = clientverificationresultsDescID.Default.(func() uuid.UUID)
	fincontractMixin := schema.FinContract{}.Mixin()
	fincontractMixinFields0 := fincontractMixin[0].Fields()
	_ = fincontractMixinFields0
	fincontractFields := schema.FinContract{}.Fields()
	_ = fincontractFields
	// fincontractDescCreateTime is the schema descriptor for create_time field.
	fincontractDescCreateTime := fincontractMixinFields0[0].Descriptor()
	// fincontract.DefaultCreateTime holds the default value on creation for the create_time field.
	fincontract.DefaultCreateTime = fincontractDescCreateTime.Default.(func() time.Time)
	// fincontractDescUpdateTime is the schema descriptor for update_time field.
	fincontractDescUpdateTime := fincontractMixinFields0[1].Descriptor()
	// fincontract.DefaultUpdateTime holds the default value on creation for the update_time field.
	fincontract.DefaultUpdateTime = fincontractDescUpdateTime.Default.(func() time.Time)
	// fincontract.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	fincontract.UpdateDefaultUpdateTime = fincontractDescUpdateTime.UpdateDefault.(func() time.Time)
	// fincontractDescCreationDate is the schema descriptor for creation_date field.
	fincontractDescCreationDate := fincontractFields[5].Descriptor()
	// fincontract.DefaultCreationDate holds the default value on creation for the creation_date field.
	fincontract.DefaultCreationDate = fincontractDescCreationDate.Default.(func() time.Time)
	// fincontractDescID is the schema descriptor for id field.
	fincontractDescID := fincontractFields[0].Descriptor()
	// fincontract.DefaultID holds the default value on creation for the id field.
	fincontract.DefaultID = fincontractDescID.Default.(func() uuid.UUID)
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
}
