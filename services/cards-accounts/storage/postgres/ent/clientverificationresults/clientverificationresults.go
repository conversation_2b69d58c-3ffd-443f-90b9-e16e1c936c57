// Code generated by ent, DO NOT EDIT.

package clientverificationresults

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the clientverificationresults type in the database.
	Label = "client_verification_results"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldDate holds the string denoting the date field in the database.
	FieldDate = "date"
	// FieldVerificationResult holds the string denoting the verification_result field in the database.
	FieldVerificationResult = "verification_result"
	// FieldRejectionReason holds the string denoting the rejection_reason field in the database.
	FieldRejectionReason = "rejection_reason"
	// Table holds the table name of the clientverificationresults in the database.
	Table = "client_verification_results"
)

// Columns holds all SQL columns for clientverificationresults fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldUserID,
	FieldStatus,
	FieldDate,
	FieldVerificationResult,
	FieldRejectionReason,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the ClientVerificationResults queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByDate orders the results by the date field.
func ByDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDate, opts...).ToFunc()
}

// ByVerificationResult orders the results by the verification_result field.
func ByVerificationResult(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVerificationResult, opts...).ToFunc()
}

// ByRejectionReason orders the results by the rejection_reason field.
func ByRejectionReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRejectionReason, opts...).ToFunc()
}
