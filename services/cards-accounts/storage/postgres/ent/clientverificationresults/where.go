// Code generated by ent, DO NOT EDIT.

package clientverificationresults

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldUpdateTime, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldUserID, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldStatus, v))
}

// Date applies equality check predicate on the "date" field. It's identical to DateEQ.
func Date(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldDate, v))
}

// VerificationResult applies equality check predicate on the "verification_result" field. It's identical to VerificationResultEQ.
func VerificationResult(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldVerificationResult, v))
}

// RejectionReason applies equality check predicate on the "rejection_reason" field. It's identical to RejectionReasonEQ.
func RejectionReason(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldRejectionReason, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLTE(FieldUpdateTime, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLTE(FieldUserID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldContainsFold(FieldStatus, v))
}

// DateEQ applies the EQ predicate on the "date" field.
func DateEQ(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldDate, v))
}

// DateNEQ applies the NEQ predicate on the "date" field.
func DateNEQ(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNEQ(FieldDate, v))
}

// DateIn applies the In predicate on the "date" field.
func DateIn(vs ...time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIn(FieldDate, vs...))
}

// DateNotIn applies the NotIn predicate on the "date" field.
func DateNotIn(vs ...time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotIn(FieldDate, vs...))
}

// DateGT applies the GT predicate on the "date" field.
func DateGT(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGT(FieldDate, v))
}

// DateGTE applies the GTE predicate on the "date" field.
func DateGTE(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGTE(FieldDate, v))
}

// DateLT applies the LT predicate on the "date" field.
func DateLT(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLT(FieldDate, v))
}

// DateLTE applies the LTE predicate on the "date" field.
func DateLTE(v time.Time) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLTE(FieldDate, v))
}

// VerificationResultEQ applies the EQ predicate on the "verification_result" field.
func VerificationResultEQ(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldVerificationResult, v))
}

// VerificationResultNEQ applies the NEQ predicate on the "verification_result" field.
func VerificationResultNEQ(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNEQ(FieldVerificationResult, v))
}

// VerificationResultIn applies the In predicate on the "verification_result" field.
func VerificationResultIn(vs ...string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIn(FieldVerificationResult, vs...))
}

// VerificationResultNotIn applies the NotIn predicate on the "verification_result" field.
func VerificationResultNotIn(vs ...string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotIn(FieldVerificationResult, vs...))
}

// VerificationResultGT applies the GT predicate on the "verification_result" field.
func VerificationResultGT(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGT(FieldVerificationResult, v))
}

// VerificationResultGTE applies the GTE predicate on the "verification_result" field.
func VerificationResultGTE(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGTE(FieldVerificationResult, v))
}

// VerificationResultLT applies the LT predicate on the "verification_result" field.
func VerificationResultLT(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLT(FieldVerificationResult, v))
}

// VerificationResultLTE applies the LTE predicate on the "verification_result" field.
func VerificationResultLTE(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLTE(FieldVerificationResult, v))
}

// VerificationResultContains applies the Contains predicate on the "verification_result" field.
func VerificationResultContains(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldContains(FieldVerificationResult, v))
}

// VerificationResultHasPrefix applies the HasPrefix predicate on the "verification_result" field.
func VerificationResultHasPrefix(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldHasPrefix(FieldVerificationResult, v))
}

// VerificationResultHasSuffix applies the HasSuffix predicate on the "verification_result" field.
func VerificationResultHasSuffix(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldHasSuffix(FieldVerificationResult, v))
}

// VerificationResultEqualFold applies the EqualFold predicate on the "verification_result" field.
func VerificationResultEqualFold(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEqualFold(FieldVerificationResult, v))
}

// VerificationResultContainsFold applies the ContainsFold predicate on the "verification_result" field.
func VerificationResultContainsFold(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldContainsFold(FieldVerificationResult, v))
}

// RejectionReasonEQ applies the EQ predicate on the "rejection_reason" field.
func RejectionReasonEQ(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEQ(FieldRejectionReason, v))
}

// RejectionReasonNEQ applies the NEQ predicate on the "rejection_reason" field.
func RejectionReasonNEQ(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNEQ(FieldRejectionReason, v))
}

// RejectionReasonIn applies the In predicate on the "rejection_reason" field.
func RejectionReasonIn(vs ...string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIn(FieldRejectionReason, vs...))
}

// RejectionReasonNotIn applies the NotIn predicate on the "rejection_reason" field.
func RejectionReasonNotIn(vs ...string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotIn(FieldRejectionReason, vs...))
}

// RejectionReasonGT applies the GT predicate on the "rejection_reason" field.
func RejectionReasonGT(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGT(FieldRejectionReason, v))
}

// RejectionReasonGTE applies the GTE predicate on the "rejection_reason" field.
func RejectionReasonGTE(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldGTE(FieldRejectionReason, v))
}

// RejectionReasonLT applies the LT predicate on the "rejection_reason" field.
func RejectionReasonLT(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLT(FieldRejectionReason, v))
}

// RejectionReasonLTE applies the LTE predicate on the "rejection_reason" field.
func RejectionReasonLTE(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldLTE(FieldRejectionReason, v))
}

// RejectionReasonContains applies the Contains predicate on the "rejection_reason" field.
func RejectionReasonContains(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldContains(FieldRejectionReason, v))
}

// RejectionReasonHasPrefix applies the HasPrefix predicate on the "rejection_reason" field.
func RejectionReasonHasPrefix(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldHasPrefix(FieldRejectionReason, v))
}

// RejectionReasonHasSuffix applies the HasSuffix predicate on the "rejection_reason" field.
func RejectionReasonHasSuffix(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldHasSuffix(FieldRejectionReason, v))
}

// RejectionReasonIsNil applies the IsNil predicate on the "rejection_reason" field.
func RejectionReasonIsNil() predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldIsNull(FieldRejectionReason))
}

// RejectionReasonNotNil applies the NotNil predicate on the "rejection_reason" field.
func RejectionReasonNotNil() predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldNotNull(FieldRejectionReason))
}

// RejectionReasonEqualFold applies the EqualFold predicate on the "rejection_reason" field.
func RejectionReasonEqualFold(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldEqualFold(FieldRejectionReason, v))
}

// RejectionReasonContainsFold applies the ContainsFold predicate on the "rejection_reason" field.
func RejectionReasonContainsFold(v string) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.FieldContainsFold(FieldRejectionReason, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ClientVerificationResults) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ClientVerificationResults) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ClientVerificationResults) predicate.ClientVerificationResults {
	return predicate.ClientVerificationResults(sql.NotPredicates(p))
}
