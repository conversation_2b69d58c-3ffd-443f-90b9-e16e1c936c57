// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// FinContractUpdate is the builder for updating FinContract entities.
type FinContractUpdate struct {
	config
	hooks    []Hook
	mutation *FinContractMutation
}

// Where appends a list predicates to the FinContractUpdate builder.
func (_u *FinContractUpdate) Where(ps ...predicate.FinContract) *FinContractUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdateTime sets the "update_time" field.
func (_u *FinContractUpdate) SetUpdateTime(v time.Time) *FinContractUpdate {
	_u.mutation.SetUpdateTime(v)
	return _u
}

// SetContractType sets the "contract_type" field.
func (_u *FinContractUpdate) SetContractType(v fincontract.ContractType) *FinContractUpdate {
	_u.mutation.SetContractType(v)
	return _u
}

// SetNillableContractType sets the "contract_type" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableContractType(v *fincontract.ContractType) *FinContractUpdate {
	if v != nil {
		_u.SetContractType(*v)
	}
	return _u
}

// SetContractCurrency sets the "contract_currency" field.
func (_u *FinContractUpdate) SetContractCurrency(v fincontract.ContractCurrency) *FinContractUpdate {
	_u.mutation.SetContractCurrency(v)
	return _u
}

// SetNillableContractCurrency sets the "contract_currency" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableContractCurrency(v *fincontract.ContractCurrency) *FinContractUpdate {
	if v != nil {
		_u.SetContractCurrency(*v)
	}
	return _u
}

// SetIban sets the "iban" field.
func (_u *FinContractUpdate) SetIban(v string) *FinContractUpdate {
	_u.mutation.SetIban(v)
	return _u
}

// SetNillableIban sets the "iban" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableIban(v *string) *FinContractUpdate {
	if v != nil {
		_u.SetIban(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *FinContractUpdate) SetStatus(v fincontract.Status) *FinContractUpdate {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableStatus(v *fincontract.Status) *FinContractUpdate {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetCardID sets the "card_id" field.
func (_u *FinContractUpdate) SetCardID(v uuid.UUID) *FinContractUpdate {
	_u.mutation.SetCardID(v)
	return _u
}

// SetNillableCardID sets the "card_id" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableCardID(v *uuid.UUID) *FinContractUpdate {
	if v != nil {
		_u.SetCardID(*v)
	}
	return _u
}

// ClearCardID clears the value of the "card_id" field.
func (_u *FinContractUpdate) ClearCardID() *FinContractUpdate {
	_u.mutation.ClearCardID()
	return _u
}

// SetContractCode sets the "contract_code" field.
func (_u *FinContractUpdate) SetContractCode(v string) *FinContractUpdate {
	_u.mutation.SetContractCode(v)
	return _u
}

// SetNillableContractCode sets the "contract_code" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableContractCode(v *string) *FinContractUpdate {
	if v != nil {
		_u.SetContractCode(*v)
	}
	return _u
}

// ClearContractCode clears the value of the "contract_code" field.
func (_u *FinContractUpdate) ClearContractCode() *FinContractUpdate {
	_u.mutation.ClearContractCode()
	return _u
}

// SetContractDataOpen sets the "contract_data_open" field.
func (_u *FinContractUpdate) SetContractDataOpen(v time.Time) *FinContractUpdate {
	_u.mutation.SetContractDataOpen(v)
	return _u
}

// SetNillableContractDataOpen sets the "contract_data_open" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableContractDataOpen(v *time.Time) *FinContractUpdate {
	if v != nil {
		_u.SetContractDataOpen(*v)
	}
	return _u
}

// SetContractDataClose sets the "contract_data_close" field.
func (_u *FinContractUpdate) SetContractDataClose(v time.Time) *FinContractUpdate {
	_u.mutation.SetContractDataClose(v)
	return _u
}

// SetNillableContractDataClose sets the "contract_data_close" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableContractDataClose(v *time.Time) *FinContractUpdate {
	if v != nil {
		_u.SetContractDataClose(*v)
	}
	return _u
}

// ClearContractDataClose clears the value of the "contract_data_close" field.
func (_u *FinContractUpdate) ClearContractDataClose() *FinContractUpdate {
	_u.mutation.ClearContractDataClose()
	return _u
}

// SetUserID sets the "user_id" field.
func (_u *FinContractUpdate) SetUserID(v string) *FinContractUpdate {
	_u.mutation.SetUserID(v)
	return _u
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (_u *FinContractUpdate) SetNillableUserID(v *string) *FinContractUpdate {
	if v != nil {
		_u.SetUserID(*v)
	}
	return _u
}

// ClearUserID clears the value of the "user_id" field.
func (_u *FinContractUpdate) ClearUserID() *FinContractUpdate {
	_u.mutation.ClearUserID()
	return _u
}

// SetCardsID sets the "cards" edge to the Cards entity by ID.
func (_u *FinContractUpdate) SetCardsID(id uuid.UUID) *FinContractUpdate {
	_u.mutation.SetCardsID(id)
	return _u
}

// SetNillableCardsID sets the "cards" edge to the Cards entity by ID if the given value is not nil.
func (_u *FinContractUpdate) SetNillableCardsID(id *uuid.UUID) *FinContractUpdate {
	if id != nil {
		_u = _u.SetCardsID(*id)
	}
	return _u
}

// SetCards sets the "cards" edge to the Cards entity.
func (_u *FinContractUpdate) SetCards(v *Cards) *FinContractUpdate {
	return _u.SetCardsID(v.ID)
}

// Mutation returns the FinContractMutation object of the builder.
func (_u *FinContractUpdate) Mutation() *FinContractMutation {
	return _u.mutation
}

// ClearCards clears the "cards" edge to the Cards entity.
func (_u *FinContractUpdate) ClearCards() *FinContractUpdate {
	_u.mutation.ClearCards()
	return _u
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *FinContractUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *FinContractUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *FinContractUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *FinContractUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *FinContractUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := fincontract.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *FinContractUpdate) check() error {
	if v, ok := _u.mutation.ContractType(); ok {
		if err := fincontract.ContractTypeValidator(v); err != nil {
			return &ValidationError{Name: "contract_type", err: fmt.Errorf(`ent: validator failed for field "FinContract.contract_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.ContractCurrency(); ok {
		if err := fincontract.ContractCurrencyValidator(v); err != nil {
			return &ValidationError{Name: "contract_currency", err: fmt.Errorf(`ent: validator failed for field "FinContract.contract_currency": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Status(); ok {
		if err := fincontract.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "FinContract.status": %w`, err)}
		}
	}
	return nil
}

func (_u *FinContractUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(fincontract.Table, fincontract.Columns, sqlgraph.NewFieldSpec(fincontract.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(fincontract.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ContractType(); ok {
		_spec.SetField(fincontract.FieldContractType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.ContractCurrency(); ok {
		_spec.SetField(fincontract.FieldContractCurrency, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.Iban(); ok {
		_spec.SetField(fincontract.FieldIban, field.TypeString, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(fincontract.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.CardID(); ok {
		_spec.SetField(fincontract.FieldCardID, field.TypeUUID, value)
	}
	if _u.mutation.CardIDCleared() {
		_spec.ClearField(fincontract.FieldCardID, field.TypeUUID)
	}
	if value, ok := _u.mutation.ContractCode(); ok {
		_spec.SetField(fincontract.FieldContractCode, field.TypeString, value)
	}
	if _u.mutation.ContractCodeCleared() {
		_spec.ClearField(fincontract.FieldContractCode, field.TypeString)
	}
	if value, ok := _u.mutation.ContractDataOpen(); ok {
		_spec.SetField(fincontract.FieldContractDataOpen, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ContractDataClose(); ok {
		_spec.SetField(fincontract.FieldContractDataClose, field.TypeTime, value)
	}
	if _u.mutation.ContractDataCloseCleared() {
		_spec.ClearField(fincontract.FieldContractDataClose, field.TypeTime)
	}
	if value, ok := _u.mutation.UserID(); ok {
		_spec.SetField(fincontract.FieldUserID, field.TypeString, value)
	}
	if _u.mutation.UserIDCleared() {
		_spec.ClearField(fincontract.FieldUserID, field.TypeString)
	}
	if _u.mutation.CardsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   fincontract.CardsTable,
			Columns: []string{fincontract.CardsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cards.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.CardsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   fincontract.CardsTable,
			Columns: []string{fincontract.CardsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cards.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{fincontract.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// FinContractUpdateOne is the builder for updating a single FinContract entity.
type FinContractUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *FinContractMutation
}

// SetUpdateTime sets the "update_time" field.
func (_u *FinContractUpdateOne) SetUpdateTime(v time.Time) *FinContractUpdateOne {
	_u.mutation.SetUpdateTime(v)
	return _u
}

// SetContractType sets the "contract_type" field.
func (_u *FinContractUpdateOne) SetContractType(v fincontract.ContractType) *FinContractUpdateOne {
	_u.mutation.SetContractType(v)
	return _u
}

// SetNillableContractType sets the "contract_type" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableContractType(v *fincontract.ContractType) *FinContractUpdateOne {
	if v != nil {
		_u.SetContractType(*v)
	}
	return _u
}

// SetContractCurrency sets the "contract_currency" field.
func (_u *FinContractUpdateOne) SetContractCurrency(v fincontract.ContractCurrency) *FinContractUpdateOne {
	_u.mutation.SetContractCurrency(v)
	return _u
}

// SetNillableContractCurrency sets the "contract_currency" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableContractCurrency(v *fincontract.ContractCurrency) *FinContractUpdateOne {
	if v != nil {
		_u.SetContractCurrency(*v)
	}
	return _u
}

// SetIban sets the "iban" field.
func (_u *FinContractUpdateOne) SetIban(v string) *FinContractUpdateOne {
	_u.mutation.SetIban(v)
	return _u
}

// SetNillableIban sets the "iban" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableIban(v *string) *FinContractUpdateOne {
	if v != nil {
		_u.SetIban(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *FinContractUpdateOne) SetStatus(v fincontract.Status) *FinContractUpdateOne {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableStatus(v *fincontract.Status) *FinContractUpdateOne {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetCardID sets the "card_id" field.
func (_u *FinContractUpdateOne) SetCardID(v uuid.UUID) *FinContractUpdateOne {
	_u.mutation.SetCardID(v)
	return _u
}

// SetNillableCardID sets the "card_id" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableCardID(v *uuid.UUID) *FinContractUpdateOne {
	if v != nil {
		_u.SetCardID(*v)
	}
	return _u
}

// ClearCardID clears the value of the "card_id" field.
func (_u *FinContractUpdateOne) ClearCardID() *FinContractUpdateOne {
	_u.mutation.ClearCardID()
	return _u
}

// SetContractCode sets the "contract_code" field.
func (_u *FinContractUpdateOne) SetContractCode(v string) *FinContractUpdateOne {
	_u.mutation.SetContractCode(v)
	return _u
}

// SetNillableContractCode sets the "contract_code" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableContractCode(v *string) *FinContractUpdateOne {
	if v != nil {
		_u.SetContractCode(*v)
	}
	return _u
}

// ClearContractCode clears the value of the "contract_code" field.
func (_u *FinContractUpdateOne) ClearContractCode() *FinContractUpdateOne {
	_u.mutation.ClearContractCode()
	return _u
}

// SetContractDataOpen sets the "contract_data_open" field.
func (_u *FinContractUpdateOne) SetContractDataOpen(v time.Time) *FinContractUpdateOne {
	_u.mutation.SetContractDataOpen(v)
	return _u
}

// SetNillableContractDataOpen sets the "contract_data_open" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableContractDataOpen(v *time.Time) *FinContractUpdateOne {
	if v != nil {
		_u.SetContractDataOpen(*v)
	}
	return _u
}

// SetContractDataClose sets the "contract_data_close" field.
func (_u *FinContractUpdateOne) SetContractDataClose(v time.Time) *FinContractUpdateOne {
	_u.mutation.SetContractDataClose(v)
	return _u
}

// SetNillableContractDataClose sets the "contract_data_close" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableContractDataClose(v *time.Time) *FinContractUpdateOne {
	if v != nil {
		_u.SetContractDataClose(*v)
	}
	return _u
}

// ClearContractDataClose clears the value of the "contract_data_close" field.
func (_u *FinContractUpdateOne) ClearContractDataClose() *FinContractUpdateOne {
	_u.mutation.ClearContractDataClose()
	return _u
}

// SetUserID sets the "user_id" field.
func (_u *FinContractUpdateOne) SetUserID(v string) *FinContractUpdateOne {
	_u.mutation.SetUserID(v)
	return _u
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableUserID(v *string) *FinContractUpdateOne {
	if v != nil {
		_u.SetUserID(*v)
	}
	return _u
}

// ClearUserID clears the value of the "user_id" field.
func (_u *FinContractUpdateOne) ClearUserID() *FinContractUpdateOne {
	_u.mutation.ClearUserID()
	return _u
}

// SetCardsID sets the "cards" edge to the Cards entity by ID.
func (_u *FinContractUpdateOne) SetCardsID(id uuid.UUID) *FinContractUpdateOne {
	_u.mutation.SetCardsID(id)
	return _u
}

// SetNillableCardsID sets the "cards" edge to the Cards entity by ID if the given value is not nil.
func (_u *FinContractUpdateOne) SetNillableCardsID(id *uuid.UUID) *FinContractUpdateOne {
	if id != nil {
		_u = _u.SetCardsID(*id)
	}
	return _u
}

// SetCards sets the "cards" edge to the Cards entity.
func (_u *FinContractUpdateOne) SetCards(v *Cards) *FinContractUpdateOne {
	return _u.SetCardsID(v.ID)
}

// Mutation returns the FinContractMutation object of the builder.
func (_u *FinContractUpdateOne) Mutation() *FinContractMutation {
	return _u.mutation
}

// ClearCards clears the "cards" edge to the Cards entity.
func (_u *FinContractUpdateOne) ClearCards() *FinContractUpdateOne {
	_u.mutation.ClearCards()
	return _u
}

// Where appends a list predicates to the FinContractUpdate builder.
func (_u *FinContractUpdateOne) Where(ps ...predicate.FinContract) *FinContractUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *FinContractUpdateOne) Select(field string, fields ...string) *FinContractUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated FinContract entity.
func (_u *FinContractUpdateOne) Save(ctx context.Context) (*FinContract, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *FinContractUpdateOne) SaveX(ctx context.Context) *FinContract {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *FinContractUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *FinContractUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *FinContractUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := fincontract.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *FinContractUpdateOne) check() error {
	if v, ok := _u.mutation.ContractType(); ok {
		if err := fincontract.ContractTypeValidator(v); err != nil {
			return &ValidationError{Name: "contract_type", err: fmt.Errorf(`ent: validator failed for field "FinContract.contract_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.ContractCurrency(); ok {
		if err := fincontract.ContractCurrencyValidator(v); err != nil {
			return &ValidationError{Name: "contract_currency", err: fmt.Errorf(`ent: validator failed for field "FinContract.contract_currency": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Status(); ok {
		if err := fincontract.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "FinContract.status": %w`, err)}
		}
	}
	return nil
}

func (_u *FinContractUpdateOne) sqlSave(ctx context.Context) (_node *FinContract, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(fincontract.Table, fincontract.Columns, sqlgraph.NewFieldSpec(fincontract.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "FinContract.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, fincontract.FieldID)
		for _, f := range fields {
			if !fincontract.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != fincontract.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(fincontract.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ContractType(); ok {
		_spec.SetField(fincontract.FieldContractType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.ContractCurrency(); ok {
		_spec.SetField(fincontract.FieldContractCurrency, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.Iban(); ok {
		_spec.SetField(fincontract.FieldIban, field.TypeString, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(fincontract.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.CardID(); ok {
		_spec.SetField(fincontract.FieldCardID, field.TypeUUID, value)
	}
	if _u.mutation.CardIDCleared() {
		_spec.ClearField(fincontract.FieldCardID, field.TypeUUID)
	}
	if value, ok := _u.mutation.ContractCode(); ok {
		_spec.SetField(fincontract.FieldContractCode, field.TypeString, value)
	}
	if _u.mutation.ContractCodeCleared() {
		_spec.ClearField(fincontract.FieldContractCode, field.TypeString)
	}
	if value, ok := _u.mutation.ContractDataOpen(); ok {
		_spec.SetField(fincontract.FieldContractDataOpen, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ContractDataClose(); ok {
		_spec.SetField(fincontract.FieldContractDataClose, field.TypeTime, value)
	}
	if _u.mutation.ContractDataCloseCleared() {
		_spec.ClearField(fincontract.FieldContractDataClose, field.TypeTime)
	}
	if value, ok := _u.mutation.UserID(); ok {
		_spec.SetField(fincontract.FieldUserID, field.TypeString, value)
	}
	if _u.mutation.UserIDCleared() {
		_spec.ClearField(fincontract.FieldUserID, field.TypeString)
	}
	if _u.mutation.CardsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   fincontract.CardsTable,
			Columns: []string{fincontract.CardsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cards.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.CardsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   fincontract.CardsTable,
			Columns: []string{fincontract.CardsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cards.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &FinContract{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{fincontract.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
