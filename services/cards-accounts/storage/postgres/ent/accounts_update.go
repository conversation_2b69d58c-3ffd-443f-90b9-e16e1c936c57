// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// AccountsUpdate is the builder for updating Accounts entities.
type AccountsUpdate struct {
	config
	hooks    []Hook
	mutation *AccountsMutation
}

// Where appends a list predicates to the AccountsUpdate builder.
func (_u *AccountsUpdate) Where(ps ...predicate.Accounts) *AccountsUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdateTime sets the "update_time" field.
func (_u *AccountsUpdate) SetUpdateTime(v time.Time) *AccountsUpdate {
	_u.mutation.SetUpdateTime(v)
	return _u
}

// SetClientIin sets the "client_iin" field.
func (_u *AccountsUpdate) SetClientIin(v string) *AccountsUpdate {
	_u.mutation.SetClientIin(v)
	return _u
}

// SetNillableClientIin sets the "client_iin" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableClientIin(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetClientIin(*v)
	}
	return _u
}

// SetClientCode sets the "client_code" field.
func (_u *AccountsUpdate) SetClientCode(v string) *AccountsUpdate {
	_u.mutation.SetClientCode(v)
	return _u
}

// SetNillableClientCode sets the "client_code" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableClientCode(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetClientCode(*v)
	}
	return _u
}

// SetClientName sets the "client_name" field.
func (_u *AccountsUpdate) SetClientName(v string) *AccountsUpdate {
	_u.mutation.SetClientName(v)
	return _u
}

// SetNillableClientName sets the "client_name" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableClientName(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetClientName(*v)
	}
	return _u
}

// SetTitle sets the "title" field.
func (_u *AccountsUpdate) SetTitle(v string) *AccountsUpdate {
	_u.mutation.SetTitle(v)
	return _u
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableTitle(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetTitle(*v)
	}
	return _u
}

// ClearTitle clears the value of the "title" field.
func (_u *AccountsUpdate) ClearTitle() *AccountsUpdate {
	_u.mutation.ClearTitle()
	return _u
}

// SetShortName sets the "short_name" field.
func (_u *AccountsUpdate) SetShortName(v string) *AccountsUpdate {
	_u.mutation.SetShortName(v)
	return _u
}

// SetNillableShortName sets the "short_name" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableShortName(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetShortName(*v)
	}
	return _u
}

// ClearShortName clears the value of the "short_name" field.
func (_u *AccountsUpdate) ClearShortName() *AccountsUpdate {
	_u.mutation.ClearShortName()
	return _u
}

// SetCurrency sets the "currency" field.
func (_u *AccountsUpdate) SetCurrency(v string) *AccountsUpdate {
	_u.mutation.SetCurrency(v)
	return _u
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableCurrency(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetCurrency(*v)
	}
	return _u
}

// SetIban sets the "iban" field.
func (_u *AccountsUpdate) SetIban(v string) *AccountsUpdate {
	_u.mutation.SetIban(v)
	return _u
}

// SetNillableIban sets the "iban" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableIban(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetIban(*v)
	}
	return _u
}

// SetType sets the "type" field.
func (_u *AccountsUpdate) SetType(v string) *AccountsUpdate {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableType(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *AccountsUpdate) SetStatus(v string) *AccountsUpdate {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableStatus(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetDateOpened sets the "date_opened" field.
func (_u *AccountsUpdate) SetDateOpened(v string) *AccountsUpdate {
	_u.mutation.SetDateOpened(v)
	return _u
}

// SetNillableDateOpened sets the "date_opened" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableDateOpened(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetDateOpened(*v)
	}
	return _u
}

// SetBalance sets the "balance" field.
func (_u *AccountsUpdate) SetBalance(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.ResetBalance()
	_u.mutation.SetBalance(v)
	return _u
}

// SetNillableBalance sets the "balance" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableBalance(v *decimal.Decimal) *AccountsUpdate {
	if v != nil {
		_u.SetBalance(*v)
	}
	return _u
}

// AddBalance adds value to the "balance" field.
func (_u *AccountsUpdate) AddBalance(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.AddBalance(v)
	return _u
}

// SetBalanceNatival sets the "balance_natival" field.
func (_u *AccountsUpdate) SetBalanceNatival(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.ResetBalanceNatival()
	_u.mutation.SetBalanceNatival(v)
	return _u
}

// SetNillableBalanceNatival sets the "balance_natival" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableBalanceNatival(v *decimal.Decimal) *AccountsUpdate {
	if v != nil {
		_u.SetBalanceNatival(*v)
	}
	return _u
}

// AddBalanceNatival adds value to the "balance_natival" field.
func (_u *AccountsUpdate) AddBalanceNatival(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.AddBalanceNatival(v)
	return _u
}

// SetBlockedBalance sets the "blocked_balance" field.
func (_u *AccountsUpdate) SetBlockedBalance(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.ResetBlockedBalance()
	_u.mutation.SetBlockedBalance(v)
	return _u
}

// SetNillableBlockedBalance sets the "blocked_balance" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableBlockedBalance(v *decimal.Decimal) *AccountsUpdate {
	if v != nil {
		_u.SetBlockedBalance(*v)
	}
	return _u
}

// AddBlockedBalance adds value to the "blocked_balance" field.
func (_u *AccountsUpdate) AddBlockedBalance(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.AddBlockedBalance(v)
	return _u
}

// SetAvailableBalance sets the "available_balance" field.
func (_u *AccountsUpdate) SetAvailableBalance(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.ResetAvailableBalance()
	_u.mutation.SetAvailableBalance(v)
	return _u
}

// SetNillableAvailableBalance sets the "available_balance" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableAvailableBalance(v *decimal.Decimal) *AccountsUpdate {
	if v != nil {
		_u.SetAvailableBalance(*v)
	}
	return _u
}

// AddAvailableBalance adds value to the "available_balance" field.
func (_u *AccountsUpdate) AddAvailableBalance(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.AddAvailableBalance(v)
	return _u
}

// SetDateClosed sets the "date_closed" field.
func (_u *AccountsUpdate) SetDateClosed(v string) *AccountsUpdate {
	_u.mutation.SetDateClosed(v)
	return _u
}

// SetNillableDateClosed sets the "date_closed" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableDateClosed(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetDateClosed(*v)
	}
	return _u
}

// SetArrestBlocking sets the "arrest_blocking" field.
func (_u *AccountsUpdate) SetArrestBlocking(v string) *AccountsUpdate {
	_u.mutation.SetArrestBlocking(v)
	return _u
}

// SetNillableArrestBlocking sets the "arrest_blocking" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableArrestBlocking(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetArrestBlocking(*v)
	}
	return _u
}

// SetArrestDebtAmount sets the "arrest_debt_amount" field.
func (_u *AccountsUpdate) SetArrestDebtAmount(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.ResetArrestDebtAmount()
	_u.mutation.SetArrestDebtAmount(v)
	return _u
}

// SetNillableArrestDebtAmount sets the "arrest_debt_amount" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableArrestDebtAmount(v *decimal.Decimal) *AccountsUpdate {
	if v != nil {
		_u.SetArrestDebtAmount(*v)
	}
	return _u
}

// AddArrestDebtAmount adds value to the "arrest_debt_amount" field.
func (_u *AccountsUpdate) AddArrestDebtAmount(v decimal.Decimal) *AccountsUpdate {
	_u.mutation.AddArrestDebtAmount(v)
	return _u
}

// SetHasArrest sets the "has_arrest" field.
func (_u *AccountsUpdate) SetHasArrest(v string) *AccountsUpdate {
	_u.mutation.SetHasArrest(v)
	return _u
}

// SetNillableHasArrest sets the "has_arrest" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableHasArrest(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetHasArrest(*v)
	}
	return _u
}

// SetOrigin sets the "origin" field.
func (_u *AccountsUpdate) SetOrigin(v string) *AccountsUpdate {
	_u.mutation.SetOrigin(v)
	return _u
}

// SetNillableOrigin sets the "origin" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableOrigin(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetOrigin(*v)
	}
	return _u
}

// SetDeaReferenceID sets the "dea_reference_id" field.
func (_u *AccountsUpdate) SetDeaReferenceID(v string) *AccountsUpdate {
	_u.mutation.SetDeaReferenceID(v)
	return _u
}

// SetNillableDeaReferenceID sets the "dea_reference_id" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableDeaReferenceID(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetDeaReferenceID(*v)
	}
	return _u
}

// SetClientType sets the "client_type" field.
func (_u *AccountsUpdate) SetClientType(v string) *AccountsUpdate {
	_u.mutation.SetClientType(v)
	return _u
}

// SetNillableClientType sets the "client_type" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableClientType(v *string) *AccountsUpdate {
	if v != nil {
		_u.SetClientType(*v)
	}
	return _u
}

// SetIsMain sets the "is_main" field.
func (_u *AccountsUpdate) SetIsMain(v bool) *AccountsUpdate {
	_u.mutation.SetIsMain(v)
	return _u
}

// SetNillableIsMain sets the "is_main" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableIsMain(v *bool) *AccountsUpdate {
	if v != nil {
		_u.SetIsMain(*v)
	}
	return _u
}

// SetAccessionAccount sets the "accession_account" field.
func (_u *AccountsUpdate) SetAccessionAccount(v bool) *AccountsUpdate {
	_u.mutation.SetAccessionAccount(v)
	return _u
}

// SetNillableAccessionAccount sets the "accession_account" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableAccessionAccount(v *bool) *AccountsUpdate {
	if v != nil {
		_u.SetAccessionAccount(*v)
	}
	return _u
}

// SetIsArrested sets the "is_arrested" field.
func (_u *AccountsUpdate) SetIsArrested(v bool) *AccountsUpdate {
	_u.mutation.SetIsArrested(v)
	return _u
}

// SetNillableIsArrested sets the "is_arrested" field if the given value is not nil.
func (_u *AccountsUpdate) SetNillableIsArrested(v *bool) *AccountsUpdate {
	if v != nil {
		_u.SetIsArrested(*v)
	}
	return _u
}

// Mutation returns the AccountsMutation object of the builder.
func (_u *AccountsUpdate) Mutation() *AccountsMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *AccountsUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *AccountsUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *AccountsUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *AccountsUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *AccountsUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := accounts.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

func (_u *AccountsUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(accounts.Table, accounts.Columns, sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(accounts.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ClientIin(); ok {
		_spec.SetField(accounts.FieldClientIin, field.TypeString, value)
	}
	if value, ok := _u.mutation.ClientCode(); ok {
		_spec.SetField(accounts.FieldClientCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.ClientName(); ok {
		_spec.SetField(accounts.FieldClientName, field.TypeString, value)
	}
	if value, ok := _u.mutation.Title(); ok {
		_spec.SetField(accounts.FieldTitle, field.TypeString, value)
	}
	if _u.mutation.TitleCleared() {
		_spec.ClearField(accounts.FieldTitle, field.TypeString)
	}
	if value, ok := _u.mutation.ShortName(); ok {
		_spec.SetField(accounts.FieldShortName, field.TypeString, value)
	}
	if _u.mutation.ShortNameCleared() {
		_spec.ClearField(accounts.FieldShortName, field.TypeString)
	}
	if value, ok := _u.mutation.Currency(); ok {
		_spec.SetField(accounts.FieldCurrency, field.TypeString, value)
	}
	if value, ok := _u.mutation.Iban(); ok {
		_spec.SetField(accounts.FieldIban, field.TypeString, value)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(accounts.FieldType, field.TypeString, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(accounts.FieldStatus, field.TypeString, value)
	}
	if value, ok := _u.mutation.DateOpened(); ok {
		_spec.SetField(accounts.FieldDateOpened, field.TypeString, value)
	}
	if value, ok := _u.mutation.Balance(); ok {
		_spec.SetField(accounts.FieldBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedBalance(); ok {
		_spec.AddField(accounts.FieldBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.BalanceNatival(); ok {
		_spec.SetField(accounts.FieldBalanceNatival, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedBalanceNatival(); ok {
		_spec.AddField(accounts.FieldBalanceNatival, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.BlockedBalance(); ok {
		_spec.SetField(accounts.FieldBlockedBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedBlockedBalance(); ok {
		_spec.AddField(accounts.FieldBlockedBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AvailableBalance(); ok {
		_spec.SetField(accounts.FieldAvailableBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedAvailableBalance(); ok {
		_spec.AddField(accounts.FieldAvailableBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.DateClosed(); ok {
		_spec.SetField(accounts.FieldDateClosed, field.TypeString, value)
	}
	if value, ok := _u.mutation.ArrestBlocking(); ok {
		_spec.SetField(accounts.FieldArrestBlocking, field.TypeString, value)
	}
	if value, ok := _u.mutation.ArrestDebtAmount(); ok {
		_spec.SetField(accounts.FieldArrestDebtAmount, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedArrestDebtAmount(); ok {
		_spec.AddField(accounts.FieldArrestDebtAmount, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.HasArrest(); ok {
		_spec.SetField(accounts.FieldHasArrest, field.TypeString, value)
	}
	if value, ok := _u.mutation.Origin(); ok {
		_spec.SetField(accounts.FieldOrigin, field.TypeString, value)
	}
	if value, ok := _u.mutation.DeaReferenceID(); ok {
		_spec.SetField(accounts.FieldDeaReferenceID, field.TypeString, value)
	}
	if value, ok := _u.mutation.ClientType(); ok {
		_spec.SetField(accounts.FieldClientType, field.TypeString, value)
	}
	if value, ok := _u.mutation.IsMain(); ok {
		_spec.SetField(accounts.FieldIsMain, field.TypeBool, value)
	}
	if value, ok := _u.mutation.AccessionAccount(); ok {
		_spec.SetField(accounts.FieldAccessionAccount, field.TypeBool, value)
	}
	if value, ok := _u.mutation.IsArrested(); ok {
		_spec.SetField(accounts.FieldIsArrested, field.TypeBool, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{accounts.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// AccountsUpdateOne is the builder for updating a single Accounts entity.
type AccountsUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AccountsMutation
}

// SetUpdateTime sets the "update_time" field.
func (_u *AccountsUpdateOne) SetUpdateTime(v time.Time) *AccountsUpdateOne {
	_u.mutation.SetUpdateTime(v)
	return _u
}

// SetClientIin sets the "client_iin" field.
func (_u *AccountsUpdateOne) SetClientIin(v string) *AccountsUpdateOne {
	_u.mutation.SetClientIin(v)
	return _u
}

// SetNillableClientIin sets the "client_iin" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableClientIin(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetClientIin(*v)
	}
	return _u
}

// SetClientCode sets the "client_code" field.
func (_u *AccountsUpdateOne) SetClientCode(v string) *AccountsUpdateOne {
	_u.mutation.SetClientCode(v)
	return _u
}

// SetNillableClientCode sets the "client_code" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableClientCode(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetClientCode(*v)
	}
	return _u
}

// SetClientName sets the "client_name" field.
func (_u *AccountsUpdateOne) SetClientName(v string) *AccountsUpdateOne {
	_u.mutation.SetClientName(v)
	return _u
}

// SetNillableClientName sets the "client_name" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableClientName(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetClientName(*v)
	}
	return _u
}

// SetTitle sets the "title" field.
func (_u *AccountsUpdateOne) SetTitle(v string) *AccountsUpdateOne {
	_u.mutation.SetTitle(v)
	return _u
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableTitle(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetTitle(*v)
	}
	return _u
}

// ClearTitle clears the value of the "title" field.
func (_u *AccountsUpdateOne) ClearTitle() *AccountsUpdateOne {
	_u.mutation.ClearTitle()
	return _u
}

// SetShortName sets the "short_name" field.
func (_u *AccountsUpdateOne) SetShortName(v string) *AccountsUpdateOne {
	_u.mutation.SetShortName(v)
	return _u
}

// SetNillableShortName sets the "short_name" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableShortName(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetShortName(*v)
	}
	return _u
}

// ClearShortName clears the value of the "short_name" field.
func (_u *AccountsUpdateOne) ClearShortName() *AccountsUpdateOne {
	_u.mutation.ClearShortName()
	return _u
}

// SetCurrency sets the "currency" field.
func (_u *AccountsUpdateOne) SetCurrency(v string) *AccountsUpdateOne {
	_u.mutation.SetCurrency(v)
	return _u
}

// SetNillableCurrency sets the "currency" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableCurrency(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetCurrency(*v)
	}
	return _u
}

// SetIban sets the "iban" field.
func (_u *AccountsUpdateOne) SetIban(v string) *AccountsUpdateOne {
	_u.mutation.SetIban(v)
	return _u
}

// SetNillableIban sets the "iban" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableIban(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetIban(*v)
	}
	return _u
}

// SetType sets the "type" field.
func (_u *AccountsUpdateOne) SetType(v string) *AccountsUpdateOne {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableType(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *AccountsUpdateOne) SetStatus(v string) *AccountsUpdateOne {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableStatus(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetDateOpened sets the "date_opened" field.
func (_u *AccountsUpdateOne) SetDateOpened(v string) *AccountsUpdateOne {
	_u.mutation.SetDateOpened(v)
	return _u
}

// SetNillableDateOpened sets the "date_opened" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableDateOpened(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetDateOpened(*v)
	}
	return _u
}

// SetBalance sets the "balance" field.
func (_u *AccountsUpdateOne) SetBalance(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.ResetBalance()
	_u.mutation.SetBalance(v)
	return _u
}

// SetNillableBalance sets the "balance" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableBalance(v *decimal.Decimal) *AccountsUpdateOne {
	if v != nil {
		_u.SetBalance(*v)
	}
	return _u
}

// AddBalance adds value to the "balance" field.
func (_u *AccountsUpdateOne) AddBalance(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.AddBalance(v)
	return _u
}

// SetBalanceNatival sets the "balance_natival" field.
func (_u *AccountsUpdateOne) SetBalanceNatival(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.ResetBalanceNatival()
	_u.mutation.SetBalanceNatival(v)
	return _u
}

// SetNillableBalanceNatival sets the "balance_natival" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableBalanceNatival(v *decimal.Decimal) *AccountsUpdateOne {
	if v != nil {
		_u.SetBalanceNatival(*v)
	}
	return _u
}

// AddBalanceNatival adds value to the "balance_natival" field.
func (_u *AccountsUpdateOne) AddBalanceNatival(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.AddBalanceNatival(v)
	return _u
}

// SetBlockedBalance sets the "blocked_balance" field.
func (_u *AccountsUpdateOne) SetBlockedBalance(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.ResetBlockedBalance()
	_u.mutation.SetBlockedBalance(v)
	return _u
}

// SetNillableBlockedBalance sets the "blocked_balance" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableBlockedBalance(v *decimal.Decimal) *AccountsUpdateOne {
	if v != nil {
		_u.SetBlockedBalance(*v)
	}
	return _u
}

// AddBlockedBalance adds value to the "blocked_balance" field.
func (_u *AccountsUpdateOne) AddBlockedBalance(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.AddBlockedBalance(v)
	return _u
}

// SetAvailableBalance sets the "available_balance" field.
func (_u *AccountsUpdateOne) SetAvailableBalance(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.ResetAvailableBalance()
	_u.mutation.SetAvailableBalance(v)
	return _u
}

// SetNillableAvailableBalance sets the "available_balance" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableAvailableBalance(v *decimal.Decimal) *AccountsUpdateOne {
	if v != nil {
		_u.SetAvailableBalance(*v)
	}
	return _u
}

// AddAvailableBalance adds value to the "available_balance" field.
func (_u *AccountsUpdateOne) AddAvailableBalance(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.AddAvailableBalance(v)
	return _u
}

// SetDateClosed sets the "date_closed" field.
func (_u *AccountsUpdateOne) SetDateClosed(v string) *AccountsUpdateOne {
	_u.mutation.SetDateClosed(v)
	return _u
}

// SetNillableDateClosed sets the "date_closed" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableDateClosed(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetDateClosed(*v)
	}
	return _u
}

// SetArrestBlocking sets the "arrest_blocking" field.
func (_u *AccountsUpdateOne) SetArrestBlocking(v string) *AccountsUpdateOne {
	_u.mutation.SetArrestBlocking(v)
	return _u
}

// SetNillableArrestBlocking sets the "arrest_blocking" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableArrestBlocking(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetArrestBlocking(*v)
	}
	return _u
}

// SetArrestDebtAmount sets the "arrest_debt_amount" field.
func (_u *AccountsUpdateOne) SetArrestDebtAmount(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.ResetArrestDebtAmount()
	_u.mutation.SetArrestDebtAmount(v)
	return _u
}

// SetNillableArrestDebtAmount sets the "arrest_debt_amount" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableArrestDebtAmount(v *decimal.Decimal) *AccountsUpdateOne {
	if v != nil {
		_u.SetArrestDebtAmount(*v)
	}
	return _u
}

// AddArrestDebtAmount adds value to the "arrest_debt_amount" field.
func (_u *AccountsUpdateOne) AddArrestDebtAmount(v decimal.Decimal) *AccountsUpdateOne {
	_u.mutation.AddArrestDebtAmount(v)
	return _u
}

// SetHasArrest sets the "has_arrest" field.
func (_u *AccountsUpdateOne) SetHasArrest(v string) *AccountsUpdateOne {
	_u.mutation.SetHasArrest(v)
	return _u
}

// SetNillableHasArrest sets the "has_arrest" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableHasArrest(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetHasArrest(*v)
	}
	return _u
}

// SetOrigin sets the "origin" field.
func (_u *AccountsUpdateOne) SetOrigin(v string) *AccountsUpdateOne {
	_u.mutation.SetOrigin(v)
	return _u
}

// SetNillableOrigin sets the "origin" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableOrigin(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetOrigin(*v)
	}
	return _u
}

// SetDeaReferenceID sets the "dea_reference_id" field.
func (_u *AccountsUpdateOne) SetDeaReferenceID(v string) *AccountsUpdateOne {
	_u.mutation.SetDeaReferenceID(v)
	return _u
}

// SetNillableDeaReferenceID sets the "dea_reference_id" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableDeaReferenceID(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetDeaReferenceID(*v)
	}
	return _u
}

// SetClientType sets the "client_type" field.
func (_u *AccountsUpdateOne) SetClientType(v string) *AccountsUpdateOne {
	_u.mutation.SetClientType(v)
	return _u
}

// SetNillableClientType sets the "client_type" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableClientType(v *string) *AccountsUpdateOne {
	if v != nil {
		_u.SetClientType(*v)
	}
	return _u
}

// SetIsMain sets the "is_main" field.
func (_u *AccountsUpdateOne) SetIsMain(v bool) *AccountsUpdateOne {
	_u.mutation.SetIsMain(v)
	return _u
}

// SetNillableIsMain sets the "is_main" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableIsMain(v *bool) *AccountsUpdateOne {
	if v != nil {
		_u.SetIsMain(*v)
	}
	return _u
}

// SetAccessionAccount sets the "accession_account" field.
func (_u *AccountsUpdateOne) SetAccessionAccount(v bool) *AccountsUpdateOne {
	_u.mutation.SetAccessionAccount(v)
	return _u
}

// SetNillableAccessionAccount sets the "accession_account" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableAccessionAccount(v *bool) *AccountsUpdateOne {
	if v != nil {
		_u.SetAccessionAccount(*v)
	}
	return _u
}

// SetIsArrested sets the "is_arrested" field.
func (_u *AccountsUpdateOne) SetIsArrested(v bool) *AccountsUpdateOne {
	_u.mutation.SetIsArrested(v)
	return _u
}

// SetNillableIsArrested sets the "is_arrested" field if the given value is not nil.
func (_u *AccountsUpdateOne) SetNillableIsArrested(v *bool) *AccountsUpdateOne {
	if v != nil {
		_u.SetIsArrested(*v)
	}
	return _u
}

// Mutation returns the AccountsMutation object of the builder.
func (_u *AccountsUpdateOne) Mutation() *AccountsMutation {
	return _u.mutation
}

// Where appends a list predicates to the AccountsUpdate builder.
func (_u *AccountsUpdateOne) Where(ps ...predicate.Accounts) *AccountsUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *AccountsUpdateOne) Select(field string, fields ...string) *AccountsUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Accounts entity.
func (_u *AccountsUpdateOne) Save(ctx context.Context) (*Accounts, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *AccountsUpdateOne) SaveX(ctx context.Context) *Accounts {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *AccountsUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *AccountsUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *AccountsUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := accounts.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

func (_u *AccountsUpdateOne) sqlSave(ctx context.Context) (_node *Accounts, err error) {
	_spec := sqlgraph.NewUpdateSpec(accounts.Table, accounts.Columns, sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Accounts.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, accounts.FieldID)
		for _, f := range fields {
			if !accounts.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != accounts.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(accounts.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ClientIin(); ok {
		_spec.SetField(accounts.FieldClientIin, field.TypeString, value)
	}
	if value, ok := _u.mutation.ClientCode(); ok {
		_spec.SetField(accounts.FieldClientCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.ClientName(); ok {
		_spec.SetField(accounts.FieldClientName, field.TypeString, value)
	}
	if value, ok := _u.mutation.Title(); ok {
		_spec.SetField(accounts.FieldTitle, field.TypeString, value)
	}
	if _u.mutation.TitleCleared() {
		_spec.ClearField(accounts.FieldTitle, field.TypeString)
	}
	if value, ok := _u.mutation.ShortName(); ok {
		_spec.SetField(accounts.FieldShortName, field.TypeString, value)
	}
	if _u.mutation.ShortNameCleared() {
		_spec.ClearField(accounts.FieldShortName, field.TypeString)
	}
	if value, ok := _u.mutation.Currency(); ok {
		_spec.SetField(accounts.FieldCurrency, field.TypeString, value)
	}
	if value, ok := _u.mutation.Iban(); ok {
		_spec.SetField(accounts.FieldIban, field.TypeString, value)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(accounts.FieldType, field.TypeString, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(accounts.FieldStatus, field.TypeString, value)
	}
	if value, ok := _u.mutation.DateOpened(); ok {
		_spec.SetField(accounts.FieldDateOpened, field.TypeString, value)
	}
	if value, ok := _u.mutation.Balance(); ok {
		_spec.SetField(accounts.FieldBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedBalance(); ok {
		_spec.AddField(accounts.FieldBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.BalanceNatival(); ok {
		_spec.SetField(accounts.FieldBalanceNatival, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedBalanceNatival(); ok {
		_spec.AddField(accounts.FieldBalanceNatival, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.BlockedBalance(); ok {
		_spec.SetField(accounts.FieldBlockedBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedBlockedBalance(); ok {
		_spec.AddField(accounts.FieldBlockedBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AvailableBalance(); ok {
		_spec.SetField(accounts.FieldAvailableBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedAvailableBalance(); ok {
		_spec.AddField(accounts.FieldAvailableBalance, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.DateClosed(); ok {
		_spec.SetField(accounts.FieldDateClosed, field.TypeString, value)
	}
	if value, ok := _u.mutation.ArrestBlocking(); ok {
		_spec.SetField(accounts.FieldArrestBlocking, field.TypeString, value)
	}
	if value, ok := _u.mutation.ArrestDebtAmount(); ok {
		_spec.SetField(accounts.FieldArrestDebtAmount, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.AddedArrestDebtAmount(); ok {
		_spec.AddField(accounts.FieldArrestDebtAmount, field.TypeFloat64, value)
	}
	if value, ok := _u.mutation.HasArrest(); ok {
		_spec.SetField(accounts.FieldHasArrest, field.TypeString, value)
	}
	if value, ok := _u.mutation.Origin(); ok {
		_spec.SetField(accounts.FieldOrigin, field.TypeString, value)
	}
	if value, ok := _u.mutation.DeaReferenceID(); ok {
		_spec.SetField(accounts.FieldDeaReferenceID, field.TypeString, value)
	}
	if value, ok := _u.mutation.ClientType(); ok {
		_spec.SetField(accounts.FieldClientType, field.TypeString, value)
	}
	if value, ok := _u.mutation.IsMain(); ok {
		_spec.SetField(accounts.FieldIsMain, field.TypeBool, value)
	}
	if value, ok := _u.mutation.AccessionAccount(); ok {
		_spec.SetField(accounts.FieldAccessionAccount, field.TypeBool, value)
	}
	if value, ok := _u.mutation.IsArrested(); ok {
		_spec.SetField(accounts.FieldIsArrested, field.TypeBool, value)
	}
	_node = &Accounts{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{accounts.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
