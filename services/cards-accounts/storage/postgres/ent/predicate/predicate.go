// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// Accounts is the predicate function for accounts builders.
type Accounts func(*sql.Selector)

// Cards is the predicate function for cards builders.
type Cards func(*sql.Selector)

// ClientVerificationResults is the predicate function for clientverificationresults builders.
type ClientVerificationResults func(*sql.Selector)

// FinContract is the predicate function for fincontract builders.
type FinContract func(*sql.Selector)

// Health is the predicate function for health builders.
type Health func(*sql.Selector)
