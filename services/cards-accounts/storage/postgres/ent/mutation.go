// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAccounts                  = "Accounts"
	TypeCards                     = "Cards"
	TypeClientVerificationResults = "ClientVerificationResults"
	TypeFinContract               = "FinContract"
	TypeHealth                    = "Health"
)

// AccountsMutation represents an operation that mutates the Accounts nodes in the graph.
type AccountsMutation struct {
	config
	op                    Op
	typ                   string
	id                    *uuid.UUID
	create_time           *time.Time
	update_time           *time.Time
	client_iin            *string
	client_code           *string
	client_name           *string
	title                 *string
	short_name            *string
	currency              *string
	iban                  *string
	_type                 *string
	status                *string
	date_opened           *string
	balance               *decimal.Decimal
	addbalance            *decimal.Decimal
	balance_natival       *decimal.Decimal
	addbalance_natival    *decimal.Decimal
	blocked_balance       *decimal.Decimal
	addblocked_balance    *decimal.Decimal
	available_balance     *decimal.Decimal
	addavailable_balance  *decimal.Decimal
	date_closed           *string
	arrest_blocking       *string
	arrest_debt_amount    *decimal.Decimal
	addarrest_debt_amount *decimal.Decimal
	has_arrest            *string
	origin                *string
	dea_reference_id      *string
	client_type           *string
	is_main               *bool
	accession_account     *bool
	is_arrested           *bool
	clearedFields         map[string]struct{}
	done                  bool
	oldValue              func(context.Context) (*Accounts, error)
	predicates            []predicate.Accounts
}

var _ ent.Mutation = (*AccountsMutation)(nil)

// accountsOption allows management of the mutation configuration using functional options.
type accountsOption func(*AccountsMutation)

// newAccountsMutation creates new mutation for the Accounts entity.
func newAccountsMutation(c config, op Op, opts ...accountsOption) *AccountsMutation {
	m := &AccountsMutation{
		config:        c,
		op:            op,
		typ:           TypeAccounts,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAccountsID sets the ID field of the mutation.
func withAccountsID(id uuid.UUID) accountsOption {
	return func(m *AccountsMutation) {
		var (
			err   error
			once  sync.Once
			value *Accounts
		)
		m.oldValue = func(ctx context.Context) (*Accounts, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Accounts.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAccounts sets the old Accounts of the mutation.
func withAccounts(node *Accounts) accountsOption {
	return func(m *AccountsMutation) {
		m.oldValue = func(context.Context) (*Accounts, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AccountsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AccountsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Accounts entities.
func (m *AccountsMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AccountsMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AccountsMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Accounts.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *AccountsMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *AccountsMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *AccountsMutation) ResetCreateTime() {
	m.create_time = nil
}

// SetUpdateTime sets the "update_time" field.
func (m *AccountsMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *AccountsMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *AccountsMutation) ResetUpdateTime() {
	m.update_time = nil
}

// SetClientIin sets the "client_iin" field.
func (m *AccountsMutation) SetClientIin(s string) {
	m.client_iin = &s
}

// ClientIin returns the value of the "client_iin" field in the mutation.
func (m *AccountsMutation) ClientIin() (r string, exists bool) {
	v := m.client_iin
	if v == nil {
		return
	}
	return *v, true
}

// OldClientIin returns the old "client_iin" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldClientIin(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientIin: %w", err)
	}
	return oldValue.ClientIin, nil
}

// ResetClientIin resets all changes to the "client_iin" field.
func (m *AccountsMutation) ResetClientIin() {
	m.client_iin = nil
}

// SetClientCode sets the "client_code" field.
func (m *AccountsMutation) SetClientCode(s string) {
	m.client_code = &s
}

// ClientCode returns the value of the "client_code" field in the mutation.
func (m *AccountsMutation) ClientCode() (r string, exists bool) {
	v := m.client_code
	if v == nil {
		return
	}
	return *v, true
}

// OldClientCode returns the old "client_code" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldClientCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientCode: %w", err)
	}
	return oldValue.ClientCode, nil
}

// ResetClientCode resets all changes to the "client_code" field.
func (m *AccountsMutation) ResetClientCode() {
	m.client_code = nil
}

// SetClientName sets the "client_name" field.
func (m *AccountsMutation) SetClientName(s string) {
	m.client_name = &s
}

// ClientName returns the value of the "client_name" field in the mutation.
func (m *AccountsMutation) ClientName() (r string, exists bool) {
	v := m.client_name
	if v == nil {
		return
	}
	return *v, true
}

// OldClientName returns the old "client_name" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldClientName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientName: %w", err)
	}
	return oldValue.ClientName, nil
}

// ResetClientName resets all changes to the "client_name" field.
func (m *AccountsMutation) ResetClientName() {
	m.client_name = nil
}

// SetTitle sets the "title" field.
func (m *AccountsMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *AccountsMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ClearTitle clears the value of the "title" field.
func (m *AccountsMutation) ClearTitle() {
	m.title = nil
	m.clearedFields[accounts.FieldTitle] = struct{}{}
}

// TitleCleared returns if the "title" field was cleared in this mutation.
func (m *AccountsMutation) TitleCleared() bool {
	_, ok := m.clearedFields[accounts.FieldTitle]
	return ok
}

// ResetTitle resets all changes to the "title" field.
func (m *AccountsMutation) ResetTitle() {
	m.title = nil
	delete(m.clearedFields, accounts.FieldTitle)
}

// SetShortName sets the "short_name" field.
func (m *AccountsMutation) SetShortName(s string) {
	m.short_name = &s
}

// ShortName returns the value of the "short_name" field in the mutation.
func (m *AccountsMutation) ShortName() (r string, exists bool) {
	v := m.short_name
	if v == nil {
		return
	}
	return *v, true
}

// OldShortName returns the old "short_name" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldShortName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldShortName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldShortName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldShortName: %w", err)
	}
	return oldValue.ShortName, nil
}

// ClearShortName clears the value of the "short_name" field.
func (m *AccountsMutation) ClearShortName() {
	m.short_name = nil
	m.clearedFields[accounts.FieldShortName] = struct{}{}
}

// ShortNameCleared returns if the "short_name" field was cleared in this mutation.
func (m *AccountsMutation) ShortNameCleared() bool {
	_, ok := m.clearedFields[accounts.FieldShortName]
	return ok
}

// ResetShortName resets all changes to the "short_name" field.
func (m *AccountsMutation) ResetShortName() {
	m.short_name = nil
	delete(m.clearedFields, accounts.FieldShortName)
}

// SetCurrency sets the "currency" field.
func (m *AccountsMutation) SetCurrency(s string) {
	m.currency = &s
}

// Currency returns the value of the "currency" field in the mutation.
func (m *AccountsMutation) Currency() (r string, exists bool) {
	v := m.currency
	if v == nil {
		return
	}
	return *v, true
}

// OldCurrency returns the old "currency" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldCurrency(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCurrency is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCurrency requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCurrency: %w", err)
	}
	return oldValue.Currency, nil
}

// ResetCurrency resets all changes to the "currency" field.
func (m *AccountsMutation) ResetCurrency() {
	m.currency = nil
}

// SetIban sets the "iban" field.
func (m *AccountsMutation) SetIban(s string) {
	m.iban = &s
}

// Iban returns the value of the "iban" field in the mutation.
func (m *AccountsMutation) Iban() (r string, exists bool) {
	v := m.iban
	if v == nil {
		return
	}
	return *v, true
}

// OldIban returns the old "iban" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldIban(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIban is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIban requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIban: %w", err)
	}
	return oldValue.Iban, nil
}

// ResetIban resets all changes to the "iban" field.
func (m *AccountsMutation) ResetIban() {
	m.iban = nil
}

// SetType sets the "type" field.
func (m *AccountsMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *AccountsMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *AccountsMutation) ResetType() {
	m._type = nil
}

// SetStatus sets the "status" field.
func (m *AccountsMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *AccountsMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *AccountsMutation) ResetStatus() {
	m.status = nil
}

// SetDateOpened sets the "date_opened" field.
func (m *AccountsMutation) SetDateOpened(s string) {
	m.date_opened = &s
}

// DateOpened returns the value of the "date_opened" field in the mutation.
func (m *AccountsMutation) DateOpened() (r string, exists bool) {
	v := m.date_opened
	if v == nil {
		return
	}
	return *v, true
}

// OldDateOpened returns the old "date_opened" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldDateOpened(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDateOpened is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDateOpened requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDateOpened: %w", err)
	}
	return oldValue.DateOpened, nil
}

// ResetDateOpened resets all changes to the "date_opened" field.
func (m *AccountsMutation) ResetDateOpened() {
	m.date_opened = nil
}

// SetBalance sets the "balance" field.
func (m *AccountsMutation) SetBalance(d decimal.Decimal) {
	m.balance = &d
	m.addbalance = nil
}

// Balance returns the value of the "balance" field in the mutation.
func (m *AccountsMutation) Balance() (r decimal.Decimal, exists bool) {
	v := m.balance
	if v == nil {
		return
	}
	return *v, true
}

// OldBalance returns the old "balance" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldBalance(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBalance is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBalance requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBalance: %w", err)
	}
	return oldValue.Balance, nil
}

// AddBalance adds d to the "balance" field.
func (m *AccountsMutation) AddBalance(d decimal.Decimal) {
	if m.addbalance != nil {
		*m.addbalance = m.addbalance.Add(d)
	} else {
		m.addbalance = &d
	}
}

// AddedBalance returns the value that was added to the "balance" field in this mutation.
func (m *AccountsMutation) AddedBalance() (r decimal.Decimal, exists bool) {
	v := m.addbalance
	if v == nil {
		return
	}
	return *v, true
}

// ResetBalance resets all changes to the "balance" field.
func (m *AccountsMutation) ResetBalance() {
	m.balance = nil
	m.addbalance = nil
}

// SetBalanceNatival sets the "balance_natival" field.
func (m *AccountsMutation) SetBalanceNatival(d decimal.Decimal) {
	m.balance_natival = &d
	m.addbalance_natival = nil
}

// BalanceNatival returns the value of the "balance_natival" field in the mutation.
func (m *AccountsMutation) BalanceNatival() (r decimal.Decimal, exists bool) {
	v := m.balance_natival
	if v == nil {
		return
	}
	return *v, true
}

// OldBalanceNatival returns the old "balance_natival" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldBalanceNatival(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBalanceNatival is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBalanceNatival requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBalanceNatival: %w", err)
	}
	return oldValue.BalanceNatival, nil
}

// AddBalanceNatival adds d to the "balance_natival" field.
func (m *AccountsMutation) AddBalanceNatival(d decimal.Decimal) {
	if m.addbalance_natival != nil {
		*m.addbalance_natival = m.addbalance_natival.Add(d)
	} else {
		m.addbalance_natival = &d
	}
}

// AddedBalanceNatival returns the value that was added to the "balance_natival" field in this mutation.
func (m *AccountsMutation) AddedBalanceNatival() (r decimal.Decimal, exists bool) {
	v := m.addbalance_natival
	if v == nil {
		return
	}
	return *v, true
}

// ResetBalanceNatival resets all changes to the "balance_natival" field.
func (m *AccountsMutation) ResetBalanceNatival() {
	m.balance_natival = nil
	m.addbalance_natival = nil
}

// SetBlockedBalance sets the "blocked_balance" field.
func (m *AccountsMutation) SetBlockedBalance(d decimal.Decimal) {
	m.blocked_balance = &d
	m.addblocked_balance = nil
}

// BlockedBalance returns the value of the "blocked_balance" field in the mutation.
func (m *AccountsMutation) BlockedBalance() (r decimal.Decimal, exists bool) {
	v := m.blocked_balance
	if v == nil {
		return
	}
	return *v, true
}

// OldBlockedBalance returns the old "blocked_balance" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldBlockedBalance(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBlockedBalance is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBlockedBalance requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBlockedBalance: %w", err)
	}
	return oldValue.BlockedBalance, nil
}

// AddBlockedBalance adds d to the "blocked_balance" field.
func (m *AccountsMutation) AddBlockedBalance(d decimal.Decimal) {
	if m.addblocked_balance != nil {
		*m.addblocked_balance = m.addblocked_balance.Add(d)
	} else {
		m.addblocked_balance = &d
	}
}

// AddedBlockedBalance returns the value that was added to the "blocked_balance" field in this mutation.
func (m *AccountsMutation) AddedBlockedBalance() (r decimal.Decimal, exists bool) {
	v := m.addblocked_balance
	if v == nil {
		return
	}
	return *v, true
}

// ResetBlockedBalance resets all changes to the "blocked_balance" field.
func (m *AccountsMutation) ResetBlockedBalance() {
	m.blocked_balance = nil
	m.addblocked_balance = nil
}

// SetAvailableBalance sets the "available_balance" field.
func (m *AccountsMutation) SetAvailableBalance(d decimal.Decimal) {
	m.available_balance = &d
	m.addavailable_balance = nil
}

// AvailableBalance returns the value of the "available_balance" field in the mutation.
func (m *AccountsMutation) AvailableBalance() (r decimal.Decimal, exists bool) {
	v := m.available_balance
	if v == nil {
		return
	}
	return *v, true
}

// OldAvailableBalance returns the old "available_balance" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldAvailableBalance(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAvailableBalance is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAvailableBalance requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAvailableBalance: %w", err)
	}
	return oldValue.AvailableBalance, nil
}

// AddAvailableBalance adds d to the "available_balance" field.
func (m *AccountsMutation) AddAvailableBalance(d decimal.Decimal) {
	if m.addavailable_balance != nil {
		*m.addavailable_balance = m.addavailable_balance.Add(d)
	} else {
		m.addavailable_balance = &d
	}
}

// AddedAvailableBalance returns the value that was added to the "available_balance" field in this mutation.
func (m *AccountsMutation) AddedAvailableBalance() (r decimal.Decimal, exists bool) {
	v := m.addavailable_balance
	if v == nil {
		return
	}
	return *v, true
}

// ResetAvailableBalance resets all changes to the "available_balance" field.
func (m *AccountsMutation) ResetAvailableBalance() {
	m.available_balance = nil
	m.addavailable_balance = nil
}

// SetDateClosed sets the "date_closed" field.
func (m *AccountsMutation) SetDateClosed(s string) {
	m.date_closed = &s
}

// DateClosed returns the value of the "date_closed" field in the mutation.
func (m *AccountsMutation) DateClosed() (r string, exists bool) {
	v := m.date_closed
	if v == nil {
		return
	}
	return *v, true
}

// OldDateClosed returns the old "date_closed" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldDateClosed(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDateClosed is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDateClosed requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDateClosed: %w", err)
	}
	return oldValue.DateClosed, nil
}

// ResetDateClosed resets all changes to the "date_closed" field.
func (m *AccountsMutation) ResetDateClosed() {
	m.date_closed = nil
}

// SetArrestBlocking sets the "arrest_blocking" field.
func (m *AccountsMutation) SetArrestBlocking(s string) {
	m.arrest_blocking = &s
}

// ArrestBlocking returns the value of the "arrest_blocking" field in the mutation.
func (m *AccountsMutation) ArrestBlocking() (r string, exists bool) {
	v := m.arrest_blocking
	if v == nil {
		return
	}
	return *v, true
}

// OldArrestBlocking returns the old "arrest_blocking" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldArrestBlocking(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldArrestBlocking is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldArrestBlocking requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldArrestBlocking: %w", err)
	}
	return oldValue.ArrestBlocking, nil
}

// ResetArrestBlocking resets all changes to the "arrest_blocking" field.
func (m *AccountsMutation) ResetArrestBlocking() {
	m.arrest_blocking = nil
}

// SetArrestDebtAmount sets the "arrest_debt_amount" field.
func (m *AccountsMutation) SetArrestDebtAmount(d decimal.Decimal) {
	m.arrest_debt_amount = &d
	m.addarrest_debt_amount = nil
}

// ArrestDebtAmount returns the value of the "arrest_debt_amount" field in the mutation.
func (m *AccountsMutation) ArrestDebtAmount() (r decimal.Decimal, exists bool) {
	v := m.arrest_debt_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldArrestDebtAmount returns the old "arrest_debt_amount" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldArrestDebtAmount(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldArrestDebtAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldArrestDebtAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldArrestDebtAmount: %w", err)
	}
	return oldValue.ArrestDebtAmount, nil
}

// AddArrestDebtAmount adds d to the "arrest_debt_amount" field.
func (m *AccountsMutation) AddArrestDebtAmount(d decimal.Decimal) {
	if m.addarrest_debt_amount != nil {
		*m.addarrest_debt_amount = m.addarrest_debt_amount.Add(d)
	} else {
		m.addarrest_debt_amount = &d
	}
}

// AddedArrestDebtAmount returns the value that was added to the "arrest_debt_amount" field in this mutation.
func (m *AccountsMutation) AddedArrestDebtAmount() (r decimal.Decimal, exists bool) {
	v := m.addarrest_debt_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetArrestDebtAmount resets all changes to the "arrest_debt_amount" field.
func (m *AccountsMutation) ResetArrestDebtAmount() {
	m.arrest_debt_amount = nil
	m.addarrest_debt_amount = nil
}

// SetHasArrest sets the "has_arrest" field.
func (m *AccountsMutation) SetHasArrest(s string) {
	m.has_arrest = &s
}

// HasArrest returns the value of the "has_arrest" field in the mutation.
func (m *AccountsMutation) HasArrest() (r string, exists bool) {
	v := m.has_arrest
	if v == nil {
		return
	}
	return *v, true
}

// OldHasArrest returns the old "has_arrest" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldHasArrest(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHasArrest is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHasArrest requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHasArrest: %w", err)
	}
	return oldValue.HasArrest, nil
}

// ResetHasArrest resets all changes to the "has_arrest" field.
func (m *AccountsMutation) ResetHasArrest() {
	m.has_arrest = nil
}

// SetOrigin sets the "origin" field.
func (m *AccountsMutation) SetOrigin(s string) {
	m.origin = &s
}

// Origin returns the value of the "origin" field in the mutation.
func (m *AccountsMutation) Origin() (r string, exists bool) {
	v := m.origin
	if v == nil {
		return
	}
	return *v, true
}

// OldOrigin returns the old "origin" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldOrigin(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOrigin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOrigin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOrigin: %w", err)
	}
	return oldValue.Origin, nil
}

// ResetOrigin resets all changes to the "origin" field.
func (m *AccountsMutation) ResetOrigin() {
	m.origin = nil
}

// SetDeaReferenceID sets the "dea_reference_id" field.
func (m *AccountsMutation) SetDeaReferenceID(s string) {
	m.dea_reference_id = &s
}

// DeaReferenceID returns the value of the "dea_reference_id" field in the mutation.
func (m *AccountsMutation) DeaReferenceID() (r string, exists bool) {
	v := m.dea_reference_id
	if v == nil {
		return
	}
	return *v, true
}

// OldDeaReferenceID returns the old "dea_reference_id" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldDeaReferenceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeaReferenceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeaReferenceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeaReferenceID: %w", err)
	}
	return oldValue.DeaReferenceID, nil
}

// ResetDeaReferenceID resets all changes to the "dea_reference_id" field.
func (m *AccountsMutation) ResetDeaReferenceID() {
	m.dea_reference_id = nil
}

// SetClientType sets the "client_type" field.
func (m *AccountsMutation) SetClientType(s string) {
	m.client_type = &s
}

// ClientType returns the value of the "client_type" field in the mutation.
func (m *AccountsMutation) ClientType() (r string, exists bool) {
	v := m.client_type
	if v == nil {
		return
	}
	return *v, true
}

// OldClientType returns the old "client_type" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldClientType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientType: %w", err)
	}
	return oldValue.ClientType, nil
}

// ResetClientType resets all changes to the "client_type" field.
func (m *AccountsMutation) ResetClientType() {
	m.client_type = nil
}

// SetIsMain sets the "is_main" field.
func (m *AccountsMutation) SetIsMain(b bool) {
	m.is_main = &b
}

// IsMain returns the value of the "is_main" field in the mutation.
func (m *AccountsMutation) IsMain() (r bool, exists bool) {
	v := m.is_main
	if v == nil {
		return
	}
	return *v, true
}

// OldIsMain returns the old "is_main" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldIsMain(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsMain is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsMain requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsMain: %w", err)
	}
	return oldValue.IsMain, nil
}

// ResetIsMain resets all changes to the "is_main" field.
func (m *AccountsMutation) ResetIsMain() {
	m.is_main = nil
}

// SetAccessionAccount sets the "accession_account" field.
func (m *AccountsMutation) SetAccessionAccount(b bool) {
	m.accession_account = &b
}

// AccessionAccount returns the value of the "accession_account" field in the mutation.
func (m *AccountsMutation) AccessionAccount() (r bool, exists bool) {
	v := m.accession_account
	if v == nil {
		return
	}
	return *v, true
}

// OldAccessionAccount returns the old "accession_account" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldAccessionAccount(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccessionAccount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccessionAccount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccessionAccount: %w", err)
	}
	return oldValue.AccessionAccount, nil
}

// ResetAccessionAccount resets all changes to the "accession_account" field.
func (m *AccountsMutation) ResetAccessionAccount() {
	m.accession_account = nil
}

// SetIsArrested sets the "is_arrested" field.
func (m *AccountsMutation) SetIsArrested(b bool) {
	m.is_arrested = &b
}

// IsArrested returns the value of the "is_arrested" field in the mutation.
func (m *AccountsMutation) IsArrested() (r bool, exists bool) {
	v := m.is_arrested
	if v == nil {
		return
	}
	return *v, true
}

// OldIsArrested returns the old "is_arrested" field's value of the Accounts entity.
// If the Accounts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccountsMutation) OldIsArrested(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsArrested is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsArrested requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsArrested: %w", err)
	}
	return oldValue.IsArrested, nil
}

// ResetIsArrested resets all changes to the "is_arrested" field.
func (m *AccountsMutation) ResetIsArrested() {
	m.is_arrested = nil
}

// Where appends a list predicates to the AccountsMutation builder.
func (m *AccountsMutation) Where(ps ...predicate.Accounts) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AccountsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AccountsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Accounts, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AccountsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AccountsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Accounts).
func (m *AccountsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AccountsMutation) Fields() []string {
	fields := make([]string, 0, 26)
	if m.create_time != nil {
		fields = append(fields, accounts.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, accounts.FieldUpdateTime)
	}
	if m.client_iin != nil {
		fields = append(fields, accounts.FieldClientIin)
	}
	if m.client_code != nil {
		fields = append(fields, accounts.FieldClientCode)
	}
	if m.client_name != nil {
		fields = append(fields, accounts.FieldClientName)
	}
	if m.title != nil {
		fields = append(fields, accounts.FieldTitle)
	}
	if m.short_name != nil {
		fields = append(fields, accounts.FieldShortName)
	}
	if m.currency != nil {
		fields = append(fields, accounts.FieldCurrency)
	}
	if m.iban != nil {
		fields = append(fields, accounts.FieldIban)
	}
	if m._type != nil {
		fields = append(fields, accounts.FieldType)
	}
	if m.status != nil {
		fields = append(fields, accounts.FieldStatus)
	}
	if m.date_opened != nil {
		fields = append(fields, accounts.FieldDateOpened)
	}
	if m.balance != nil {
		fields = append(fields, accounts.FieldBalance)
	}
	if m.balance_natival != nil {
		fields = append(fields, accounts.FieldBalanceNatival)
	}
	if m.blocked_balance != nil {
		fields = append(fields, accounts.FieldBlockedBalance)
	}
	if m.available_balance != nil {
		fields = append(fields, accounts.FieldAvailableBalance)
	}
	if m.date_closed != nil {
		fields = append(fields, accounts.FieldDateClosed)
	}
	if m.arrest_blocking != nil {
		fields = append(fields, accounts.FieldArrestBlocking)
	}
	if m.arrest_debt_amount != nil {
		fields = append(fields, accounts.FieldArrestDebtAmount)
	}
	if m.has_arrest != nil {
		fields = append(fields, accounts.FieldHasArrest)
	}
	if m.origin != nil {
		fields = append(fields, accounts.FieldOrigin)
	}
	if m.dea_reference_id != nil {
		fields = append(fields, accounts.FieldDeaReferenceID)
	}
	if m.client_type != nil {
		fields = append(fields, accounts.FieldClientType)
	}
	if m.is_main != nil {
		fields = append(fields, accounts.FieldIsMain)
	}
	if m.accession_account != nil {
		fields = append(fields, accounts.FieldAccessionAccount)
	}
	if m.is_arrested != nil {
		fields = append(fields, accounts.FieldIsArrested)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AccountsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case accounts.FieldCreateTime:
		return m.CreateTime()
	case accounts.FieldUpdateTime:
		return m.UpdateTime()
	case accounts.FieldClientIin:
		return m.ClientIin()
	case accounts.FieldClientCode:
		return m.ClientCode()
	case accounts.FieldClientName:
		return m.ClientName()
	case accounts.FieldTitle:
		return m.Title()
	case accounts.FieldShortName:
		return m.ShortName()
	case accounts.FieldCurrency:
		return m.Currency()
	case accounts.FieldIban:
		return m.Iban()
	case accounts.FieldType:
		return m.GetType()
	case accounts.FieldStatus:
		return m.Status()
	case accounts.FieldDateOpened:
		return m.DateOpened()
	case accounts.FieldBalance:
		return m.Balance()
	case accounts.FieldBalanceNatival:
		return m.BalanceNatival()
	case accounts.FieldBlockedBalance:
		return m.BlockedBalance()
	case accounts.FieldAvailableBalance:
		return m.AvailableBalance()
	case accounts.FieldDateClosed:
		return m.DateClosed()
	case accounts.FieldArrestBlocking:
		return m.ArrestBlocking()
	case accounts.FieldArrestDebtAmount:
		return m.ArrestDebtAmount()
	case accounts.FieldHasArrest:
		return m.HasArrest()
	case accounts.FieldOrigin:
		return m.Origin()
	case accounts.FieldDeaReferenceID:
		return m.DeaReferenceID()
	case accounts.FieldClientType:
		return m.ClientType()
	case accounts.FieldIsMain:
		return m.IsMain()
	case accounts.FieldAccessionAccount:
		return m.AccessionAccount()
	case accounts.FieldIsArrested:
		return m.IsArrested()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AccountsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case accounts.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case accounts.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case accounts.FieldClientIin:
		return m.OldClientIin(ctx)
	case accounts.FieldClientCode:
		return m.OldClientCode(ctx)
	case accounts.FieldClientName:
		return m.OldClientName(ctx)
	case accounts.FieldTitle:
		return m.OldTitle(ctx)
	case accounts.FieldShortName:
		return m.OldShortName(ctx)
	case accounts.FieldCurrency:
		return m.OldCurrency(ctx)
	case accounts.FieldIban:
		return m.OldIban(ctx)
	case accounts.FieldType:
		return m.OldType(ctx)
	case accounts.FieldStatus:
		return m.OldStatus(ctx)
	case accounts.FieldDateOpened:
		return m.OldDateOpened(ctx)
	case accounts.FieldBalance:
		return m.OldBalance(ctx)
	case accounts.FieldBalanceNatival:
		return m.OldBalanceNatival(ctx)
	case accounts.FieldBlockedBalance:
		return m.OldBlockedBalance(ctx)
	case accounts.FieldAvailableBalance:
		return m.OldAvailableBalance(ctx)
	case accounts.FieldDateClosed:
		return m.OldDateClosed(ctx)
	case accounts.FieldArrestBlocking:
		return m.OldArrestBlocking(ctx)
	case accounts.FieldArrestDebtAmount:
		return m.OldArrestDebtAmount(ctx)
	case accounts.FieldHasArrest:
		return m.OldHasArrest(ctx)
	case accounts.FieldOrigin:
		return m.OldOrigin(ctx)
	case accounts.FieldDeaReferenceID:
		return m.OldDeaReferenceID(ctx)
	case accounts.FieldClientType:
		return m.OldClientType(ctx)
	case accounts.FieldIsMain:
		return m.OldIsMain(ctx)
	case accounts.FieldAccessionAccount:
		return m.OldAccessionAccount(ctx)
	case accounts.FieldIsArrested:
		return m.OldIsArrested(ctx)
	}
	return nil, fmt.Errorf("unknown Accounts field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AccountsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case accounts.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case accounts.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case accounts.FieldClientIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientIin(v)
		return nil
	case accounts.FieldClientCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientCode(v)
		return nil
	case accounts.FieldClientName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientName(v)
		return nil
	case accounts.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case accounts.FieldShortName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetShortName(v)
		return nil
	case accounts.FieldCurrency:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCurrency(v)
		return nil
	case accounts.FieldIban:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIban(v)
		return nil
	case accounts.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case accounts.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case accounts.FieldDateOpened:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDateOpened(v)
		return nil
	case accounts.FieldBalance:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBalance(v)
		return nil
	case accounts.FieldBalanceNatival:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBalanceNatival(v)
		return nil
	case accounts.FieldBlockedBalance:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBlockedBalance(v)
		return nil
	case accounts.FieldAvailableBalance:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAvailableBalance(v)
		return nil
	case accounts.FieldDateClosed:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDateClosed(v)
		return nil
	case accounts.FieldArrestBlocking:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetArrestBlocking(v)
		return nil
	case accounts.FieldArrestDebtAmount:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetArrestDebtAmount(v)
		return nil
	case accounts.FieldHasArrest:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHasArrest(v)
		return nil
	case accounts.FieldOrigin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOrigin(v)
		return nil
	case accounts.FieldDeaReferenceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeaReferenceID(v)
		return nil
	case accounts.FieldClientType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientType(v)
		return nil
	case accounts.FieldIsMain:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsMain(v)
		return nil
	case accounts.FieldAccessionAccount:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccessionAccount(v)
		return nil
	case accounts.FieldIsArrested:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsArrested(v)
		return nil
	}
	return fmt.Errorf("unknown Accounts field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AccountsMutation) AddedFields() []string {
	var fields []string
	if m.addbalance != nil {
		fields = append(fields, accounts.FieldBalance)
	}
	if m.addbalance_natival != nil {
		fields = append(fields, accounts.FieldBalanceNatival)
	}
	if m.addblocked_balance != nil {
		fields = append(fields, accounts.FieldBlockedBalance)
	}
	if m.addavailable_balance != nil {
		fields = append(fields, accounts.FieldAvailableBalance)
	}
	if m.addarrest_debt_amount != nil {
		fields = append(fields, accounts.FieldArrestDebtAmount)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AccountsMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case accounts.FieldBalance:
		return m.AddedBalance()
	case accounts.FieldBalanceNatival:
		return m.AddedBalanceNatival()
	case accounts.FieldBlockedBalance:
		return m.AddedBlockedBalance()
	case accounts.FieldAvailableBalance:
		return m.AddedAvailableBalance()
	case accounts.FieldArrestDebtAmount:
		return m.AddedArrestDebtAmount()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AccountsMutation) AddField(name string, value ent.Value) error {
	switch name {
	case accounts.FieldBalance:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddBalance(v)
		return nil
	case accounts.FieldBalanceNatival:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddBalanceNatival(v)
		return nil
	case accounts.FieldBlockedBalance:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddBlockedBalance(v)
		return nil
	case accounts.FieldAvailableBalance:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAvailableBalance(v)
		return nil
	case accounts.FieldArrestDebtAmount:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddArrestDebtAmount(v)
		return nil
	}
	return fmt.Errorf("unknown Accounts numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AccountsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(accounts.FieldTitle) {
		fields = append(fields, accounts.FieldTitle)
	}
	if m.FieldCleared(accounts.FieldShortName) {
		fields = append(fields, accounts.FieldShortName)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AccountsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AccountsMutation) ClearField(name string) error {
	switch name {
	case accounts.FieldTitle:
		m.ClearTitle()
		return nil
	case accounts.FieldShortName:
		m.ClearShortName()
		return nil
	}
	return fmt.Errorf("unknown Accounts nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AccountsMutation) ResetField(name string) error {
	switch name {
	case accounts.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case accounts.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case accounts.FieldClientIin:
		m.ResetClientIin()
		return nil
	case accounts.FieldClientCode:
		m.ResetClientCode()
		return nil
	case accounts.FieldClientName:
		m.ResetClientName()
		return nil
	case accounts.FieldTitle:
		m.ResetTitle()
		return nil
	case accounts.FieldShortName:
		m.ResetShortName()
		return nil
	case accounts.FieldCurrency:
		m.ResetCurrency()
		return nil
	case accounts.FieldIban:
		m.ResetIban()
		return nil
	case accounts.FieldType:
		m.ResetType()
		return nil
	case accounts.FieldStatus:
		m.ResetStatus()
		return nil
	case accounts.FieldDateOpened:
		m.ResetDateOpened()
		return nil
	case accounts.FieldBalance:
		m.ResetBalance()
		return nil
	case accounts.FieldBalanceNatival:
		m.ResetBalanceNatival()
		return nil
	case accounts.FieldBlockedBalance:
		m.ResetBlockedBalance()
		return nil
	case accounts.FieldAvailableBalance:
		m.ResetAvailableBalance()
		return nil
	case accounts.FieldDateClosed:
		m.ResetDateClosed()
		return nil
	case accounts.FieldArrestBlocking:
		m.ResetArrestBlocking()
		return nil
	case accounts.FieldArrestDebtAmount:
		m.ResetArrestDebtAmount()
		return nil
	case accounts.FieldHasArrest:
		m.ResetHasArrest()
		return nil
	case accounts.FieldOrigin:
		m.ResetOrigin()
		return nil
	case accounts.FieldDeaReferenceID:
		m.ResetDeaReferenceID()
		return nil
	case accounts.FieldClientType:
		m.ResetClientType()
		return nil
	case accounts.FieldIsMain:
		m.ResetIsMain()
		return nil
	case accounts.FieldAccessionAccount:
		m.ResetAccessionAccount()
		return nil
	case accounts.FieldIsArrested:
		m.ResetIsArrested()
		return nil
	}
	return fmt.Errorf("unknown Accounts field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AccountsMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AccountsMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AccountsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AccountsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AccountsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AccountsMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AccountsMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Accounts unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AccountsMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Accounts edge %s", name)
}

// CardsMutation represents an operation that mutates the Cards nodes in the graph.
type CardsMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	create_time         *time.Time
	update_time         *time.Time
	client_id           *uuid.UUID
	embossing_name      *string
	masked_pan          *string
	card_type           *cards.CardType
	product_type        *cards.ProductType
	payment_system      *cards.PaymentSystem
	card_class          *cards.CardClass
	status              *cards.Status
	tokenization_status *cards.TokenizationStatus
	wallet              *string
	creation_date       *time.Time
	expire_date         *time.Time
	modification_date   *time.Time
	clearedFields       map[string]struct{}
	account             *uuid.UUID
	clearedaccount      bool
	done                bool
	oldValue            func(context.Context) (*Cards, error)
	predicates          []predicate.Cards
}

var _ ent.Mutation = (*CardsMutation)(nil)

// cardsOption allows management of the mutation configuration using functional options.
type cardsOption func(*CardsMutation)

// newCardsMutation creates new mutation for the Cards entity.
func newCardsMutation(c config, op Op, opts ...cardsOption) *CardsMutation {
	m := &CardsMutation{
		config:        c,
		op:            op,
		typ:           TypeCards,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withCardsID sets the ID field of the mutation.
func withCardsID(id uuid.UUID) cardsOption {
	return func(m *CardsMutation) {
		var (
			err   error
			once  sync.Once
			value *Cards
		)
		m.oldValue = func(ctx context.Context) (*Cards, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Cards.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withCards sets the old Cards of the mutation.
func withCards(node *Cards) cardsOption {
	return func(m *CardsMutation) {
		m.oldValue = func(context.Context) (*Cards, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m CardsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m CardsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Cards entities.
func (m *CardsMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *CardsMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *CardsMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Cards.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *CardsMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *CardsMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *CardsMutation) ResetCreateTime() {
	m.create_time = nil
}

// SetUpdateTime sets the "update_time" field.
func (m *CardsMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *CardsMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *CardsMutation) ResetUpdateTime() {
	m.update_time = nil
}

// SetClientID sets the "client_id" field.
func (m *CardsMutation) SetClientID(u uuid.UUID) {
	m.client_id = &u
}

// ClientID returns the value of the "client_id" field in the mutation.
func (m *CardsMutation) ClientID() (r uuid.UUID, exists bool) {
	v := m.client_id
	if v == nil {
		return
	}
	return *v, true
}

// OldClientID returns the old "client_id" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldClientID(ctx context.Context) (v *uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientID: %w", err)
	}
	return oldValue.ClientID, nil
}

// ClearClientID clears the value of the "client_id" field.
func (m *CardsMutation) ClearClientID() {
	m.client_id = nil
	m.clearedFields[cards.FieldClientID] = struct{}{}
}

// ClientIDCleared returns if the "client_id" field was cleared in this mutation.
func (m *CardsMutation) ClientIDCleared() bool {
	_, ok := m.clearedFields[cards.FieldClientID]
	return ok
}

// ResetClientID resets all changes to the "client_id" field.
func (m *CardsMutation) ResetClientID() {
	m.client_id = nil
	delete(m.clearedFields, cards.FieldClientID)
}

// SetAttachedAccountID sets the "attached_account_id" field.
func (m *CardsMutation) SetAttachedAccountID(u uuid.UUID) {
	m.account = &u
}

// AttachedAccountID returns the value of the "attached_account_id" field in the mutation.
func (m *CardsMutation) AttachedAccountID() (r uuid.UUID, exists bool) {
	v := m.account
	if v == nil {
		return
	}
	return *v, true
}

// OldAttachedAccountID returns the old "attached_account_id" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldAttachedAccountID(ctx context.Context) (v *uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAttachedAccountID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAttachedAccountID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAttachedAccountID: %w", err)
	}
	return oldValue.AttachedAccountID, nil
}

// ClearAttachedAccountID clears the value of the "attached_account_id" field.
func (m *CardsMutation) ClearAttachedAccountID() {
	m.account = nil
	m.clearedFields[cards.FieldAttachedAccountID] = struct{}{}
}

// AttachedAccountIDCleared returns if the "attached_account_id" field was cleared in this mutation.
func (m *CardsMutation) AttachedAccountIDCleared() bool {
	_, ok := m.clearedFields[cards.FieldAttachedAccountID]
	return ok
}

// ResetAttachedAccountID resets all changes to the "attached_account_id" field.
func (m *CardsMutation) ResetAttachedAccountID() {
	m.account = nil
	delete(m.clearedFields, cards.FieldAttachedAccountID)
}

// SetEmbossingName sets the "embossing_name" field.
func (m *CardsMutation) SetEmbossingName(s string) {
	m.embossing_name = &s
}

// EmbossingName returns the value of the "embossing_name" field in the mutation.
func (m *CardsMutation) EmbossingName() (r string, exists bool) {
	v := m.embossing_name
	if v == nil {
		return
	}
	return *v, true
}

// OldEmbossingName returns the old "embossing_name" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldEmbossingName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmbossingName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmbossingName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmbossingName: %w", err)
	}
	return oldValue.EmbossingName, nil
}

// ClearEmbossingName clears the value of the "embossing_name" field.
func (m *CardsMutation) ClearEmbossingName() {
	m.embossing_name = nil
	m.clearedFields[cards.FieldEmbossingName] = struct{}{}
}

// EmbossingNameCleared returns if the "embossing_name" field was cleared in this mutation.
func (m *CardsMutation) EmbossingNameCleared() bool {
	_, ok := m.clearedFields[cards.FieldEmbossingName]
	return ok
}

// ResetEmbossingName resets all changes to the "embossing_name" field.
func (m *CardsMutation) ResetEmbossingName() {
	m.embossing_name = nil
	delete(m.clearedFields, cards.FieldEmbossingName)
}

// SetMaskedPan sets the "masked_pan" field.
func (m *CardsMutation) SetMaskedPan(s string) {
	m.masked_pan = &s
}

// MaskedPan returns the value of the "masked_pan" field in the mutation.
func (m *CardsMutation) MaskedPan() (r string, exists bool) {
	v := m.masked_pan
	if v == nil {
		return
	}
	return *v, true
}

// OldMaskedPan returns the old "masked_pan" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldMaskedPan(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMaskedPan is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMaskedPan requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMaskedPan: %w", err)
	}
	return oldValue.MaskedPan, nil
}

// ClearMaskedPan clears the value of the "masked_pan" field.
func (m *CardsMutation) ClearMaskedPan() {
	m.masked_pan = nil
	m.clearedFields[cards.FieldMaskedPan] = struct{}{}
}

// MaskedPanCleared returns if the "masked_pan" field was cleared in this mutation.
func (m *CardsMutation) MaskedPanCleared() bool {
	_, ok := m.clearedFields[cards.FieldMaskedPan]
	return ok
}

// ResetMaskedPan resets all changes to the "masked_pan" field.
func (m *CardsMutation) ResetMaskedPan() {
	m.masked_pan = nil
	delete(m.clearedFields, cards.FieldMaskedPan)
}

// SetCardType sets the "card_type" field.
func (m *CardsMutation) SetCardType(ct cards.CardType) {
	m.card_type = &ct
}

// CardType returns the value of the "card_type" field in the mutation.
func (m *CardsMutation) CardType() (r cards.CardType, exists bool) {
	v := m.card_type
	if v == nil {
		return
	}
	return *v, true
}

// OldCardType returns the old "card_type" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldCardType(ctx context.Context) (v cards.CardType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCardType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCardType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCardType: %w", err)
	}
	return oldValue.CardType, nil
}

// ResetCardType resets all changes to the "card_type" field.
func (m *CardsMutation) ResetCardType() {
	m.card_type = nil
}

// SetProductType sets the "product_type" field.
func (m *CardsMutation) SetProductType(ct cards.ProductType) {
	m.product_type = &ct
}

// ProductType returns the value of the "product_type" field in the mutation.
func (m *CardsMutation) ProductType() (r cards.ProductType, exists bool) {
	v := m.product_type
	if v == nil {
		return
	}
	return *v, true
}

// OldProductType returns the old "product_type" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldProductType(ctx context.Context) (v cards.ProductType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProductType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProductType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProductType: %w", err)
	}
	return oldValue.ProductType, nil
}

// ResetProductType resets all changes to the "product_type" field.
func (m *CardsMutation) ResetProductType() {
	m.product_type = nil
}

// SetPaymentSystem sets the "payment_system" field.
func (m *CardsMutation) SetPaymentSystem(cs cards.PaymentSystem) {
	m.payment_system = &cs
}

// PaymentSystem returns the value of the "payment_system" field in the mutation.
func (m *CardsMutation) PaymentSystem() (r cards.PaymentSystem, exists bool) {
	v := m.payment_system
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymentSystem returns the old "payment_system" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldPaymentSystem(ctx context.Context) (v cards.PaymentSystem, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymentSystem is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymentSystem requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymentSystem: %w", err)
	}
	return oldValue.PaymentSystem, nil
}

// ResetPaymentSystem resets all changes to the "payment_system" field.
func (m *CardsMutation) ResetPaymentSystem() {
	m.payment_system = nil
}

// SetCardClass sets the "card_class" field.
func (m *CardsMutation) SetCardClass(cc cards.CardClass) {
	m.card_class = &cc
}

// CardClass returns the value of the "card_class" field in the mutation.
func (m *CardsMutation) CardClass() (r cards.CardClass, exists bool) {
	v := m.card_class
	if v == nil {
		return
	}
	return *v, true
}

// OldCardClass returns the old "card_class" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldCardClass(ctx context.Context) (v cards.CardClass, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCardClass is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCardClass requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCardClass: %w", err)
	}
	return oldValue.CardClass, nil
}

// ResetCardClass resets all changes to the "card_class" field.
func (m *CardsMutation) ResetCardClass() {
	m.card_class = nil
}

// SetStatus sets the "status" field.
func (m *CardsMutation) SetStatus(c cards.Status) {
	m.status = &c
}

// Status returns the value of the "status" field in the mutation.
func (m *CardsMutation) Status() (r cards.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldStatus(ctx context.Context) (v cards.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *CardsMutation) ResetStatus() {
	m.status = nil
}

// SetTokenizationStatus sets the "tokenization_status" field.
func (m *CardsMutation) SetTokenizationStatus(cs cards.TokenizationStatus) {
	m.tokenization_status = &cs
}

// TokenizationStatus returns the value of the "tokenization_status" field in the mutation.
func (m *CardsMutation) TokenizationStatus() (r cards.TokenizationStatus, exists bool) {
	v := m.tokenization_status
	if v == nil {
		return
	}
	return *v, true
}

// OldTokenizationStatus returns the old "tokenization_status" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldTokenizationStatus(ctx context.Context) (v cards.TokenizationStatus, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTokenizationStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTokenizationStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTokenizationStatus: %w", err)
	}
	return oldValue.TokenizationStatus, nil
}

// ResetTokenizationStatus resets all changes to the "tokenization_status" field.
func (m *CardsMutation) ResetTokenizationStatus() {
	m.tokenization_status = nil
}

// SetWallet sets the "wallet" field.
func (m *CardsMutation) SetWallet(s string) {
	m.wallet = &s
}

// Wallet returns the value of the "wallet" field in the mutation.
func (m *CardsMutation) Wallet() (r string, exists bool) {
	v := m.wallet
	if v == nil {
		return
	}
	return *v, true
}

// OldWallet returns the old "wallet" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldWallet(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWallet is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWallet requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWallet: %w", err)
	}
	return oldValue.Wallet, nil
}

// ClearWallet clears the value of the "wallet" field.
func (m *CardsMutation) ClearWallet() {
	m.wallet = nil
	m.clearedFields[cards.FieldWallet] = struct{}{}
}

// WalletCleared returns if the "wallet" field was cleared in this mutation.
func (m *CardsMutation) WalletCleared() bool {
	_, ok := m.clearedFields[cards.FieldWallet]
	return ok
}

// ResetWallet resets all changes to the "wallet" field.
func (m *CardsMutation) ResetWallet() {
	m.wallet = nil
	delete(m.clearedFields, cards.FieldWallet)
}

// SetCreationDate sets the "creation_date" field.
func (m *CardsMutation) SetCreationDate(t time.Time) {
	m.creation_date = &t
}

// CreationDate returns the value of the "creation_date" field in the mutation.
func (m *CardsMutation) CreationDate() (r time.Time, exists bool) {
	v := m.creation_date
	if v == nil {
		return
	}
	return *v, true
}

// OldCreationDate returns the old "creation_date" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldCreationDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreationDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreationDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreationDate: %w", err)
	}
	return oldValue.CreationDate, nil
}

// ResetCreationDate resets all changes to the "creation_date" field.
func (m *CardsMutation) ResetCreationDate() {
	m.creation_date = nil
}

// SetExpireDate sets the "expire_date" field.
func (m *CardsMutation) SetExpireDate(t time.Time) {
	m.expire_date = &t
}

// ExpireDate returns the value of the "expire_date" field in the mutation.
func (m *CardsMutation) ExpireDate() (r time.Time, exists bool) {
	v := m.expire_date
	if v == nil {
		return
	}
	return *v, true
}

// OldExpireDate returns the old "expire_date" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldExpireDate(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExpireDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExpireDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExpireDate: %w", err)
	}
	return oldValue.ExpireDate, nil
}

// ClearExpireDate clears the value of the "expire_date" field.
func (m *CardsMutation) ClearExpireDate() {
	m.expire_date = nil
	m.clearedFields[cards.FieldExpireDate] = struct{}{}
}

// ExpireDateCleared returns if the "expire_date" field was cleared in this mutation.
func (m *CardsMutation) ExpireDateCleared() bool {
	_, ok := m.clearedFields[cards.FieldExpireDate]
	return ok
}

// ResetExpireDate resets all changes to the "expire_date" field.
func (m *CardsMutation) ResetExpireDate() {
	m.expire_date = nil
	delete(m.clearedFields, cards.FieldExpireDate)
}

// SetModificationDate sets the "modification_date" field.
func (m *CardsMutation) SetModificationDate(t time.Time) {
	m.modification_date = &t
}

// ModificationDate returns the value of the "modification_date" field in the mutation.
func (m *CardsMutation) ModificationDate() (r time.Time, exists bool) {
	v := m.modification_date
	if v == nil {
		return
	}
	return *v, true
}

// OldModificationDate returns the old "modification_date" field's value of the Cards entity.
// If the Cards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CardsMutation) OldModificationDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldModificationDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldModificationDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldModificationDate: %w", err)
	}
	return oldValue.ModificationDate, nil
}

// ResetModificationDate resets all changes to the "modification_date" field.
func (m *CardsMutation) ResetModificationDate() {
	m.modification_date = nil
}

// SetAccountID sets the "account" edge to the Accounts entity by id.
func (m *CardsMutation) SetAccountID(id uuid.UUID) {
	m.account = &id
}

// ClearAccount clears the "account" edge to the Accounts entity.
func (m *CardsMutation) ClearAccount() {
	m.clearedaccount = true
	m.clearedFields[cards.FieldAttachedAccountID] = struct{}{}
}

// AccountCleared reports if the "account" edge to the Accounts entity was cleared.
func (m *CardsMutation) AccountCleared() bool {
	return m.AttachedAccountIDCleared() || m.clearedaccount
}

// AccountID returns the "account" edge ID in the mutation.
func (m *CardsMutation) AccountID() (id uuid.UUID, exists bool) {
	if m.account != nil {
		return *m.account, true
	}
	return
}

// AccountIDs returns the "account" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// AccountID instead. It exists only for internal usage by the builders.
func (m *CardsMutation) AccountIDs() (ids []uuid.UUID) {
	if id := m.account; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetAccount resets all changes to the "account" edge.
func (m *CardsMutation) ResetAccount() {
	m.account = nil
	m.clearedaccount = false
}

// Where appends a list predicates to the CardsMutation builder.
func (m *CardsMutation) Where(ps ...predicate.Cards) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the CardsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *CardsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Cards, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *CardsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *CardsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Cards).
func (m *CardsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *CardsMutation) Fields() []string {
	fields := make([]string, 0, 16)
	if m.create_time != nil {
		fields = append(fields, cards.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, cards.FieldUpdateTime)
	}
	if m.client_id != nil {
		fields = append(fields, cards.FieldClientID)
	}
	if m.account != nil {
		fields = append(fields, cards.FieldAttachedAccountID)
	}
	if m.embossing_name != nil {
		fields = append(fields, cards.FieldEmbossingName)
	}
	if m.masked_pan != nil {
		fields = append(fields, cards.FieldMaskedPan)
	}
	if m.card_type != nil {
		fields = append(fields, cards.FieldCardType)
	}
	if m.product_type != nil {
		fields = append(fields, cards.FieldProductType)
	}
	if m.payment_system != nil {
		fields = append(fields, cards.FieldPaymentSystem)
	}
	if m.card_class != nil {
		fields = append(fields, cards.FieldCardClass)
	}
	if m.status != nil {
		fields = append(fields, cards.FieldStatus)
	}
	if m.tokenization_status != nil {
		fields = append(fields, cards.FieldTokenizationStatus)
	}
	if m.wallet != nil {
		fields = append(fields, cards.FieldWallet)
	}
	if m.creation_date != nil {
		fields = append(fields, cards.FieldCreationDate)
	}
	if m.expire_date != nil {
		fields = append(fields, cards.FieldExpireDate)
	}
	if m.modification_date != nil {
		fields = append(fields, cards.FieldModificationDate)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *CardsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case cards.FieldCreateTime:
		return m.CreateTime()
	case cards.FieldUpdateTime:
		return m.UpdateTime()
	case cards.FieldClientID:
		return m.ClientID()
	case cards.FieldAttachedAccountID:
		return m.AttachedAccountID()
	case cards.FieldEmbossingName:
		return m.EmbossingName()
	case cards.FieldMaskedPan:
		return m.MaskedPan()
	case cards.FieldCardType:
		return m.CardType()
	case cards.FieldProductType:
		return m.ProductType()
	case cards.FieldPaymentSystem:
		return m.PaymentSystem()
	case cards.FieldCardClass:
		return m.CardClass()
	case cards.FieldStatus:
		return m.Status()
	case cards.FieldTokenizationStatus:
		return m.TokenizationStatus()
	case cards.FieldWallet:
		return m.Wallet()
	case cards.FieldCreationDate:
		return m.CreationDate()
	case cards.FieldExpireDate:
		return m.ExpireDate()
	case cards.FieldModificationDate:
		return m.ModificationDate()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *CardsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case cards.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case cards.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case cards.FieldClientID:
		return m.OldClientID(ctx)
	case cards.FieldAttachedAccountID:
		return m.OldAttachedAccountID(ctx)
	case cards.FieldEmbossingName:
		return m.OldEmbossingName(ctx)
	case cards.FieldMaskedPan:
		return m.OldMaskedPan(ctx)
	case cards.FieldCardType:
		return m.OldCardType(ctx)
	case cards.FieldProductType:
		return m.OldProductType(ctx)
	case cards.FieldPaymentSystem:
		return m.OldPaymentSystem(ctx)
	case cards.FieldCardClass:
		return m.OldCardClass(ctx)
	case cards.FieldStatus:
		return m.OldStatus(ctx)
	case cards.FieldTokenizationStatus:
		return m.OldTokenizationStatus(ctx)
	case cards.FieldWallet:
		return m.OldWallet(ctx)
	case cards.FieldCreationDate:
		return m.OldCreationDate(ctx)
	case cards.FieldExpireDate:
		return m.OldExpireDate(ctx)
	case cards.FieldModificationDate:
		return m.OldModificationDate(ctx)
	}
	return nil, fmt.Errorf("unknown Cards field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CardsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case cards.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case cards.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case cards.FieldClientID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientID(v)
		return nil
	case cards.FieldAttachedAccountID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAttachedAccountID(v)
		return nil
	case cards.FieldEmbossingName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmbossingName(v)
		return nil
	case cards.FieldMaskedPan:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMaskedPan(v)
		return nil
	case cards.FieldCardType:
		v, ok := value.(cards.CardType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCardType(v)
		return nil
	case cards.FieldProductType:
		v, ok := value.(cards.ProductType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProductType(v)
		return nil
	case cards.FieldPaymentSystem:
		v, ok := value.(cards.PaymentSystem)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymentSystem(v)
		return nil
	case cards.FieldCardClass:
		v, ok := value.(cards.CardClass)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCardClass(v)
		return nil
	case cards.FieldStatus:
		v, ok := value.(cards.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case cards.FieldTokenizationStatus:
		v, ok := value.(cards.TokenizationStatus)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTokenizationStatus(v)
		return nil
	case cards.FieldWallet:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWallet(v)
		return nil
	case cards.FieldCreationDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreationDate(v)
		return nil
	case cards.FieldExpireDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExpireDate(v)
		return nil
	case cards.FieldModificationDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetModificationDate(v)
		return nil
	}
	return fmt.Errorf("unknown Cards field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *CardsMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *CardsMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CardsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Cards numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *CardsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(cards.FieldClientID) {
		fields = append(fields, cards.FieldClientID)
	}
	if m.FieldCleared(cards.FieldAttachedAccountID) {
		fields = append(fields, cards.FieldAttachedAccountID)
	}
	if m.FieldCleared(cards.FieldEmbossingName) {
		fields = append(fields, cards.FieldEmbossingName)
	}
	if m.FieldCleared(cards.FieldMaskedPan) {
		fields = append(fields, cards.FieldMaskedPan)
	}
	if m.FieldCleared(cards.FieldWallet) {
		fields = append(fields, cards.FieldWallet)
	}
	if m.FieldCleared(cards.FieldExpireDate) {
		fields = append(fields, cards.FieldExpireDate)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *CardsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *CardsMutation) ClearField(name string) error {
	switch name {
	case cards.FieldClientID:
		m.ClearClientID()
		return nil
	case cards.FieldAttachedAccountID:
		m.ClearAttachedAccountID()
		return nil
	case cards.FieldEmbossingName:
		m.ClearEmbossingName()
		return nil
	case cards.FieldMaskedPan:
		m.ClearMaskedPan()
		return nil
	case cards.FieldWallet:
		m.ClearWallet()
		return nil
	case cards.FieldExpireDate:
		m.ClearExpireDate()
		return nil
	}
	return fmt.Errorf("unknown Cards nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *CardsMutation) ResetField(name string) error {
	switch name {
	case cards.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case cards.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case cards.FieldClientID:
		m.ResetClientID()
		return nil
	case cards.FieldAttachedAccountID:
		m.ResetAttachedAccountID()
		return nil
	case cards.FieldEmbossingName:
		m.ResetEmbossingName()
		return nil
	case cards.FieldMaskedPan:
		m.ResetMaskedPan()
		return nil
	case cards.FieldCardType:
		m.ResetCardType()
		return nil
	case cards.FieldProductType:
		m.ResetProductType()
		return nil
	case cards.FieldPaymentSystem:
		m.ResetPaymentSystem()
		return nil
	case cards.FieldCardClass:
		m.ResetCardClass()
		return nil
	case cards.FieldStatus:
		m.ResetStatus()
		return nil
	case cards.FieldTokenizationStatus:
		m.ResetTokenizationStatus()
		return nil
	case cards.FieldWallet:
		m.ResetWallet()
		return nil
	case cards.FieldCreationDate:
		m.ResetCreationDate()
		return nil
	case cards.FieldExpireDate:
		m.ResetExpireDate()
		return nil
	case cards.FieldModificationDate:
		m.ResetModificationDate()
		return nil
	}
	return fmt.Errorf("unknown Cards field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *CardsMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.account != nil {
		edges = append(edges, cards.EdgeAccount)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *CardsMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case cards.EdgeAccount:
		if id := m.account; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *CardsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *CardsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *CardsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedaccount {
		edges = append(edges, cards.EdgeAccount)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *CardsMutation) EdgeCleared(name string) bool {
	switch name {
	case cards.EdgeAccount:
		return m.clearedaccount
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *CardsMutation) ClearEdge(name string) error {
	switch name {
	case cards.EdgeAccount:
		m.ClearAccount()
		return nil
	}
	return fmt.Errorf("unknown Cards unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *CardsMutation) ResetEdge(name string) error {
	switch name {
	case cards.EdgeAccount:
		m.ResetAccount()
		return nil
	}
	return fmt.Errorf("unknown Cards edge %s", name)
}

// ClientVerificationResultsMutation represents an operation that mutates the ClientVerificationResults nodes in the graph.
type ClientVerificationResultsMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	create_time         *time.Time
	update_time         *time.Time
	user_id             *uuid.UUID
	status              *string
	date                *time.Time
	verification_result *string
	rejection_reason    *string
	clearedFields       map[string]struct{}
	done                bool
	oldValue            func(context.Context) (*ClientVerificationResults, error)
	predicates          []predicate.ClientVerificationResults
}

var _ ent.Mutation = (*ClientVerificationResultsMutation)(nil)

// clientverificationresultsOption allows management of the mutation configuration using functional options.
type clientverificationresultsOption func(*ClientVerificationResultsMutation)

// newClientVerificationResultsMutation creates new mutation for the ClientVerificationResults entity.
func newClientVerificationResultsMutation(c config, op Op, opts ...clientverificationresultsOption) *ClientVerificationResultsMutation {
	m := &ClientVerificationResultsMutation{
		config:        c,
		op:            op,
		typ:           TypeClientVerificationResults,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withClientVerificationResultsID sets the ID field of the mutation.
func withClientVerificationResultsID(id uuid.UUID) clientverificationresultsOption {
	return func(m *ClientVerificationResultsMutation) {
		var (
			err   error
			once  sync.Once
			value *ClientVerificationResults
		)
		m.oldValue = func(ctx context.Context) (*ClientVerificationResults, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().ClientVerificationResults.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withClientVerificationResults sets the old ClientVerificationResults of the mutation.
func withClientVerificationResults(node *ClientVerificationResults) clientverificationresultsOption {
	return func(m *ClientVerificationResultsMutation) {
		m.oldValue = func(context.Context) (*ClientVerificationResults, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ClientVerificationResultsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ClientVerificationResultsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of ClientVerificationResults entities.
func (m *ClientVerificationResultsMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ClientVerificationResultsMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ClientVerificationResultsMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().ClientVerificationResults.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *ClientVerificationResultsMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *ClientVerificationResultsMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the ClientVerificationResults entity.
// If the ClientVerificationResults object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientVerificationResultsMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *ClientVerificationResultsMutation) ResetCreateTime() {
	m.create_time = nil
}

// SetUpdateTime sets the "update_time" field.
func (m *ClientVerificationResultsMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *ClientVerificationResultsMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the ClientVerificationResults entity.
// If the ClientVerificationResults object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientVerificationResultsMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *ClientVerificationResultsMutation) ResetUpdateTime() {
	m.update_time = nil
}

// SetUserID sets the "user_id" field.
func (m *ClientVerificationResultsMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *ClientVerificationResultsMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the ClientVerificationResults entity.
// If the ClientVerificationResults object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientVerificationResultsMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *ClientVerificationResultsMutation) ResetUserID() {
	m.user_id = nil
}

// SetStatus sets the "status" field.
func (m *ClientVerificationResultsMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *ClientVerificationResultsMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the ClientVerificationResults entity.
// If the ClientVerificationResults object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientVerificationResultsMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *ClientVerificationResultsMutation) ResetStatus() {
	m.status = nil
}

// SetDate sets the "date" field.
func (m *ClientVerificationResultsMutation) SetDate(t time.Time) {
	m.date = &t
}

// Date returns the value of the "date" field in the mutation.
func (m *ClientVerificationResultsMutation) Date() (r time.Time, exists bool) {
	v := m.date
	if v == nil {
		return
	}
	return *v, true
}

// OldDate returns the old "date" field's value of the ClientVerificationResults entity.
// If the ClientVerificationResults object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientVerificationResultsMutation) OldDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDate: %w", err)
	}
	return oldValue.Date, nil
}

// ResetDate resets all changes to the "date" field.
func (m *ClientVerificationResultsMutation) ResetDate() {
	m.date = nil
}

// SetVerificationResult sets the "verification_result" field.
func (m *ClientVerificationResultsMutation) SetVerificationResult(s string) {
	m.verification_result = &s
}

// VerificationResult returns the value of the "verification_result" field in the mutation.
func (m *ClientVerificationResultsMutation) VerificationResult() (r string, exists bool) {
	v := m.verification_result
	if v == nil {
		return
	}
	return *v, true
}

// OldVerificationResult returns the old "verification_result" field's value of the ClientVerificationResults entity.
// If the ClientVerificationResults object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientVerificationResultsMutation) OldVerificationResult(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVerificationResult is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVerificationResult requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVerificationResult: %w", err)
	}
	return oldValue.VerificationResult, nil
}

// ResetVerificationResult resets all changes to the "verification_result" field.
func (m *ClientVerificationResultsMutation) ResetVerificationResult() {
	m.verification_result = nil
}

// SetRejectionReason sets the "rejection_reason" field.
func (m *ClientVerificationResultsMutation) SetRejectionReason(s string) {
	m.rejection_reason = &s
}

// RejectionReason returns the value of the "rejection_reason" field in the mutation.
func (m *ClientVerificationResultsMutation) RejectionReason() (r string, exists bool) {
	v := m.rejection_reason
	if v == nil {
		return
	}
	return *v, true
}

// OldRejectionReason returns the old "rejection_reason" field's value of the ClientVerificationResults entity.
// If the ClientVerificationResults object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientVerificationResultsMutation) OldRejectionReason(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRejectionReason is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRejectionReason requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRejectionReason: %w", err)
	}
	return oldValue.RejectionReason, nil
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (m *ClientVerificationResultsMutation) ClearRejectionReason() {
	m.rejection_reason = nil
	m.clearedFields[clientverificationresults.FieldRejectionReason] = struct{}{}
}

// RejectionReasonCleared returns if the "rejection_reason" field was cleared in this mutation.
func (m *ClientVerificationResultsMutation) RejectionReasonCleared() bool {
	_, ok := m.clearedFields[clientverificationresults.FieldRejectionReason]
	return ok
}

// ResetRejectionReason resets all changes to the "rejection_reason" field.
func (m *ClientVerificationResultsMutation) ResetRejectionReason() {
	m.rejection_reason = nil
	delete(m.clearedFields, clientverificationresults.FieldRejectionReason)
}

// Where appends a list predicates to the ClientVerificationResultsMutation builder.
func (m *ClientVerificationResultsMutation) Where(ps ...predicate.ClientVerificationResults) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ClientVerificationResultsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ClientVerificationResultsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.ClientVerificationResults, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ClientVerificationResultsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ClientVerificationResultsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (ClientVerificationResults).
func (m *ClientVerificationResultsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ClientVerificationResultsMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.create_time != nil {
		fields = append(fields, clientverificationresults.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, clientverificationresults.FieldUpdateTime)
	}
	if m.user_id != nil {
		fields = append(fields, clientverificationresults.FieldUserID)
	}
	if m.status != nil {
		fields = append(fields, clientverificationresults.FieldStatus)
	}
	if m.date != nil {
		fields = append(fields, clientverificationresults.FieldDate)
	}
	if m.verification_result != nil {
		fields = append(fields, clientverificationresults.FieldVerificationResult)
	}
	if m.rejection_reason != nil {
		fields = append(fields, clientverificationresults.FieldRejectionReason)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ClientVerificationResultsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case clientverificationresults.FieldCreateTime:
		return m.CreateTime()
	case clientverificationresults.FieldUpdateTime:
		return m.UpdateTime()
	case clientverificationresults.FieldUserID:
		return m.UserID()
	case clientverificationresults.FieldStatus:
		return m.Status()
	case clientverificationresults.FieldDate:
		return m.Date()
	case clientverificationresults.FieldVerificationResult:
		return m.VerificationResult()
	case clientverificationresults.FieldRejectionReason:
		return m.RejectionReason()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ClientVerificationResultsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case clientverificationresults.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case clientverificationresults.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case clientverificationresults.FieldUserID:
		return m.OldUserID(ctx)
	case clientverificationresults.FieldStatus:
		return m.OldStatus(ctx)
	case clientverificationresults.FieldDate:
		return m.OldDate(ctx)
	case clientverificationresults.FieldVerificationResult:
		return m.OldVerificationResult(ctx)
	case clientverificationresults.FieldRejectionReason:
		return m.OldRejectionReason(ctx)
	}
	return nil, fmt.Errorf("unknown ClientVerificationResults field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientVerificationResultsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case clientverificationresults.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case clientverificationresults.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case clientverificationresults.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case clientverificationresults.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case clientverificationresults.FieldDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDate(v)
		return nil
	case clientverificationresults.FieldVerificationResult:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVerificationResult(v)
		return nil
	case clientverificationresults.FieldRejectionReason:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRejectionReason(v)
		return nil
	}
	return fmt.Errorf("unknown ClientVerificationResults field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ClientVerificationResultsMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ClientVerificationResultsMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientVerificationResultsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown ClientVerificationResults numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ClientVerificationResultsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(clientverificationresults.FieldRejectionReason) {
		fields = append(fields, clientverificationresults.FieldRejectionReason)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ClientVerificationResultsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ClientVerificationResultsMutation) ClearField(name string) error {
	switch name {
	case clientverificationresults.FieldRejectionReason:
		m.ClearRejectionReason()
		return nil
	}
	return fmt.Errorf("unknown ClientVerificationResults nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ClientVerificationResultsMutation) ResetField(name string) error {
	switch name {
	case clientverificationresults.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case clientverificationresults.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case clientverificationresults.FieldUserID:
		m.ResetUserID()
		return nil
	case clientverificationresults.FieldStatus:
		m.ResetStatus()
		return nil
	case clientverificationresults.FieldDate:
		m.ResetDate()
		return nil
	case clientverificationresults.FieldVerificationResult:
		m.ResetVerificationResult()
		return nil
	case clientverificationresults.FieldRejectionReason:
		m.ResetRejectionReason()
		return nil
	}
	return fmt.Errorf("unknown ClientVerificationResults field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ClientVerificationResultsMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ClientVerificationResultsMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ClientVerificationResultsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ClientVerificationResultsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ClientVerificationResultsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ClientVerificationResultsMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ClientVerificationResultsMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown ClientVerificationResults unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ClientVerificationResultsMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown ClientVerificationResults edge %s", name)
}

// FinContractMutation represents an operation that mutates the FinContract nodes in the graph.
type FinContractMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	create_time         *time.Time
	update_time         *time.Time
	contract_type       *fincontract.ContractType
	contract_currency   *fincontract.ContractCurrency
	iban                *string
	status              *fincontract.Status
	creation_date       *time.Time
	card_id             *uuid.UUID
	contract_code       *string
	contract_data_open  *time.Time
	contract_data_close *time.Time
	user_id             *string
	clearedFields       map[string]struct{}
	cards               *uuid.UUID
	clearedcards        bool
	done                bool
	oldValue            func(context.Context) (*FinContract, error)
	predicates          []predicate.FinContract
}

var _ ent.Mutation = (*FinContractMutation)(nil)

// fincontractOption allows management of the mutation configuration using functional options.
type fincontractOption func(*FinContractMutation)

// newFinContractMutation creates new mutation for the FinContract entity.
func newFinContractMutation(c config, op Op, opts ...fincontractOption) *FinContractMutation {
	m := &FinContractMutation{
		config:        c,
		op:            op,
		typ:           TypeFinContract,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withFinContractID sets the ID field of the mutation.
func withFinContractID(id uuid.UUID) fincontractOption {
	return func(m *FinContractMutation) {
		var (
			err   error
			once  sync.Once
			value *FinContract
		)
		m.oldValue = func(ctx context.Context) (*FinContract, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().FinContract.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withFinContract sets the old FinContract of the mutation.
func withFinContract(node *FinContract) fincontractOption {
	return func(m *FinContractMutation) {
		m.oldValue = func(context.Context) (*FinContract, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m FinContractMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m FinContractMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of FinContract entities.
func (m *FinContractMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *FinContractMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *FinContractMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().FinContract.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *FinContractMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *FinContractMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *FinContractMutation) ResetCreateTime() {
	m.create_time = nil
}

// SetUpdateTime sets the "update_time" field.
func (m *FinContractMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *FinContractMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *FinContractMutation) ResetUpdateTime() {
	m.update_time = nil
}

// SetContractType sets the "contract_type" field.
func (m *FinContractMutation) SetContractType(ft fincontract.ContractType) {
	m.contract_type = &ft
}

// ContractType returns the value of the "contract_type" field in the mutation.
func (m *FinContractMutation) ContractType() (r fincontract.ContractType, exists bool) {
	v := m.contract_type
	if v == nil {
		return
	}
	return *v, true
}

// OldContractType returns the old "contract_type" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldContractType(ctx context.Context) (v fincontract.ContractType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContractType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContractType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContractType: %w", err)
	}
	return oldValue.ContractType, nil
}

// ResetContractType resets all changes to the "contract_type" field.
func (m *FinContractMutation) ResetContractType() {
	m.contract_type = nil
}

// SetContractCurrency sets the "contract_currency" field.
func (m *FinContractMutation) SetContractCurrency(fc fincontract.ContractCurrency) {
	m.contract_currency = &fc
}

// ContractCurrency returns the value of the "contract_currency" field in the mutation.
func (m *FinContractMutation) ContractCurrency() (r fincontract.ContractCurrency, exists bool) {
	v := m.contract_currency
	if v == nil {
		return
	}
	return *v, true
}

// OldContractCurrency returns the old "contract_currency" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldContractCurrency(ctx context.Context) (v fincontract.ContractCurrency, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContractCurrency is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContractCurrency requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContractCurrency: %w", err)
	}
	return oldValue.ContractCurrency, nil
}

// ResetContractCurrency resets all changes to the "contract_currency" field.
func (m *FinContractMutation) ResetContractCurrency() {
	m.contract_currency = nil
}

// SetIban sets the "iban" field.
func (m *FinContractMutation) SetIban(s string) {
	m.iban = &s
}

// Iban returns the value of the "iban" field in the mutation.
func (m *FinContractMutation) Iban() (r string, exists bool) {
	v := m.iban
	if v == nil {
		return
	}
	return *v, true
}

// OldIban returns the old "iban" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldIban(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIban is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIban requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIban: %w", err)
	}
	return oldValue.Iban, nil
}

// ResetIban resets all changes to the "iban" field.
func (m *FinContractMutation) ResetIban() {
	m.iban = nil
}

// SetStatus sets the "status" field.
func (m *FinContractMutation) SetStatus(f fincontract.Status) {
	m.status = &f
}

// Status returns the value of the "status" field in the mutation.
func (m *FinContractMutation) Status() (r fincontract.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldStatus(ctx context.Context) (v fincontract.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *FinContractMutation) ResetStatus() {
	m.status = nil
}

// SetCreationDate sets the "creation_date" field.
func (m *FinContractMutation) SetCreationDate(t time.Time) {
	m.creation_date = &t
}

// CreationDate returns the value of the "creation_date" field in the mutation.
func (m *FinContractMutation) CreationDate() (r time.Time, exists bool) {
	v := m.creation_date
	if v == nil {
		return
	}
	return *v, true
}

// OldCreationDate returns the old "creation_date" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldCreationDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreationDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreationDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreationDate: %w", err)
	}
	return oldValue.CreationDate, nil
}

// ResetCreationDate resets all changes to the "creation_date" field.
func (m *FinContractMutation) ResetCreationDate() {
	m.creation_date = nil
}

// SetCardID sets the "card_id" field.
func (m *FinContractMutation) SetCardID(u uuid.UUID) {
	m.card_id = &u
}

// CardID returns the value of the "card_id" field in the mutation.
func (m *FinContractMutation) CardID() (r uuid.UUID, exists bool) {
	v := m.card_id
	if v == nil {
		return
	}
	return *v, true
}

// OldCardID returns the old "card_id" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldCardID(ctx context.Context) (v *uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCardID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCardID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCardID: %w", err)
	}
	return oldValue.CardID, nil
}

// ClearCardID clears the value of the "card_id" field.
func (m *FinContractMutation) ClearCardID() {
	m.card_id = nil
	m.clearedFields[fincontract.FieldCardID] = struct{}{}
}

// CardIDCleared returns if the "card_id" field was cleared in this mutation.
func (m *FinContractMutation) CardIDCleared() bool {
	_, ok := m.clearedFields[fincontract.FieldCardID]
	return ok
}

// ResetCardID resets all changes to the "card_id" field.
func (m *FinContractMutation) ResetCardID() {
	m.card_id = nil
	delete(m.clearedFields, fincontract.FieldCardID)
}

// SetContractCode sets the "contract_code" field.
func (m *FinContractMutation) SetContractCode(s string) {
	m.contract_code = &s
}

// ContractCode returns the value of the "contract_code" field in the mutation.
func (m *FinContractMutation) ContractCode() (r string, exists bool) {
	v := m.contract_code
	if v == nil {
		return
	}
	return *v, true
}

// OldContractCode returns the old "contract_code" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldContractCode(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContractCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContractCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContractCode: %w", err)
	}
	return oldValue.ContractCode, nil
}

// ClearContractCode clears the value of the "contract_code" field.
func (m *FinContractMutation) ClearContractCode() {
	m.contract_code = nil
	m.clearedFields[fincontract.FieldContractCode] = struct{}{}
}

// ContractCodeCleared returns if the "contract_code" field was cleared in this mutation.
func (m *FinContractMutation) ContractCodeCleared() bool {
	_, ok := m.clearedFields[fincontract.FieldContractCode]
	return ok
}

// ResetContractCode resets all changes to the "contract_code" field.
func (m *FinContractMutation) ResetContractCode() {
	m.contract_code = nil
	delete(m.clearedFields, fincontract.FieldContractCode)
}

// SetContractDataOpen sets the "contract_data_open" field.
func (m *FinContractMutation) SetContractDataOpen(t time.Time) {
	m.contract_data_open = &t
}

// ContractDataOpen returns the value of the "contract_data_open" field in the mutation.
func (m *FinContractMutation) ContractDataOpen() (r time.Time, exists bool) {
	v := m.contract_data_open
	if v == nil {
		return
	}
	return *v, true
}

// OldContractDataOpen returns the old "contract_data_open" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldContractDataOpen(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContractDataOpen is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContractDataOpen requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContractDataOpen: %w", err)
	}
	return oldValue.ContractDataOpen, nil
}

// ResetContractDataOpen resets all changes to the "contract_data_open" field.
func (m *FinContractMutation) ResetContractDataOpen() {
	m.contract_data_open = nil
}

// SetContractDataClose sets the "contract_data_close" field.
func (m *FinContractMutation) SetContractDataClose(t time.Time) {
	m.contract_data_close = &t
}

// ContractDataClose returns the value of the "contract_data_close" field in the mutation.
func (m *FinContractMutation) ContractDataClose() (r time.Time, exists bool) {
	v := m.contract_data_close
	if v == nil {
		return
	}
	return *v, true
}

// OldContractDataClose returns the old "contract_data_close" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldContractDataClose(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContractDataClose is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContractDataClose requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContractDataClose: %w", err)
	}
	return oldValue.ContractDataClose, nil
}

// ClearContractDataClose clears the value of the "contract_data_close" field.
func (m *FinContractMutation) ClearContractDataClose() {
	m.contract_data_close = nil
	m.clearedFields[fincontract.FieldContractDataClose] = struct{}{}
}

// ContractDataCloseCleared returns if the "contract_data_close" field was cleared in this mutation.
func (m *FinContractMutation) ContractDataCloseCleared() bool {
	_, ok := m.clearedFields[fincontract.FieldContractDataClose]
	return ok
}

// ResetContractDataClose resets all changes to the "contract_data_close" field.
func (m *FinContractMutation) ResetContractDataClose() {
	m.contract_data_close = nil
	delete(m.clearedFields, fincontract.FieldContractDataClose)
}

// SetUserID sets the "user_id" field.
func (m *FinContractMutation) SetUserID(s string) {
	m.user_id = &s
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *FinContractMutation) UserID() (r string, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the FinContract entity.
// If the FinContract object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FinContractMutation) OldUserID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ClearUserID clears the value of the "user_id" field.
func (m *FinContractMutation) ClearUserID() {
	m.user_id = nil
	m.clearedFields[fincontract.FieldUserID] = struct{}{}
}

// UserIDCleared returns if the "user_id" field was cleared in this mutation.
func (m *FinContractMutation) UserIDCleared() bool {
	_, ok := m.clearedFields[fincontract.FieldUserID]
	return ok
}

// ResetUserID resets all changes to the "user_id" field.
func (m *FinContractMutation) ResetUserID() {
	m.user_id = nil
	delete(m.clearedFields, fincontract.FieldUserID)
}

// SetCardsID sets the "cards" edge to the Cards entity by id.
func (m *FinContractMutation) SetCardsID(id uuid.UUID) {
	m.cards = &id
}

// ClearCards clears the "cards" edge to the Cards entity.
func (m *FinContractMutation) ClearCards() {
	m.clearedcards = true
}

// CardsCleared reports if the "cards" edge to the Cards entity was cleared.
func (m *FinContractMutation) CardsCleared() bool {
	return m.clearedcards
}

// CardsID returns the "cards" edge ID in the mutation.
func (m *FinContractMutation) CardsID() (id uuid.UUID, exists bool) {
	if m.cards != nil {
		return *m.cards, true
	}
	return
}

// CardsIDs returns the "cards" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// CardsID instead. It exists only for internal usage by the builders.
func (m *FinContractMutation) CardsIDs() (ids []uuid.UUID) {
	if id := m.cards; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetCards resets all changes to the "cards" edge.
func (m *FinContractMutation) ResetCards() {
	m.cards = nil
	m.clearedcards = false
}

// Where appends a list predicates to the FinContractMutation builder.
func (m *FinContractMutation) Where(ps ...predicate.FinContract) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the FinContractMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *FinContractMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.FinContract, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *FinContractMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *FinContractMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (FinContract).
func (m *FinContractMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *FinContractMutation) Fields() []string {
	fields := make([]string, 0, 12)
	if m.create_time != nil {
		fields = append(fields, fincontract.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, fincontract.FieldUpdateTime)
	}
	if m.contract_type != nil {
		fields = append(fields, fincontract.FieldContractType)
	}
	if m.contract_currency != nil {
		fields = append(fields, fincontract.FieldContractCurrency)
	}
	if m.iban != nil {
		fields = append(fields, fincontract.FieldIban)
	}
	if m.status != nil {
		fields = append(fields, fincontract.FieldStatus)
	}
	if m.creation_date != nil {
		fields = append(fields, fincontract.FieldCreationDate)
	}
	if m.card_id != nil {
		fields = append(fields, fincontract.FieldCardID)
	}
	if m.contract_code != nil {
		fields = append(fields, fincontract.FieldContractCode)
	}
	if m.contract_data_open != nil {
		fields = append(fields, fincontract.FieldContractDataOpen)
	}
	if m.contract_data_close != nil {
		fields = append(fields, fincontract.FieldContractDataClose)
	}
	if m.user_id != nil {
		fields = append(fields, fincontract.FieldUserID)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *FinContractMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case fincontract.FieldCreateTime:
		return m.CreateTime()
	case fincontract.FieldUpdateTime:
		return m.UpdateTime()
	case fincontract.FieldContractType:
		return m.ContractType()
	case fincontract.FieldContractCurrency:
		return m.ContractCurrency()
	case fincontract.FieldIban:
		return m.Iban()
	case fincontract.FieldStatus:
		return m.Status()
	case fincontract.FieldCreationDate:
		return m.CreationDate()
	case fincontract.FieldCardID:
		return m.CardID()
	case fincontract.FieldContractCode:
		return m.ContractCode()
	case fincontract.FieldContractDataOpen:
		return m.ContractDataOpen()
	case fincontract.FieldContractDataClose:
		return m.ContractDataClose()
	case fincontract.FieldUserID:
		return m.UserID()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *FinContractMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case fincontract.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case fincontract.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case fincontract.FieldContractType:
		return m.OldContractType(ctx)
	case fincontract.FieldContractCurrency:
		return m.OldContractCurrency(ctx)
	case fincontract.FieldIban:
		return m.OldIban(ctx)
	case fincontract.FieldStatus:
		return m.OldStatus(ctx)
	case fincontract.FieldCreationDate:
		return m.OldCreationDate(ctx)
	case fincontract.FieldCardID:
		return m.OldCardID(ctx)
	case fincontract.FieldContractCode:
		return m.OldContractCode(ctx)
	case fincontract.FieldContractDataOpen:
		return m.OldContractDataOpen(ctx)
	case fincontract.FieldContractDataClose:
		return m.OldContractDataClose(ctx)
	case fincontract.FieldUserID:
		return m.OldUserID(ctx)
	}
	return nil, fmt.Errorf("unknown FinContract field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FinContractMutation) SetField(name string, value ent.Value) error {
	switch name {
	case fincontract.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case fincontract.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case fincontract.FieldContractType:
		v, ok := value.(fincontract.ContractType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContractType(v)
		return nil
	case fincontract.FieldContractCurrency:
		v, ok := value.(fincontract.ContractCurrency)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContractCurrency(v)
		return nil
	case fincontract.FieldIban:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIban(v)
		return nil
	case fincontract.FieldStatus:
		v, ok := value.(fincontract.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case fincontract.FieldCreationDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreationDate(v)
		return nil
	case fincontract.FieldCardID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCardID(v)
		return nil
	case fincontract.FieldContractCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContractCode(v)
		return nil
	case fincontract.FieldContractDataOpen:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContractDataOpen(v)
		return nil
	case fincontract.FieldContractDataClose:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContractDataClose(v)
		return nil
	case fincontract.FieldUserID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	}
	return fmt.Errorf("unknown FinContract field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *FinContractMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *FinContractMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FinContractMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown FinContract numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *FinContractMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(fincontract.FieldCardID) {
		fields = append(fields, fincontract.FieldCardID)
	}
	if m.FieldCleared(fincontract.FieldContractCode) {
		fields = append(fields, fincontract.FieldContractCode)
	}
	if m.FieldCleared(fincontract.FieldContractDataClose) {
		fields = append(fields, fincontract.FieldContractDataClose)
	}
	if m.FieldCleared(fincontract.FieldUserID) {
		fields = append(fields, fincontract.FieldUserID)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *FinContractMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *FinContractMutation) ClearField(name string) error {
	switch name {
	case fincontract.FieldCardID:
		m.ClearCardID()
		return nil
	case fincontract.FieldContractCode:
		m.ClearContractCode()
		return nil
	case fincontract.FieldContractDataClose:
		m.ClearContractDataClose()
		return nil
	case fincontract.FieldUserID:
		m.ClearUserID()
		return nil
	}
	return fmt.Errorf("unknown FinContract nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *FinContractMutation) ResetField(name string) error {
	switch name {
	case fincontract.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case fincontract.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case fincontract.FieldContractType:
		m.ResetContractType()
		return nil
	case fincontract.FieldContractCurrency:
		m.ResetContractCurrency()
		return nil
	case fincontract.FieldIban:
		m.ResetIban()
		return nil
	case fincontract.FieldStatus:
		m.ResetStatus()
		return nil
	case fincontract.FieldCreationDate:
		m.ResetCreationDate()
		return nil
	case fincontract.FieldCardID:
		m.ResetCardID()
		return nil
	case fincontract.FieldContractCode:
		m.ResetContractCode()
		return nil
	case fincontract.FieldContractDataOpen:
		m.ResetContractDataOpen()
		return nil
	case fincontract.FieldContractDataClose:
		m.ResetContractDataClose()
		return nil
	case fincontract.FieldUserID:
		m.ResetUserID()
		return nil
	}
	return fmt.Errorf("unknown FinContract field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *FinContractMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.cards != nil {
		edges = append(edges, fincontract.EdgeCards)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *FinContractMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case fincontract.EdgeCards:
		if id := m.cards; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *FinContractMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *FinContractMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *FinContractMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedcards {
		edges = append(edges, fincontract.EdgeCards)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *FinContractMutation) EdgeCleared(name string) bool {
	switch name {
	case fincontract.EdgeCards:
		return m.clearedcards
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *FinContractMutation) ClearEdge(name string) error {
	switch name {
	case fincontract.EdgeCards:
		m.ClearCards()
		return nil
	}
	return fmt.Errorf("unknown FinContract unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *FinContractMutation) ResetEdge(name string) error {
	switch name {
	case fincontract.EdgeCards:
		m.ResetCards()
		return nil
	}
	return fmt.Errorf("unknown FinContract edge %s", name)
}

// HealthMutation represents an operation that mutates the Health nodes in the graph.
type HealthMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Health, error)
	predicates    []predicate.Health
}

var _ ent.Mutation = (*HealthMutation)(nil)

// healthOption allows management of the mutation configuration using functional options.
type healthOption func(*HealthMutation)

// newHealthMutation creates new mutation for the Health entity.
func newHealthMutation(c config, op Op, opts ...healthOption) *HealthMutation {
	m := &HealthMutation{
		config:        c,
		op:            op,
		typ:           TypeHealth,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withHealthID sets the ID field of the mutation.
func withHealthID(id uuid.UUID) healthOption {
	return func(m *HealthMutation) {
		var (
			err   error
			once  sync.Once
			value *Health
		)
		m.oldValue = func(ctx context.Context) (*Health, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Health.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withHealth sets the old Health of the mutation.
func withHealth(node *Health) healthOption {
	return func(m *HealthMutation) {
		m.oldValue = func(context.Context) (*Health, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m HealthMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m HealthMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Health entities.
func (m *HealthMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *HealthMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *HealthMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Health.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the HealthMutation builder.
func (m *HealthMutation) Where(ps ...predicate.Health) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the HealthMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *HealthMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Health, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *HealthMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *HealthMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Health).
func (m *HealthMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *HealthMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *HealthMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *HealthMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Health field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *HealthMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *HealthMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Health numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *HealthMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *HealthMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *HealthMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Health nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *HealthMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *HealthMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *HealthMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *HealthMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *HealthMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *HealthMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *HealthMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *HealthMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Health unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *HealthMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Health edge %s", name)
}
