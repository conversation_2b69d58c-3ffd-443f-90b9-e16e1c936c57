// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
)

// CardsUpdate is the builder for updating Cards entities.
type CardsUpdate struct {
	config
	hooks    []Hook
	mutation *CardsMutation
}

// Where appends a list predicates to the CardsUpdate builder.
func (_u *CardsUpdate) Where(ps ...predicate.Cards) *CardsUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdateTime sets the "update_time" field.
func (_u *CardsUpdate) SetUpdateTime(v time.Time) *CardsUpdate {
	_u.mutation.SetUpdateTime(v)
	return _u
}

// SetClientID sets the "client_id" field.
func (_u *CardsUpdate) SetClientID(v uuid.UUID) *CardsUpdate {
	_u.mutation.SetClientID(v)
	return _u
}

// SetNillableClientID sets the "client_id" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableClientID(v *uuid.UUID) *CardsUpdate {
	if v != nil {
		_u.SetClientID(*v)
	}
	return _u
}

// ClearClientID clears the value of the "client_id" field.
func (_u *CardsUpdate) ClearClientID() *CardsUpdate {
	_u.mutation.ClearClientID()
	return _u
}

// SetAttachedAccountID sets the "attached_account_id" field.
func (_u *CardsUpdate) SetAttachedAccountID(v uuid.UUID) *CardsUpdate {
	_u.mutation.SetAttachedAccountID(v)
	return _u
}

// SetNillableAttachedAccountID sets the "attached_account_id" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableAttachedAccountID(v *uuid.UUID) *CardsUpdate {
	if v != nil {
		_u.SetAttachedAccountID(*v)
	}
	return _u
}

// ClearAttachedAccountID clears the value of the "attached_account_id" field.
func (_u *CardsUpdate) ClearAttachedAccountID() *CardsUpdate {
	_u.mutation.ClearAttachedAccountID()
	return _u
}

// SetEmbossingName sets the "embossing_name" field.
func (_u *CardsUpdate) SetEmbossingName(v string) *CardsUpdate {
	_u.mutation.SetEmbossingName(v)
	return _u
}

// SetNillableEmbossingName sets the "embossing_name" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableEmbossingName(v *string) *CardsUpdate {
	if v != nil {
		_u.SetEmbossingName(*v)
	}
	return _u
}

// ClearEmbossingName clears the value of the "embossing_name" field.
func (_u *CardsUpdate) ClearEmbossingName() *CardsUpdate {
	_u.mutation.ClearEmbossingName()
	return _u
}

// SetMaskedPan sets the "masked_pan" field.
func (_u *CardsUpdate) SetMaskedPan(v string) *CardsUpdate {
	_u.mutation.SetMaskedPan(v)
	return _u
}

// SetNillableMaskedPan sets the "masked_pan" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableMaskedPan(v *string) *CardsUpdate {
	if v != nil {
		_u.SetMaskedPan(*v)
	}
	return _u
}

// ClearMaskedPan clears the value of the "masked_pan" field.
func (_u *CardsUpdate) ClearMaskedPan() *CardsUpdate {
	_u.mutation.ClearMaskedPan()
	return _u
}

// SetCardType sets the "card_type" field.
func (_u *CardsUpdate) SetCardType(v cards.CardType) *CardsUpdate {
	_u.mutation.SetCardType(v)
	return _u
}

// SetNillableCardType sets the "card_type" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableCardType(v *cards.CardType) *CardsUpdate {
	if v != nil {
		_u.SetCardType(*v)
	}
	return _u
}

// SetProductType sets the "product_type" field.
func (_u *CardsUpdate) SetProductType(v cards.ProductType) *CardsUpdate {
	_u.mutation.SetProductType(v)
	return _u
}

// SetNillableProductType sets the "product_type" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableProductType(v *cards.ProductType) *CardsUpdate {
	if v != nil {
		_u.SetProductType(*v)
	}
	return _u
}

// SetPaymentSystem sets the "payment_system" field.
func (_u *CardsUpdate) SetPaymentSystem(v cards.PaymentSystem) *CardsUpdate {
	_u.mutation.SetPaymentSystem(v)
	return _u
}

// SetNillablePaymentSystem sets the "payment_system" field if the given value is not nil.
func (_u *CardsUpdate) SetNillablePaymentSystem(v *cards.PaymentSystem) *CardsUpdate {
	if v != nil {
		_u.SetPaymentSystem(*v)
	}
	return _u
}

// SetCardClass sets the "card_class" field.
func (_u *CardsUpdate) SetCardClass(v cards.CardClass) *CardsUpdate {
	_u.mutation.SetCardClass(v)
	return _u
}

// SetNillableCardClass sets the "card_class" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableCardClass(v *cards.CardClass) *CardsUpdate {
	if v != nil {
		_u.SetCardClass(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *CardsUpdate) SetStatus(v cards.Status) *CardsUpdate {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableStatus(v *cards.Status) *CardsUpdate {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetTokenizationStatus sets the "tokenization_status" field.
func (_u *CardsUpdate) SetTokenizationStatus(v cards.TokenizationStatus) *CardsUpdate {
	_u.mutation.SetTokenizationStatus(v)
	return _u
}

// SetNillableTokenizationStatus sets the "tokenization_status" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableTokenizationStatus(v *cards.TokenizationStatus) *CardsUpdate {
	if v != nil {
		_u.SetTokenizationStatus(*v)
	}
	return _u
}

// SetWallet sets the "wallet" field.
func (_u *CardsUpdate) SetWallet(v string) *CardsUpdate {
	_u.mutation.SetWallet(v)
	return _u
}

// SetNillableWallet sets the "wallet" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableWallet(v *string) *CardsUpdate {
	if v != nil {
		_u.SetWallet(*v)
	}
	return _u
}

// ClearWallet clears the value of the "wallet" field.
func (_u *CardsUpdate) ClearWallet() *CardsUpdate {
	_u.mutation.ClearWallet()
	return _u
}

// SetExpireDate sets the "expire_date" field.
func (_u *CardsUpdate) SetExpireDate(v time.Time) *CardsUpdate {
	_u.mutation.SetExpireDate(v)
	return _u
}

// SetNillableExpireDate sets the "expire_date" field if the given value is not nil.
func (_u *CardsUpdate) SetNillableExpireDate(v *time.Time) *CardsUpdate {
	if v != nil {
		_u.SetExpireDate(*v)
	}
	return _u
}

// ClearExpireDate clears the value of the "expire_date" field.
func (_u *CardsUpdate) ClearExpireDate() *CardsUpdate {
	_u.mutation.ClearExpireDate()
	return _u
}

// SetModificationDate sets the "modification_date" field.
func (_u *CardsUpdate) SetModificationDate(v time.Time) *CardsUpdate {
	_u.mutation.SetModificationDate(v)
	return _u
}

// SetAccountID sets the "account" edge to the Accounts entity by ID.
func (_u *CardsUpdate) SetAccountID(id uuid.UUID) *CardsUpdate {
	_u.mutation.SetAccountID(id)
	return _u
}

// SetNillableAccountID sets the "account" edge to the Accounts entity by ID if the given value is not nil.
func (_u *CardsUpdate) SetNillableAccountID(id *uuid.UUID) *CardsUpdate {
	if id != nil {
		_u = _u.SetAccountID(*id)
	}
	return _u
}

// SetAccount sets the "account" edge to the Accounts entity.
func (_u *CardsUpdate) SetAccount(v *Accounts) *CardsUpdate {
	return _u.SetAccountID(v.ID)
}

// Mutation returns the CardsMutation object of the builder.
func (_u *CardsUpdate) Mutation() *CardsMutation {
	return _u.mutation
}

// ClearAccount clears the "account" edge to the Accounts entity.
func (_u *CardsUpdate) ClearAccount() *CardsUpdate {
	_u.mutation.ClearAccount()
	return _u
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *CardsUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *CardsUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *CardsUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *CardsUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *CardsUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := cards.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
	if _, ok := _u.mutation.ModificationDate(); !ok {
		v := cards.UpdateDefaultModificationDate()
		_u.mutation.SetModificationDate(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *CardsUpdate) check() error {
	if v, ok := _u.mutation.EmbossingName(); ok {
		if err := cards.EmbossingNameValidator(v); err != nil {
			return &ValidationError{Name: "embossing_name", err: fmt.Errorf(`ent: validator failed for field "Cards.embossing_name": %w`, err)}
		}
	}
	if v, ok := _u.mutation.MaskedPan(); ok {
		if err := cards.MaskedPanValidator(v); err != nil {
			return &ValidationError{Name: "masked_pan", err: fmt.Errorf(`ent: validator failed for field "Cards.masked_pan": %w`, err)}
		}
	}
	if v, ok := _u.mutation.CardType(); ok {
		if err := cards.CardTypeValidator(v); err != nil {
			return &ValidationError{Name: "card_type", err: fmt.Errorf(`ent: validator failed for field "Cards.card_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.ProductType(); ok {
		if err := cards.ProductTypeValidator(v); err != nil {
			return &ValidationError{Name: "product_type", err: fmt.Errorf(`ent: validator failed for field "Cards.product_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.PaymentSystem(); ok {
		if err := cards.PaymentSystemValidator(v); err != nil {
			return &ValidationError{Name: "payment_system", err: fmt.Errorf(`ent: validator failed for field "Cards.payment_system": %w`, err)}
		}
	}
	if v, ok := _u.mutation.CardClass(); ok {
		if err := cards.CardClassValidator(v); err != nil {
			return &ValidationError{Name: "card_class", err: fmt.Errorf(`ent: validator failed for field "Cards.card_class": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Status(); ok {
		if err := cards.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Cards.status": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TokenizationStatus(); ok {
		if err := cards.TokenizationStatusValidator(v); err != nil {
			return &ValidationError{Name: "tokenization_status", err: fmt.Errorf(`ent: validator failed for field "Cards.tokenization_status": %w`, err)}
		}
	}
	return nil
}

func (_u *CardsUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(cards.Table, cards.Columns, sqlgraph.NewFieldSpec(cards.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(cards.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ClientID(); ok {
		_spec.SetField(cards.FieldClientID, field.TypeUUID, value)
	}
	if _u.mutation.ClientIDCleared() {
		_spec.ClearField(cards.FieldClientID, field.TypeUUID)
	}
	if value, ok := _u.mutation.EmbossingName(); ok {
		_spec.SetField(cards.FieldEmbossingName, field.TypeString, value)
	}
	if _u.mutation.EmbossingNameCleared() {
		_spec.ClearField(cards.FieldEmbossingName, field.TypeString)
	}
	if value, ok := _u.mutation.MaskedPan(); ok {
		_spec.SetField(cards.FieldMaskedPan, field.TypeString, value)
	}
	if _u.mutation.MaskedPanCleared() {
		_spec.ClearField(cards.FieldMaskedPan, field.TypeString)
	}
	if value, ok := _u.mutation.CardType(); ok {
		_spec.SetField(cards.FieldCardType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.ProductType(); ok {
		_spec.SetField(cards.FieldProductType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.PaymentSystem(); ok {
		_spec.SetField(cards.FieldPaymentSystem, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.CardClass(); ok {
		_spec.SetField(cards.FieldCardClass, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(cards.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.TokenizationStatus(); ok {
		_spec.SetField(cards.FieldTokenizationStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.Wallet(); ok {
		_spec.SetField(cards.FieldWallet, field.TypeString, value)
	}
	if _u.mutation.WalletCleared() {
		_spec.ClearField(cards.FieldWallet, field.TypeString)
	}
	if value, ok := _u.mutation.ExpireDate(); ok {
		_spec.SetField(cards.FieldExpireDate, field.TypeTime, value)
	}
	if _u.mutation.ExpireDateCleared() {
		_spec.ClearField(cards.FieldExpireDate, field.TypeTime)
	}
	if value, ok := _u.mutation.ModificationDate(); ok {
		_spec.SetField(cards.FieldModificationDate, field.TypeTime, value)
	}
	if _u.mutation.AccountCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cards.AccountTable,
			Columns: []string{cards.AccountColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.AccountIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cards.AccountTable,
			Columns: []string{cards.AccountColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cards.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// CardsUpdateOne is the builder for updating a single Cards entity.
type CardsUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CardsMutation
}

// SetUpdateTime sets the "update_time" field.
func (_u *CardsUpdateOne) SetUpdateTime(v time.Time) *CardsUpdateOne {
	_u.mutation.SetUpdateTime(v)
	return _u
}

// SetClientID sets the "client_id" field.
func (_u *CardsUpdateOne) SetClientID(v uuid.UUID) *CardsUpdateOne {
	_u.mutation.SetClientID(v)
	return _u
}

// SetNillableClientID sets the "client_id" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableClientID(v *uuid.UUID) *CardsUpdateOne {
	if v != nil {
		_u.SetClientID(*v)
	}
	return _u
}

// ClearClientID clears the value of the "client_id" field.
func (_u *CardsUpdateOne) ClearClientID() *CardsUpdateOne {
	_u.mutation.ClearClientID()
	return _u
}

// SetAttachedAccountID sets the "attached_account_id" field.
func (_u *CardsUpdateOne) SetAttachedAccountID(v uuid.UUID) *CardsUpdateOne {
	_u.mutation.SetAttachedAccountID(v)
	return _u
}

// SetNillableAttachedAccountID sets the "attached_account_id" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableAttachedAccountID(v *uuid.UUID) *CardsUpdateOne {
	if v != nil {
		_u.SetAttachedAccountID(*v)
	}
	return _u
}

// ClearAttachedAccountID clears the value of the "attached_account_id" field.
func (_u *CardsUpdateOne) ClearAttachedAccountID() *CardsUpdateOne {
	_u.mutation.ClearAttachedAccountID()
	return _u
}

// SetEmbossingName sets the "embossing_name" field.
func (_u *CardsUpdateOne) SetEmbossingName(v string) *CardsUpdateOne {
	_u.mutation.SetEmbossingName(v)
	return _u
}

// SetNillableEmbossingName sets the "embossing_name" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableEmbossingName(v *string) *CardsUpdateOne {
	if v != nil {
		_u.SetEmbossingName(*v)
	}
	return _u
}

// ClearEmbossingName clears the value of the "embossing_name" field.
func (_u *CardsUpdateOne) ClearEmbossingName() *CardsUpdateOne {
	_u.mutation.ClearEmbossingName()
	return _u
}

// SetMaskedPan sets the "masked_pan" field.
func (_u *CardsUpdateOne) SetMaskedPan(v string) *CardsUpdateOne {
	_u.mutation.SetMaskedPan(v)
	return _u
}

// SetNillableMaskedPan sets the "masked_pan" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableMaskedPan(v *string) *CardsUpdateOne {
	if v != nil {
		_u.SetMaskedPan(*v)
	}
	return _u
}

// ClearMaskedPan clears the value of the "masked_pan" field.
func (_u *CardsUpdateOne) ClearMaskedPan() *CardsUpdateOne {
	_u.mutation.ClearMaskedPan()
	return _u
}

// SetCardType sets the "card_type" field.
func (_u *CardsUpdateOne) SetCardType(v cards.CardType) *CardsUpdateOne {
	_u.mutation.SetCardType(v)
	return _u
}

// SetNillableCardType sets the "card_type" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableCardType(v *cards.CardType) *CardsUpdateOne {
	if v != nil {
		_u.SetCardType(*v)
	}
	return _u
}

// SetProductType sets the "product_type" field.
func (_u *CardsUpdateOne) SetProductType(v cards.ProductType) *CardsUpdateOne {
	_u.mutation.SetProductType(v)
	return _u
}

// SetNillableProductType sets the "product_type" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableProductType(v *cards.ProductType) *CardsUpdateOne {
	if v != nil {
		_u.SetProductType(*v)
	}
	return _u
}

// SetPaymentSystem sets the "payment_system" field.
func (_u *CardsUpdateOne) SetPaymentSystem(v cards.PaymentSystem) *CardsUpdateOne {
	_u.mutation.SetPaymentSystem(v)
	return _u
}

// SetNillablePaymentSystem sets the "payment_system" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillablePaymentSystem(v *cards.PaymentSystem) *CardsUpdateOne {
	if v != nil {
		_u.SetPaymentSystem(*v)
	}
	return _u
}

// SetCardClass sets the "card_class" field.
func (_u *CardsUpdateOne) SetCardClass(v cards.CardClass) *CardsUpdateOne {
	_u.mutation.SetCardClass(v)
	return _u
}

// SetNillableCardClass sets the "card_class" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableCardClass(v *cards.CardClass) *CardsUpdateOne {
	if v != nil {
		_u.SetCardClass(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *CardsUpdateOne) SetStatus(v cards.Status) *CardsUpdateOne {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableStatus(v *cards.Status) *CardsUpdateOne {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetTokenizationStatus sets the "tokenization_status" field.
func (_u *CardsUpdateOne) SetTokenizationStatus(v cards.TokenizationStatus) *CardsUpdateOne {
	_u.mutation.SetTokenizationStatus(v)
	return _u
}

// SetNillableTokenizationStatus sets the "tokenization_status" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableTokenizationStatus(v *cards.TokenizationStatus) *CardsUpdateOne {
	if v != nil {
		_u.SetTokenizationStatus(*v)
	}
	return _u
}

// SetWallet sets the "wallet" field.
func (_u *CardsUpdateOne) SetWallet(v string) *CardsUpdateOne {
	_u.mutation.SetWallet(v)
	return _u
}

// SetNillableWallet sets the "wallet" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableWallet(v *string) *CardsUpdateOne {
	if v != nil {
		_u.SetWallet(*v)
	}
	return _u
}

// ClearWallet clears the value of the "wallet" field.
func (_u *CardsUpdateOne) ClearWallet() *CardsUpdateOne {
	_u.mutation.ClearWallet()
	return _u
}

// SetExpireDate sets the "expire_date" field.
func (_u *CardsUpdateOne) SetExpireDate(v time.Time) *CardsUpdateOne {
	_u.mutation.SetExpireDate(v)
	return _u
}

// SetNillableExpireDate sets the "expire_date" field if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableExpireDate(v *time.Time) *CardsUpdateOne {
	if v != nil {
		_u.SetExpireDate(*v)
	}
	return _u
}

// ClearExpireDate clears the value of the "expire_date" field.
func (_u *CardsUpdateOne) ClearExpireDate() *CardsUpdateOne {
	_u.mutation.ClearExpireDate()
	return _u
}

// SetModificationDate sets the "modification_date" field.
func (_u *CardsUpdateOne) SetModificationDate(v time.Time) *CardsUpdateOne {
	_u.mutation.SetModificationDate(v)
	return _u
}

// SetAccountID sets the "account" edge to the Accounts entity by ID.
func (_u *CardsUpdateOne) SetAccountID(id uuid.UUID) *CardsUpdateOne {
	_u.mutation.SetAccountID(id)
	return _u
}

// SetNillableAccountID sets the "account" edge to the Accounts entity by ID if the given value is not nil.
func (_u *CardsUpdateOne) SetNillableAccountID(id *uuid.UUID) *CardsUpdateOne {
	if id != nil {
		_u = _u.SetAccountID(*id)
	}
	return _u
}

// SetAccount sets the "account" edge to the Accounts entity.
func (_u *CardsUpdateOne) SetAccount(v *Accounts) *CardsUpdateOne {
	return _u.SetAccountID(v.ID)
}

// Mutation returns the CardsMutation object of the builder.
func (_u *CardsUpdateOne) Mutation() *CardsMutation {
	return _u.mutation
}

// ClearAccount clears the "account" edge to the Accounts entity.
func (_u *CardsUpdateOne) ClearAccount() *CardsUpdateOne {
	_u.mutation.ClearAccount()
	return _u
}

// Where appends a list predicates to the CardsUpdate builder.
func (_u *CardsUpdateOne) Where(ps ...predicate.Cards) *CardsUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *CardsUpdateOne) Select(field string, fields ...string) *CardsUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Cards entity.
func (_u *CardsUpdateOne) Save(ctx context.Context) (*Cards, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *CardsUpdateOne) SaveX(ctx context.Context) *Cards {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *CardsUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *CardsUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *CardsUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := cards.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
	if _, ok := _u.mutation.ModificationDate(); !ok {
		v := cards.UpdateDefaultModificationDate()
		_u.mutation.SetModificationDate(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *CardsUpdateOne) check() error {
	if v, ok := _u.mutation.EmbossingName(); ok {
		if err := cards.EmbossingNameValidator(v); err != nil {
			return &ValidationError{Name: "embossing_name", err: fmt.Errorf(`ent: validator failed for field "Cards.embossing_name": %w`, err)}
		}
	}
	if v, ok := _u.mutation.MaskedPan(); ok {
		if err := cards.MaskedPanValidator(v); err != nil {
			return &ValidationError{Name: "masked_pan", err: fmt.Errorf(`ent: validator failed for field "Cards.masked_pan": %w`, err)}
		}
	}
	if v, ok := _u.mutation.CardType(); ok {
		if err := cards.CardTypeValidator(v); err != nil {
			return &ValidationError{Name: "card_type", err: fmt.Errorf(`ent: validator failed for field "Cards.card_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.ProductType(); ok {
		if err := cards.ProductTypeValidator(v); err != nil {
			return &ValidationError{Name: "product_type", err: fmt.Errorf(`ent: validator failed for field "Cards.product_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.PaymentSystem(); ok {
		if err := cards.PaymentSystemValidator(v); err != nil {
			return &ValidationError{Name: "payment_system", err: fmt.Errorf(`ent: validator failed for field "Cards.payment_system": %w`, err)}
		}
	}
	if v, ok := _u.mutation.CardClass(); ok {
		if err := cards.CardClassValidator(v); err != nil {
			return &ValidationError{Name: "card_class", err: fmt.Errorf(`ent: validator failed for field "Cards.card_class": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Status(); ok {
		if err := cards.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "Cards.status": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TokenizationStatus(); ok {
		if err := cards.TokenizationStatusValidator(v); err != nil {
			return &ValidationError{Name: "tokenization_status", err: fmt.Errorf(`ent: validator failed for field "Cards.tokenization_status": %w`, err)}
		}
	}
	return nil
}

func (_u *CardsUpdateOne) sqlSave(ctx context.Context) (_node *Cards, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(cards.Table, cards.Columns, sqlgraph.NewFieldSpec(cards.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Cards.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cards.FieldID)
		for _, f := range fields {
			if !cards.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != cards.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(cards.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ClientID(); ok {
		_spec.SetField(cards.FieldClientID, field.TypeUUID, value)
	}
	if _u.mutation.ClientIDCleared() {
		_spec.ClearField(cards.FieldClientID, field.TypeUUID)
	}
	if value, ok := _u.mutation.EmbossingName(); ok {
		_spec.SetField(cards.FieldEmbossingName, field.TypeString, value)
	}
	if _u.mutation.EmbossingNameCleared() {
		_spec.ClearField(cards.FieldEmbossingName, field.TypeString)
	}
	if value, ok := _u.mutation.MaskedPan(); ok {
		_spec.SetField(cards.FieldMaskedPan, field.TypeString, value)
	}
	if _u.mutation.MaskedPanCleared() {
		_spec.ClearField(cards.FieldMaskedPan, field.TypeString)
	}
	if value, ok := _u.mutation.CardType(); ok {
		_spec.SetField(cards.FieldCardType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.ProductType(); ok {
		_spec.SetField(cards.FieldProductType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.PaymentSystem(); ok {
		_spec.SetField(cards.FieldPaymentSystem, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.CardClass(); ok {
		_spec.SetField(cards.FieldCardClass, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(cards.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.TokenizationStatus(); ok {
		_spec.SetField(cards.FieldTokenizationStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.Wallet(); ok {
		_spec.SetField(cards.FieldWallet, field.TypeString, value)
	}
	if _u.mutation.WalletCleared() {
		_spec.ClearField(cards.FieldWallet, field.TypeString)
	}
	if value, ok := _u.mutation.ExpireDate(); ok {
		_spec.SetField(cards.FieldExpireDate, field.TypeTime, value)
	}
	if _u.mutation.ExpireDateCleared() {
		_spec.ClearField(cards.FieldExpireDate, field.TypeTime)
	}
	if value, ok := _u.mutation.ModificationDate(); ok {
		_spec.SetField(cards.FieldModificationDate, field.TypeTime, value)
	}
	if _u.mutation.AccountCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cards.AccountTable,
			Columns: []string{cards.AccountColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.AccountIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cards.AccountTable,
			Columns: []string{cards.AccountColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(accounts.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Cards{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cards.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
