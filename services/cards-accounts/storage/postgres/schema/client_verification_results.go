package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/mixin"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"github.com/google/uuid"
)

type (
	ClientVerificationResults struct {
		ent.Schema
	}
)

const (
	ClientVerificationResultsTable = "client_verification_results"
)

// Схема таблицы public.client_verification_results в БД Postgres сервиса cards-accounts
func (ClientVerificationResults) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default((func() uuid.UUID)(uuid.New)).
			Annotations(annotation.CustomAnnotation{Description: "Уникальный идентификатор записи"}),

		field.UUID("user_id", uuid.UUID{}).
			Annotations(annotation.CustomAnnotation{Description: "ID пользователя (клиента)"}),

		field.String("status").
			Annotations(annotation.CustomAnnotation{Description: "Статус проверки (InProgress, Done, Error)"}),

		field.Time("date").
			Annotations(annotation.CustomAnnotation{Description: "Дата/время создания записи"}),

		field.String("verification_result").
			Annotations(annotation.CustomAnnotation{Description: "Результат проверки (Pending, Approved, Rejected)"}),

		field.String("rejection_reason").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Причина отклонения (если есть)"}),
	}
}

func (ClientVerificationResults) Annotations() []schema.Annotation {
	return []schema.Annotation{}
}

func (ClientVerificationResults) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
