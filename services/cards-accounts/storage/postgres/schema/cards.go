/*
# Схема таблицы public.cards в БД Postgres сервиса cards-accounts

- ТТ: https://rmrkz.atlassian.net/wiki/spaces/ZMNRET/pages/********/VIRTUAL_CARD.ISSUE-
*/

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"entgo.io/ent/schema/mixin"
	"github.com/google/uuid"

	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	// usersSchema "git.redmadrobot.com/zaman/backend/zaman/services/users/storage/postgres/schema"
)

type (
	Cards struct {
		ent.Schema
	}
)

const (
	CardsTable = "cards"

	CardFieldID                 = "id"
	CardFieldClientID           = "client_id"
	CardFieldAttachedAccountID  = "attached_account_id"
	CardFieldEmbossingName      = "embossing_name"
	CardFieldMaskedPAN          = "masked_pan"
	CardFieldCardType           = "card_type"
	CardFieldProductType        = "product_type"
	CardFieldPaymentSystem      = "payment_system"
	CardFieldCardClass          = "card_class"
	CardFieldStatus             = "status"
	CardFieldTokenizationStatus = "tokenization_status"
	CardFieldWallet             = "wallet"
	CardFieldCreationDate       = "creation_date"
	CardFieldExpireDate         = "expire_date"
	CardFieldModificationDate   = "modification_date"
)

// Схема таблицы public.cards в БД Postgres сервиса cards-accounts
func (Cards) Fields() []ent.Field {
	return []ent.Field{
		field.UUID(CardFieldID, uuid.UUID{}).
			Default(uuid.New).
			Unique().
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор карты"}).
			Comment("идентификатор карты"),

		field.UUID(CardFieldClientID, uuid.UUID{}).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор клиента в БД Users (FK)"}).
			Comment("идентификатор клиента в БД Users (FK)"),

		field.UUID(CardFieldAttachedAccountID, uuid.UUID{}).
			Unique().
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор текущего счета, к которому привязана карта (FK)"}).
			Comment("идентификатор текущего счета, к которому привязана карта (FK O2O)"),

		field.String(CardFieldEmbossingName).
			MaxLen(25).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Наименование держателя карты"}).
			Comment("Наименование держателя карты"),

		field.String(CardFieldMaskedPAN).
			MaxLen(16).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Маскированный номер карты"}).
			Comment("маскированный номер карты (поле обновляется после создания карты в ПЦ)"),

		field.Enum(CardFieldCardType).
			Values(
				consts.CardPhysical.Str(),
				consts.CardVirtual.Str(),
			).
			Default(consts.CardVirtual.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Тип карты"}).
			Comment("тип карты"),

		field.Enum(CardFieldProductType).
			Values(
				consts.DebitCard.Str(),
			).
			Default(consts.DebitCard.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Тип карточного продукта"}).
			Comment("тип карточного продукта"),

		field.Enum(CardFieldPaymentSystem).
			Values(
				consts.MasterCard.Str(),
			).
			Default(consts.MasterCard.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Платежная система"}).
			Comment("платежная система"),

		field.Enum(CardFieldCardClass).
			Values(
				consts.MastercardPlatinum.Str(),
			).
			Default(consts.MastercardPlatinum.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Класс карты"}).
			Comment("класс карты"),

		field.Enum(CardFieldStatus).
			Values(
				consts.CardInOpening.Str(),
				consts.CardError.Str(),
				consts.CardActive.Str(),
				consts.CardBlocked.Str(),
			).
			Default(consts.CardInOpening.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Статус карты"}).
			Comment("статус карты (поле обновляется после создания карты в ПЦ)"),

		field.Enum(CardFieldTokenizationStatus).
			Values(
				consts.TokenizationTokenized.Str(),
				consts.TokenizationNeedVerify.Str(),
				consts.TokenizationNotTokenized.Str(),
			).
			Default(consts.TokenizationNotTokenized.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Статус токенизации карты"}).
			Comment("статус токенизации карты"),

		field.String(CardFieldWallet).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Кошелек"}).
			Comment("кошелек"),

		field.Time(CardFieldCreationDate).
			Default(time.Now).
			Immutable().
			Annotations(annotation.CustomAnnotation{Description: "Дата создания карты"}).
			Comment("дата и время создания карты"),

		field.Time(CardFieldExpireDate).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Срок действия карты"}).
			Comment("срок действия карты (поле обновляется после создания карты в ПЦ)"),

		field.Time(CardFieldModificationDate).
			Default(time.Now).
			UpdateDefault(time.Now).
			Annotations(annotation.CustomAnnotation{Description: "Дата и время последнего изменения данных карты"}).
			Comment("дата и время последнего изменения данных карты"),
	}
}

func (Cards) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("account", Accounts.Type).
			Field(CardFieldAttachedAccountID).
			Unique(),
	}
}

func (Cards) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields(
			CardFieldClientID,
			CardFieldStatus,
			CardFieldTokenizationStatus,
			CardFieldExpireDate,
			CardFieldModificationDate,
			CardFieldCreationDate,
		),
	}
}

func (Cards) Annotations() []schema.Annotation {
	return []schema.Annotation{}
}

func (Cards) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
