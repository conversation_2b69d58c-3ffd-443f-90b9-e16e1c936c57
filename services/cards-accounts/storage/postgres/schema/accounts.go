package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/mixin"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type (
	Accounts struct {
		ent.Schema
	}
)

const (
	AccountsTable = "accounts"
)

// Схема таблицы public.accounts в БД Postgres сервиса cards-accounts
func (Accounts) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default((func() uuid.UUID)(uuid.New)),
		field.String("client_iin").Annotations(annotation.CustomAnnotation{Description: "IИН клиента"}),
		field.String("client_code").Annotations(annotation.CustomAnnotation{Description: "Код клиента"}),
		field.String("client_name").Annotations(annotation.CustomAnnotation{Description: "Имя клиента"}),
		field.String("title").Annotations(annotation.CustomAnnotation{Description: "Наименование"}).Optional(),
		field.String("short_name").Annotations(annotation.CustomAnnotation{Description: "Краткое наименование счета"}).Optional(),
		field.String("currency").Annotations(annotation.CustomAnnotation{Description: "Валюта"}),
		field.String("iban").Annotations(annotation.CustomAnnotation{Description: "IBAN"}),
		field.String("type").Annotations(annotation.CustomAnnotation{Description: "Тип счета"}),
		field.String("status").Annotations(annotation.CustomAnnotation{Description: "Статус счета"}),
		field.String("date_opened").Annotations(annotation.CustomAnnotation{Description: "Дата открытия счета"}),

		field.Float("balance").GoType(decimal.Decimal{}).
			SchemaType(map[string]string{
				dialect.Postgres: "decimal(18, 2)",
			}).
			Annotations(annotation.CustomAnnotation{Description: "Баланс"}),

		field.Float("balance_natival").GoType(decimal.Decimal{}).
			SchemaType(map[string]string{
				dialect.Postgres: "decimal(18, 2)",
			}).
			Annotations(annotation.CustomAnnotation{Description: "Баланс в национальной валюте"}),

		field.Float("blocked_balance").GoType(decimal.Decimal{}).
			SchemaType(map[string]string{
				dialect.Postgres: "decimal(18, 2)",
			}).
			Annotations(annotation.CustomAnnotation{Description: "Заблокированный баланс"}),

		field.Float("available_balance").GoType(decimal.Decimal{}).
			SchemaType(map[string]string{
				dialect.Postgres: "decimal(18, 2)",
			}).
			Annotations(annotation.CustomAnnotation{Description: "Доступный баланс"}),

		field.String("date_closed").Annotations(annotation.CustomAnnotation{Description: "Дата закрытия счета"}),
		field.String("arrest_blocking").Annotations(annotation.CustomAnnotation{Description: "аресты"}),

		field.Float("arrest_debt_amount").GoType(decimal.Decimal{}).
			SchemaType(map[string]string{
				dialect.Postgres: "decimal(18, 2)",
			}).
			Annotations(annotation.CustomAnnotation{Description: "аресты в национальной валюте"}),
		field.String("has_arrest").Annotations(annotation.CustomAnnotation{Description: "аресты в валюте"}).Deprecated("использовать is_arrested"), // deprecated, использовать is_arrested
		field.String("origin").Annotations(annotation.CustomAnnotation{Description: "Источник счета"}),
		field.String("dea_reference_id").Annotations(annotation.CustomAnnotation{Description: "ИД договора (DEPID_ID)"}).Default(""),
		field.String("client_type").Annotations(annotation.CustomAnnotation{Description: "Тип клиента"}).Default(""),
		field.Bool("is_main").Annotations(annotation.CustomAnnotation{Description: "Основной счет"}).Default(false),
		field.Bool("accession_account").Annotations(annotation.CustomAnnotation{Description: "присоединенный счет"}).Default(false),
		field.Bool("is_arrested").Annotations(annotation.CustomAnnotation{Description: "Счет арестован"}).Default(false),
	}
}

func (Accounts) Annotations() []schema.Annotation {
	return []schema.Annotation{}
}

func (Accounts) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
