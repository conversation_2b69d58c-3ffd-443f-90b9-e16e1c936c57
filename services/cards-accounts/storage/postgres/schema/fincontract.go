package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"entgo.io/ent/schema/mixin"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"github.com/google/uuid"

	commonConsts "git.redmadrobot.com/zaman/backend/zaman/pkg/common"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
)

type FinContract struct {
	ent.Schema
}

const (
	FinContractTable = "fin_contract"

	FinContractFieldID               = "id"
	FinContractFieldContractType     = "contract_type"
	FinContractFieldContractCurrency = "contract_currency"
	FinContractFieldIBAN             = "iban"
	FinContractFieldStatus           = "status"
	FinContractFieldCreationDate     = "creation_date"
	FinContractCardID                = "card_id"
	FinContractCode                  = "contract_code"
	FinContractContractDataOpen      = "contract_data_open"
	FinContractContractDataClose     = "contract_data_close"
	FinContractUserID                = "user_id"
)

// Схема таблицы public.fin_contracts в БД Postgres сервиса cards-accounts
// NB! все поля с датами выставляются в TZ=KZ (UTC+6)
func (FinContract) Fields() []ent.Field {
	return []ent.Field{
		field.UUID(FinContractFieldID, uuid.UUID{}).
			Default(uuid.New).
			Unique().
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор фин. контракта"}).
			Comment("идентификатор фин. контракта"),

		field.Enum(FinContractFieldContractType).
			Values(
				consts.ContractKZT.Str(),
				consts.ContractUSD.Str(),
				consts.ContractEUR.Str(),
			).
			Default(consts.ContractKZT.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Тип контракта"}).
			Comment("тип контракта"),

		field.Enum(FinContractFieldContractCurrency).
			Values(
				commonConsts.CurrencyKZT.Str(),
				commonConsts.CurrencyUSD.Str(),
				commonConsts.CurrencyEUR.Str(),
				commonConsts.CurrencyRUB.Str(),
				commonConsts.CurrencyCNY.Str(),
			).
			Default(commonConsts.CurrencyKZT.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Валюта"}).
			Comment("валюта фин. контракта (совпадает с валютой счета)"),

		field.String(FinContractFieldIBAN).
			Annotations(annotation.CustomAnnotation{Description: "IBAN"}).
			Comment("IBAN фин. контракта (совпадает с IBAN счета)"),

		field.Enum(FinContractFieldStatus).
			Values(
				consts.ContractStatusInOpening.Str(),
				consts.ContractStatusError.Str(),
				consts.ContractStatusActive.Str(),
				consts.ContractStatusBlocked.Str(),
			).
			Default(consts.ContractStatusInOpening.Str()).
			Annotations(annotation.CustomAnnotation{Description: "Статус"}).
			Comment("статус фин. контракта"),

		field.Time(FinContractFieldCreationDate).
			Default(time.Now).
			Immutable().
			Annotations(annotation.CustomAnnotation{Description: "Дата создания"}).
			Comment("дата и время создания фин. контракта"),

		field.UUID(FinContractCardID, uuid.UUID{}).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор карты"}),

		field.String(FinContractCode).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Код контракта"}).
			Comment("код контракта, например, номер договора с банком"),

		field.Time(FinContractContractDataOpen).
			Annotations(annotation.CustomAnnotation{Description: "Дата открытия контракта"}).
			Comment("дата открытия контракта, если контракт открыт"),

		field.Time(FinContractContractDataClose).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Дата закрытия контракта"}).
			Comment("дата закрытия контракта, если контракт закрыт"),

		field.String(FinContractUserID).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор пользователя"}).
			Comment("идентификатор пользователя, которому принадлежит фин. контракт"),
	}
}

func (FinContract) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("cards", Cards.Type).Unique(),
	}
}

func (FinContract) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields(FinContractFieldIBAN).Unique(),
		index.Fields(FinContractFieldStatus),
	}
}

func (FinContract) Annotations() []schema.Annotation {
	return []schema.Annotation{}
}

func (FinContract) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
