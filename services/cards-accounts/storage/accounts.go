package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/transaction"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	pg "git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/predicate"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type AccountsData interface {
	SaveAccount(ctx context.Context, account *entity.SaveAccountReq) error
	SaveOrUpdateAccountsDataSME(ctx context.Context, accounts []*colvirBridge.Account, clientIIN string) error
	// UpdateAccountData обновляет данные по счетам клиента в БД Cards-Accounts
	UpdateAccountData(ctx context.Context, accounts []*colvirBridge.Account, clientIIN string) error
	// UpdateAccount обновляет существующий счет в БД Cards-Accounts
	UpdateAccountByID(ctx context.Context, accountID string, account *entity.AccountSaveToDB) error
	GetAccountsByClientCode(ctx context.Context, clientCodes []string) ([]entity.Account, error)
	GetAccountByClientIIN(ctx context.Context, clientIIN string) ([]*ent.Accounts, bool)
	GetAccountByIban(ctx context.Context, iban string) (*entity.Account, error)
	GetAccountByID(ctx context.Context, accountID string) (*entity.Account, error)
}

// SaveAccount сохраняет(создает или обновляет) счёт в БД Cards-Accounts
// TODO: Отрефакторить вместе с методом SaveOrUpdateAccountsData
// - должен остаться только один метод по-хорошему, как единая точка входа для сохранения/обновления счетов в БД Cards-Accounts
func (s *storageImpl) SaveAccount(ctx context.Context, account *entity.SaveAccountReq) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: SaveAccount")

	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get origin")

		return err
	}

	// Ищем счет в БД CardsAccounts
	accToSave := account.Account
	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()
	recordExist, err := tx.Client.Accounts.Query().
		Where(
			buildAccountPredicates(ctx, *accToSave)..., // Используем функцию для построения предикатов
		).
		Exist(ctx)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to check if account exists. iban: %s, origin: %s", accToSave.Iban, origin)

		return fmt.Errorf("failed to check if account exists: %w", err)
	}

	if recordExist {
		err = s.updateAccount(ctx, tx, accToSave)
		if err != nil {
			return fmt.Errorf("failed to update account: %w", err)
		}
	} else {
		err = s.createAccount(ctx, tx, accToSave)
		if err != nil {
			return fmt.Errorf("failed to create account: %w", err)
		}
	}
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: SaveAccount")
	return nil
}

// updateAccount обновляет(создает или обновляет) счёт в БД Cards-Accounts
func (s *storageImpl) updateAccount(ctx context.Context, tx *transaction.Tx[ent.Client], account *entity.AccountSaveToDB) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: updateAccount")

	balance := decimal.NewFromFloat(account.Balance.Main).RoundBank(2)
	balanceNatival := decimal.NewFromFloat(account.Balance.Natival).RoundBank(2)
	blockedBalance := decimal.NewFromFloat(account.Balance.Blocked).RoundBank(2)
	availableBalance := decimal.NewFromFloat(account.Balance.Available).RoundBank(2)
	blockedBalanceNatival := decimal.NewFromFloat(account.Balance.Blocked).RoundBank(2)

	// Получаем origin из контекста для фильтрации и обновления счета соответствующей части банка
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get origin")

		return err
	}

	// TODO: Поменять схему в БД для этого поля
	hasArrest := "0"
	if account.Arrest.Blocking {
		hasArrest = "1"
	}

	parseID, err := uuid.Parse(account.ID)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to parse account ID %s", account.ID)
		return fmt.Errorf("failed to parse account ID %s: %w", account.ID, err)
	}

	err = tx.Client.Accounts.Update().
		Where(
			accounts.ID(parseID),
			accounts.IbanEQ(account.Iban),
			accounts.OriginEQ(origin),
		).
		SetClientName(account.Client.Name).
		SetTitle(account.Title).
		SetClientCode(account.Client.Code).
		SetDateOpened(account.Date.Opened).
		SetDateClosed(account.Date.Closed).
		SetIban(account.Iban).
		SetType(account.Type).
		SetBalance(balance).
		SetCurrency(account.Currency).
		SetArrestBlocking(hasArrest).
		SetStatus(account.Status).
		SetAvailableBalance(availableBalance).
		SetBalanceNatival(balanceNatival).
		SetBlockedBalance(blockedBalance).
		SetHasArrest(hasArrest).
		SetArrestDebtAmount(blockedBalanceNatival).
		SetDeaReferenceID(account.DeaReferenceID).
		SetClientType(account.Client.Type).
		SetIsMain(account.IsMain).
		SetAccessionAccount(account.AccessionAccount).
		SetIsArrested(account.Arrest.Blocking).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update account data: %w", err)
	}

	logger.Info().Msgf("end: updateAccount")

	return nil
}

// createAccount сохраняет(создает) счёт в БД Cards-Accounts
func (s *storageImpl) createAccount(ctx context.Context, tx *transaction.Tx[ent.Client], account *entity.AccountSaveToDB) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: createAccount")

	parsedID, err := uuid.Parse(account.ID)
	if err != nil {
		return fmt.Errorf("failed to parse account ID %s: %w", account.ID, err)
	}

	// Получаем origin из контекста для установки принадлежности счета к части банка
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		return err
	}

	// TODO: Поменять схему в БД для этого поля
	hasArrest := "0"
	if account.Arrest.Blocking {
		hasArrest = "1"
	}

	balance := decimal.NewFromFloat(account.Balance.Main).RoundBank(2)
	balanceNatival := decimal.NewFromFloat(account.Balance.Natival).RoundBank(2)
	blockedBalance := decimal.NewFromFloat(account.Balance.Blocked).RoundBank(2)
	availableBalance := decimal.NewFromFloat(account.Balance.Available).RoundBank(2)
	blockedBalanceNatival := decimal.NewFromFloat(account.Balance.Blocked).RoundBank(2)

	err = tx.Client.Accounts.Create().
		SetID(parsedID).
		SetClientIin(account.Client.Iin).
		SetClientName(account.Client.Name).
		SetTitle(account.Title).
		SetClientCode(account.Client.Code).
		SetDateOpened(account.Date.Opened).
		SetDateClosed(account.Date.Closed).
		SetIban(account.Iban).
		SetType(account.Type).
		SetBalance(balance).
		SetCurrency(account.Currency).
		SetArrestBlocking(hasArrest).
		SetStatus(account.Status).
		SetAvailableBalance(availableBalance).
		SetBalanceNatival(balanceNatival).
		SetBlockedBalance(blockedBalance).
		SetHasArrest(hasArrest).
		SetArrestDebtAmount(blockedBalanceNatival).
		SetOrigin(origin).
		SetDeaReferenceID(account.DeaReferenceID).
		SetClientType(account.Client.Type).
		SetIsMain(account.IsMain).
		SetAccessionAccount(account.AccessionAccount).
		SetIsArrested(account.Arrest.Blocking).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to save account data: %w", err)
	}

	logger.Info().Msgf("end: createAccount")

	return nil
}

// SaveOrUpdateAccountsData сохраняет или обновляет данные по счетам клиента в БД Cards-Accounts
//
// Входные параметры:
// colvirAccounts - список счетов клиента
// clientIIN - ИИН клиента
//
// Не рефакторить без согласования с SME cardsAccounts
// NB! Только для 'https://sme-dev.zaman.redmadrobot.com/api/v1/user/accounts', origin='sme'
func (s *storageImpl) SaveOrUpdateAccountsDataSME(ctx context.Context, colvirAccounts []*colvirBridge.Account, clientIIN string) error {
	if len(colvirAccounts) == 0 {
		return nil
	}

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	for _, acc := range colvirAccounts {
		if err := s.saveOrUpdateSingleAccount(ctx, tx, acc, clientIIN); err != nil {
			return fmt.Errorf("failed to process account %s: %w", acc.Iban, err)
		}
	}
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func (s *storageImpl) saveOrUpdateSingleAccount(ctx context.Context, tx *transaction.Tx[ent.Client], account *colvirBridge.Account, clientIIN string) error {
	// Получаем origin из контекста для установки принадлежности счета к части банка
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		return err
	}

	recordExist, err := tx.Client.Accounts.Query().
		Where(
			accounts.IbanEQ(account.Iban),
			accounts.OriginEQ(origin),
		).
		Exist(ctx)
	if err != nil {
		return fmt.Errorf("failed to check if account exists: %w", err)
	}

	if recordExist {
		return s.updateClientAccount(ctx, tx, account)
	}

	return s.createClientAccount(ctx, tx, account, clientIIN)
}

// createClientAccount создает новый счет клиента с учетом origin (источника).
// Origin определяет принадлежность создаваемого счета к розничной (mobile) или корпоративной (sme) части банка.
// For example: mobile - retail banking (физ. лица), sme - corporate banking (юр. лица)
func (s *storageImpl) createClientAccount(
	ctx context.Context, tx *transaction.Tx[ent.Client], account *colvirBridge.Account, clientIIN string,
) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: createClientAccount")

	parsedID, err := uuid.Parse(account.ID)
	if err != nil {
		return fmt.Errorf("failed to parse account ID %s: %w", account.ID, err)
	}

	// Получаем origin из контекста для установки принадлежности счета к части банка
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		return err
	}

	balance := decimal.NewFromFloat(account.Balance).RoundBank(2)
	balanceNatival := decimal.NewFromFloat(account.BalanceNatival).RoundBank(2)
	blockedBalance := decimal.NewFromFloat(account.BlockedBalance).RoundBank(2)
	availableBalance := decimal.NewFromFloat(math.Abs(account.Balance) - math.Abs(account.BlockedBalance)).RoundBank(2)
	blockedBalanceNatival := decimal.NewFromFloat(account.BlockedBalanceNatival).RoundBank(2)

	var isArrested bool
	if account.HasArrest == "1" {
		isArrested = true
	}

	err = tx.Client.Accounts.Create().
		SetID(parsedID).
		SetClientIin(clientIIN).
		SetClientName(account.OwnerName).
		SetShortName(account.ShortName).
		SetTitle(account.Title).
		SetClientCode(account.ClientCode).
		SetDateOpened(account.DateOpened).
		SetDateClosed(account.DateClosed).
		SetIban(account.Iban).
		SetType(account.Type).
		SetBalance(balance).
		SetCurrency(account.Currency).
		SetArrestBlocking(account.HasArrest).
		SetStatus(account.Status).
		SetAvailableBalance(availableBalance).
		SetBalanceNatival(balanceNatival).
		SetBlockedBalance(blockedBalance).
		SetHasArrest(account.HasArrest).
		SetArrestDebtAmount(blockedBalanceNatival).
		SetOrigin(origin).
		SetIsArrested(isArrested).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to save account data: %w", err)
	}

	logger.Info().Msgf("end: createClientAccount")

	return nil
}

func (s *storageImpl) UpdateAccountData(ctx context.Context, accounts []*colvirBridge.Account, clientIIN string) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: UpdateAccountData")

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	for _, acc := range accounts {
		if err := s.updateClientAccount(ctx, tx, acc); err != nil {
			return fmt.Errorf("failed to update account %s: %w", acc.Iban, err)
		}
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: UpdateAccountData")
	return nil
}

// UpdateAccount обновляет существующий счет в БД Cards-Accounts.
func (s *storageImpl) UpdateAccount(ctx context.Context, account *entity.AccountSaveToDB) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: UpdateAccount")

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	err = s.updateAccount(ctx, tx, account)
	if err != nil {
		return fmt.Errorf("failed to update account: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: UpdateAccount")
	return nil
}

func (s *storageImpl) UpdateAccountByID(ctx context.Context, accountID string, account *entity.AccountSaveToDB) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: UpdateAccountByID")

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	if err := tx.Client.Accounts.UpdateOneID(uuid.MustParse(accountID)).
		SetAccessionAccount(account.AccessionAccount).
		SetClientIin(account.Client.Iin).
		SetClientName(account.Client.Name).
		SetShortName(account.ShortName).
		SetTitle(account.Title).
		SetClientCode(account.Client.Code).
		SetDateOpened(account.Date.Opened).
		SetDateClosed(account.Date.Closed).
		SetIban(account.Iban).
		SetType(account.Type).
		SetBalance(decimal.NewFromFloat(account.Balance.Main).RoundBank(2)).
		SetCurrency(account.Currency).
		SetAvailableBalance(decimal.NewFromFloat(account.Balance.Available).RoundBank(2)).
		SetBalanceNatival(decimal.NewFromFloat(account.Balance.Natival).RoundBank(2)).
		SetBlockedBalance(decimal.NewFromFloat(account.Balance.Blocked).RoundBank(2)).
		SetDeaReferenceID(account.DeaReferenceID).
		SetIsMain(account.IsMain).
		SetAccessionAccount(account.AccessionAccount).
		SetClientType(account.Client.Type).
		Exec(ctx); err != nil {
		logger.Error().Err(err).Msgf("failed to update account by ID %s", accountID)
		return fmt.Errorf("failed to update account by ID %s: %w", accountID, err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: UpdateAccountByID")
	return nil
}

// updateClientAccount обновляет существующий счет клиента с учетом origin (источника).
// Origin определяет принадлежность обновляемого счета к розничной (mobile) или корпоративной (sme) части банка.
// For example: mobile - retail banking (физ. лица), sme - corporate banking (юр. лица)
func (s *storageImpl) updateClientAccount(ctx context.Context, tx *transaction.Tx[ent.Client], account *colvirBridge.Account) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: updateClientAccount")

	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get origin")
		return fmt.Errorf("failed to get origin: %w", err)
	}

	logger.Info().Msgf("Updating account with ClientCode: %s, Iban: %s, Origin: %s", account.ClientCode, account.Iban, origin)

	// проверка на аресты по счету
	isArrested := account.HasArrest == consts.AccountBlocked.String() &&
		(account.RestrictionType == consts.AccountBlockTypeAll.String() || account.RestrictionType == consts.AccountBlockTypeLock.String())

	err = tx.Client.Accounts.Update().
		Where(
			accounts.ClientCodeEQ(account.ClientCode),
			accounts.IbanEQ(account.Iban),
			accounts.OriginEQ(origin),
		).
		SetClientName(account.OwnerName).
		SetShortName(account.ShortName).
		SetTitle(account.Title).
		SetClientCode(account.ClientCode).
		SetDateOpened(account.DateOpened).
		SetDateClosed(account.DateClosed).
		SetIban(account.Iban).
		SetType(account.Type).
		SetBalance(decimal.NewFromFloat(account.Balance).RoundBank(2)).
		SetCurrency(account.Currency).
		SetArrestBlocking(account.HasArrest).
		SetStatus(account.Status).
		SetAvailableBalance(decimal.NewFromFloat(math.Abs(account.Balance) - math.Abs(account.BlockedBalance)).RoundBank(2)).
		SetBalanceNatival(decimal.NewFromFloat(account.BalanceNatival).RoundBank(2)).
		SetBlockedBalance(decimal.NewFromFloat(account.BlockedBalance).RoundBank(2)).
		SetHasArrest(account.HasArrest).
		SetArrestDebtAmount(decimal.NewFromFloat(account.BlockedBalanceNatival).RoundBank(2)).
		SetIsArrested(isArrested).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update account: %w", err)
	}

	logger.Info().Msgf("end: updateClientAccount")
	return nil
}

func (s *storageImpl) GetAccountsByClientCode(ctx context.Context, clientCodes []string) ([]entity.Account, error) {
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		return nil, err
	}
	accounts, err := s.PostgresClient.Accounts.Query().
		Where(accounts.ClientCodeIn(clientCodes...), accounts.OriginEQ(origin)).
		All(ctx)
	if err != nil {
		return nil, err
	}

	return entity.MakeAccountsEntToEntity(accounts), nil
}

func (s *storageImpl) GetAccountByClientIIN(ctx context.Context, clientIIN string) ([]*ent.Accounts, bool) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: GetAccountByClientIIN")

	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logger.Error().Err(err).Msgf("GetAccountByClientIIN: failed to get origin from context")
		return nil, false
	}
	accounts, err := s.PostgresClient.Accounts.Query().
		Where(accounts.ClientIinEQ(clientIIN), accounts.OriginEQ(origin)).
		All(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, false
		}

		return nil, false
	}

	if len(accounts) == 0 {
		return nil, false
	}

	logger.Info().Msgf("end: GetAccountByClientIIN: %+v", accounts)

	return accounts, true
}

func (s *storageImpl) GetAccountByIban(ctx context.Context, iban string) (*entity.Account, error) {
	account, err := s.PostgresClient.Accounts.Query().
		Where(accounts.IbanEQ(iban)).
		Only(ctx)
	if err != nil {
		return nil, err
	}

	if account == nil {
		return &entity.Account{}, nil
	}

	return entity.MakeAccountEntToEntity(account), nil
}

func (s *storageImpl) GetAccountByID(ctx context.Context, accountID string) (*entity.Account, error) {
	parsedID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("failed to parse account ID %s: %w", accountID, err)
	}

	account, err := s.PostgresClient.Accounts.Query().
		Where(accounts.IDEQ(parsedID)).
		Only(ctx)
	if err != nil {
		return nil, err
	}

	if account == nil {
		return &entity.Account{}, nil
	}

	return entity.MakeAccountEntToEntity(account), nil
}

func buildAccountPredicates(ctx context.Context, account entity.AccountSaveToDB) []predicate.Accounts {
	var predicates []predicate.Accounts

	logs.FromContext(ctx).Info().Msgf("building predicates for account: %+v", account)

	// проверка на случай, если ID задан, а Iban нет
	if account.ID != "" && account.Iban == "" {
		parsedID, err := uuid.Parse(account.ID)
		if err != nil {
			logs.FromContext(ctx).Error().Err(err).Msgf("failed to parse account ID %s", account.ID)
		} else {
			predicates = append(predicates, accounts.IDEQ(parsedID))
		}
	}

	if account.Iban != "" {
		predicates = append(predicates, accounts.IbanEQ(account.Iban))
	}

	if account.Origin != "" {
		predicates = append(predicates, accounts.OriginEQ(account.Origin))
	}

	return predicates
}
