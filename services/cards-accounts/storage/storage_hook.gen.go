// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// CreateClientVerificationResult implements Storage
func (_w *StorageHook) CreateClientVerificationResult(ctx context.Context, req *entity.SaveClientVerificationReq) (err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "CreateClientVerificationResult", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateClientVerificationResult", _params)

	err = _w.Storage.CreateClientVerificationResult(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateClientVerificationResult", []any{err})
	return err
}

// CreateFinContract implements Storage
func (_w *StorageHook) CreateFinContract(ctx context.Context, finContract *entity.FinContract) (err error) {
	_params := []any{ctx, finContract}
	defer _w._onPanic.Hook(_w.Storage, "CreateFinContract", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateFinContract", _params)

	err = _w.Storage.CreateFinContract(_ctx, finContract)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateFinContract", []any{err})
	return err
}

// DeleteCard implements Storage
func (_w *StorageHook) DeleteCard(ctx context.Context, cardID string) (err error) {
	_params := []any{ctx, cardID}
	defer _w._onPanic.Hook(_w.Storage, "DeleteCard", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "DeleteCard", _params)

	err = _w.Storage.DeleteCard(_ctx, cardID)
	_w._postCall.Hook(_ctx, _w.Storage, "DeleteCard", []any{err})
	return err
}

// GetAccountByClientIIN implements Storage
func (_w *StorageHook) GetAccountByClientIIN(ctx context.Context, clientIIN string) (apa1 []*ent.Accounts, b1 bool) {
	_params := []any{ctx, clientIIN}
	defer _w._onPanic.Hook(_w.Storage, "GetAccountByClientIIN", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetAccountByClientIIN", _params)

	apa1, b1 = _w.Storage.GetAccountByClientIIN(_ctx, clientIIN)
	_w._postCall.Hook(_ctx, _w.Storage, "GetAccountByClientIIN", []any{apa1, b1})
	return apa1, b1
}

// GetAccountByID implements Storage
func (_w *StorageHook) GetAccountByID(ctx context.Context, accountID string) (ap1 *entity.Account, err error) {
	_params := []any{ctx, accountID}
	defer _w._onPanic.Hook(_w.Storage, "GetAccountByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetAccountByID", _params)

	ap1, err = _w.Storage.GetAccountByID(_ctx, accountID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetAccountByID", []any{ap1, err})
	return ap1, err
}

// GetAccountByIban implements Storage
func (_w *StorageHook) GetAccountByIban(ctx context.Context, iban string) (ap1 *entity.Account, err error) {
	_params := []any{ctx, iban}
	defer _w._onPanic.Hook(_w.Storage, "GetAccountByIban", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetAccountByIban", _params)

	ap1, err = _w.Storage.GetAccountByIban(_ctx, iban)
	_w._postCall.Hook(_ctx, _w.Storage, "GetAccountByIban", []any{ap1, err})
	return ap1, err
}

// GetAccountsByClientCode implements Storage
func (_w *StorageHook) GetAccountsByClientCode(ctx context.Context, clientCodes []string) (aa1 []entity.Account, err error) {
	_params := []any{ctx, clientCodes}
	defer _w._onPanic.Hook(_w.Storage, "GetAccountsByClientCode", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetAccountsByClientCode", _params)

	aa1, err = _w.Storage.GetAccountsByClientCode(_ctx, clientCodes)
	_w._postCall.Hook(_ctx, _w.Storage, "GetAccountsByClientCode", []any{aa1, err})
	return aa1, err
}

// GetCardByID implements Storage
func (_w *StorageHook) GetCardByID(ctx context.Context, cardID string) (cp1 *entity.Card, err error) {
	_params := []any{ctx, cardID}
	defer _w._onPanic.Hook(_w.Storage, "GetCardByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetCardByID", _params)

	cp1, err = _w.Storage.GetCardByID(_ctx, cardID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetCardByID", []any{cp1, err})
	return cp1, err
}

// GetCardsByAccountID implements Storage
func (_w *StorageHook) GetCardsByAccountID(ctx context.Context, accountID string) (cpa1 []*entity.Card, err error) {
	_params := []any{ctx, accountID}
	defer _w._onPanic.Hook(_w.Storage, "GetCardsByAccountID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetCardsByAccountID", _params)

	cpa1, err = _w.Storage.GetCardsByAccountID(_ctx, accountID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetCardsByAccountID", []any{cpa1, err})
	return cpa1, err
}

// GetCardsByClientIIN implements Storage
func (_w *StorageHook) GetCardsByClientIIN(ctx context.Context, clientIIN string) (cpa1 []*entity.Card, err error) {
	_params := []any{ctx, clientIIN}
	defer _w._onPanic.Hook(_w.Storage, "GetCardsByClientIIN", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetCardsByClientIIN", _params)

	cpa1, err = _w.Storage.GetCardsByClientIIN(_ctx, clientIIN)
	_w._postCall.Hook(_ctx, _w.Storage, "GetCardsByClientIIN", []any{cpa1, err})
	return cpa1, err
}

// GetClientVerificationResults implements Storage
func (_w *StorageHook) GetClientVerificationResults(ctx context.Context, userID uuid.UUID) (cpa1 []*ent.ClientVerificationResults, b1 bool) {
	_params := []any{ctx, userID}
	defer _w._onPanic.Hook(_w.Storage, "GetClientVerificationResults", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetClientVerificationResults", _params)

	cpa1, b1 = _w.Storage.GetClientVerificationResults(_ctx, userID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetClientVerificationResults", []any{cpa1, b1})
	return cpa1, b1
}

// GetFinContractByCardID implements Storage
func (_w *StorageHook) GetFinContractByCardID(ctx context.Context, cardID string) (fp1 *entity.FinContract, err error) {
	_params := []any{ctx, cardID}
	defer _w._onPanic.Hook(_w.Storage, "GetFinContractByCardID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetFinContractByCardID", _params)

	fp1, err = _w.Storage.GetFinContractByCardID(_ctx, cardID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetFinContractByCardID", []any{fp1, err})
	return fp1, err
}

// GetFinContractListByAccountIbans implements Storage
func (_w *StorageHook) GetFinContractListByAccountIbans(ctx context.Context, ibans []string) (fpa1 []*entity.FinContract, err error) {
	_params := []any{ctx, ibans}
	defer _w._onPanic.Hook(_w.Storage, "GetFinContractListByAccountIbans", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetFinContractListByAccountIbans", _params)

	fpa1, err = _w.Storage.GetFinContractListByAccountIbans(_ctx, ibans)
	_w._postCall.Hook(_ctx, _w.Storage, "GetFinContractListByAccountIbans", []any{fpa1, err})
	return fpa1, err
}

// SaveAccount implements Storage
func (_w *StorageHook) SaveAccount(ctx context.Context, account *entity.SaveAccountReq) (err error) {
	_params := []any{ctx, account}
	defer _w._onPanic.Hook(_w.Storage, "SaveAccount", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveAccount", _params)

	err = _w.Storage.SaveAccount(_ctx, account)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveAccount", []any{err})
	return err
}

// SaveCard implements Storage
func (_w *StorageHook) SaveCard(ctx context.Context, card *entity.SaveCardReq) (err error) {
	_params := []any{ctx, card}
	defer _w._onPanic.Hook(_w.Storage, "SaveCard", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveCard", _params)

	err = _w.Storage.SaveCard(_ctx, card)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveCard", []any{err})
	return err
}

// SaveOrUpdateAccountsDataSME implements Storage
func (_w *StorageHook) SaveOrUpdateAccountsDataSME(ctx context.Context, accounts []*colvirBridge.Account, clientIIN string) (err error) {
	_params := []any{ctx, accounts, clientIIN}
	defer _w._onPanic.Hook(_w.Storage, "SaveOrUpdateAccountsDataSME", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveOrUpdateAccountsDataSME", _params)

	err = _w.Storage.SaveOrUpdateAccountsDataSME(_ctx, accounts, clientIIN)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveOrUpdateAccountsDataSME", []any{err})
	return err
}

// UpdateAccountByID implements Storage
func (_w *StorageHook) UpdateAccountByID(ctx context.Context, accountID string, account *entity.AccountSaveToDB) (err error) {
	_params := []any{ctx, accountID, account}
	defer _w._onPanic.Hook(_w.Storage, "UpdateAccountByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateAccountByID", _params)

	err = _w.Storage.UpdateAccountByID(_ctx, accountID, account)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateAccountByID", []any{err})
	return err
}

// UpdateAccountData implements Storage
func (_w *StorageHook) UpdateAccountData(ctx context.Context, accounts []*colvirBridge.Account, clientIIN string) (err error) {
	_params := []any{ctx, accounts, clientIIN}
	defer _w._onPanic.Hook(_w.Storage, "UpdateAccountData", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateAccountData", _params)

	err = _w.Storage.UpdateAccountData(_ctx, accounts, clientIIN)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateAccountData", []any{err})
	return err
}

// UpdateCard implements Storage
func (_w *StorageHook) UpdateCard(ctx context.Context, cardID string, cardData *entity.UpdateCardReq) (err error) {
	_params := []any{ctx, cardID, cardData}
	defer _w._onPanic.Hook(_w.Storage, "UpdateCard", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateCard", _params)

	err = _w.Storage.UpdateCard(_ctx, cardID, cardData)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateCard", []any{err})
	return err
}

// UpdateClientVerificationResult implements Storage
func (_w *StorageHook) UpdateClientVerificationResult(ctx context.Context, req *entity.ClientVerificationDTO) (err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "UpdateClientVerificationResult", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateClientVerificationResult", _params)

	err = _w.Storage.UpdateClientVerificationResult(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateClientVerificationResult", []any{err})
	return err
}

// UpdateFinContractByAttachedAccount implements Storage
func (_w *StorageHook) UpdateFinContractByAttachedAccount(ctx context.Context, accountID string, finContract *entity.FinContract) (err error) {
	_params := []any{ctx, accountID, finContract}
	defer _w._onPanic.Hook(_w.Storage, "UpdateFinContractByAttachedAccount", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateFinContractByAttachedAccount", _params)

	err = _w.Storage.UpdateFinContractByAttachedAccount(_ctx, accountID, finContract)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateFinContractByAttachedAccount", []any{err})
	return err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
