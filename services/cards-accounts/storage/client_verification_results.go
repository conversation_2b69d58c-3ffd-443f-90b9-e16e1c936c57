package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	pg "git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/clientverificationresults"
)

type ClientVerificationResultsData interface {
	GetClientVerificationResults(ctx context.Context, userID uuid.UUID) ([]*ent.ClientVerificationResults, bool)
	CreateClientVerificationResult(ctx context.Context, req *entity.SaveClientVerificationReq) error
	UpdateClientVerificationResult(ctx context.Context, req *entity.ClientVerificationDTO) error
}

func (s *storageImpl) GetClientVerificationResults(ctx context.Context, userID uuid.UUID) ([]*ent.ClientVerificationResults, bool) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: GetClientVerificationResults")

	results, err := s.PostgresClient.ClientVerificationResults.Query().
		Where(clientverificationresults.UserIDEQ(userID)).
		All(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, false
		}

		return nil, false
	}

	if len(results) == 0 {
		return nil, false
	}

	logger.Info().Msgf("end: GetClientVerificationResults: %+v", results)

	return results, true
}

func (s *storageImpl) CreateClientVerificationResult(ctx context.Context, req *entity.SaveClientVerificationReq) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: CreateClientVerificationResult")

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return fmt.Errorf("failed to begin transaction CreateClientVerificationResult: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	err = tx.Client.ClientVerificationResults.Create().
		SetID(req.ID).
		SetUserID(req.UserID).
		SetStatus(req.Status).
		SetDate(req.Date).
		SetVerificationResult(req.VerificationResult).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to save client verification result: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: CreateClientVerificationResult")

	return nil
}

func (s *storageImpl) UpdateClientVerificationResult(ctx context.Context, dto *entity.ClientVerificationDTO) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: UpdateClientVerificationResult")

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return fmt.Errorf("failed to begin transaction UpdateClientVerificationResult: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	err = tx.Client.ClientVerificationResults.UpdateOneID(dto.ID).
		SetVerificationResult(dto.VerificationResult).
		SetStatus(dto.Status).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to save client verification result: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: UpdateClientVerificationResult")

	return nil
}
