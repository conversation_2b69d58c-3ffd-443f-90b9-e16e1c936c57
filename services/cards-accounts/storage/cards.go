package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	pg "git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/accounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
)

type CardsData interface {
	SaveCard(ctx context.Context, card *entity.SaveCardReq) error
	UpdateCard(ctx context.Context, cardID string, cardData *entity.UpdateCardReq) error
	DeleteCard(ctx context.Context, cardID string) error
	GetCardByID(ctx context.Context, cardID string) (*entity.Card, error)
	GetCardsByClientIIN(ctx context.Context, clientIIN string) ([]*entity.Card, error)
	GetCardsByAccountID(ctx context.Context, accountID string) ([]*entity.Card, error)
}

// SaveCard создает карту в БД Cards-Accounts со всеми обязательными полями
func (s *storageImpl) SaveCard(ctx context.Context, card *entity.SaveCardReq) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: SaveCard")

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	if card.ClientID == nil {
		return fmt.Errorf("client_id is required")
	}
	if card.AttachedAccountID == nil {
		return fmt.Errorf("attached_account_id is required")
	}
	if card.EmbossingName == nil {
		return fmt.Errorf("embossing_name is required")
	}
	if card.MaskedPAN == nil {
		return fmt.Errorf("masked_pan is required")
	}

	parsedID := card.ID
	parsedClientID, err := uuid.Parse(*card.ClientID)
	if err != nil {
		return fmt.Errorf("failed to parse client_id %s: %w", *card.ClientID, err)
	}
	parsedAccountID, err := uuid.Parse(*card.AttachedAccountID)
	if err != nil {
		return fmt.Errorf("failed to parse attached_account_id %s: %w", *card.AttachedAccountID, err)
	}

	createQuery := tx.Client.Cards.Create().
		SetID(parsedID).
		SetClientID(parsedClientID).
		SetAttachedAccountID(parsedAccountID).
		SetEmbossingName(*card.EmbossingName).
		SetMaskedPan(*card.MaskedPAN)

	if card.CardType != "" {
		createQuery = createQuery.SetCardType(cards.CardType(card.CardType))
	} else {
		createQuery = createQuery.SetCardType(cards.CardTypePHYSICAL)
	}

	if card.ProductType != "" {
		createQuery = createQuery.SetProductType(cards.ProductType(card.ProductType))
	} else {
		createQuery = createQuery.SetProductType(cards.ProductTypeDEBIT_CARD)
	}

	if card.PaymentSystem != "" {
		createQuery = createQuery.SetPaymentSystem(cards.PaymentSystem(card.PaymentSystem))
	} else {
		createQuery = createQuery.SetPaymentSystem(cards.PaymentSystemMASTERCARD)
	}

	if card.CardClass != "" {
		createQuery = createQuery.SetCardClass(cards.CardClass(card.CardClass))
	}

	if card.Status != "" {
		createQuery = createQuery.SetStatus(cards.Status(card.Status))
	} else {
		createQuery = createQuery.SetStatus(cards.StatusIN_OPENING)
	}

	if card.TokenizationStatus != "" {
		createQuery = createQuery.SetTokenizationStatus(cards.TokenizationStatus(card.TokenizationStatus))
	}

	if card.Wallet != nil {
		createQuery = createQuery.SetWallet(*card.Wallet)
	}

	if card.ExpireDate != nil {
		createQuery = createQuery.SetExpireDate(*card.ExpireDate)
	}

	err = createQuery.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to create card: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: SaveCard, card ID: %s", parsedID)
	return nil
}

// UpdateCard обновляет карту в БД Cards-Accounts с любыми переданными полями
func (s *storageImpl) UpdateCard(ctx context.Context, cardID string, cardData *entity.UpdateCardReq) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: UpdateCard, cardID: %s", cardID)

	parsedID, err := uuid.Parse(cardID)
	if err != nil {
		return fmt.Errorf("failed to parse card ID %s: %w", cardID, err)
	}

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	// Проверяем существование карты
	exists, err := tx.Client.Cards.Query().
		Where(cards.IDEQ(parsedID)).
		Exist(ctx)
	if err != nil {
		return fmt.Errorf("failed to check if card exists: %w", err)
	}
	if !exists {
		return fmt.Errorf("card with ID %s not found", cardID)
	}

	updateQuery := tx.Client.Cards.Update().Where(cards.IDEQ(parsedID))

	if cardData.ClientID != nil {
		parsedClientID, err := uuid.Parse(*cardData.ClientID)
		if err != nil {
			return fmt.Errorf("failed to parse client_id %s: %w", *cardData.ClientID, err)
		}
		updateQuery = updateQuery.SetClientID(parsedClientID)
	}

	if cardData.AttachedAccountID != nil {
		parsedAccountID, err := uuid.Parse(*cardData.AttachedAccountID)
		if err != nil {
			return fmt.Errorf("failed to parse attached_account_id %s: %w", *cardData.AttachedAccountID, err)
		}
		updateQuery = updateQuery.SetAttachedAccountID(parsedAccountID)
	}

	if cardData.EmbossingName != nil {
		updateQuery = updateQuery.SetEmbossingName(*cardData.EmbossingName)
	}

	if cardData.MaskedPAN != nil {
		updateQuery = updateQuery.SetMaskedPan(*cardData.MaskedPAN)
	}

	if cardData.CardType != nil {
		updateQuery = updateQuery.SetCardType(cards.CardType(*cardData.CardType))
	}

	if cardData.ProductType != nil {
		updateQuery = updateQuery.SetProductType(cards.ProductType(*cardData.ProductType))
	}

	if cardData.PaymentSystem != nil {
		updateQuery = updateQuery.SetPaymentSystem(cards.PaymentSystem(*cardData.PaymentSystem))
	}

	if cardData.CardClass != nil {
		updateQuery = updateQuery.SetCardClass(cards.CardClass(*cardData.CardClass))
	}

	if cardData.Status != nil {
		updateQuery = updateQuery.SetStatus(cards.Status(strings.ToUpper(*cardData.Status)))
	}

	if cardData.TokenizationStatus != nil {
		updateQuery = updateQuery.SetTokenizationStatus(cards.TokenizationStatus(*cardData.TokenizationStatus))
	}

	if cardData.Wallet != nil {
		updateQuery = updateQuery.SetWallet(*cardData.Wallet)
	}

	if cardData.ExpireDate != nil {
		updateQuery = updateQuery.SetExpireDate(*cardData.ExpireDate)
	}

	err = updateQuery.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update card: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: UpdateCard, cardID: %s", cardID)
	return nil
}

// DeleteCard выполняет удаление карты из БД Cards-Accounts используя card.id (UUID)
func (s *storageImpl) DeleteCard(ctx context.Context, cardID string) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: DeleteCard, cardID: %s", cardID)

	parsedID, err := uuid.Parse(cardID)
	if err != nil {
		return fmt.Errorf("failed to parse card ID %s: %w", cardID, err)
	}

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	exists, err := tx.Client.Cards.Query().
		Where(cards.IDEQ(parsedID)).
		Exist(ctx)
	if err != nil {
		return fmt.Errorf("failed to check if card exists: %w", err)
	}
	if !exists {
		return fmt.Errorf("card with ID %s not found", cardID)
	}

	// Устанавливаем статус "BLOCKED"
	err = tx.Client.Cards.Update().
		Where(cards.IDEQ(parsedID)).
		SetStatus(cards.StatusBLOCKED).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to soft delete card: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Info().Msgf("end: DeleteCard, cardID: %s", cardID)
	return nil
}

// GetCardByID получает карту из БД Cards-Accounts по card.id (UUID)
func (s *storageImpl) GetCardByID(ctx context.Context, cardID string) (*entity.Card, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: GetCardByID, cardID: %s", cardID)

	parsedID, err := uuid.Parse(cardID)
	if err != nil {
		return nil, fmt.Errorf("failed to parse card ID %s: %w", cardID, err)
	}

	cardEnt, err := s.PostgresClient.Cards.Query().
		Where(cards.IDEQ(parsedID)).
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, fmt.Errorf("card with ID %s not found", cardID)
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	card := entity.MakeCardEntToEntity(cardEnt)

	logger.Info().Msgf("end: GetCardByID, cardID: %s", cardID)
	return card, nil
}

// GetCardsByClientIIN получает карты клиента по ИИН из БД Cards-Accounts
func (s *storageImpl) GetCardsByClientIIN(ctx context.Context, clientIIN string) ([]*entity.Card, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: GetCardsByClientIIN, clientIIN: %s", clientIIN)

	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get origin: %w", err)
	}

	cardsEnt, err := s.PostgresClient.Cards.Query().
		Where(
			cards.HasAccountWith(
				accounts.ClientIinEQ(clientIIN),
				accounts.OriginEQ(origin),
			),
		).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get cards by client IIN: %w", err)
	}

	if len(cardsEnt) == 0 {
		logger.Info().Msgf("no cards found for client IIN: %s", clientIIN)
		return []*entity.Card{}, nil
	}

	cards := entity.MakeCardsEntToEntity(cardsEnt)

	logger.Info().Msgf("end: GetCardsByClientIIN, found %d cards for clientIIN: %s", len(cards), clientIIN)
	return cards, nil
}

// GetCardsByAccountID получает карты привязанные к конкретному счету
func (s *storageImpl) GetCardsByAccountID(ctx context.Context, accountID string) ([]*entity.Card, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: GetCardsByAccountID, accountID: %s", accountID)

	parsedAccountID, err := uuid.Parse(accountID)
	if err != nil {
		return nil, fmt.Errorf("failed to parse account ID %s: %w", accountID, err)
	}

	cardsEnt, err := s.PostgresClient.Cards.Query().
		Where(cards.AttachedAccountIDEQ(parsedAccountID)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get cards by account ID: %w", err)
	}

	if len(cardsEnt) == 0 {
		logger.Info().Msgf("no cards found for account ID: %s", accountID)
		return []*entity.Card{}, nil
	}

	cards := entity.MakeCardsEntToEntity(cardsEnt)

	logger.Info().Msgf("end: GetCardsByAccountID, found %d cards for accountID: %s", len(cards), accountID)
	return cards, nil
}
