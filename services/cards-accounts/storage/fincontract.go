package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	pg "git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/cards"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent/fincontract"
)

type FinContractData interface {
	// CreateFinContract создает новый финансовый контракт в БД
	CreateFinContract(ctx context.Context, finContract *entity.FinContract) error
	// GetFinContractByCardID получает финансовый контракт по ID счета
	GetFinContractByCardID(ctx context.Context, cardID string) (*entity.FinContract, error)
	// GetFinContractListByAccountIbans получает финансовый контракт по списку iban
	GetFinContractListByAccountIbans(ctx context.Context, ibans []string) ([]*entity.FinContract, error)
	// UpdateFinContractByAttachedAccount обновляет финансовый контракт в БД
	UpdateFinContractByAttachedAccount(ctx context.Context, accountID string, finContract *entity.FinContract) error
}

// storageImpl реализует интерфейс FinContractData
func (s *storageImpl) GetFinContractByCardID(ctx context.Context, cardID string) (*entity.FinContract, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msg("start: FinContract.GetFinContractByCardID")

	parsedID, err := uuid.Parse(cardID)
	if err != nil {
		return nil, fmt.Errorf("failed to parse account ID %s: %w", cardID, err)
	}

	finContractEnt, err := s.PostgresClient.FinContract.
		Query().
		Where(fincontract.CardID(parsedID)).WithCards().
		Only(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			logger.Error().Err(err).Msgf("fin contract not found for card ID %s", cardID)
			return nil, fmt.Errorf("fin contract not found for card ID %s: %w", cardID, err)
		}
		logger.Error().Err(err).Msgf("failed to query fin contract for card ID %s", cardID)
		return nil, fmt.Errorf("failed to query fin contract for card ID %s: %w", cardID, err)
	}

	return entity.MakeFinContractEntToEntity(finContractEnt), nil
}

// GetFinContractListByAccountIbans получает список фин контрактов по IBAN
func (s *storageImpl) GetFinContractListByAccountIbans(
	ctx context.Context, ibans []string,
) ([]*entity.FinContract, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msg("start: FinContract.GetFinContractListByAccountIbans")

	finContractsEnt, err := s.PostgresClient.FinContract.
		Query().
		Where(fincontract.IbanIn(ibans...)).
		All(ctx)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to query fin contracts for ibans %v", ibans)
		return nil, fmt.Errorf("failed to query fin contract for ibans %s: %w", ibans, err)
	}

	return entity.MakeFinContractsEntToEntity(finContractsEnt), nil
}

// CreateFinContract создает новый фин контракт в БД
func (s *storageImpl) CreateFinContract(ctx context.Context, finContract *entity.FinContract) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msg("start: FinContract.CreateFinContract")

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return fmt.Errorf("failed to begin transaction CreateFinContract: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	currentTime, err := utils.GetCurrentKzTime()
	if err != nil {
		logger.Error().Err(err).Msg("failed to get current time")
		return err
	}

	err = tx.Client.FinContract.Create().
		SetID(finContract.ID).
		SetContractCurrency(fincontract.ContractCurrency(finContract.ContractCurrency)).
		SetContractCode(finContract.Code).
		SetCardID(finContract.CardID).
		SetContractDataOpen(currentTime).
		SetContractType(fincontract.ContractType(finContract.ContractType)).
		SetIban(finContract.IBAN).
		SetStatus(fincontract.Status(finContract.Status)).
		SetCreationDate(currentTime).
		SetUserID(finContract.UserID).
		Exec(ctx)
	if err != nil {
		logger.Error().Err(err).Msg("failed to create fin contract")
		return fmt.Errorf("failed to create fin contract: %w", err)
	}
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// UpdateFinContractByAttachedAccount обновляет фин контракт в БД
func (s *storageImpl) UpdateFinContractByAttachedAccount(ctx context.Context, accountID string, finContract *entity.FinContract) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msg("start: FinContract.UpdateFinContract")

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return fmt.Errorf("failed to begin transaction UpdateFinContract: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logger.Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	parsedID, err := uuid.Parse(accountID)
	if err != nil {
		return fmt.Errorf("failed to parse account ID %s: %w", accountID, err)
	}

	card, err := tx.Client.Cards.Query().Where(cards.AttachedAccountID(parsedID)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("card not found for fin contract ID %s: %w", parsedID, err)
		}
		return fmt.Errorf("failed to query card for fin contract ID %s: %w", parsedID, err)
	}

	// проверяем, что карта и фин контракт связаны
	updateQuery := tx.Client.FinContract.Update().Where(fincontract.CardID(card.ID))
	updateQuery = applyFinContractUpdates(updateQuery, finContract)

	if err := updateQuery.Exec(ctx); err != nil {
		return fmt.Errorf("failed to update fin contract: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// applyFinContractUpdates applies updates to the FinContract update query
func applyFinContractUpdates(updateQuery *ent.FinContractUpdate, finContract *entity.FinContract) *ent.FinContractUpdate {
	if finContract.ContractCurrency != "" {
		updateQuery = updateQuery.SetContractCurrency(fincontract.ContractCurrency(finContract.ContractCurrency))
	}
	if finContract.Code != "" {
		updateQuery = updateQuery.SetContractCode(finContract.Code)
	}
	if finContract.CardID != uuid.Nil {
		updateQuery = updateQuery.SetCardID(finContract.CardID)
	}
	if !finContract.ContractDataOpen.IsZero() {
		updateQuery = updateQuery.SetContractDataOpen(finContract.ContractDataOpen)
	}
	if finContract.ContractType != "" {
		updateQuery = updateQuery.SetContractType(fincontract.ContractType(finContract.ContractType))
	}
	if finContract.IBAN != "" {
		updateQuery = updateQuery.SetIban(finContract.IBAN)
	}
	if finContract.Status != "" {
		updateQuery = updateQuery.SetStatus(fincontract.Status(strings.ToUpper(finContract.Status)))
	}
	return updateQuery
}
