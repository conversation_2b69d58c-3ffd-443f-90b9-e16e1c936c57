package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

	cardsErrs "git.redmadrobot.com/zaman/backend/zaman/errs/cardsAccounts"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	processingBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

// GetCards получает список счетов клиента по его коду
func (u *useCasesImpl) GetCards(ctx context.Context, req *entity.GetCardsReq) (*entity.GetCardsResult, error) {
	logger := logs.FromContext(ctx)
	errTmpl := "GetCards Error:%s"

	// Extract user ID from context
	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// получаем пользователя из БД по userID
	user, err := u.Providers.Users.GetUserByID(ctx, &pb.GetUserByIDReq{ID: userID})
	if err != nil {
		return nil, errs.Wrapf(err, errTmpl, "getUserIDFromContext")
	}

	// получаем счета и карты из БД
	accountsFromDB, cacheExists := u.Providers.Storage.GetAccountByClientIIN(ctx, user.Iin)
	cardsDB, err := u.Providers.Storage.GetCardsByClientIIN(ctx, user.Iin)
	if err != nil {
		return nil, errs.Wrapf(err, errTmpl, "GetCardsByClientIIN")
	}

	// получаем клиента из Colvir по IIN
	client, err := u.Providers.Colvirbridge.FindClient(ctx, &colvirBridge.FindClientReq{Iin: user.Iin})
	if err != nil {
		return handleError(ctx, accountsFromDB, cacheExists, fmt.Errorf("could not find client: %w", err))
	}

	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get origin from context")
	}
	clientCodes := filterClientCodesByOrigin(client.Clients, origin)

	// получаем список счетов клиента из Colvir по его коду слиента
	accountFromColvir, err := u.Providers.Colvirbridge.FindClientAccountList(ctx, &colvirBridge.FindClientAccountListReq{ClientCodes: clientCodes})
	if err != nil {
		return handleError(ctx, accountsFromDB, cacheExists, cardsErrs.CardsAccountsErrs().CodeNoAccountsListError())
	}

	// получаем список финансовых контрактов из БД по IBAN счетов
	finContracts, err := u.Providers.Storage.GetFinContractListByAccountIbans(ctx, filterAccountIbansByAccount(accountsFromDB))
	if err != nil {
		return nil, errs.Wrapf(err, errTmpl, "GetFinContractListByAccountIbans")
	}

	// Если счета из Colvir не найдены, возвращаем данные из кэша или пустой список
	if accountFromColvir == nil || len(accountFromColvir.Accounts) == 0 {
		if cacheExists {
			return &entity.GetCardsResult{
				Accounts: fillAccountsWithFinContracts(entity.MakeAccountsEntToEntity(accountsFromDB), finContracts),
				Cards:    entity.MakeCardsEntityToResult(cardsDB),
			}, nil
		}
		return &entity.GetCardsResult{Accounts: []entity.Account{}}, nil
	}

	// обновляем счета в БД полученные из Colvir
	if err := u.Providers.Storage.UpdateAccountData(ctx, accountFromColvir.Accounts, user.Iin); err != nil {
		logger.Error().Err(err).Msg("Failed to save account data")
		return nil, errs.Wrapf(err, errTmpl, "UpdateAccountData")
	}

	// обновляем статусы карты и финансовые контракты в БД
	if err := u.updateCardAndFinContractStatus(ctx, userID, cardsDB, accountsFromDB); err != nil {
		logger.Error().Err(err).Msg("Failed to update card and financial contract statuses")
	}

	// получаем обновленные счета из БД по IIN клиента
	updatedAccountsFromDB, exists := u.Providers.Storage.GetAccountByClientIIN(ctx, user.Iin)
	if exists {
		logger.Info().Msgf("updated accounts from DB: %+v", updatedAccountsFromDB)
	}

	return &entity.GetCardsResult{
		Accounts: fillAccountsWithFinContracts(entity.MakeAccountsEntToEntity(updatedAccountsFromDB), finContracts),
		Cards:    entity.MakeCardsEntityToResult(cardsDB),
	}, nil
}

// fillAccountsWithFinContracts заполняет счета финансовыми контрактами по IBAN
func fillAccountsWithFinContracts(
	accounts []entity.Account,
	finContracts []*entity.FinContract,
) []entity.Account {
	mFinContractsByIban := make(map[string]*entity.FinContract, len(finContracts))
	for _, finContract := range finContracts {
		mFinContractsByIban[finContract.IBAN] = finContract
	}

	for i := range accounts {
		if finContract, ok := mFinContractsByIban[accounts[i].Iban]; ok {
			accounts[i].FinContract = finContract
		}
	}

	return accounts
}

// для каждого счета, если IBAN совпадает с IBAN финансового контракта
func filterAccountIbansByAccount(accounts []*ent.Accounts) []string {
	ibans := make([]string, 0, len(accounts))
	for _, account := range accounts {
		ibans = append(ibans, account.Iban)
	}

	return ibans
}

// filterClientCodesByOrigin фильтрует коды клиентов по их типу
func filterClientCodesByOrigin(clients []*colvirBridge.Client, origin string) []string {
	clientCodes := make([]string, 0, len(clients))
	for _, client := range clients {
		switch origin {
		case utils.UserOriginSme:
			if client.GetType() == consts.ClientIP.String() {
				clientCodes = append(clientCodes, client.Code)
			}
		case utils.UserOriginMobile:
			if client.GetType() == consts.ClientFiz.String() {
				clientCodes = append(clientCodes, client.Code)
			}
		}
	}
	return clientCodes
}

// updateCardAndFinContractStatus обновляет статусы карт и финансовых контрактов в БД
func (u *useCasesImpl) updateCardAndFinContractStatus(ctx context.Context, userID string, cardDB []*entity.Card, accounts []*ent.Accounts) error {
	logger := logs.FromContext(ctx)

	errTmpl := "updateCardAndFinContractStatus Error: %s"

	if len(cardDB) == 0 || len(accounts) == 0 {
		logger.Info().Msg("No cards or financial contracts to update status")
		return nil
	}

	// извлекаем ID карт и финансовых контрактов (счетов так как id счета равно id фин контакта)
	cardIDs := extractCardIDs(cardDB)
	accountIDs := extractAccountIDs(accounts)

	// получаем статусы карт и финансовых контрактов из Processing Bridge
	statuses, err := u.Providers.Processingbridge.GetCardAndFinContractStatus(ctx, &processingBridge.GetFinContractStatusReq{
		CardRIDs:     cardIDs,
		ContractRIDs: accountIDs,
		ClientID:     userID,
	})
	if err != nil {
		logger.Error().Msgf("Failed to get card and financial contract statuses: %s", err.Error())
		return errs.Wrapf(err, errTmpl, "GetCardAndFinContractStatus")
	}

	// обновляем статусы карт в БД
	if err := u.updateCardStatuses(ctx, statuses.CardStatuses); err != nil {
		return err
	}

	// обновляем статусы финансовых контрактов в БД
	if err := u.updateFinContractStatuses(ctx, statuses.ContractStatuses); err != nil {
		return err
	}

	logger.Info().Msgf("Successfully updated card and financial contract statuses: %+v", statuses)
	return nil
}

// extractCardIDs извлекает ID карт из массива карт
func extractCardIDs(cards []*entity.Card) []string {
	cardIDs := make([]string, len(cards))
	for i, card := range cards {
		cardIDs[i] = card.ID
	}
	return cardIDs
}

// extractAccountIDs извлекает ID финансовых контрактов из массива счетов
func extractAccountIDs(accounts []*ent.Accounts) []string {
	accountIDs := make([]string, len(accounts))
	for i, acc := range accounts {
		accountIDs[i] = acc.ID.String()
	}
	return accountIDs
}

// updateCardStatuses обновляет статусы карт в БД
func (u *useCasesImpl) updateCardStatuses(ctx context.Context, statuses []*processingBridge.CardStatus) error {
	logger := logs.FromContext(ctx)
	for _, status := range statuses {
		if err := u.Providers.Storage.UpdateCard(ctx, status.ContractRid, &entity.UpdateCardReq{Status: &status.Status}); err != nil {
			logger.Error().Msgf("Failed to update card status: %s", err.Error())
			return errs.Wrapf(err, "Failed to update card status for ID %s", status.ContractRid)
		}
	}
	return nil
}

// updateFinContractStatuses обновляет статусы финансовых контрактов в БД
func (u *useCasesImpl) updateFinContractStatuses(ctx context.Context, statuses []*processingBridge.ContractStatus) error {
	logger := logs.FromContext(ctx)
	for _, status := range statuses {
		if err := u.Providers.Storage.UpdateFinContractByAttachedAccount(ctx, status.ContractRid, &entity.FinContract{Status: status.Status}); err != nil {
			logger.Error().Msgf("Failed to update financial contract status for ID %s: %s", status.ContractRid, err.Error())
			continue
		}
	}
	return nil
}

// handleError обрабатывает ошибку и возвращает результат если есть данные в БД
func handleError(ctx context.Context, accountsCache []*ent.Accounts, cacheExists bool, err error) (*entity.GetCardsResult, error) {
	if cacheExists {
		logs.FromContext(ctx).Info().Msgf("handleError error (cached data available: %+v) Error: %s", accountsCache, err)
		return &entity.GetCardsResult{Accounts: entity.MakeAccountsEntToEntity(accountsCache)}, nil
	}

	logs.FromContext(ctx).Warn().Msgf("handleError error (no cached data): %s", err)
	return nil, err
}
