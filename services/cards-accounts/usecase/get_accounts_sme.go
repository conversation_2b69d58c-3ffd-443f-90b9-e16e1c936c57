package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/cardsAccounts"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

func (u *useCasesImpl) GetAccountsSME(ctx context.Context, req *entity.GetAccountsSMEReq) (*entity.GetAccountsSMEResult, error) {
	logger := logs.FromContext(ctx)
	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	user, err := u.Providers.Users.GetUserByID(ctx, &pb.GetUserByIDReq{ID: userID})
	if err != nil {
		logger.Warn().Msgf("could not get user: %s", err.Error())
		return nil, fmt.Errorf("could not get user: %w", err)
	}

	accountsFromDB, cacheExists := u.Providers.Storage.GetAccountByClientIIN(ctx, user.Iin)
	if cacheExists {
		logger.Info().Msgf("accounts from DB: %+v", accountsFromDB)
	}

	client, err := u.Providers.Colvirbridge.FindClient(ctx, &colvirBridge.FindClientReq{Iin: user.Iin})
	if err != nil {
		return handleSMEError(ctx, accountsFromDB, cacheExists, fmt.Errorf("could not get findClient: %w", err))
	}

	clientCodes := getSmeClientCodes(client.Clients)

	accountFromColvir, err := u.Providers.Colvirbridge.FindClientAccountList(ctx, &colvirBridge.FindClientAccountListReq{ClientCodes: clientCodes})
	if err != nil {
		return handleSMEError(ctx, accountsFromDB, cacheExists, errs.CardsAccountsErrs().CodeNoAccountsListError())
	}

	if accountFromColvir == nil || len(accountFromColvir.Accounts) == 0 {
		if cacheExists {
			return &entity.GetAccountsSMEResult{Accounts: entity.MakeAccountsSMEEntToEntity(accountsFromDB)}, nil
		}
		return &entity.GetAccountsSMEResult{Accounts: []entity.AccountSME{}}, nil
	}

	accountsFiltred := entity.MakeColvirAccountsSMEListFilter(accountFromColvir.Accounts, accountsFromDB)
	logger.Info().Msgf("accounts from Colvir: %+v", accountsFiltred)
	if err := u.Providers.Storage.SaveOrUpdateAccountsDataSME(ctx, accountsFiltred, user.Iin); err != nil {
		logger.Error().Err(err).
			Str("userIIN", user.Iin).
			Int("accountsCount", len(accountsFiltred)).
			Msg("failed to save account data")
		return nil, fmt.Errorf("failed to save account data: %w", err)
	}

	updatedAccountsFromDB, exists := u.Providers.Storage.GetAccountByClientIIN(ctx, user.Iin)
	if exists {
		logger.Info().Msgf("updated accounts from DB: %+v", updatedAccountsFromDB)
	}

	return &entity.GetAccountsSMEResult{Accounts: entity.MakeAccountsSMEEntToEntity(updatedAccountsFromDB)}, nil
}

func handleSMEError(ctx context.Context, accountsCache []*ent.Accounts, cacheExists bool, err error) (*entity.GetAccountsSMEResult, error) {
	if cacheExists {
		logs.FromContext(ctx).Info().Msgf("handleError error (cached data available: %+v) Error: %s", accountsCache, err)
		return &entity.GetAccountsSMEResult{Accounts: entity.MakeAccountsSMEEntToEntity(accountsCache)}, nil
	}

	logs.FromContext(ctx).Warn().Msgf("handleError error (no cached data): %s", err)
	return nil, err
}

func getSmeClientCodes(clients []*colvirBridge.Client) []string {
	codes := make([]string, 0, len(clients))
	for _, client := range clients {
		if client.GetType() == consts.ClientIP.String() {
			codes = append(codes, client.Code)
		}
	}
	return codes
}
