package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"slices"
	"strconv"
	"strings"
	"time"

	dictionaryPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/sync/errgroup"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	colvirConsts "git.redmadrobot.com/zaman/backend/zaman/services/colvir-bridge/consts"
	colvirPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pkbBridgePb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

func (u *useCasesImpl) CreateAccountSme(ctx context.Context, req *entity.CreateAccountReq) (*entity.CreateAccountResult, error) {
	const operationID string = "CreateAccountSME"
	logger := logs.FromContext(ctx)
	requestID := utils.ExtractRequestID(ctx)

	// Создаем основной span для операции
	tracer := otel.Tracer("cards-accounts")
	ctx, span := tracer.Start(ctx, "CreateAccount")
	defer span.End()

	logger.Info().Msgf("%s, %s - Получен запрос на создание расчётного счёта", operationID, requestID)

	account := entity.NewAccountDTO(ctx, req.UserIIN)
	logger.Info().Msgf("%s, %s - Статус счёта c IIN:'%s' изменён на статус: %s", operationID, requestID, req.UserIIN, account.Status)

	// Получаем необходимые данные для создания счета
	clientRecord, err := u.gatherClientRecord(ctx, req.UserIIN, operationID, requestID, tracer)
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		return nil, err
	}

	// Обрабатываем клиента SME
	ctx, processClientSpan := tracer.Start(ctx, "ProcessClientSme")
	ipClient, err := u.ProcessClientSme(
		ctx,
		clientRecord.Clients,
		req.UserIIN,
		tracer,
	)
	if err != nil {
		processClientSpan.RecordError(err)
		processClientSpan.SetStatus(codes.Error, err.Error())
	}
	processClientSpan.End()
	if err != nil {
		logger.Error().Msgf("%s, %s - Не удалось обработать клиента SME - %s", operationID, requestID, err.Error())
		return nil, err
	}

	// Проверка на nil для ipClient
	if ipClient == nil {
		logger.Error().Msgf("%s, %s - ipClient is nil", operationID, requestID)
		return nil, errors.New("ipClient is nil")
	}

	// Создаем договор РКО и сохраняем счет
	agreement, err := u.createAndSaveAccount(ctx, ipClient, account, req.UserIIN, operationID, requestID, tracer)
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		return nil, err
	}

	// Финализируем создание счета
	err = u.finalizeAccountCreation(ctx, account, agreement, requestID)
	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		return nil, err
	}

	logger.Info().Msgf("%s, %s - Создан договор РКО с deaReferenceID='%s' со статусом='%s' и ColvirStatus='%s'",
		operationID, requestID, agreement.DeaReferenceID, account.Status, account.ColvirStatus)

	return &entity.CreateAccountResult{}, nil
}

// gatherClientRecord собирает данные по клиенту из Colvir
func (u *useCasesImpl) gatherClientRecord(
	ctx context.Context,
	userIIN,
	operationID,
	requestID string,
	tracer trace.Tracer,
) (*colvirPb.FindClientResp, error) {
	logger := logs.FromContext(ctx)

	// Создаем span для поиска клиента
	ctx, findClientSpan := tracer.Start(ctx, "FindClient")
	colvirClientRecord, err := u.Providers.Colvirbridge.FindClient(ctx, &colvirPb.FindClientReq{Iin: userIIN})
	if err != nil && !strings.Contains(err.Error(), "not found") {
		findClientSpan.RecordError(err)
		findClientSpan.SetStatus(codes.Error, err.Error())
		logger.Error().Msgf("%s, %s - Не смог получить данные по клиенту в Colvir - %s", operationID, requestID, err.Error())
		return nil, err
	}
	findClientSpan.End()

	// Критическая проверка: colvirClientRecord может быть nil
	if colvirClientRecord == nil {
		logger.Warn().Msgf("%s, %s - colvirClientRecord is nil, treating as no clients found", operationID, requestID)
		colvirClientRecord = &colvirPb.FindClientResp{Clients: []*colvirPb.Client{}}
	}

	return colvirClientRecord, nil
}

// createAndSaveAccount создает договор РКО и сохраняет счет в БД
func (u *useCasesImpl) createAndSaveAccount(
	ctx context.Context,
	ipClient *colvirPb.Client,
	account *entity.AccountDTO,
	userIIN,
	operationID,
	requestID string,
	tracer trace.Tracer,
) (*colvirPb.CreateClientAgreementResp, error) {
	logger := logs.FromContext(ctx)

	activeAccount, err := u.findActiveAccountSme(
		ctx,
		ipClient.Code,
		operationID,
		userIIN,
		requestID,
		account,
	)
	if err != nil {
		logger.Error().Msgf("%s, %s - Не могу найти активный счёт в Colvir - %s", operationID, requestID, err.Error())
		return nil, err
	}

	var clientCode string
	if activeAccount != nil {
		clientCode = activeAccount.ClientCode
	} else {
		clientCode = ipClient.Code
	}

	agreement, err := u.OpenColvirAccountSme(
		ctx,
		clientCode,
		operationID,
		requestID,
		userIIN,
	)
	if err != nil {
		logger.Error().Msgf("%s, %s - Не удалось создать договор РКО для клиента - %s", operationID, requestID, err.Error())
		return nil, err
	}

	// Если договор был создан, то проверяем его статус и сохраняем в БД
	if agreement != nil {
		err = u.checkAndSaveAgreement(ctx, agreement, clientCode, userIIN, operationID, requestID, tracer)
		if err != nil {
			return nil, err
		}
	}

	return agreement, nil
}

// checkAndSaveAgreement проверяет и сохраняет договор в БД
func (u *useCasesImpl) checkAndSaveAgreement(ctx context.Context, agreement *colvirPb.CreateClientAgreementResp, clientCode, userIIN, operationID, requestID string, tracer trace.Tracer) error {
	logger := logs.FromContext(ctx)

	// Проверяем договор РКО для клиента
	checkAgreement, err := u.Providers.Colvirbridge.CheckClientAgreementRKO(ctx, &colvirPb.CheckClientAgreementReq{
		DeaReferenceIDList: []string{agreement.DeaReferenceID},
	})
	if err != nil {
		logger.Error().Msgf("%s, %s - Не могу проверить договор РКО для клиента - %s", operationID, requestID, err.Error())
		return err
	}

	logger.Info().Msgf("%s, %s - Договор РКО для клиента c ИИН: %s успешно создан", operationID, requestID, userIIN)

	// Если проверка договора успешна и есть договоры, сохраняем счёт в БД
	if checkAgreement != nil && len(checkAgreement.Agreements) > 0 {
		logger.Info().Msgf("%s, %s - Сохраняем созданный счет в БД: %+v", operationID, requestID, checkAgreement)

		// Создаем запрос на сохранение счета
		saveAccountReq := mapToSaveAccountSME(checkAgreement.Agreements[0], clientCode, userIIN)

		// Создаем span для сохранения счета
		ctx, saveAccountSpan := tracer.Start(ctx, "SaveAccount")
		err = u.Providers.Storage.SaveAccount(ctx, saveAccountReq)
		if err != nil {
			saveAccountSpan.RecordError(err)
			saveAccountSpan.SetStatus(codes.Error, err.Error())
			logger.Error().Msgf("%s, %s - Не удалось сохранить счёт в БД - %s", operationID, requestID, err.Error())
			// Не прерываем процесс, если не удалось сохранить счёт в БД
		} else {
			logger.Info().Msgf("%s, %s - Счёт успешно сохранён в БД", operationID, requestID)
		}
		saveAccountSpan.End()
	} else {
		logger.Warn().Msgf("%s, %s - Нет договоров в ответе от Colvir, не могу сохранить счёт", operationID, requestID)
	}

	return nil
}

// finalizeAccountCreation финализирует создание счета
func (u *useCasesImpl) finalizeAccountCreation(ctx context.Context, account *entity.AccountDTO, agreement *colvirPb.CreateClientAgreementResp, requestID string) error {
	const operationID string = "finalizeAccountCreation"
	logger := logs.FromContext(ctx)

	logger.Info().Msgf("%s, %s - Начинаем финализацию создания счета", operationID, requestID)

	kzTime, err := utils.GetCurrentKzTime()
	if err != nil {
		logger.Error().Msgf("%s, %s - Не удалось получить текущее время КЗ - %s", operationID, requestID, err.Error())
		return err
	}

	account.OpenDate = kzTime
	account.DeaReferenceID = agreement.DeaReferenceID
	account.Status = consts.AccountActive
	account.ColvirStatus = colvirConsts.AccountOpened

	logger.Info().Msgf("%s, %s - Счет успешно финализирован со статусом: %s, ColvirStatus: %s, DeaReferenceID: %s",
		operationID, requestID, account.Status, account.ColvirStatus, account.DeaReferenceID)

	return nil
}

func (u *useCasesImpl) OpenColvirAccountSme(
	ctx context.Context,
	clientCode string,
	operationID string,
	requestID string,
	userIIN string,
) (*colvirPb.CreateClientAgreementResp, error) {
	var agreement *colvirPb.CreateClientAgreementResp
	var err error
	agreement, err = u.Providers.Colvirbridge.CreateClientAgreement(ctx, mapToCreateClientAgreementRequestSME(clientCode))
	if err != nil {
		logs.FromContext(ctx).Error().Msgf("%s, %s - Не могу создать договор РКО для клиента - %s", operationID, requestID, err.Error())
		return nil, err
	}

	return agreement, nil
}

// Хелперы для работы с данными пользователя
func (u *useCasesImpl) findActiveAccountSme(
	ctx context.Context,
	clientCode,
	operationID,
	userIIN,
	requestID string,
	account *entity.AccountDTO,
) (*colvirPb.Account, error) {
	clientAccounts, err := u.Providers.Colvirbridge.FindClientAccountList(ctx, &colvirPb.FindClientAccountListReq{
		ClientCodes: []string{clientCode},
	})
	if err != nil {
		logs.FromContext(ctx).
			Error().
			Msgf("%s, %s - Не могу получить данные по расчётным счётам клиента в Colvir - %s", operationID, requestID, err.Error())
		return nil, err
	}
	var activeAccount *colvirPb.Account
	activeAccount = nil

	// Проверка на nil для clientAccounts
	if clientAccounts == nil {
		logs.FromContext(ctx).Warn().Msgf("%s, %s - clientAccounts is nil", operationID, requestID)
		return activeAccount, nil
	}

	for _, account := range clientAccounts.Accounts {
		// Проверка на nil для каждого account
		if account == nil {
			continue
		}
		if account.Status == consts.AccountActive.Str() && account.HasArrest == consts.AccountUnblocked.String() {
			activeAccount = account
			break
		}
	}

	account.Status = consts.AccountInOpening
	logs.FromContext(ctx).
		Info().
		Msgf("%s, %s - Статус счёта c IIN:'%s' изменён на статус: %s", operationID, requestID, userIIN, account.Status)

	return activeAccount, nil
}

// getGenderString возвращает строковое представление пола
func getGenderString(genderCode int64) string {
	if genderCode == 1 {
		return consts.GenderMale.String()
	} else {
		return consts.GenderFemale.String()
	}
}

// getDocTypeFromUserInfo возвращает тип документа из персональных данных
func getDocTypeFromUserInfo(userInfo *usersPb.GetPersonalDataResp) string {
	if userInfo != nil && userInfo.Documents != nil {
		for _, doc := range userInfo.Documents {
			if doc != nil && doc.Type != nil && doc.Type.Code == consts.DefaultDocumentClientCode.String() {
				return consts.PersonalDocumentFL.String()
			}
		}
	}
	return ""
}

// getDocNumberFromUserInfo возвращает номер документа из персональных данных
func getDocNumberFromUserInfo(userInfo *usersPb.GetPersonalDataResp) string {
	if userInfo != nil && userInfo.Documents != nil {
		for _, doc := range userInfo.Documents {
			if doc != nil && doc.Type != nil && doc.Type.Code == consts.DefaultDocumentClientCode.String() {
				return doc.Number
			}
		}
	}
	return ""
}

// getDocIssueDateFromUserInfo возвращает дату выдачи документа из персональных данных
func getDocIssueDateFromUserInfo(userInfo *usersPb.GetPersonalDataResp) string {
	if userInfo != nil && userInfo.Documents != nil {
		for _, doc := range userInfo.Documents {
			if doc != nil && doc.Type != nil && doc.Type.Code == consts.DefaultDocumentClientCode.String() {
				return doc.BeginDate
			}
		}
	}
	return ""
}

// getDocExpireDateFromUserInfo возвращает дату окончания действия документа из персональных данных
func getDocExpireDateFromUserInfo(userInfo *usersPb.GetPersonalDataResp) string {
	if userInfo != nil && userInfo.Documents != nil {
		for _, doc := range userInfo.Documents {
			if doc != nil && doc.Type != nil && doc.Type.Code == consts.DefaultDocumentClientCode.String() {
				return doc.EndDate
			}
		}
	}
	return ""
}

// makeRequestCreateIP создаёт запрос на создание ИП клиента в Colvir напрямую из данных пользователя
func (u *useCasesImpl) makeRequestCreateIP(
	ctx context.Context,
	fizClientInfo *colvirPb.Client, // Информация о найденном клиенте-физлице из Colvir (если есть)
	userIIN string, // ИИН пользователя
	userID string, // ID пользователя
	smeData *entity.SmeIPCreationData,
) (*colvirPb.CreateClientSMEIPRequest, error) {
	// Валидируем входные данные
	if err := u.validateIPCreationInput(ctx, userIIN, smeData); err != nil {
		return nil, err
	}

	// Формируем данные физлица
	clientFiz := u.buildClientFizData(fizClientInfo, smeData.UserInfo)

	// Формируем регистрационные документы
	regDocs := u.buildIPRegistrationDocs(ctx, smeData)

	// Формируем классификаторы
	classifications := u.buildIPClassifications(smeData)

	// Формируем итоговый запрос
	return &colvirPb.CreateClientSMEIPRequest{
		RequestID:       uuid.New().String(),                      // Уникальный идентификатор запроса
		FilialCode:      consts.DefaultFilialCode,                 // Головной офис
		Name:            utils.FormatIPName(smeData.JurInfo.Name), // Наименование ИП из PKB
		LongName:        utils.FormatIPName(smeData.JurInfo.Name), // Длинное наименование ИП из PKB
		RegDate:         smeData.IPInfo.RegistrationDate,          // Дата регистрации ИП
		Iin:             userIIN,                                  // ИИН ИП
		ClientFiz:       clientFiz,                                // Данные физлица
		Addresses:       []*colvirPb.AddressIP{},                  // Массив адресов (пустой)
		RegDocs:         regDocs,                                  // Массив регистрационных документов
		Contacts:        []*colvirPb.ContactIP{},                  // Массив контактов (пустой)
		Classifications: classifications,                          // Массив классификаторов
		UserId:          userID,                                   // ID пользователя
	}, nil
}

// validateIPCreationInput валидирует входные данные для создания ИП
func (u *useCasesImpl) validateIPCreationInput(
	ctx context.Context,
	userIIN string,
	smeData *entity.SmeIPCreationData,
) error {
	logger := logs.FromContext(ctx)

	if smeData.UserInfo == nil || smeData.JurInfo == nil || smeData.IPInfo == nil {
		logger.Warn().Msgf("smeData.UserInfo is nil %t smeData.JurInfo is nil %t smeData.IPInfo is nil %t",
			smeData.UserInfo == nil, smeData.JurInfo == nil, smeData.IPInfo == nil)

		return errors.New("all required objects must be non-nil")
	}
	if userIIN == "" {
		return errors.New("IIN is required")
	}
	if smeData.JurInfo.Name == "" || smeData.JurInfo.OkedCode == "" || smeData.JurInfo.OkedName == "" {
		logger.Warn().Msgf("smeData.JurInfo.Name is %s smeData.JurInfo.OkedCode is %s smeData.JurInfo.OkedName is %s",
			smeData.JurInfo.Name, smeData.JurInfo.OkedCode, smeData.JurInfo.OkedName)

		return errors.New("name, okedCode, and okedName are required in PKB response")
	}
	if smeData.IPInfo.RegistrationDate == "" || smeData.IPInfo.RegistrationCert == "" || smeData.IPInfo.IssuingAuthority == "" {
		logger.Warn().Msgf("smeData.IPInfo.RegistrationDate is %s smeData.IPInfo.RegistrationCert is %s smeData.JurInfo.OkedName is %s",
			smeData.IPInfo.RegistrationDate, smeData.IPInfo.RegistrationCert, smeData.IPInfo.IssuingAuthority)

		return errors.New("registration date, certificate, and issuing authority are required for IP")
	}
	if smeData.Kato == nil {
		return errors.New("kato is required")
	}
	return nil
}

// buildClientFizData формирует данные физлица для запроса создания ИП
func (u *useCasesImpl) buildClientFizData(fizClientInfo *colvirPb.Client, userInfo *usersPb.GetPersonalDataResp) *colvirPb.ClientFiz {
	if fizClientInfo != nil {
		// Сценарий A: Привязка к существующему клиенту-физлицу из Colvir
		return &colvirPb.ClientFiz{
			ClientCode:  fizClientInfo.Code,  // Код клиента из Colvir
			ClientDepID: fizClientInfo.DepID, // Код подразделения из Colvir
			ClientID:    fizClientInfo.ID,    // ID клиента из Colvir
		}
	}

	// Сценарий B: Создание нового клиента-физлица
	gender := getGenderString(userInfo.Gender.Code)
	docType := getDocTypeFromUserInfo(userInfo)
	docNumber := getDocNumberFromUserInfo(userInfo)
	docIssueDate := getDocIssueDateFromUserInfo(userInfo)
	docExpireDate := getDocExpireDateFromUserInfo(userInfo)
	docIssuePlace := consts.DefaultDocumentIssuePlaceCode.String()

	return &colvirPb.ClientFiz{
		LastName:        userInfo.Surname,    // Фамилия
		FirstName:       userInfo.Name,       // Имя
		MiddleName:      userInfo.Patronymic, // Отчество
		BirthDate:       userInfo.BirthDate,  // Дата рождения
		Gender:          gender,              // Пол
		Resident:        true,                // Признак резидентства
		ResidenceCode:   "1",                 // Код страны резидентства
		CitizenshipCode: "1",                 // Код гражданства
		DocType:         docType,             // Тип документа
		DocNumber:       docNumber,           // Номер документа
		DocIssueDate:    docIssueDate,        // Дата выдачи
		DocExpireDate:   docExpireDate,       // Срок действия
		DocIssuePlace:   docIssuePlace,       // Место выдачи
	}
}

// buildIPRegistrationDocs формирует регистрационные документы ИП
func (u *useCasesImpl) buildIPRegistrationDocs(
	ctx context.Context,
	smeData *entity.SmeIPCreationData,
) []*colvirPb.RegDoc {
	regDocs := []*colvirPb.RegDoc{}

	// В базе данных он уже валидирован перед сохранеием в базе данных
	regDate, _ := time.Parse(utils.RegDateFormat, smeData.IPInfo.RegistrationDate)
	NewRegTypeDate, _ := time.Parse(utils.RegDateFormat, utils.NewRegTypeDate)
	// определяем тип регистрационного документа по дате регистрации ИП
	RegistrationInfo := &colvirPb.RegDoc{}
	if regDate.Before(NewRegTypeDate) {
		RegistrationInfo.Type = consts.OldRegType
		RegistrationInfo.Series = utils.FormatRegDocSeries(smeData.IPInfo.RegistrationCert)
		RegistrationInfo.Number = consts.BlankForColvir
	} else {
		RegistrationInfo.Type = consts.NewRegType
		RegistrationInfo.Number = utils.FormatRegDocNumber(smeData.IPInfo.RegistrationCert)
		RegistrationInfo.Series = consts.BlankForColvir
	}

	RegistrationInfo.RegDate = smeData.IPInfo.RegistrationDate
	RegistrationInfo.IssueDate = consts.EmptyDate
	RegistrationInfo.ExpireDate = consts.EmptyDate
	RegistrationInfo.IssuePlace = utils.FormatRegDocIssuePlace(smeData.IPInfo.IssuingAuthority)

	regDocs = append(regDocs, RegistrationInfo)

	licenseCount := 1

	// Получаем список допустимых кодов лицензий
	validLicenseCodes, err := u.getValidLicenseCodes(ctx)
	if err != nil {
		// Если не удалось получить коды лицензий, логируем ошибку но продолжаем
		logs.FromContext(ctx).Error().Msgf("Failed to get valid license codes: %v", err)
		return regDocs
	}

	for _, license := range smeData.Permits.TaxPayerLicenses {
		if u.validateZamanLicense(license, validLicenseCodes) {
			doc := &colvirPb.RegDoc{
				Type:       consts.ZamanLicensePrefix + strconv.Itoa(licenseCount),
				RegDate:    consts.EmptyDate,
				Number:     utils.FormatRegDocNumber(license.DocumentID),
				Series:     utils.FormatRegDocSeries(license.ActivityType.Code),
				IssueDate:  license.ValidityStartDate,
				ExpireDate: license.ValidityEndDate,
				IssuePlace: utils.FormatRegDocIssuePlace(license.Licensiar.NameRu),
			}
			regDocs = append(regDocs, doc)
			licenseCount++
		}
	}

	return regDocs
}

func (u *useCasesImpl) getValidLicenseCodes(ctx context.Context) ([]string, error) {
	licenseDict, err := u.Providers.Dictionary.DocGetByName(ctx, &dictionaryPb.DocGetByNameReq{
		DocName:  consts.LicenseCodeDictName,
		DictName: consts.LicenseCodeDictName,
	})
	if err != nil {
		return nil, err
	}

	var licenseCodes struct {
		Codes []string `json:"codes"`
	}

	err = json.Unmarshal([]byte(licenseDict.GetDoc().GetData()), &licenseCodes)
	if err != nil {
		return nil, err
	}

	return licenseCodes.Codes, nil
}

func (u *useCasesImpl) validateZamanLicense(data *pkbBridgePb.TaxPayerLicense, validLicenseCodes []string) bool {
	if data.Licensiar == nil || data.ActivityType == nil {
		return false
	}

	// Проверяем что даты не пустые
	if data.ValidityStartDate == "" || data.ValidityEndDate == "" {
		return false
	}
	// Проверяем что код лицензии в списке допустимых
	if !slices.Contains[[]string, string](validLicenseCodes, data.ActivityType.Code) {
		return false
	}

	// Парсим даты начала и окончания действия лицензии
	startDate, err := time.Parse("2006-01-02T15:04:05", data.ValidityStartDate)
	if err != nil {
		return false
	}

	endDate, err := time.Parse("2006-01-02T15:04:05", data.ValidityEndDate)
	if err != nil {
		return false
	}

	now := time.Now()

	// Проверяем что лицензия уже вступила в силу (start <= сегодня)
	// и еще не истекла (end > сегодня)
	return !startDate.After(now) && endDate.After(now)
}

// buildIPClassifications формирует классификаторы для ИП
func (u *useCasesImpl) buildIPClassifications(smeData *entity.SmeIPCreationData) []*colvirPb.ClassificationIP {
	return []*colvirPb.ClassificationIP{
		{
			Code:      consts.ClientClassificationOkved,
			Value:     smeData.JurInfo.OkedCode,
			ValueName: smeData.JurInfo.OkedName,
			Base:      consts.ClientClassificationBase,
		},
		consts.GetCreateClassificationKatoMock(),
		consts.GetCreateClassificationKopfMock(),
		// consts.GetCreateClassificationOkvedMock(),
		consts.GetCreateClassificationOkfsMock(),
		consts.GetCreateClassificationPSAMock(),
	}
}

// ProcessClientSme обрабатывает клиента SME: находит или создает IP клиента
func (u *useCasesImpl) ProcessClientSme(
	ctx context.Context,
	colvirRecord []*colvirPb.Client,
	userIIN string,
	tracer trace.Tracer,
) (*colvirPb.Client, error) {
	logger := logs.FromContext(ctx)
	requestID := utils.ExtractRequestID(ctx)
	const operationID string = "ProcessClientSme"

	// Классифицируем клиентов
	fizClient, ipClient := u.classifySMEClients(colvirRecord)

	// Если нашли клиента ИП, просто возвращаем его
	if ipClient != nil {
		logger.Info().Msgf("%s, %s - Найден активный профиль клиента ИП в Colvir", operationID, requestID)
		return ipClient, nil
	}

	// Получаем необходимые данные для создания ИП
	smeIPData, err := u.getSMEIPCreationData(ctx, userIIN, operationID, requestID, tracer)
	if err != nil {
		return nil, err
	}

	// Создаем ИП клиента в зависимости от наличия ФЛ
	return u.createSMEIPClient(ctx, fizClient, userIIN, smeIPData, operationID, requestID, tracer)
}

// classifySMEClients разделяет клиентов на ФЛ и ИП
func (u *useCasesImpl) classifySMEClients(clients []*colvirPb.Client) (*colvirPb.Client, *colvirPb.Client) {
	var fizClient, ipClient *colvirPb.Client

	for _, client := range clients {
		if client == nil {
			continue
		}
		if client.Type == consts.ClientFiz.String() && client.State != string(consts.ClientProfileClosed) {
			fizClient = client
		}
		if client.Type == consts.ClientIP.String() && client.State != string(consts.ClientProfileClosed) {
			ipClient = client
		}
	}

	return fizClient, ipClient
}

// getSMEIPCreationData получает все необходимые данные для создания ИП
func (u *useCasesImpl) getSMEIPCreationData(
	ctx context.Context,
	userIIN,
	operationID,
	requestID string,
	tracer trace.Tracer,
) (*entity.SmeIPCreationData, error) {
	logger := logs.FromContext(ctx)

	// Используем errgroup с контекстом для параллельного выполнения запросов
	g, gCtx := errgroup.WithContext(ctx)
	gCtx = utils.CopyContextValues(ctx, gCtx)

	// Переменные для хранения результатов
	var jurInfo *pkbBridgePb.SendJurSearchByIinResp
	var ipinfo *usersPb.GetUserSmeIPInfoResp
	var permits *pkbBridgePb.GetPermitDocumentsByIinResp
	// var userInfo *usersPb.GetPersonalDataResp
	// var kato *dictionaryPb.KATOMapFromTSOIDResp

	// Получаем нужную информацию об ИП из PKB: ОКЭД и название ИП
	g.Go(func() error {
		tCtx, span := tracer.Start(gCtx, "GetJurInfoFromPKB")
		defer span.End()

		var err error
		jurInfo, err = u.Providers.Pkbbridge.SendJurSearchByIin(
			tCtx,
			&pkbBridgePb.SendJurSearchByIinReq{Iin: userIIN},
		)
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
			logger.Error().Msgf("%s, %s - failed to get jur info from PKB - %s", operationID, requestID, err.Error())
			return err
		}
		return nil
	})

	// Получаем информацию об ИП из БД
	g.Go(func() error {
		tCtx, span := tracer.Start(gCtx, "GetUserSmeIPInfo")
		defer span.End()

		var err error
		ipinfo, err = u.Providers.Users.GetUserSmeIPInfo(
			tCtx,
			&usersPb.GetUserSmeIPInfoReq{Iin: userIIN},
		)
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
			logger.Error().Msgf("%s, %s - failed to get user sme ip info - %s", operationID, requestID, err.Error())
			return err
		}
		return nil
	})

	// Получаем список лицензий ИП
	g.Go(func() error {
		tCtx, span := tracer.Start(gCtx, "GetPermitDocuments")
		defer span.End()

		var err error
		permits, err = u.Providers.Pkbbridge.GetPermitDocumentsByIin(tCtx, &pkbBridgePb.GetPermitDocumentsByIinReq{
			Iin: userIIN,
		})
		if err != nil {
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
			logger.Error().Msgf("%s, %s - failed to get permit documents - %s", operationID, requestID, err.Error())
			return err
		}
		return nil
	})

	// Получение персональных данных и KATO временно отключено
	// TODO: раскомментировать когда понадобится получение персональных данных

	// if err := g.Go(func() error {
	// 	tCtx, span := tracer.Start(gCtx, "GetPersonalData")
	// 	defer span.End()

	// 	var err error
	// 	userInfo, err = u.Providers.Users.GetPersonalData(
	// 		tCtx,
	// 		&usersPb.GetPersonalDataReq{Iin: userIIN},
	// 	)
	// 	if err != nil {
	// 		span.RecordError(err)
	// 		span.SetStatus(codes.Error, err.Error())
	// 		logger.Error().Msgf("%s, %s - failed to get personal data - %s", operationID, requestID, err.Error())
	// 		return err
	// 	}
	// 	return nil
	// }); err != nil {
	// 	return nil, err
	// }

	// if err := g.Go(func() error {
	// 	tCtx, span := tracer.Start(gCtx, "GetKATO")
	// 	defer span.End()

	// 	var err error
	// 	kato, err = u.Providers.Dictionary.KATOMapFromTSOID(
	// 		tCtx,
	// 		&dictionaryPb.KATOMapFromTSOIDReq{TSOID: smeData.Kato.TSOID},
	// 	)
	// 	if err != nil {
	// 		span.RecordError(err)
	// 		span.SetStatus(codes.Error, err.Error())
	// 		logger.Error().Msgf("%s, %s - failed to get KATO - %s", operationID, requestID, err.Error())
	// 		return err
	// 	}
	// 	return nil
	// }); err != nil {
	// 	return nil, err
	// }

	// Ждем завершения всех горутин
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// Проверяем результаты на nil
	if jurInfo == nil {
		logger.Error().Msgf("%s, %s - jurInfo is nil", operationID, requestID)
		return nil, errors.New("jurInfo is nil")
	}
	if ipinfo == nil || ipinfo.SmeIpInfo == nil {
		logger.Error().Msgf("%s, %s - ipinfo or ipinfo.SmeIpInfo is nil", operationID, requestID)
		return nil, errors.New("ipinfo or ipinfo.SmeIpInfo is nil")
	}
	if permits == nil {
		logger.Error().Msgf("%s, %s - permits is nil", operationID, requestID)
		return nil, errors.New("permits is nil")
	}

	// if userInfo == nil {
	// 	logger.Error().Msgf("%s, %s - userInfo is nil", operationID, requestID)
	// 	return nil, errors.New("userInfo is nil")
	// }

	// if kato == nil {
	// 	logger.Error().Msgf("%s, %s - kato is nil", operationID, requestID)
	// 	return nil, errors.New("kato is nil")
	// }

	return &entity.SmeIPCreationData{
		JurInfo: jurInfo,
		IPInfo:  ipinfo.SmeIpInfo,
		Permits: permits,
		Kato:    &dictionaryPb.KATOMapFromTSOIDResp{},
	}, nil
}

// createSMEIPClient создает ИП клиента в Colvir
func (u *useCasesImpl) createSMEIPClient(
	ctx context.Context,
	fizClient *colvirPb.Client,
	userIIN string,
	smeData *entity.SmeIPCreationData,
	operationID, requestID string,
	tracer trace.Tracer,
) (*colvirPb.Client, error) {
	logger := logs.FromContext(ctx)

	var logMessage string
	if fizClient != nil {
		logMessage = "Найден активный профиль клиента ФЛ в Colvir, создаём профиль ИП"
	} else {
		logMessage = "Не найдено профилей клиента в Colvir, создаём профиль ФЛ и ИП"
	}
	logger.Info().Msgf("%s, %s - %s", operationID, requestID, logMessage)

	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		logger.Error().Msgf("%s, %s - Не удалось получить ID пользователя - %s", operationID, requestID, err.Error())
		return nil, err
	}

	// Создаем запрос на создание ИП
	createReqSme, err := u.makeRequestCreateIP(
		ctx,
		fizClient,
		userIIN,
		userID,
		smeData,
	)
	if err != nil {
		logger.Error().Msgf("%s, %s - Не удалось сформировать запрос на создание ИП клиента - %s", operationID, requestID, err.Error())
		return nil, err
	}

	// Создаем ИП клиента в Colvir
	ctx, createIPSpan := tracer.Start(ctx, "CreateClientSMEIP")
	ipClientResp, err := u.Providers.Colvirbridge.CreateClientSMEIP(ctx, createReqSme)
	if err != nil {
		createIPSpan.RecordError(err)
		createIPSpan.SetStatus(codes.Error, err.Error())
	}
	createIPSpan.End()
	if err != nil {
		logger.Error().Msgf("%s, %s - Не удалось создать ИП клиента в Colvir - %s", operationID, requestID, err.Error())
		return nil, err
	}

	// Формируем ответ
	return &colvirPb.Client{
		ID:    ipClientResp.ColvirClientId,
		DepID: ipClientResp.ColvirClientDepId,
		Code:  ipClientResp.ColvirClientCode,
		Type:  consts.ClientIP.String(),
		State: consts.ClientProfileOpened.String(),
	}, nil
}

func mapToCreateClientAgreementRequestSME(clientCode string) *colvirPb.CreateClientAgreementReq {
	return &colvirPb.CreateClientAgreementReq{
		ClientCode:      clientCode,
		DeaTemplateCode: consts.DeaTemplateCodeDefaultValue,
		CurrencyCode:    consts.CurrencyKZT.String(),
		DepCode:         consts.DepCodeDefaultValue,
		DepServiceCode:  consts.DepServiceCodeDefaultValue,
		DepSellCode:     consts.DepSellCodeDefaultValue,
		AutGroupCode:    consts.AutGroupCodeDefaultValue,
		TarifCode:       consts.TarifCodeDefaultValueIP,
	}
}

// mapToSaveAccountSME создаёт запрос на сохранение счёта SME клиента в БД
func mapToSaveAccountSME(clientAgreement *colvirPb.Agreement, clientCode, userIIN string) *entity.SaveAccountReq {
	return &entity.SaveAccountReq{
		Account: &entity.AccountSaveToDB{
			ID: uuid.NewString(),
			Balance: &entity.AccountBalance{
				Main:      0,
				Blocked:   0,
				Available: 0,
			},
			Client: &entity.AccountClient{
				Iin:  userIIN,
				Code: clientCode,
				Type: consts.ClientIP.String(), // Тип клиента - ИП для SME
			},
			Document: &entity.AccountDocument{},
			Date: &entity.AccountDate{
				Opened: clientAgreement.DocumentDate.FromDate.AsTime().Format(consts.ColvirDateTimeFormat),
			},
			Arrest: &entity.AccountArrest{
				Blocking: false,
			},
			Iban:             clientAgreement.AccountCode,
			DeaReferenceID:   clientAgreement.DeaReferenceID,
			Status:           clientAgreement.DocumentStatus.Code,
			Currency:         consts.CurrencyKZT.String(),
			Type:             consts.CurrentType.String(),
			IsMain:           true,
			AccessionAccount: false,
			// Для SME счетов можно добавить метаданные в поле Metadata, если оно есть в структуре
		},
	}
}
