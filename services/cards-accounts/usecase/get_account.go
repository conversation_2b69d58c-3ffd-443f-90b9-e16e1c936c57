package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/cardsAccounts"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	colvirConsts "git.redmadrobot.com/zaman/backend/zaman/services/colvir-bridge/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
)

func (u *useCasesImpl) GetAccount(ctx context.Context, req *entity.GetAccountReq) (*entity.GetAccountResult, error) {
	logger := logs.FromContext(ctx)

	// получаем userID из контекста
	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// получаем пользователя по userID
	user, err := u.Providers.Users.GetUserByID(ctx, &pb.GetUserByIDReq{ID: userID})
	if err != nil {
		logger.Warn().Msgf("could not get user: %s", err.Error())
		return nil, fmt.Errorf("could not get user: %w", err)
	}

	// получаем аккаунт по ID
	account, err := u.Providers.Storage.GetAccountByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// проверяем, что аккаунт принадлежит пользователю
	if account.ClientIIN != user.Iin {
		return nil, errs.CardsAccountsErrs().CodeNoAccountsListError()
	}

	logger.Info().Msgf("GetAccountByID from DB: %+v", account)

	if account == nil {
		return nil, errs.CardsAccountsErrs().CodeNoAccountsListError()
	}

	if account.Status == colvirConsts.AccountOpened.String() {
		account.Status = consts.AccountActive.Str()
	}

	return &entity.GetAccountResult{
		ID:               account.ID,
		Status:           account.Status,
		Type:             account.Type,
		Currency:         account.Currency,
		Iban:             account.Iban,
		OpenDate:         account.OpenDate,
		Balance:          account.Balance,
		BalanceNatval:    account.BalanceNatval,
		PlanSum:          account.PlanSum,
		AvailableBalance: account.AvailableBalance,
		Arrest: entity.Arrest{
			Blocking: account.Arrest.Blocking,
		},
	}, nil
}
