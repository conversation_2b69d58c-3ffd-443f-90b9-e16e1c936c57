package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"golang.org/x/sync/errgroup"

	cardsErrs "git.redmadrobot.com/zaman/backend/zaman/errs/cardsAccounts"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	antifraudPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/antifraud"
	pkbBridgePb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

// GetApplicationForSign метод проверки пользователя aml и антифрод и геренерации документа для подписи заявления на открытие виртуальной карты
func (u *useCasesImpl) GetApplicationForSign(ctx context.Context, req *entity.GetApplicationForSignReq) (*entity.GetApplicationForSignResult, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msg("GetApplicationForSign started")
	defer logger.Info().Msg("end")
	group, ctx := errgroup.WithContext(ctx)
	group.SetLimit(2)
	// получаем пользователя из контекста
	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		logger.Error().Err(err).Msg("could not get user ID from context")
		return nil, err
	}

	user, err := u.Providers.Users.GetUserByID(ctx, &pb.GetUserByIDReq{ID: userID})
	if err != nil {
		logger.Error().Msgf("could not get user by ID %s: %s", userID, err.Error())
		return nil, fmt.Errorf("could not get user: %w", err)
	}

	personalInfo, err := u.Providers.Pkbbridge.GetPersonalInfoByIin(ctx, &pkbBridgePb.GetPersonalInfoByIinReq{
		Iin: user.Iin,
	})
	if err != nil {
		logger.Error().Err(err).Msg("Error getting personal info by IIN")
		return nil, cardsErrs.CardsAccountsErrs().CodePersonalDataRequestErrorError()
	}

	if personalInfo == nil {
		logger.Error().Msgf("Personal info not found for IIN: %s", user.Iin)
		return nil, cardsErrs.CardsAccountsErrs().CodePersonalDataRequestErrorError()
	}

	// проверяем пользователя в списках антифрода
	group.Go(func() error {
		return u.checkingUserAntifraud(ctx, personalInfo)
	})

	group.Go(func() error {
		return u.checkUserAml(ctx, userID, personalInfo)
	})

	if err := group.Wait(); err != nil {
		logger.Error().Err(err).Msg("Error during user checks in antifraud and AML")
		return nil, err
	}

	// TODO: Генерация документа для подписи в другой задаче

	return &entity.GetApplicationForSignResult{}, nil
}

// проверка о присутствии юзера в списках анти-фрода
func (u *useCasesImpl) checkingUserAntifraud(ctx context.Context, personalInfo *pkbBridgePb.GetPersonalInfoByIinResp) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msg("checkingUserAntifraud: checking user antifraud started")
	defer logger.Info().Msg("end")

	checkResult, err := u.Providers.Antifraud.FraudCheckClient(ctx, &antifraudPb.FraudCheckClientReq{
		ClientType: consts.AntifraudClientTypePhysical.String(),
		Iin:        &personalInfo.Iin,
		Firstname:  &personalInfo.Name,
		Lastname:   &personalInfo.Surname,
		Patronymic: &personalInfo.Patronymic,
	})
	logger.Debug().Msgf("checkingUserAntifraud: checkingResult: %+v", checkResult)
	if err != nil || checkResult == nil {
		logger.Error().Err(err).Msg("checkingUserAntifraud: error checking user antifraud")
		return cardsErrs.CardsAccountsErrs().CodeExternalServiceNotAvailableError()
	}

	logger.Info().Msgf("checkingUserAntifraud: checking status: %s", checkResult.Status)

	// TODO: 6. Результаты проверки сохраняются в БД Accounts
	if checkResult.Status == consts.AntifraudCheckStatusAllowed.String() {
		return nil
	}

	return cardsErrs.CardsAccountsErrs().CodeAntiFraudCheckFailedError()
}

// checkUserAml проверяет пользователя в AML
// Ожидаемые статусы решения AML для действующих клиентов в ответе
// [-1] Ошибка входных данных
// [0] Можно проводить
// [2] Запретить проведение
// [3] Есть совпадение. Разрешить проведение
func (u *useCasesImpl) checkUserAml(ctx context.Context, userID string, personalInfo *pkbBridgePb.GetPersonalInfoByIinResp) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msg("checkUserAml: checking user aml started")
	defer logger.Info().Msg("end")

	amlCheckRes, err := u.Providers.Amlbridge.NewCheckOnlineClientCard(ctx, entity.PrepareAmlNewCheckRequest(ctx, userID, personalInfo))
	if err != nil {
		logs.FromContext(ctx).Error().Err(err).Msg("Error checking user in AML")
		return cardsErrs.CardsAccountsErrs().CodeExternalServiceNotAvailableError()
	}

	if amlCheckRes.Status == consts.AmlCheckStatus.Int32() {
		return nil
	}

	logs.FromContext(ctx).Debug().Msgf("AML check result: %+v", amlCheckRes)
	return cardsErrs.CardsAccountsErrs().CodeAmlCheckFailedError()
}
