package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/generic"
	"github.com/rs/zerolog"

	commonConsts "git.redmadrobot.com/zaman/backend/zaman/pkg/common"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/embossing"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/transliteration"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	colvirConsts "git.redmadrobot.com/zaman/backend/zaman/services/colvir-bridge/consts"
	colvirPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	notificationsPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/notifications"
	processingBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

const (
	createVirtualCardOpID string = "CreateVirtualCard"
	defaultRequestRetries int    = 3
)

// CreateVirtualCard создает виртуальную карту и счёт для пользователя
func (u *useCasesImpl) CreateVirtualCard(ctx context.Context, req *entity.CreateVirtualCardReq) (*entity.CreateVirtualCardResult, error) {
	customLogger := logs.FromContext(ctx).With().
		Str("request_id", utils.ExtractRequestID(ctx)).
		Str("operation_id", createVirtualCardOpID).
		Str("user_id", req.UserID).
		Logger()
	customLogger.Info().Msgf("start - Have request to create virtual card and account")
	defer customLogger.Info().Msgf("end")

	// Создаем новый объект CardAndAccountDTO с инициализированными полями по умолчанию
	cardAndAccount, err := u.createAccountAndCardWithDefaultStatus(ctx, customLogger, req)
	if err != nil {
		return nil, logWrapErr(customLogger, "error creating account and card with default status", err)
	}

	retryParams := generic.ParamsRetryWithBackoff[entity.CreateVirtualCardResult]{
		MaxRetries:          defaultRequestRetries,
		ErrMapFunc:          func(err error) error { return err },
		CheckRetryNecessity: func(err error) bool { return err != nil },
		Operation: func() (*entity.CreateVirtualCardResult, error) {
			return u.createVirtualCard(ctx, cardAndAccount, customLogger)
		},
	}

	return generic.RetryWithBackoff(retryParams)
}

func (u *useCasesImpl) createVirtualCard(ctx context.Context, cardAndAccount *entity.CardAndAccountDTO, customLogger zerolog.Logger) (*entity.CreateVirtualCardResult, error) {
	// Получаем персональные данные клиента
	userPersData, err := u.Providers.Users.GetPersonalData(ctx, &usersPb.GetPersonalDataReq{Iin: cardAndAccount.UserIIN})
	if err != nil {
		return nil, logWrapErr(customLogger, "error getting personal data for user", err)
	}

	if userPersData == nil {
		return nil, logWrapErr(customLogger, "personal data for user is empty", err)
	}
	cardAndAccount.UserIIN = userPersData.Iin

	// проверяем карточку клиента в Colvir если ее не создаем ее
	resultClientProfile, err := u.createOrUpdateColvirClientProfile(ctx, userPersData, cardAndAccount.UserIIN)
	if err != nil {
		return nil, logWrapErr(customLogger, "error creating or updating client profile in Colvir", err)
	}
	customLogger.Info().Msgf("get client profile from Colvir: %+v", resultClientProfile)

	// поиск активного счёта клиента
	activeAccount, err := u.findActiveAccount(
		ctx,
		&colvirPb.FindClientResp{Clients: []*colvirPb.Client{resultClientProfile}}, createVirtualCardOpID, userPersData.Iin, &cardAndAccount.Account)
	if err != nil {
		return nil, logWrapErr(customLogger, "error finding active account for client", err)
	}

	kzTime, err := utils.GetCurrentKzTime()
	if err != nil {
		return nil, logWrapErr(customLogger, "error getting current time in Kazakhstan", err)
	}

	if activeAccount == nil || activeAccount.Status != colvirConsts.ClientProfileOpened.String() && !cardAndAccount.Account.AccessionAccount {
		agreement, err := u.Providers.Colvirbridge.CreateClientAgreement(ctx, mapToCreateClientAgreementRequest(resultClientProfile.Code))
		if err != nil {
			cardAndAccount.Account.Status = consts.AccountError
			return nil, logWrapErr(customLogger, "error creating client agreement", err)
		}
		customLogger.Info().Msgf("has successful req to CreateClientAgreement from Colvir deaReferenceID='%s'", agreement.DeaReferenceID)
		cardAndAccount.Account.ColvirStatus = colvirConsts.AccountOpened
		cardAndAccount.Account.DeaReferenceID = agreement.DeaReferenceID
		cardAndAccount.Account.IsMain = true
		cardAndAccount.Account.OpenDate = kzTime // текущее время в Казахстане на момент создания счёта
		cardAndAccount.Account.Status = consts.AccountActive
	}

	// Заполняем данные активного счёта, если он найден
	if activeAccount != nil {
		cardAndAccount.Account.Iban = activeAccount.Iban
		cardAndAccount.Account.Status = consts.AccountActive
	}

	if err := u.handleCheckClientAgreementRKO(ctx, cardAndAccount, resultClientProfile); err != nil {
		return nil, logWrapErr(customLogger, "error handling client agreement RKO", err)
	}

	createCardResp, err := u.createFinContractAndCard(ctx, cardAndAccount, userPersData)
	if err != nil {
		cardAndAccount.Status = consts.AccountError.Str()
		// TODO: временно ошибку игнорируем чтобы не блокировать создание счёта
		customLogger.Error().Err(err).Msg("error creating financial contract and card")
	}

	// обновляем статус карты в БД
	if err := u.Providers.Storage.UpdateCard(ctx, cardAndAccount.ID.String(), &entity.UpdateCardReq{Status: &cardAndAccount.Status}); err != nil {
		return nil, logWrapErr(customLogger, "error updating card status in DB", err)
	}

	// TODO: SensitiveData
	customLogger.Info().Msgf("card created in ProcessingBridge: %+v", createCardResp)

	// Асинхронная отправка SMS при успешном создании карты
	if createCardResp != nil && createCardResp.Pan != "" {
		go func() {
			if err := u.sendSMSNotification(ctx, userPersData.Phone, cardAndAccount.UserID, cardAndAccount.Pan, cardAndAccount.UserLocale); err != nil {
				customLogger.Error().Err(err).Msg("error sending SMS notification")
			}
		}()
	}

	return &entity.CreateVirtualCardResult{
		CardID:    cardAndAccount.ID.String(),
		AccountID: cardAndAccount.AccountID,
		IsSuccess: true,
	}, nil
}

// sendSMSNotification отправляет SMS уведомление о создании карты
func (u *useCasesImpl) sendSMSNotification(ctx context.Context, phoneNumber, userID, cardNumber, locale string) error {
	notificationRes, err := u.Providers.Notifications.SendSMS(ctx, &notificationsPb.SendSmsReq{
		Args: map[string]string{
			consts.CardNumberFieldKey: cardNumber, // Маскированный номер карты для отображения
		},
		PhoneNumber: phoneNumber,
		Type:        consts.TemplateCardOpened.String(),
		Locale:      &locale,
		UserID:      &userID,
	})
	if err != nil {
		return fmt.Errorf("error sending SMS notification: %w", err)
	}

	logs.FromContext(ctx).Info().Msgf("SMS notification sent successfully: %+v", notificationRes)

	return nil
}

// handleCheckClientAgreementRKO проверяет наличие соглашения РКО для клиента и создает счёт, если соглашение найдено
func (u *useCasesImpl) handleCheckClientAgreementRKO(ctx context.Context, cardAndAccount *entity.CardAndAccountDTO, resultClientProfile *colvirPb.Client) error {
	customLogger := logs.FromContext(ctx).With().
		Str("request_id", utils.ExtractRequestID(ctx)).
		Str("operation_id", createVirtualCardOpID).
		Str("user_id", cardAndAccount.UserID).
		Logger()
	customLogger.Info().Msgf("start - handleCheckClientAgreementRKO")
	defer customLogger.Info().Msgf("end")

	// Подготовим запрос для проверки соглашения РКО в Colvir
	req := buildCheckAgreementReq(&cardAndAccount.Account)
	checkListAgreement, err := u.Providers.Colvirbridge.CheckClientAgreementRKO(ctx, req)
	if err != nil {
		cardAndAccount.Account.Status = consts.AccountError
		return logWrapErr(customLogger, "error checking client agreement RKO", err)
	}

	customLogger.Info().Msgf("RKO agreement created Colvir")
	if checkListAgreement != nil && len(checkListAgreement.Agreements) > 0 {
		mainAgreement := checkListAgreement.Agreements[0]
		cardAndAccount.Account.DeaReferenceID = mainAgreement.DeaReferenceID
		cardAndAccount.Account.Status = consts.AccountActive
		cardAndAccount.Account.OpenDate = mainAgreement.DocumentDate.FromDate.AsTime()
		cardAndAccount.Account.Type = consts.CurrentType
		cardAndAccount.Account.IsMain = true
		cardAndAccount.Account.AccessionAccount = false
		cardAndAccount.Account.Iban = mainAgreement.AccountCode
		cardAndAccount.Account.Currency = commonConsts.CurrencyKZT

		customLogger.Info().Msgf("Save account to DB: %+v from account list: %+v", mainAgreement, checkListAgreement)
	}

	colvirProfile := &colvirPb.CreateClientResponse{
		ColvirClientCode:  resultClientProfile.Code,
		ColvirClientId:    resultClientProfile.ID,
		ColvirClientDepId: resultClientProfile.DepID,
	}

	customLogger.Info().Msgf("save account to DB cards-accounts: %+v", cardAndAccount.Account)

	saveAccountReq := mapToSaveAccount(&cardAndAccount.Account, colvirProfile, cardAndAccount.UserIIN)
	if err := u.Providers.Storage.UpdateAccountByID(ctx, cardAndAccount.AccountID, saveAccountReq.Account); err != nil {
		return logWrapErr(customLogger, "error updating account in DB", err)
	}
	return nil
}

// buildCheckAgreementReq создает запрос для проверки соглашения РКО
func buildCheckAgreementReq(acc *entity.AccountDTO) *colvirPb.CheckClientAgreementReq {
	// Приоритет: если есть DeaReferenceID — отправляем его, иначе по IBAN
	if acc.DeaReferenceID != "" {
		return &colvirPb.CheckClientAgreementReq{
			DeaReferenceIDList: []string{acc.DeaReferenceID},
		}
	}
	if acc.Iban != "" {
		return &colvirPb.CheckClientAgreementReq{
			AccountList: []string{acc.Iban},
		}
	}
	return &colvirPb.CheckClientAgreementReq{}
}

// createFinContractAndCard создает финансовый контракт и карту в ProcessingBridge
func (u *useCasesImpl) createFinContractAndCard(ctx context.Context, cardAndAccount *entity.CardAndAccountDTO, userPersData *usersPb.GetPersonalDataResp) (*entity.CardAndAccountDTO, error) {
	customLogger := logs.FromContext(ctx).With().
		Str("request_id", utils.ExtractRequestID(ctx)).
		Str("operation_id", createVirtualCardOpID).
		Str("user_id", cardAndAccount.UserID).
		Logger()
	customLogger.Info().Msgf("start - has create fin contract and card")
	defer customLogger.Info().Msgf("end")

	createCardResp, err := u.createCardInProcessingBridge(ctx, customLogger, cardAndAccount, userPersData)
	if err != nil {
		return nil, logWrapErr(customLogger, "error creating card in ProcessingBridge", err)
	}
	expiredDate, err := time.Parse(commonConsts.CardExpireDate, createCardResp.ExpiryDate)
	if err != nil {
		return nil, logWrapErr(customLogger, "error parsing expiredDate", err)
	}
	cardAndAccount.EmbossingName = createCardResp.EmbossName
	cardAndAccount.Pan = createCardResp.Pan
	cardAndAccount.Status = createCardResp.Status   // Статус карты
	cardAndAccount.ExpiresAt = expiredDate          // Срок действия карты
	cardAndAccount.Status = consts.CardActive.Str() // Устанавливаем статус карты как активный

	if err := u.Providers.Storage.UpdateCard(ctx, cardAndAccount.ID.String(), &entity.UpdateCardReq{
		EmbossingName: &cardAndAccount.EmbossingName,
		MaskedPAN:     &cardAndAccount.Pan,
		ExpireDate:    &cardAndAccount.ExpiresAt,
		Status:        &cardAndAccount.Status,
	}); err != nil {
		return nil, logWrapErr(customLogger, "error updating card in DB", err)
	}

	if err := u.Providers.Storage.CreateFinContract(ctx, entity.MakeCardAccountToFinContractEntity(cardAndAccount)); err != nil {
		return nil, logWrapErr(customLogger, "error saving fin contract to DB", err)
	}

	customLogger.Info().Msgf("card save to DB cards-accounts: %+v", cardAndAccount)
	return cardAndAccount, nil
}

// createCardInProcessingBridge создает карту в ProcessingBridge
func (u *useCasesImpl) createCardInProcessingBridge(ctx context.Context,
	baseLogger zerolog.Logger,
	cardAndAccount *entity.CardAndAccountDTO,
	userPersData *usersPb.GetPersonalDataResp,
) (*processingBridge.CreateClientAccountAndCardResp, error) {
	customLogger := utils.LoggerWithOperationID(baseLogger, createVirtualCardOpID)
	customLogger.Info().Msgf("start")
	defer customLogger.Info().Msgf("end")

	// получаем документ клиента
	document, err := extractClientDocument(userPersData)
	if err != nil {
		return nil, logWrapErr(customLogger, "error extracting client document", err)
	}

	// получаем структуру с эмбоссированным именем и фамилией
	embossed, err := createEmbossedLine(ctx, customLogger, userPersData.Name, userPersData.Surname)
	if err != nil {
		return nil, logWrapErr(customLogger, "error creating embossing line", err)
	}

	// создаем запрос для создания клиента и карты
	req := entity.MakeCreateClientAccountAndCardReq(
		userPersData,
		cardAndAccount,
		document,
		embossed,
	)

	return u.Providers.Processingbridge.CreateClientAccountAndCard(ctx, req)
}

// extractClientDocument извлекает документ клиента из персональных данных клиента
func extractClientDocument(userPersData *usersPb.GetPersonalDataResp) (*processingBridge.Document, error) {
	for _, doc := range userPersData.Documents {
		if isDefaultDocument(doc.Type.Code, doc.Status.Code) {
			return mapToProcessingBridgeDocument(doc, consts.PCDocumentCodeID.String())
		}

		if isPassportDocument(doc.Type.Code, doc.Status.Code) {
			return mapToProcessingBridgeDocument(doc, consts.PCDocumentCodePassport.String())
		}
	}
	return nil, errors.New("no valid document found")
}

// isDefaultDocument проверяет, является ли документ дефолтным
func isDefaultDocument(code string, status string) bool {
	return status == consts.DefaultDocumentStatusCode || code == consts.DefaultDocumentClientCode.String()
}

// isPassportDocument проверяет, является ли документ паспортом
func isPassportDocument(code string, status string) bool {
	return status == consts.DefaultDocumentStatusCode || code == consts.DefaultDocumentClientCodePassport.String()
}

// mapToProcessingBridgeDocument мапит документ в структуру ProcessingBridge.Document
func mapToProcessingBridgeDocument(doc *usersPb.Document, docType string) (*processingBridge.Document, error) {
	beginDate, err := time.Parse(utils.RegDateFormat, doc.BeginDate)
	if err != nil {
		return nil, fmt.Errorf("parsing begin date: %w", err)
	}

	endDate, err := time.Parse(utils.RegDateFormat, doc.EndDate)
	if err != nil {
		return nil, fmt.Errorf("parsing end date: %w", err)
	}

	return &processingBridge.Document{
		Type:      docType,
		Number:    doc.Number,
		IssueDate: beginDate.Format(commonConsts.DocumentDateTimeFormat.Str()),
		ExpDate:   endDate.Format(commonConsts.DocumentDateTimeFormat.Str()),
		Issuer:    consts.CitizenshipKZ.String(),
	}, nil
}

// createEmbossedLine создаёт строку для эмбоссирования карты
func createEmbossedLine(ctx context.Context, baseLogger zerolog.Logger, name, surname string) (embossing.UserNameResult, error) {
	customLogger := utils.LoggerWithOperationID(baseLogger, createVirtualCardOpID)
	customLogger.Info().Msgf("start")
	defer customLogger.Info().Msgf("end")

	// транслитерируем имя и фамилию
	tlr := transliteration.NewTransliterator()
	transliterated := tlr.TransliterateUserName(ctx, name, surname)
	customLogger.Debug().Msgf("transliterated struct: %+v", transliterated)

	// эмбоссируем имя и фамилию
	embosser := embossing.NewEmbosserWithConfig(
		embossing.DefaultConfig(),
	)

	embossed, err := embosser.EmbossUserName(ctx, transliterated.FirstName, transliterated.LastName)
	if err != nil {
		switch err {
		case embossing.ErrNotTransliterated:
			customLogger.Error().Err(err).Msgf("error embossing name: not transliterated - firstName:'%s' surName:'%s'", transliterated.FirstName, transliterated.LastName)
		default:
			customLogger.Error().Err(err).Msgf("error embossing name - firstName:'%s' surName:'%s'", transliterated.FirstName, transliterated.LastName)
		}

		return embossed, err
	}

	customLogger.Debug().Msgf("embossed struct: %+v", embossed)

	return embossed, nil
}

// createOrUpdateColvirClientProfile создает или обновляет профиль клиента в Colvir
func (u *useCasesImpl) createOrUpdateColvirClientProfile(ctx context.Context, userInfo *usersPb.GetPersonalDataResp, userID string) (*colvirPb.Client, error) {
	var (
		fizClientProfile    *colvirPb.Client
		isValidColvirClient bool
	)

	customLogger := logs.FromContext(ctx).With().
		Str("request_id", utils.ExtractRequestID(ctx)).
		Str("operation_id", createVirtualCardOpID).
		Str("user_id", userID).
		Logger()
	customLogger.Info().Msgf("start - has create or update Colvir client profile")
	defer customLogger.Info().Msgf("end")

	// Получаем данные по карточке клиента в Colvir
	colvirClient, err := u.Providers.Colvirbridge.FindClient(ctx, &colvirPb.FindClientReq{Iin: userInfo.Iin})
	if err != nil {
		customLogger.Error().Err(err).Msg("error finding client in Colvir")
	}

	// Получаем данные по открытому профилю клиента в Colvir
	if colvirClient != nil && len(colvirClient.Clients) > 0 {
		for _, client := range colvirClient.Clients {
			if client.State == colvirConsts.ClientProfileOpened.String() && client.Type == colvirConsts.ClientFiz.String() {
				fizClientProfile = client
				isValidColvirClient = true
				// Проверяем, что профиль клиента соответствует данным пользователя
				// После получения статуса карточки клиента OPENED, необходимо отправить запрос GET/personal в Users DB для получения актуальных данных клиента
				// После их необходимо с мапить с данными из АБИС, для проверки актуальности данных карточки клиента.
				if userInfo.ColvirInfo != nil {
					isValidColvirClient = userInfo.ColvirInfo.ColvirClientCode == client.Code &&
						userInfo.ColvirInfo.ColvirClientId == client.ID &&
						userInfo.ColvirInfo.ColvirClientDepId == client.DepID
				}
			}
		}
	}

	if (colvirClient == nil || len(colvirClient.Clients) == 0) || !isValidColvirClient {
		createClientReq := mapToCreateClientRequest(userInfo)
		newClientProfile, err := u.Providers.Colvirbridge.CreateClient(ctx, createClientReq)
		if err != nil {
			return nil, fmt.Errorf("error creating client profile in Colvir: %w", err)
		}

		fizClientProfile = &colvirPb.Client{
			Code:  newClientProfile.ColvirClientCode,
			ID:    newClientProfile.ColvirClientId,
			DepID: newClientProfile.ColvirClientDepId,
		}
	} else {
		// Обновляем данные карточки клиента в Colvir
		_, err = u.Providers.Colvirbridge.UpdateClient(ctx, mapToUpdateClientRequest(userInfo, userID))
		if err != nil {
			customLogger.Error().Err(err).Msg("error updating client profile in Colvir")
		}
		customLogger.Info().Msg("client profile updated in colvir")
	}

	return fizClientProfile, nil
}

// createAccountAndCardWithDefaultStatus создает счет и карту с дефолтным статусом IN_OPENING и проверяет, на наличие присоединенного счёта
func (u *useCasesImpl) createAccountAndCardWithDefaultStatus(ctx context.Context, customLogger zerolog.Logger, req *entity.CreateVirtualCardReq) (*entity.CardAndAccountDTO, error) {
	cardAndAccount := entity.NewCardAndAccountDTO(req.UserID)

	if err := u.userDetails(ctx, customLogger, cardAndAccount); err != nil {
		return nil, err
	}

	if err := u.checkAndSetAccessionAccount(ctx, customLogger, cardAndAccount); err != nil {
		return nil, logWrapErr(customLogger, "error checking and setting accession account", err)
	}

	if err := u.saveAccountAndCard(ctx, customLogger, cardAndAccount); err != nil {
		return nil, err
	}

	return cardAndAccount, nil
}

// Если присоединенный счёт найден, то мапит его детали в CardAndAccountDTO
func (u *useCasesImpl) userDetails(ctx context.Context, customLogger zerolog.Logger, cardAndAccount *entity.CardAndAccountDTO) error {
	user, err := u.Providers.Users.GetUserByID(ctx, &usersPb.GetUserByIDReq{ID: cardAndAccount.UserID})
	if err != nil {
		return logWrapErr(customLogger, "error getting user by ID", err)
	}
	cardAndAccount.UserLocale = user.Locale
	cardAndAccount.UserIIN = user.Iin
	return nil
}

// checkAndSetAccessionAccount проверяет наличие активного присоединенного счёта для клиента и устанавливает его в CardAndAccountDTO
func (u *useCasesImpl) checkAndSetAccessionAccount(ctx context.Context, customLogger zerolog.Logger, cardAndAccount *entity.CardAndAccountDTO) error {
	accounts, exist := u.Providers.Storage.GetAccountByClientIIN(ctx, cardAndAccount.UserIIN)
	if !exist || len(accounts) == 0 {
		return nil
	}

	for _, acc := range accounts {
		switch {
		case isNonMainNonAccession(acc):
			return logWrapErr(customLogger, "non-main non-accession account already exists",
				fmt.Errorf("non-main non-accession account already exists for user %s", cardAndAccount.UserIIN))

		case isActiveAccessionFiz(acc) || isInOpeningStatus(acc) || isMainNonAccession(acc):
			if err := mapAccountDetails(ctx, acc, cardAndAccount); err != nil {
				return logWrapErr(customLogger, "error mapping account details", err)
			}
			return nil
		}
	}

	return nil
}

// isNonMainNonAccession проверяет, является ли счёт неосновным и неявляется ли он присоединённым
func isNonMainNonAccession(account *ent.Accounts) bool {
	return !account.IsMain && !account.AccessionAccount
}

// isMainNonAccession проверяет, является ли счёт основным, но не является ли он присоединённым
func isMainNonAccession(account *ent.Accounts) bool {
	return account.IsMain && !account.AccessionAccount && account.ClientType == consts.ClientFiz.String()
}

// isActiveAccessionFiz проверяет, является ли счёт активным присоединённым для физического лица
func isActiveAccessionFiz(account *ent.Accounts) bool {
	return account.AccessionAccount && account.IsMain && account.ClientType == consts.ClientFiz.String()
}

// isInOpeningStatus проверяет, является ли счёт в состоянии открытия
func isInOpeningStatus(account *ent.Accounts) bool {
	return account.Status == consts.AccountInOpening.Str()
}

// mapAccountDetails мапит детали счёта из Accounts в CardAndAccountDTO
func mapAccountDetails(ctx context.Context, account *ent.Accounts, cardAndAccount *entity.CardAndAccountDTO) error {
	logs.FromContext(ctx).Info().Msgf("start - map account details to CardAndAccountDTO for account: %s", account)

	if account == nil {
		logs.FromContext(ctx).Error().Msg("account is nil, cannot map details")
		return fmt.Errorf("account is nil, cannot map details")
	}

	openDate, err := time.Parse(commonConsts.DFDefault.Str(), account.DateOpened)
	if err != nil {
		logs.FromContext(ctx).Error().Err(err).Msgf("error parsing open date for account: %s", account)
	}

	cardAndAccount.AccountID = account.ID.String()
	cardAndAccount.ClientCode = account.ClientCode
	cardAndAccount.Account = entity.AccountDTO{
		AccessionAccount: account.AccessionAccount,
		Iban:             account.Iban,
		ID:               account.ID.String(),
		IsMain:           account.IsMain,
		Status:           consts.AccountStatus(account.Status),
		Type:             consts.AccountType(account.Type),
		Currency:         commonConsts.CurrencyCode(account.Currency),
		DeaReferenceID:   account.DeaReferenceID,
		OpenDate:         openDate,
	}
	return nil
}

// saveAccountAndCard сохраняет счёт и карту в базе данных
func (u *useCasesImpl) saveAccountAndCard(ctx context.Context, customLogger zerolog.Logger, cardAndAccount *entity.CardAndAccountDTO) error {
	// 1) Получаем карты клиента по ИИН
	cards, err := u.Providers.Storage.GetCardsByClientIIN(ctx, cardAndAccount.UserIIN)
	if err != nil {
		return logWrapErr(customLogger, "error getting cards by client IIN", err)
	}
	customLogger.Info().Msgf("found %d cards for user %s", len(cards), cardAndAccount.UserIIN)

	// 2) Проверяем наличие карты у клиента если они уже имеются прерываем
	if len(cards) > 0 {
		return logWrapErr(customLogger, "card already exists for user", fmt.Errorf("card already exists for user %s", cardAndAccount.UserIIN))
	}

	// 3) Сохраняем счёт и карту
	if err := u.Providers.Storage.SaveAccount(ctx, entity.MakeCardsDtoToAccountSaveReq(cardAndAccount)); err != nil {
		return logWrapErr(customLogger, "error saving account", err)
	}

	if err := u.Providers.Storage.SaveCard(ctx, entity.MakeSaveCardReq(cardAndAccount)); err != nil {
		return logWrapErr(customLogger, "error saving card", err)
	}

	return nil
}

// LogWrapErr логирует сообщение, а затем возвращает новую обёрнутую ошибку с этим же сообщением
func logWrapErr(logger zerolog.Logger, msg string, err error) error {
	logger.Error().Err(err).Msg(msg)

	return fmt.Errorf("%s: %w", msg, err)
}
