package usecase

import (
	"context"
	"errors"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel"

	commonConsts "git.redmadrobot.com/zaman/backend/zaman/pkg/common"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	colvirConsts "git.redmadrobot.com/zaman/backend/zaman/services/colvir-bridge/consts"
	usersConsts "git.redmadrobot.com/zaman/backend/zaman/services/users/consts"
	colvirPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

const operationID string = "CreateAccountRetail"

// CreateAccount - создаёт расчётный счёт для клиента в Colvir (синхронная операция)
// - Эпик - https://rmrkz.atlassian.net/browse/ZAM-10 Создание счета в АБИС, в Тенге
func (u *useCasesImpl) CreateAccount(ctx context.Context, req *entity.CreateAccountReq) (*entity.CreateAccountResult, error) {
	customLogger := logs.FromContext(ctx).With().
		Str("request_id", utils.ExtractRequestID(ctx)).
		Str("operation_id", operationID).
		Str("user_iin", req.UserIIN).
		Logger()
	customLogger.Info().Msgf("start - Получен запрос на создание расчётного счёта")
	defer customLogger.Info().Msgf("end")

	// Создаем основной span для операции
	tracer := otel.Tracer("cards-accounts")
	ctx, span := tracer.Start(ctx, "CreateAccount")
	defer span.End()

	// Создаём объект счёта со статусом account.Status=InOpening , account.ColvirStatus=EmptyString
	account := entity.NewAccountDTO(ctx, req.UserIIN)
	customLogger.Info().Msgf("Статус счёта изменён на статус: %s", account.Status)

	// Создаем span для поиска клиента
	ctx, findClientSpan := tracer.Start(ctx, "FindClient")
	// Получаем данные по клиенту в Colvir
	colvirClientRecord, err := u.Providers.Colvirbridge.FindClient(ctx, &colvirPb.FindClientReq{Iin: req.UserIIN})
	if err != nil {
		customLogger.Error().Err(err).Msgf("Не смог получить данные по клиенту в Colvir")
	}
	findClientSpan.End()

	// Создаем span для получения персональных данных
	ctx, personalDataSpan := tracer.Start(ctx, "GetPersonalData")
	// Получаем персональные данные клиента из БД Users
	userInfo, err := u.Providers.Users.GetPersonalData(ctx, &usersPb.GetPersonalDataReq{Iin: req.UserIIN})
	if err != nil {
		customLogger.Error().Err(err).Msgf("Не могу получить персональные данные клиента")
		return nil, err
	}
	personalDataSpan.End()

	// Создаем span для получения данных пользователя
	ctx, userDataSpan := tracer.Start(ctx, "GetUserByIIN")
	// Получаем данные пользователя из БД Users по ИИН
	user, err := u.Providers.Users.GetUserByIIN(ctx, &usersPb.GetUserByIINReq{Iin: req.UserIIN})
	if err != nil {
		customLogger.Error().Err(err).Msgf("Не могу получить userID")
		return nil, err
	}
	userDataSpan.End()

	// Получаем данные по открытому профилю клиента в Colvir
	var openedFizClientProfile *colvirPb.Client
	if colvirClientRecord != nil {
		for _, client := range colvirClientRecord.Clients {
			if client.State == colvirConsts.ClientProfileOpened.String() && client.Type == colvirConsts.ClientFiz.String() {
				openedFizClientProfile = client
			}
		}
	}

	// Создаем span для обработки профиля клиента
	ctx, profileSpan := tracer.Start(ctx, "ProcessClientProfile")
	// Обрабатываем профиль клиента в Colvir
	var newClientProfile *colvirPb.CreateClientResponse
	newClientProfile, err = u.colvirClientProfileProcessing(ctx, newClientProfile, openedFizClientProfile, colvirClientRecord, operationID, userInfo, req.UserIIN, user.User.Id)
	if err != nil {
		customLogger.Error().Err(err).Msgf("Не могу обработать профиль клиента в Colvir")
		return nil, err
	}
	profileSpan.End()

	// Создаем span для поиска активного аккаунта
	ctx, accountSpan := tracer.Start(ctx, "FindActiveAccount")
	// Ищем активный счёт в Colvir
	activeAccount, err := u.findActiveAccount(ctx, colvirClientRecord, operationID, req.UserIIN, account)
	if err != nil {
		customLogger.Error().Err(err).Msgf("Не могу найти активный счёт в Colvir")
		return nil, err
	}
	accountSpan.End()

	// Создаем span для открытия аккаунта
	ctx, openAccountSpan := tracer.Start(ctx, "OpenColvirAccount")
	// Создаём договор РКО для клиента
	agreement, err := u.OpenColvirAccount(ctx, account, activeAccount, newClientProfile, req.UserIIN)
	switch {
	case err != nil:
		customLogger.Error().Err(err).Msgf("Не могу создать договор РКО для клиента")
		return nil, err
	case agreement == nil:
		customLogger.Error().Msgf("Счёт не был открыт в Colvir")
		return nil, errors.New("счёт не был открыт в Colvir")
	default:
		customLogger.Info().Msgf("Успешный запрос на создание счета в Colvir с deaReferenceID='%s'", agreement.DeaReferenceID)
		account.ColvirStatus = colvirConsts.AccountOpened
	}
	openAccountSpan.End()

	// BPMN Step: Уведомить BE об НАЛИЧИИ карточки клиента

	result, err := saveNewAccountToDB(ctx, u, account, agreement, newClientProfile, req.UserIIN)
	if err != nil {
		customLogger.Error().Err(err).Msgf("Не могу сохранить счёт в БД")

		return result, err
	}

	return result, nil
}

// Сохраняем новый созданный счёт в БД cards-accounts c проверкой договора РКО
func saveNewAccountToDB(
	ctx context.Context,
	u *useCasesImpl,
	account *entity.AccountDTO,
	agreement *colvirPb.CreateClientAgreementResp,
	newClientProfile *colvirPb.CreateClientResponse,
	userIIN string,
) (*entity.CreateAccountResult, error) {
	customLogger := logs.FromContext(ctx).With().
		Str("operation_id", operationID+".saveNewAccountToDB").
		Str("user_iin", userIIN).
		Str("request_id", utils.ExtractRequestID(ctx)).
		Logger()
	customLogger.Info().Msgf("start")
	defer customLogger.Info().Msgf("end")

	kzTime, err := utils.GetCurrentKzTime()
	if err != nil {
		return nil, err
	}
	account.OpenDate = kzTime // текущее время в Казахстане на момент создания счёта
	account.DeaReferenceID = agreement.DeaReferenceID
	account.Status = consts.AccountActive

	// Проверяем договор РКО в Colvir - появился ли он в Colvir?
	checkListAgreement, err := u.Providers.Colvirbridge.CheckClientAgreementRKO(ctx, &colvirPb.CheckClientAgreementReq{
		DeaReferenceIDList: []string{agreement.DeaReferenceID},
	})
	if err != nil {
		account.Status = consts.AccountError
		customLogger.Error().Err(err).Msgf("Не могу проверить договор РКО для клиента, статус счёта изменён на: %s", account.Status)

		return nil, err
	}

	customLogger.Info().Msgf("Договор РКО успешно создан в Colvir")

	// Сохраняем новый созданный счёт в БД cards-accounts c проверкой договора РКО
	if checkListAgreement != nil && len(checkListAgreement.Agreements) > 0 {
		// берём первый договор из списка, потому что в запросе указан только один деаReferenceID
		mainAgreement := checkListAgreement.Agreements[0]
		account.DeaReferenceID = mainAgreement.DeaReferenceID
		account.Status = consts.AccountActive
		account.OpenDate = mainAgreement.DocumentDate.FromDate.AsTime()
		account.Type = consts.CurrentType
		account.IsMain = true
		account.AccessionAccount = false
		account.Iban = mainAgreement.AccountCode
		account.Currency = commonConsts.CurrencyKZT // TODO: сейчас это KZT, но в будущем будет использоваться что-то другое
		account.Type = consts.CurrentType
		account.IsMain = true
		account.AccessionAccount = false

		customLogger.Info().Msgf("Сохраняем созданный счет в БД: %+v из списка счетов клиента: %+v", mainAgreement, checkListAgreement)
		if err := u.Providers.Storage.SaveAccount(ctx, mapToSaveAccount(account, newClientProfile, userIIN)); err != nil {
			customLogger.Error().Err(err).Msgf("Не могу сохранить счёт в БД")
			return nil, err
		}

		customLogger.Info().Msgf("Счёт успешно сохранён в БД")
	}

	return &entity.CreateAccountResult{}, nil
}

func (u *useCasesImpl) colvirClientProfileProcessing(
	ctx context.Context,
	newClientProfile *colvirPb.CreateClientResponse,
	openedFizClientProfile *colvirPb.Client,
	colvirClientRecord *colvirPb.FindClientResp,
	operationID string,
	userInfo *usersPb.GetPersonalDataResp,
	userIIN string,
	userID string,
) (*colvirPb.CreateClientResponse, error) {
	customLogger := logs.FromContext(ctx).With().
		Str("operation_id", operationID+".colvirClientProfileProcessing").
		Str("user_iin", userIIN).
		Str("request_id", utils.ExtractRequestID(ctx)).
		Logger()
	customLogger.Info().Msgf("start")
	defer customLogger.Info().Msgf("end")

	var err error
	// Профиля клиента нет в Colvir - создаём его

	if openedFizClientProfile == nil || len(colvirClientRecord.Clients) == 0 {
		// BPMN Step: Уведомить BE об ОТСУТСТВИИ карточки клиента

		customLogger.Info().Msgf("Не могу найти открытый профиль клиента физ.лица в Colvir. Создаём новую профиль клиента.")

		createClientReq := mapToCreateClientRequest(userInfo)
		newClientProfile, err = u.Providers.Colvirbridge.CreateClient(ctx, createClientReq)
		if err != nil {
			customLogger.Error().Err(err).Msgf("Профиля клиента в Colvir не было, не смог создать новую карточку клиента.")

			return nil, err
		}
		customLogger.Info().Msgf("Профиля клиента в Colvir не было, создали новую карточку клиента.")
	} else {
		if openedFizClientProfile.State != colvirConsts.ClientProfileOpened.String() {
			newClientProfile, err = u.Providers.Colvirbridge.CreateClient(ctx, mapToCreateClientRequest(userInfo))
			if err != nil {
				customLogger.Error().Err(err).Msgf("Активного профиля клиента в Colvir не было и не смог создать новый карточку клиента.")
				return nil, err
			}
			customLogger.Info().Msgf("Активного профиля клиента в Colvir не было, создали новый карточку клиента.")
		} else {
			customLogger.Info().Msgf("Профиль клиента в Colvir есть, и он открыт- Обновляем данные клиента в Colvir")
			_, err := u.Providers.Colvirbridge.UpdateClient(ctx, mapToUpdateClientRequest(userInfo, userID))
			if err != nil {
				customLogger.Error().Err(err).Msgf("Не могу обновить данные в карточке клиента")
			}
			customLogger.Info().Msgf("Обновили данные в карточке клиента")
		}
	}

	// TODO: Если профиль клиента был открыт, то возвращаем его данные для дальнейшей обработки
	if newClientProfile == nil && openedFizClientProfile != nil && openedFizClientProfile.State == colvirConsts.ClientProfileOpened.String() {
		newClientProfile = &colvirPb.CreateClientResponse{
			ColvirClientCode:  openedFizClientProfile.Code,
			ColvirClientDepId: openedFizClientProfile.DepID,
			ColvirClientId:    openedFizClientProfile.ID,
		}
	}

	return newClientProfile, nil
}

func (u *useCasesImpl) findActiveAccount(ctx context.Context,
	colvirClientRecord *colvirPb.FindClientResp,
	operationID string,
	userIIN string,
	account *entity.AccountDTO,
) (*colvirPb.Account, error) {
	customLogger := logs.FromContext(ctx).With().
		Str("operation_id", operationID+".findActiveAccount").
		Str("user_iin", userIIN).
		Str("request_id", utils.ExtractRequestID(ctx)).
		Logger()
	customLogger.Info().Msgf("start")
	defer customLogger.Info().Msgf("end")

	var clientCodes []string
	for _, client := range colvirClientRecord.Clients {
		clientCodes = append(clientCodes, client.Code)
	}
	allAccounts, err := u.Providers.Colvirbridge.FindClientAccountList(ctx, &colvirPb.FindClientAccountListReq{
		ClientCodes: clientCodes,
	})
	if err != nil {
		customLogger.Error().Err(err).Msgf("Не могу получить данные по расчётным счётам клиента в Colvir")
		return nil, err
	}
	var activeAccount *colvirPb.Account
	for _, account := range allAccounts.Accounts {
		// Проверяем, что счёт активен и не арестован
		if account.Status == consts.ColvirAccountOpened.Str() &&
			account.HasArrest == consts.AccountUnblocked.String() &&
			account.Type == usersConsts.AccountTypeCURR.Str() &&
			account.Currency == consts.CurrencyKZT.String() {
			activeAccount = account
			break
		}
	}

	account.Status = consts.AccountInOpening
	customLogger.Info().Msgf("Статус счёта изменён на статус: %s", account.Status)

	return activeAccount, nil
}

func (u *useCasesImpl) OpenColvirAccount(ctx context.Context,
	account *entity.AccountDTO,
	activeAccount *colvirPb.Account,
	newClientProfile *colvirPb.CreateClientResponse,
	userIIN string,
) (*colvirPb.CreateClientAgreementResp, error) {
	customLogger := logs.FromContext(ctx).With().
		Str("operation_id", operationID+".OpenColvirAccount").
		Str("user_iin", userIIN).
		Str("request_id", utils.ExtractRequestID(ctx)).
		Logger()
	customLogger.Info().Msgf("start")
	defer customLogger.Info().Msgf("end")

	var (
		agreement *colvirPb.CreateClientAgreementResp
		err       error
	)

	if activeAccount == nil || activeAccount.Status != colvirConsts.ClientProfileOpened.String() {
		agreement, err = u.Providers.Colvirbridge.CreateClientAgreement(ctx, mapToCreateClientAgreementRequest(newClientProfile.ColvirClientCode))
		if err != nil {
			account.Status = consts.AccountError
			customLogger.Error().Err(err).Msgf("Не могу создать договор РКО для клиента, статус счёта изменён на: %s", account.Status)
			return nil, err
		}
	} else {
		agreement, err = u.Providers.Colvirbridge.CreateClientAgreement(ctx, mapToCreateClientAgreementRequest(activeAccount.ClientCode))
		if err != nil {
			account.Status = consts.AccountError
			customLogger.Error().Err(err).Msgf("Не могу создать договор РКО для клиента, статус счёта изменён на: %s", account.Status)

			return nil, err
		}
	}

	return agreement, nil
}

// mapToSaveAccount создаёт структуру для сохранения нового ГЛАВНОГО счёта клиента в БД Cards-Accounts
func mapToSaveAccount(account *entity.AccountDTO, clientProfile *colvirPb.CreateClientResponse, userIIN string) *entity.SaveAccountReq {
	return &entity.SaveAccountReq{
		Account: &entity.AccountSaveToDB{
			ID: uuid.NewString(),
			Balance: &entity.AccountBalance{
				Main:      0,
				Blocked:   0,
				Available: 0,
			},
			Client: &entity.AccountClient{
				Iin:  userIIN,
				Code: clientProfile.ColvirClientCode,
				Type: colvirConsts.ClientFiz.String(),
			},
			Document: &entity.AccountDocument{}, // счёт новый и не имеет документа
			Date: &entity.AccountDate{
				Opened: account.OpenDate.Format(commonConsts.DFDefault.Str()),
			},
			Arrest: &entity.AccountArrest{
				Blocking: false,
			},
			Iban:             account.Iban,
			DeaReferenceID:   account.DeaReferenceID,
			Status:           account.Status.Str(),
			Currency:         account.Currency.Str(), // TODO: сейчас это KZT, но в будущем будет использоваться что-то другое
			Type:             account.Type.String(),
			IsMain:           account.IsMain,
			AccessionAccount: account.AccessionAccount,
		},
	}
}

// mapToCreateClientRequest создаёт запрос на создание клиента из данных пользователя
func mapToCreateClientRequest(userInfo *usersPb.GetPersonalDataResp) *colvirPb.CreateClientRequest {
	gender := consts.GenderMale.String()
	if userInfo.Gender.Code == 2 {
		gender = consts.GenderFemale.String()
	}

	var (
		docType       string
		docIssueDate  string
		docExpireDate string
		docIssuePlace string
		docNumber     string
		isResident    bool
	)

	for _, doc := range userInfo.Documents {
		if doc.Type.Code == consts.DefaultDocumentClientCode.String() {
			docType = consts.PersonalDocumentFL.String()
			docIssueDate = doc.BeginDate
			docExpireDate = doc.EndDate
			// Определяем организацию, выдавшую документ, по данным пользователя
			if doc.IssueOrganization != nil {
				docIssuePlace = setIssueOrganizationByLang(locale.Ru, doc.IssueOrganization.NameRu)
			}
			docNumber = doc.Number
			isResident = true
			break
		}
	}

	return &colvirPb.CreateClientRequest{
		LastName:        userInfo.Surname,
		FirstName:       userInfo.Name,
		MiddleName:      userInfo.Patronymic,
		BirthDate:       userInfo.BirthDate,
		Gender:          gender,
		Resident:        isResident,
		ResidenceCode:   consts.ResidenceKZ.String(),
		CitizenshipCode: consts.CitizenshipKZ.String(),
		DocType:         docType,
		DocNumber:       docNumber,
		DocIssueDate:    docIssueDate,
		DocExpireDate:   docExpireDate,
		DocIssuePlace:   docIssuePlace,
		Iin:             userInfo.Iin,
		Addresses:       []*colvirPb.Address{},
		Contacts:        []*colvirPb.Contact{},
	}
}

// mapToUpdateClientRequest создаёт запрос на обновление данных клиента
func mapToUpdateClientRequest(userInfo *usersPb.GetPersonalDataResp, userID string) *colvirPb.UpdateClientRequest {
	gender := consts.GenderMale.String()
	if userInfo.Gender.Code == 2 {
		gender = consts.GenderFemale.String()
	}

	citizenship := consts.CitizenshipKZ.String()
	var isResident bool
	var docType, docIssueDate, docExpireDate, docIssuePlace, docNumber string

	for _, doc := range userInfo.Documents {
		if doc.Type.Code == consts.DefaultDocumentClientCode.String() {
			docType = consts.PersonalDocumentFL.String()
			docIssueDate = doc.BeginDate
			docExpireDate = doc.EndDate
			// Определяем организацию, выдавшую документ, по данным пользователя
			if doc.IssueOrganization != nil {
				docIssuePlace = setIssueOrganizationByLang(locale.Ru, doc.IssueOrganization.NameRu)
			}
			docNumber = doc.Number
			isResident = true
			break
		}
	}

	return &colvirPb.UpdateClientRequest{
		UserId:          userID,
		LastName:        &userInfo.Surname,
		FirstName:       &userInfo.Name,
		MiddleName:      &userInfo.Patronymic,
		BirthDate:       &userInfo.BirthDate,
		Gender:          &gender,
		Resident:        isResident,
		CitizenshipCode: &citizenship,
		DocType:         &docType,
		DocNumber:       &docNumber,
		DocIssueDate:    &docIssueDate,
		DocExpireDate:   &docExpireDate,
		DocIssuePlace:   &docIssuePlace,
	}
}

// setIssueOrganizationByLang
// функция для выставления дефолтных значений организаций, выдавшей документ
func setIssueOrganizationByLang(lang locale.Locale, issueOrganization string) string {
	var result string
	if lang == locale.Ru {
		switch issueOrganization {
		case "МИНИСТЕРСТВО ВНУТРЕННИХ ДЕЛ РК", "МВД":
			result = usersConsts.IssueOrganizationMfaKz
		case "МИНИСТЕРСТВО ЮСТИЦИИ РК", "МЮ":
			result = usersConsts.IssueOrganizationMinJustKz
		}

		return result
	}

	return result
}

// mapToCreateClientAgreementRequest создаёт запрос на создание договора РКО
func mapToCreateClientAgreementRequest(clientCode string) *colvirPb.CreateClientAgreementReq {
	return &colvirPb.CreateClientAgreementReq{
		ClientCode:      clientCode,
		DeaTemplateCode: consts.DeaTemplateCodeDefaultValue,
		CurrencyCode:    commonConsts.CurrencyKZT.Str(),
		DepCode:         consts.DepCodeDefaultValue,
		DepServiceCode:  consts.DepServiceCodeDefaultValue,
		DepSellCode:     consts.DepSellCodeDefaultValue,
		AutGroupCode:    consts.AutGroupCodeDefaultValue,
		TarifCode:       consts.TarifCodeDefaultValue,
	}
}
