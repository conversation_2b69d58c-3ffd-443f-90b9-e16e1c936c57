package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"unicode"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"
	"github.com/go-chi/chi/v5/middleware"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	topics "git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads/types"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	pbTaskManager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads"

	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads/message"
)

func (u *useCasesImpl) HandleCreateAccountEvent(ctx context.Context, msg *kafka.Message) {
	logger := logs.FromContext(ctx)
	ctx = utils.PutInfoFromMsgHeadersIntoCtx(ctx, *msg)

	logger.Info().Msgf("start create account event")

	var (
		taskStatus string
		errMessage string
	)

	logger.Info().Msgf("Getting create account event from kafka message and validating this message")
	createAccountReqData, err := kafka.ValueJSONToTyped[message.CreateTaskResp](msg.Value)
	if err != nil {
		logger.Error().Msgf("failed to get value CreateAccountReq from kafka message - %s", err.Error())
		return
	}

	logger.Info().Msgf("Is suitable this message - task type: %s", createAccountReqData.TaskType)
	if createAccountReqData.TaskType != topics.TaskTypeCreateAccount.String() {
		logger.Warn().Msgf("The task type in task payload is not suitable for this handler")
		return
	}

	logger.Info().Any("createAccount taskPayload", createAccountReqData.TaskPayload).Msg("got create account payload")

	var taskPayload payloads.CreateAccountPayload
	if err := json.Unmarshal([]byte(createAccountReqData.TaskPayload), &taskPayload); err != nil {
		logger.Error().Msgf("failed to unmarshal task payload - %s has error: %s", createAccountReqData.TaskPayload, err.Error())
		return
	}

	logger.Info().Any("user_iin", createAccountReqData.TaskPayload).Msg("got create account event")

	if taskPayload.UserIIN == "" {
		logger.Error().Msgf("user_iin is missing or invalid in task payload: '%+v'", taskPayload)

		taskStatus = consts.TaskStatusFailed.String()
		errMessage = "invalid user_iin in task payload"

		// Обновляем статус задачи перед возвратом
		if err := u.updateTaskStatus(ctx, createAccountReqData.TaskID, taskStatus, errMessage); err != nil {
			logger.Error().Msgf("failed to update task status - %s", err.Error())
		}

		return
	}

	// Валидация ИИН
	if validateIIN(taskPayload.UserIIN) != nil {
		logger.Error().Msgf("invalid user_iin in task payload: '%+v'", taskPayload)
	}

	// Создаем контекст с источником запроса и requestID
	ctx = putContext(ctx, utils.GetRequestSourceByOrigin(taskPayload.Origin), taskPayload.RequestID)

	logger.Info().Any("origin", taskPayload.Origin).Any("user_iin", taskPayload.UserIIN).Msg("Create account")
	// в зависимости от источника запроса создаём расчётный счёт
	if taskPayload.Origin == utils.UserOriginSme {
		_, err = u.CreateAccountSme(ctx, &entity.CreateAccountReq{UserIIN: taskPayload.UserIIN})
	} else {
		_, err = u.CreateAccount(ctx, &entity.CreateAccountReq{UserIIN: taskPayload.UserIIN})
	}

	if err != nil {
		logger.Error().Msgf("failed to create account - %s", err.Error())
		taskStatus = consts.TaskStatusFailed.String()
		errMessage = err.Error()
	} else {
		taskStatus = consts.TaskStatusSuccess.String()
	}
	logger.Info().Msgf("account has been created with status: %s", taskStatus)

	logger.Info().Msgf("Updating task status")
	if err := u.updateTaskStatus(ctx, createAccountReqData.TaskID, taskStatus, errMessage); err != nil {
		logger.Error().Msgf("failed to update task status - %s", err.Error())
		return
	}

	logger.Info().Msgf("Task status has been updated")
}

// validateIIN создает счет клиента по его ИИН
func validateIIN(iin string) error {
	// Проверяем, что IIN содержит 12 символов
	if len(iin) != 12 {
		return fmt.Errorf("IIN must contain 12 characters, but got %v", iin)
	}

	// Проверяем, что IIN содержит только цифры
	for _, char := range iin {
		if !unicode.IsDigit(char) {
			return fmt.Errorf("IIN must contain only digits, but got %v", iin)
		}
	}

	return nil
}

// putContext создает контекст с источником запроса и requestID
func putContext(ctx context.Context, reqSource, requestID string) context.Context {
	ctx = context.WithValue(ctx, middleware.RequestIDKey, requestID)
	// TODO: убрать и добавить ReqSourceKey в rmr-pkg вместо этого
	ctx = context.WithValue(ctx, grpcx.ReqSourceKey, reqSource) //nolint:staticcheck
	return ctx
}

func (u *useCasesImpl) updateTaskStatus(ctx context.Context, taskID string, status string, errMessage string) error {
	logger := logs.FromContext(ctx)

	logger.Info().Msgf("Updating task - taskID: %s status: %s errMessage: %v", status, taskID, errMessage)
	_, err := u.Providers.Taskmanager.UpdateTask(ctx, &pbTaskManager.UpdateTaskReq{
		TaskId:       taskID,
		TaskStatus:   status,
		ErrorMessage: &errMessage,
	})
	if err != nil {
		return err
	}

	return nil
}
