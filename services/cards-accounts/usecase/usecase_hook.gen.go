// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/usecase -i CardsAccounts -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ CardsAccounts = (*CardsAccountsHook)(nil)

// CardsAccountsHook implements CardsAccounts interface wrapper
type CardsAccountsHook struct {
	CardsAccounts
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// CreateAccount implements CardsAccounts
func (_w *CardsAccountsHook) CreateAccount(ctx context.Context, req *entity.CreateAccountReq) (cp1 *entity.CreateAccountResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "CreateAccount", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "CreateAccount", _params)

	cp1, err = _w.CardsAccounts.CreateAccount(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "CreateAccount", []any{cp1, err})
	return cp1, err
}

// CreateVirtualCard implements CardsAccounts
func (_w *CardsAccountsHook) CreateVirtualCard(ctx context.Context, req *entity.CreateVirtualCardReq) (cp1 *entity.CreateVirtualCardResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "CreateVirtualCard", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "CreateVirtualCard", _params)

	cp1, err = _w.CardsAccounts.CreateVirtualCard(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "CreateVirtualCard", []any{cp1, err})
	return cp1, err
}

// GetAccount implements CardsAccounts
func (_w *CardsAccountsHook) GetAccount(ctx context.Context, req *entity.GetAccountReq) (gp1 *entity.GetAccountResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "GetAccount", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "GetAccount", _params)

	gp1, err = _w.CardsAccounts.GetAccount(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "GetAccount", []any{gp1, err})
	return gp1, err
}

// GetAccounts implements CardsAccounts
func (_w *CardsAccountsHook) GetAccounts(ctx context.Context, req *entity.GetAccountsReq) (gp1 *entity.GetAccountsResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "GetAccounts", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "GetAccounts", _params)

	gp1, err = _w.CardsAccounts.GetAccounts(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "GetAccounts", []any{gp1, err})
	return gp1, err
}

// GetAccountsSME implements CardsAccounts
func (_w *CardsAccountsHook) GetAccountsSME(ctx context.Context, req *entity.GetAccountsSMEReq) (gp1 *entity.GetAccountsSMEResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "GetAccountsSME", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "GetAccountsSME", _params)

	gp1, err = _w.CardsAccounts.GetAccountsSME(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "GetAccountsSME", []any{gp1, err})
	return gp1, err
}

// GetApplicationForSign implements CardsAccounts
func (_w *CardsAccountsHook) GetApplicationForSign(ctx context.Context, req *entity.GetApplicationForSignReq) (gp1 *entity.GetApplicationForSignResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "GetApplicationForSign", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "GetApplicationForSign", _params)

	gp1, err = _w.CardsAccounts.GetApplicationForSign(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "GetApplicationForSign", []any{gp1, err})
	return gp1, err
}

// GetBalance implements CardsAccounts
func (_w *CardsAccountsHook) GetBalance(ctx context.Context, req *entity.GetBalanceReq) (gp1 *entity.GetBalanceResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "GetBalance", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "GetBalance", _params)

	gp1, err = _w.CardsAccounts.GetBalance(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "GetBalance", []any{gp1, err})
	return gp1, err
}

// GetCards implements CardsAccounts
func (_w *CardsAccountsHook) GetCards(ctx context.Context, req *entity.GetCardsReq) (gp1 *entity.GetCardsResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "GetCards", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "GetCards", _params)

	gp1, err = _w.CardsAccounts.GetCards(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "GetCards", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements CardsAccounts
func (_w *CardsAccountsHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.CardsAccounts, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "HealthCheck", _params)

	hp1, err = _w.CardsAccounts.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements CardsAccounts
func (_w *CardsAccountsHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.CardsAccounts, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "HealthEvent", _params)

	_w.CardsAccounts.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "HealthEvent", []any{})
	return
}

// InitConsumer implements CardsAccounts
func (_w *CardsAccountsHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.CardsAccounts, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "InitConsumer", _params)

	_w.CardsAccounts.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "InitConsumer", []any{})
	return
}

// SaveAccount implements CardsAccounts
func (_w *CardsAccountsHook) SaveAccount(ctx context.Context, req *entity.SaveAccountReq) (sp1 *entity.SaveAccountResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "SaveAccount", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "SaveAccount", _params)

	sp1, err = _w.CardsAccounts.SaveAccount(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "SaveAccount", []any{sp1, err})
	return sp1, err
}

// VerifyClient implements CardsAccounts
func (_w *CardsAccountsHook) VerifyClient(ctx context.Context, req *entity.VerifyClientReq) (vp1 *entity.VerifyClientResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.CardsAccounts, "VerifyClient", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.CardsAccounts, "VerifyClient", _params)

	vp1, err = _w.CardsAccounts.VerifyClient(_ctx, req)
	_w._postCall.Hook(_ctx, _w.CardsAccounts, "VerifyClient", []any{vp1, err})
	return vp1, err
}

// NewCardsAccountsHook returns CardsAccountsHook
func NewCardsAccountsHook(object CardsAccounts, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *CardsAccountsHook {
	return &CardsAccountsHook{
		CardsAccounts: object,
		_beforeCall:   beforeCall,
		_postCall:     postCall,
		_onPanic:      onPanic,
	}
}
