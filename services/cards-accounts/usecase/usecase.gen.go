// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package usecase

import (
	"context"

	cardsaccounts "git.redmadrobot.com/zaman/backend/zaman/config/services/cards-accounts"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/bus"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/backend-go/rmr-pkg/proxy/hooks"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/cards-accounts/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

var _ CardsAccounts = (*useCasesImpl)(nil)

type CardsAccounts interface {
	HealthCheck(ctx context.Context) (*entity.Health, error)
	HealthEvent(ctx context.Context, message *kafka.Message)
	InitConsumer(ctx context.Context)
	GetBalance(ctx context.Context, req *entity.GetBalanceReq) (*entity.GetBalanceResult, error)
	GetCards(ctx context.Context, req *entity.GetCardsReq) (*entity.GetCardsResult, error)
	GetAccounts(ctx context.Context, req *entity.GetAccountsReq) (*entity.GetAccountsResult, error)
	GetAccount(ctx context.Context, req *entity.GetAccountReq) (*entity.GetAccountResult, error)
	CreateAccount(ctx context.Context, req *entity.CreateAccountReq) (*entity.CreateAccountResult, error)
	SaveAccount(ctx context.Context, req *entity.SaveAccountReq) (*entity.SaveAccountResult, error)
	CreateVirtualCard(ctx context.Context, req *entity.CreateVirtualCardReq) (*entity.CreateVirtualCardResult, error)
	GetAccountsSME(ctx context.Context, req *entity.GetAccountsSMEReq) (*entity.GetAccountsSMEResult, error)
	GetApplicationForSign(ctx context.Context, req *entity.GetApplicationForSignReq) (*entity.GetApplicationForSignResult, error)
	VerifyClient(ctx context.Context, req *entity.VerifyClientReq) (*entity.VerifyClientResult, error)
}

type useCasesImpl struct {
	CardsAccounts
	cfg       *cardsaccounts.Config
	Providers providers.ServiceLocatorImpl
}

func New(ctx context.Context, locator providers.ServiceLocatorImpl, cfg *cardsaccounts.Config) *CardsAccountsHook {
	useCases := &useCasesImpl{
		cfg:       cfg,
		Providers: locator,
	}

	logger := logs.FromContext(ctx)

	hook := NewCardsAccountsHook(
		useCases,
		hooks.GrpcServiceLogBeforeCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPostCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPanic(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
	)
	useCases.CardsAccounts = hook

	return hook
}
func (u *useCasesImpl) InitConsumer(ctx context.Context) {
	register := func(ctx context.Context, busName string, handler func(context.Context, *kafka.Message)) error {
		subscriber := bus.SubscriberFn[*kafka.Message](func(ctx context.Context, messages ...*kafka.Message) error {
			for _, message := range messages {
				handler(ctx, message)
				message.Ack(ctx)
			}
			return nil
		})

		err := u.Providers.Event.Subscribe(ctx, busName, subscriber)
		if err != nil {
			return err
		}
		return nil
	}

	eventRegistry := u.EventRegistry()
	for busName, handler := range eventRegistry {
		err := register(ctx, busName, handler)
		if err != nil {
			logs.FromContext(ctx).Err(err).Msg("unable to register handler")
		}
	}
}
