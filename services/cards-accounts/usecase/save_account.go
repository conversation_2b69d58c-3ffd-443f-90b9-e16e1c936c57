package usecase

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (u *useCasesImpl) SaveAccount(ctx context.Context, accountRecord *entity.SaveAccountReq) (*entity.SaveAccountResult, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: SaveAccount")

	err := u.Providers.Storage.SaveAccount(ctx, accountRecord)
	if err != nil {
		logger.Error().Err(err).Msgf("Error while saving account: %+v", err)

		return &entity.SaveAccountResult{
			Success: false,
		}, err
	}

	logger.Info().Msgf("end: SaveAccount")

	return &entity.SaveAccountResult{
		Success: true,
	}, nil
}
