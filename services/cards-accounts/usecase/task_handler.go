package usecase

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads/message"
	topics "git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads/types"
)

func (u *useCasesImpl) HandleReadyTasks(ctx context.Context, msg *kafka.Message) {
	logger := logs.FromContext(ctx)
	ctx = utils.PutInfoFromMsgHeadersIntoCtx(ctx, *msg)

	logger.Info().Msgf("start handling ready task")

	createTaskReqData, err := kafka.ValueJSONToTyped[message.CreateTaskResp](msg.Value)
	if err != nil {
		logger.Error().Msgf("failed to get value CreateTaskResp from kafka message - %s", err.Error())
		return
	}

	logger.Info().Msgf("Processing task - task type: %s", createTaskReqData.TaskType)

	registry := u.ReadyTaskRegistry()

	if handler, exists := registry[createTaskReqData.TaskType]; exists {
		logger.Debug().Msgf("Handler was matched with task type: %s and id: %s", createTaskReqData.TaskType, createTaskReqData.TaskID)
		handler(ctx, msg)
	} else {
		logger.Warn().Msgf("No handler found for task type: %s", createTaskReqData.TaskType)
	}
}

func (u *useCasesImpl) ReadyTaskRegistry() map[string]func(ctx context.Context, msg *kafka.Message) {
	return map[string]func(ctx context.Context, msg *kafka.Message){
		topics.TaskTypeCreateAccount.String():     u.HandleCreateAccountEvent,
		topics.TaskTypeCreateVirtualCard.String(): u.HandleCreateAccountAndCardEvent,
	}
}
