package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/cardsAccounts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

func (u *useCasesImpl) GetAccounts(ctx context.Context, req *entity.GetAccountsReq) (*entity.GetAccountsResult, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: GetAccounts")

	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	user, err := u.Providers.Users.GetUserByID(ctx, &pb.GetUserByIDReq{ID: userID})
	if err != nil {
		logger.Warn().Msgf("could not get user: %s", err.Error())
		return nil, fmt.Errorf("could not get user: %w", err)
	}

	accountsFromDB, cacheExists := u.Providers.Storage.GetAccountByClientIIN(ctx, user.Iin)

	client, err := u.Providers.Colvirbridge.FindClient(ctx, &colvirBridge.FindClientReq{Iin: user.Iin})
	if err != nil {
		return handleAccoutsError(ctx, accountsFromDB, cacheExists, fmt.Errorf("could not get findClient: %w", err))
	}

	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logger.Error().Msgf("failed to get origin from context: %s", err.Error())
	}

	clientCodes := filterClientCodesByOrigin(client.Clients, origin)

	accountList, err := u.Providers.Colvirbridge.FindClientAccountList(ctx, &colvirBridge.FindClientAccountListReq{ClientCodes: clientCodes})
	if err != nil {
		return handleAccoutsError(ctx, accountsFromDB, cacheExists, errs.CardsAccountsErrs().CodeNoAccountsListError())
	}

	if accountList == nil || len(accountList.Accounts) == 0 {
		if cacheExists {
			return &entity.GetAccountsResult{Accounts: entity.MakeAccountsEntToEntity(accountsFromDB)}, nil
		}
		return &entity.GetAccountsResult{Accounts: []entity.Account{}}, nil
	}

	if err := u.Providers.Storage.UpdateAccountData(ctx, accountList.Accounts, user.Iin); err != nil {
		logger.Error().Err(err).
			Str("userIIN", user.Iin).
			Int("accountsCount", len(accountList.Accounts)).
			Msg("failed to save account data")
		return nil, fmt.Errorf("failed to save account data: %w", err)
	}

	updatedAccounts, _ := u.Providers.Storage.GetAccountByClientIIN(ctx, user.Iin)

	logger.Info().Msgf("end: GetAccounts")
	return &entity.GetAccountsResult{Accounts: entity.MakeAccountsEntToEntity(updatedAccounts)}, nil
}

// handleError обрабатывает ошибку и возвращает результат если есть данные в БД
func handleAccoutsError(ctx context.Context, accountsCache []*ent.Accounts, cacheExists bool, err error) (*entity.GetAccountsResult, error) {
	if cacheExists {
		logs.FromContext(ctx).Info().Msgf("handleError error (cached data available: %+v) Error: %s", accountsCache, err)
		return &entity.GetAccountsResult{Accounts: entity.MakeAccountsEntToEntity(accountsCache)}, nil
	}

	logs.FromContext(ctx).Warn().Msgf("handleError error (no cached data): %s", err)
	return nil, err
}
