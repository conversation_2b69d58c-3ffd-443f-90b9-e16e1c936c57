package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"golang.org/x/sync/errgroup"

	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pkbBridgePb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/storage/postgres/ent"
)

func (u *useCasesImpl) VerifyClient(ctx context.Context, req *entity.VerifyClientReq) (*entity.VerifyClientResult, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("start: VerifyClient")

	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		logger.Warn().Msgf("could not get user: %s", err.Error())
		return nil, fmt.Errorf("could not get user: %w", err)
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		logger.Error().Err(err).Msgf("invalid userID format: %s", userID)
		return nil, fmt.Errorf("could not parse user: %w", err)
	}

	resultsFromDB, cacheExists := u.Providers.Storage.GetClientVerificationResults(ctx, userUUID)
	if cacheExists {
		logger.Info().Int("resultCount", len(resultsFromDB)).Msg("Retrieved verification results from DB")
	} else {
		logger.Info().Msg("No cached verification results found")
	}

	todayResult, err := FilterResultsByDateAndStatus(ctx, resultsFromDB)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to filter verification results")
		return nil, fmt.Errorf("failed to filter verification results: %w", err)
	}

	if todayResult != nil && todayResult.Status != consts.ClientVerificationResultStatusError.Str() {
		logger.Info().Str("status", todayResult.Status).Msg("Found today's verification result")
		return entity.MakeClientVerificationResultEntToEntity(todayResult), nil
	}

	logger.Info().Msg("Creating new verification result")
	verificationResult, err := entity.MakeClientVerificationResultToDBSave(userUUID)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to create verification result")
		return nil, fmt.Errorf("failed to create verification result: %w", err)
	}
	// Сохранение результата в БД
	err = u.Providers.Storage.CreateClientVerificationResult(ctx, verificationResult)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to save verification result to DB")
		return nil, fmt.Errorf("failed to save verification result: %w", err)
	}

	// TODO: реализовать проверки
	u.startVerificationChecks(ctx, userID, verificationResult.ID)

	logger.Info().Msgf("verification result saved to DB: %+v", verificationResult)
	return verificationResult, nil
}

func FilterResultsByDateAndStatus(ctx context.Context, resultsFromDB []*ent.ClientVerificationResults) (*ent.ClientVerificationResults, error) {
	location, err := utils.GetKzTimeLocation()
	if err != nil {
		logs.FromContext(ctx).Error().Err(err).Msg("Failed to load Asia/Almaty location")
		return nil, err
	}
	today := time.Now().In(location).Truncate(24 * time.Hour)
	for _, result := range resultsFromDB {
		if result.Date.In(location).Truncate(24*time.Hour).Equal(today) &&
			(result.Status == consts.ClientVerificationResultStatusInProgress.Str() || result.Status == consts.ClientVerificationResultStatusDone.Str()) {
			logs.FromContext(ctx).Info().Msgf("found today's verification, returning cached result")
			return result, nil
		}
	}
	return nil, nil
}

func isEqual(pkbInfo *pkbBridgePb.GetPersonalInfoByIinResp, colvirInfo *colvirBridge.GetClientSMEIPResponse) bool {
	if pkbInfo.Name != colvirInfo.ClientFiz.FirstName ||
		pkbInfo.Surname != colvirInfo.ClientFiz.LastName ||
		pkbInfo.Patronymic != colvirInfo.ClientFiz.MiddleName {
		return false
	}
	if pkbInfo.Dob != colvirInfo.ClientFiz.BirthDate {
		return false
	}
	if pkbInfo.Citizenship.Code != colvirInfo.ClientFiz.CitizenshipCode {
		return false
	}
	return true
}

func (u *useCasesImpl) startVerificationChecks(ctx context.Context, userID string, clientVerificationID uuid.UUID) {
	go func() {
		logger := logs.FromContext(ctx)
		user, err := u.Providers.Users.GetUserByID(ctx, &users.GetUserByIDReq{ID: userID})
		if err != nil {
			logger.Error().Err(err).Msg("Failed to get user")
			return
		}

		verificationResultDTO := entity.MakeClientVerificationResultToDTO(clientVerificationID)

		collectedData, err := u.collectDataForVerificationChecks(ctx, user.Iin)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to collect data")
			verificationResultDTO.Status = consts.ClientVerificationResultStatusError.Str()
			var errWithCause *entity.ErrorWithCause
			if errors.As(err, &errWithCause) {
				verificationResultDTO.VerificationResult = errWithCause.Type
			}
			u.updateVerificationResult(ctx, verificationResultDTO)
			return
		}

		// Проверка 7 сравниваем данные ПКБ и данные Колвир
		isPersonalDataAndColvirInfoEqual := isEqual(collectedData.PersonalInfo, collectedData.ColvirInfo)
		// Placeholder for aml
		logger.Debug().Msgf("check if personal data and colvir are equal: %t", isPersonalDataAndColvirInfoEqual)

		verificationResultDTO.Status = consts.ClientVerificationResultStatusDone.Str()
		verificationResultDTO.VerificationResult = consts.ClientVerificationResultApproved.Str()
		u.updateVerificationResult(ctx, verificationResultDTO)
	}()
}

func (u *useCasesImpl) updateVerificationResult(ctx context.Context, verificationResult *entity.ClientVerificationDTO) {
	logger := logs.FromContext(ctx)
	err := u.Providers.Storage.UpdateClientVerificationResult(ctx, verificationResult)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to save verification result to DB")
	}
}

func (u *useCasesImpl) collectDataForVerificationChecks(ctx context.Context, userIIN string) (*entity.DataForVerification, error) {
	logger := logs.FromContext(ctx)
	logger.Info().Msgf("Start collecting data for account opening application SME IP")
	var personalInfo *pkbBridgePb.GetPersonalInfoByIinResp
	var jurInfo *pkbBridgePb.SendJurSearchByIinResp
	var permitInfo *pkbBridgePb.GetPermitDocumentsByIinResp
	var colvirInfo *colvirBridge.GetClientSMEIPResponse
	// Используем группу горутин для параллельного выполнения
	g, gCtx := errgroup.WithContext(ctx)

	// Получаем персональную информацию из ПКБ
	g.Go(func() error {
		var err error
		personalInfo, err = u.Providers.Pkbbridge.GetPersonalInfoByIin(gCtx, &pkbBridgePb.GetPersonalInfoByIinReq{
			Iin: userIIN,
		})
		if err != nil {
			logger.Error().Err(err).Msgf("Error getting personal info by IIN")
			return &entity.ErrorWithCause{Type: entity.PkbPersonalInfo, Cause: err}
		}
		return nil
	})

	// Получаем данные по ОКЭД ПКБ
	g.Go(func() error {
		var err error
		jurInfo, err = u.Providers.Pkbbridge.SendJurSearchByIin(gCtx, &pkbBridgePb.SendJurSearchByIinReq{
			Iin: userIIN,
		})
		if err != nil {
			logger.Error().Err(err).Msgf("Error getting jur info by IIN")
			return &entity.ErrorWithCause{Type: entity.PkbJurInfo, Cause: err}
		}
		return nil
	})

	// Получаем данныем по лицензиям ПКБ
	g.Go(func() error {
		var err error
		permitInfo, err = u.Providers.Pkbbridge.GetPermitDocumentsByIin(gCtx, &pkbBridgePb.GetPermitDocumentsByIinReq{
			Iin: userIIN,
		})
		if err != nil {
			logger.Error().Err(err).Msgf("Error getting permit documents by IIN")
			return &entity.ErrorWithCause{Type: entity.PkbPermitInfo, Cause: err}
		}
		return nil
	})

	// Получаем данные ИП карточки Колвир
	g.Go(func() error {
		var err error
		clientIP, err := u.Providers.Colvirbridge.GetClientIPCard(gCtx, &colvirBridge.GetClientIPCardRequest{
			UserID: userIIN,
		})
		if err != nil {
			logger.Error().Err(err).Msgf("Error getting client IP card")
			return &entity.ErrorWithCause{Type: entity.ColvirClientIP, Cause: err}
		}
		colvirInfo, err = u.Providers.Colvirbridge.GetClientSMEIP(gCtx, &colvirBridge.GetClientSMEIPRequest{ClientId: clientIP.ClientId, ClientCode: clientIP.ClientCode})
		if err != nil {
			logger.Error().Err(err).Msgf("Error getting client SME IP info")
			return &entity.ErrorWithCause{Type: entity.ColvirSMEIP, Cause: err}
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		// При возникновении ошибки в любой из горутин прерываем выполнение
		return nil, err
	}

	return &entity.DataForVerification{
		PersonalInfo: personalInfo,
		JurInfo:      jurInfo,
		PermitInfo:   permitInfo,
		ColvirInfo:   colvirInfo,
	}, nil
}
