package usecase

import (
	"context"
	"encoding/json"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads"
	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads/message"
	topics "git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads/types"
)

func (u *useCasesImpl) HandleCreateAccountAndCardEvent(ctx context.Context, msg *kafka.Message) {
	logger := logs.FromContext(ctx)
	ctx = utils.PutInfoFromMsgHeadersIntoCtx(ctx, *msg)

	logger.Info().Msgf("start create account and card event")

	var (
		taskStatus string
		errMessage string
	)

	logger.Info().Msgf("Getting create account and card event from kafka message and validating this message")
	createCardReqData, err := kafka.ValueJSONToTyped[message.CreateTaskResp](msg.Value)
	if err != nil {
		logger.Error().Msgf("failed to get value CreateCardReq from kafka message - %s", err.Error())
		return
	}

	logger.Info().Msgf("Is suitable this message - task type: %s", createCardReqData.TaskType)
	if createCardReqData.TaskType != topics.TaskTypeCreateVirtualCard.String() {
		logger.Warn().Msgf("The task type in task payload is not suitable for this handler")
		return
	}

	logger.Info().Any("createCard taskPayload", createCardReqData.TaskPayload).Msg("got create account and card payload")

	var taskPayload payloads.CreateAccountAndCardPayload
	if err := json.Unmarshal([]byte(createCardReqData.TaskPayload), &taskPayload); err != nil {
		logger.Error().Msgf("failed to unmarshal task payload - %s has error: %s", createCardReqData.TaskPayload, err.Error())
		return
	}

	if taskPayload.UserID == "" {
		logger.Error().Msgf("user_iin is missing or invalid in task payload: '%+v'", taskPayload)

		// Обновляем статус задачи перед возвратом
		if err := u.updateTaskStatus(ctx, createCardReqData.TaskID, consts.TaskStatusFailed.String(), "invalid user_iin in task payload"); err != nil {
			logger.Error().Msgf("failed to update task status - %s", err.Error())
		}

		return
	}

	// Создаем контекст с источником запроса и requestID
	ctx = putContext(ctx, utils.GetRequestSourceByOrigin(taskPayload.Origin), taskPayload.RequestID)
	_, err = u.CreateVirtualCard(ctx, &entity.CreateVirtualCardReq{
		UserID: taskPayload.UserID,
	})
	if err != nil {
		logger.Error().Msgf("failed to create account and card - %s", err.Error())
		taskStatus = consts.TaskStatusFailed.String()
		errMessage = err.Error()
	} else {
		taskStatus = consts.TaskStatusSuccess.String()
	}

	logger.Info().Msgf("Updating task status")
	if err := u.updateTaskStatus(ctx, createCardReqData.TaskID, taskStatus, errMessage); err != nil {
		logger.Error().Msgf("failed to update task status - %s", err.Error())
		return
	}

	logger.Info().Msgf("Task status has been updated")
}
