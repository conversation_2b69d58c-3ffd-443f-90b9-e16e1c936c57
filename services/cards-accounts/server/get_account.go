package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) GetAccount(ctx context.Context, req *pb.GetAccountRequest) (*pb.GetAccountResponse, error) {
	getAccountEntity := entity.MakeGetAccountPbToEntity(req)

	getAccount, err := s.useCase.GetAccount(ctx, getAccountEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetAccountEntityToPb(getAccount), nil
}
