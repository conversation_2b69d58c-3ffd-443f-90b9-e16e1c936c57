package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) VerifyClient(ctx context.Context, req *pb.VerifyClientRequest) (*pb.VerifyClientResponse, error) {
	verifyClientEntity := entity.MakeVerifyClientPbToEntity(req)

	verifyClient, err := s.useCase.VerifyClient(ctx, verifyClientEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeVerifyClientEntityToPb(verifyClient), nil
}
