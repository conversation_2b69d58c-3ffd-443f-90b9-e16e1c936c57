package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) GetApplicationForSign(ctx context.Context, req *pb.GetApplicationForSignReq) (*pb.GetApplicationForSignResp, error) {
	getApplicationForSignEntity := entity.MakeGetApplicationForSignPbToEntity(req)

	getApplicationForSign, err := s.useCase.GetApplicationForSign(ctx, getApplicationForSignEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetApplicationForSignEntityToPb(getApplicationForSign), nil
}
