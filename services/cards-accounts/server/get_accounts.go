package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) GetAccounts(ctx context.Context, req *pb.GetAccountsRequest) (*pb.GetAccountsResponse, error) {
	getAccountsEntity := entity.MakeGetAccountsPbToEntity(req)

	getAccounts, err := s.useCase.GetAccounts(ctx, getAccountsEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetAccountsEntityToPb(getAccounts), nil
}
