package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) SaveAccount(ctx context.Context, req *pb.SaveAccountRequest) (*pb.SaveAccountResponse, error) {
	saveAccountEntity := entity.MakeSaveAccountPbToEntity(req)

	saveAccount, err := s.useCase.SaveAccount(ctx, saveAccountEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeSaveAccountEntityToPb(saveAccount), nil
}
