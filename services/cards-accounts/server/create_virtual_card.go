package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) CreateVirtualCard(ctx context.Context, req *pb.CreateVirtualCardRequest) (*pb.CreateVirtualCardResponse, error) {
	createVirtualCardEntity := entity.MakeCreateVirtualCardPbToEntity(req)

	createVirtualCard, err := s.useCase.CreateVirtualCard(ctx, createVirtualCardEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeCreateVirtualCardEntityToPb(createVirtualCard), nil
}
