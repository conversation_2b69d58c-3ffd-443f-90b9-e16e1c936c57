package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) CreateAccount(ctx context.Context, req *pb.CreateAccountRequest) (*pb.CreateAccountResponse, error) {
	createAccountEntity := entity.MakeCreateAccountPbToEntity(req)

	createAccount, err := s.useCase.CreateAccount(ctx, createAccountEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeCreateAccountEntityToPb(createAccount), nil
}
