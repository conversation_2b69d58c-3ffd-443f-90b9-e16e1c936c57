package server

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
)

func (s *Server) GetCards(ctx context.Context, req *pb.GetCardsRequest) (*pb.GetCardsResponse, error) {
	getCardsEntity := entity.MakeGetCardsPbToEntity(req)

	getCards, err := s.useCase.GetCards(ctx, getCardsEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetCardsEntityToPb(getCards), nil
}
