package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) GetAccountsSME(ctx context.Context, req *pb.GetAccountsSMERequest) (*pb.GetAccountsSMEResponse, error) {
	getAccountsSMEEntity := entity.MakeGetAccountsSMEPbToEntity(req)

	getAccountsSME, err := s.useCase.GetAccountsSME(ctx, getAccountsSMEEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetAccountsSMEEntityToPb(getAccountsSME), nil
}
