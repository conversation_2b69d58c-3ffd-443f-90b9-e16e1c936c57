package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/entity"
)

func (s *Server) GetBalance(ctx context.Context, req *pb.GetBalanceReq) (*pb.GetBalanceResp, error) {
	getBalanceEntity := entity.MakeGetBalancePbToEntity(req)

	getBalance, err := s.useCase.GetBalance(ctx, getBalanceEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetBalanceEntityToPb(getBalance), nil
}
