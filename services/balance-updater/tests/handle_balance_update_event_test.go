package tests

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

// Unit тесты для маппинга данных из АБИС в запросы для ПЦ
func TestMapEventToProcessingRequest(t *testing.T) {
	tests := []struct {
		name     string
		event    *message.BalanceUpdateEvent
		expected map[string]interface{}
	}{
		{
			name: "Complete debit event mapping",
			event: &message.BalanceUpdateEvent{
				DepID:    692,
				ID:       1131433,
				IncomFL:  "0",
				AccCode:  "KZ51896KZT0522045417",
				SDok:     1500.75,
				ValCode:  "KZT",
				DocDate:  "12.26.2023",
				SendDate: "26.12.2023 10:15:30",
			},
			expected: map[string]interface{}{
				"contractRid":  "KZ51896KZT0522045417",
				"amount":       1500.75,
				"currency":     "KZT",
				"rrn":          "1131433",
				"categoryText": "",
				"isReversal":   false,
			},
		},
		{
			name: "Credit event with different currency",
			event: &message.BalanceUpdateEvent{
				DepID:    692,
				ID:       9876543,
				IncomFL:  "1",
				AccCode:  "KZ51896USD0522045418",
				SDok:     2500.00,
				ValCode:  "USD",
				DocDate:  "12.27.2023",
				SendDate: "",
			},
			expected: map[string]interface{}{
				"contractRid":  "KZ51896USD0522045418",
				"amount":       2500.00,
				"currency":     "USD",
				"rrn":          "9876543",
				"categoryText": "",
				"isReversal":   false,
			},
		},
		{
			name: "Event with zero amount",
			event: &message.BalanceUpdateEvent{
				DepID:   100,
				ID:      555,
				IncomFL: "0",
				AccCode: "KZ51896KZT0522045419",
				SDok:    0.0,
				ValCode: "KZT",
				DocDate: "01.01.2024",
			},
			expected: map[string]interface{}{
				"contractRid":  "KZ51896KZT0522045419",
				"amount":       0.0,
				"currency":     "KZT",
				"rrn":          "555",
				"categoryText": "",
				"isReversal":   false,
			},
		},
		{
			name: "Event with large ID",
			event: &message.BalanceUpdateEvent{
				DepID:   999,
				ID:      999999999,
				IncomFL: "1",
				AccCode: "KZ86125EUR5004100326",
				SDok:    99999.99,
				ValCode: "EUR",
				DocDate: "31.12.2024",
			},
			expected: map[string]interface{}{
				"contractRid":  "KZ86125EUR5004100326",
				"amount":       99999.99,
				"currency":     "EUR",
				"rrn":          "999999999",
				"categoryText": "",
				"isReversal":   false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := mapEventToProcessingRequestForTest(tt.event)

			assert.Equal(t, tt.expected["contractRid"], result.ContractRid)
			assert.Equal(t, tt.expected["amount"], result.Amount)
			assert.Equal(t, tt.expected["currency"], result.Currency)
			assert.Equal(t, tt.expected["rrn"], result.RRN)
			assert.Equal(t, tt.expected["categoryText"], result.CategoryText)
			assert.Equal(t, tt.expected["isReversal"], result.IsReversal)
		})
	}
}

func TestEventOperationTypeLogic(t *testing.T) {
	tests := []struct {
		name             string
		incomFL          string
		expectedType     string
		expectedIsDebit  bool
		expectedIsCredit bool
	}{
		{
			name:             "Debit operation",
			incomFL:          "0",
			expectedType:     "debit",
			expectedIsDebit:  true,
			expectedIsCredit: false,
		},
		{
			name:             "Credit operation",
			incomFL:          "1",
			expectedType:     "credit",
			expectedIsDebit:  false,
			expectedIsCredit: true,
		},
		{
			name:             "Unknown operation",
			incomFL:          "2",
			expectedType:     "unknown",
			expectedIsDebit:  false,
			expectedIsCredit: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				IncomFL: tt.incomFL,
			}

			assert.Equal(t, tt.expectedType, event.GetOperationType())
			assert.Equal(t, tt.expectedIsDebit, event.IsDebit())
			assert.Equal(t, tt.expectedIsCredit, event.IsCredit())
		})
	}
}

type ProcessingRequest struct {
	ContractRid  string  `json:"contractRid"`
	Amount       float64 `json:"amount"`
	Currency     string  `json:"currency"`
	RRN          string  `json:"rrn"`
	CategoryText string  `json:"categoryText,omitempty"`
	IsReversal   bool    `json:"isReversal"`
}

func mapEventToProcessingRequestForTest(event *message.BalanceUpdateEvent) *ProcessingRequest {
	return &ProcessingRequest{
		ContractRid:  event.AccCode,               // acc_code → contractRid
		Amount:       event.SDok,                  // sdok → amount
		Currency:     event.ValCode,               // val_code → currency
		RRN:          fmt.Sprintf("%d", event.ID), // id → rrn (как строка)
		CategoryText: "",                          // пока пустое (в проработке)
		IsReversal:   false,                       // по умолчанию false
	}
}
