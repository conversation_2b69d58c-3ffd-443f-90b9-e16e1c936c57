// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
	Processingbridge *MockProcessingbridgeClient
}

type Providers struct {
	Deduplicator *MockDeduplicator
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{
			Processingbridge: NewMockProcessingbridgeClient(gomock.NewController(t)),
		},
		Providers: Providers{
			Deduplicator: NewMockDeduplicator(gomock.NewController(t)),
		},
	}
}
