// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/deduplicator (interfaces: Deduplicator)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	deduplicator "git.redmadrobot.com/zaman/backend/zaman/pkg/deduplicator"
)

// MockDeduplicator is a mock of Deduplicator interface.
type MockDeduplicator struct {
	ctrl     *gomock.Controller
	recorder *MockDeduplicatorMockRecorder
}

// MockDeduplicatorMockRecorder is the mock recorder for MockDeduplicator.
type MockDeduplicatorMockRecorder struct {
	mock *MockDeduplicator
}

// NewMockDeduplicator creates a new mock instance.
func NewMockDeduplicator(ctrl *gomock.Controller) *MockDeduplicator {
	mock := &MockDeduplicator{ctrl: ctrl}
	mock.recorder = &MockDeduplicatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeduplicator) EXPECT() *MockDeduplicatorMockRecorder {
	return m.recorder
}

// GetStats mocks base method.
func (m *MockDeduplicator) GetStats() deduplicator.Stats {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStats")
	ret0, _ := ret[0].(deduplicator.Stats)
	return ret0
}

// GetStats indicates an expected call of GetStats.
func (mr *MockDeduplicatorMockRecorder) GetStats() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStats", reflect.TypeOf((*MockDeduplicator)(nil).GetStats))
}

// InitializeCache mocks base method.
func (m *MockDeduplicator) InitializeCache(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitializeCache", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitializeCache indicates an expected call of InitializeCache.
func (mr *MockDeduplicatorMockRecorder) InitializeCache(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitializeCache", reflect.TypeOf((*MockDeduplicator)(nil).InitializeCache), arg0)
}

// IsDuplicate mocks base method.
func (m *MockDeduplicator) IsDuplicate(arg0 context.Context, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsDuplicate", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsDuplicate indicates an expected call of IsDuplicate.
func (mr *MockDeduplicatorMockRecorder) IsDuplicate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsDuplicate", reflect.TypeOf((*MockDeduplicator)(nil).IsDuplicate), arg0, arg1)
}

// MarkAsProcessed mocks base method.
func (m *MockDeduplicator) MarkAsProcessed(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkAsProcessed", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkAsProcessed indicates an expected call of MarkAsProcessed.
func (mr *MockDeduplicatorMockRecorder) MarkAsProcessed(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkAsProcessed", reflect.TypeOf((*MockDeduplicator)(nil).MarkAsProcessed), arg0, arg1)
}

// Start mocks base method.
func (m *MockDeduplicator) Start(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockDeduplicatorMockRecorder) Start(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockDeduplicator)(nil).Start), arg0)
}

// Stop mocks base method.
func (m *MockDeduplicator) Stop() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stop")
	ret0, _ := ret[0].(error)
	return ret0
}

// Stop indicates an expected call of Stop.
func (mr *MockDeduplicatorMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockDeduplicator)(nil).Stop))
}
