// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package tests

import (
	"context"
	"encoding/json"
	"os"
	"path/filepath"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/httpx/mw"
)

// UserFixture represents the data structure that will be loaded from user.json
type UserFixture struct {
	UserID      string   `json:"user_id"`
	PhoneNumber string   `json:"phone_number"`
	Name        string   `json:"name"`
	Authorities []string `json:"authorities"`
}

// SetUserContext loads user parameters from user.json and sets them in the context
func (s *Suite) SetUserContext() {
	// Use the current context from s.ctx
	ctx := s.ctx
	logger := logs.FromContext(ctx)

	// Get the current test name to determine the path to the fixture file
	testName := s.getCurrentTestName()

	// Define the path to the user.json file
	filePath := filepath.Join("./fixtures", testName, "user.json")

	// Open the user.json file
	file, err := os.Open(filePath)
	if err != nil {
		s.T().Fatalf("failed to open file %s: %v", filePath, err)
	}
	defer file.Close()

	// Decode the data from the file into the UserFixture structure
	var userFixture UserFixture
	if err := json.NewDecoder(file).Decode(&userFixture); err != nil {
		s.T().Fatalf("failed to decode JSON from file %s: %v", filePath, err)
	}

	// Update the logger context
	logger.UpdateContext(logs.Options(
		logs.UserIDTag.Option(userFixture.UserID),
		logs.UserAuthorityTag.Option(logs.TagStringArray(userFixture.Authorities)),
	))

	// Create user information
	userInfo := map[string]interface{}{
		"user_id":      userFixture.UserID,
		"phone_number": userFixture.PhoneNumber,
		"name":         userFixture.Name,
	}

	// Set user information in the context
	ctx = context.WithValue(ctx, mw.UserInfoKey, userInfo)

	// Update the logger context and save the new context back to s.ctx
	s.ctx = logs.ToContext(ctx, logger)
}
