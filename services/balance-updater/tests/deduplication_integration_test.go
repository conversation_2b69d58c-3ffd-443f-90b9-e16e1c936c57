package tests

import (
	"context"
	"testing"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/deduplicator"
	deduplib "git.redmadrobot.com/zaman/backend/zaman/services/balance-updater"
)

// DeduplicationIntegrationSuite тестирует полную интеграцию дедупликации
type DeduplicationIntegrationSuite struct {
	suite.Suite
	ctx            context.Context
	mockCacheTable *mockCacheTable
	deduplicator   deduplicator.Deduplicator
}

func (s *DeduplicationIntegrationSuite) SetupTest() {
	logger := logs.Logger(context.Background(), &logs.Config{Level: "debug"}, logs.Options())
	s.ctx = logger.WithContext(context.Background())
	s.mockCacheTable = newMockCacheTable()

	// Создаем deduplicator с тестовой конфигурацией
	config := deduplicator.Config{
		TTL:                  24 * time.Hour,
		CleanupInterval:      5 * time.Minute,
		CleanupWindow:        24 * time.Hour,
		MaxHotCacheSize:      1000,
		InitialLoadBatchSize: 100,
		EnableMetrics:        true,
	}

	s.deduplicator = deduplicator.NewDeduplicatorWithConfig(s.mockCacheTable, config)
	require.NotNil(s.T(), s.deduplicator)
}

func (s *DeduplicationIntegrationSuite) TearDownTest() {
	if s.deduplicator != nil {
		s.deduplicator.Stop()
	}
}

func (s *DeduplicationIntegrationSuite) TestBalanceUpdaterDeduplication_NewEvent() {
	// Arrange
	colvirReferenceID := "692_1234567"

	// Act
	isDuplicate, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, colvirReferenceID)

	// Assert
	require.NoError(s.T(), err)
	assert.False(s.T(), isDuplicate, "New event should not be duplicate")

	// Проверяем статистику
	stats := s.deduplicator.GetStats()
	assert.Equal(s.T(), int64(1), stats.TotalChecks)
	assert.Equal(s.T(), int64(0), stats.DuplicatesDetected)
}

func (s *DeduplicationIntegrationSuite) TestBalanceUpdaterDeduplication_MarkAndCheck() {
	// Arrange
	colvirReferenceID := "692_7654321"

	err := deduplib.MarkEventAsProcessed(s.ctx, s.deduplicator, colvirReferenceID)
	require.NoError(s.T(), err)

	isDuplicate, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, colvirReferenceID)

	// Assert
	require.NoError(s.T(), err)
	assert.True(s.T(), isDuplicate, "Processed event should be duplicate")

	// Проверяем статистику
	stats := s.deduplicator.GetStats()
	assert.Equal(s.T(), int64(1), stats.TotalChecks)
	assert.Equal(s.T(), int64(1), stats.ProcessedEvents)
	assert.Equal(s.T(), int64(1), stats.DuplicatesDetected)
	assert.Equal(s.T(), int64(1), stats.HotCacheHits)
}

func (s *DeduplicationIntegrationSuite) TestBalanceUpdaterDeduplication_EmptyColvirID() {
	// Act & Assert - пустой ID должен вызывать ошибку
	_, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, "")
	assert.Error(s.T(), err)
	assert.Contains(s.T(), err.Error(), "colvirReferenceID cannot be empty")

	// Act & Assert - пустой ID для маркировки
	err = deduplib.MarkEventAsProcessed(s.ctx, s.deduplicator, "")
	assert.Error(s.T(), err)
	assert.Contains(s.T(), err.Error(), "colvirReferenceID cannot be empty")
}

func (s *DeduplicationIntegrationSuite) TestBalanceUpdaterDeduplication_DatabaseError() {
	// Arrange
	s.mockCacheTable.setFailure("exists", true) // Симулируем ошибку БД
	colvirReferenceID := "692_error_test"

	// Act
	isDuplicate, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, colvirReferenceID)

	// Assert
	require.Error(s.T(), err)
	assert.False(s.T(), isDuplicate)
	assert.Contains(s.T(), err.Error(), "failed to check duplicate")
}

func (s *DeduplicationIntegrationSuite) TestBalanceUpdaterDeduplication_FullWorkflow() {
	// Arrange
	events := []string{
		"692_1111111",
		"692_2222222",
		"692_3333333",
		"692_1111111",
		"692_4444444",
		"692_2222222",
	}

	expectedDuplicates := []bool{false, false, false, true, false, true}
	processedCount := 0

	for i, eventID := range events {
		isDuplicate, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, eventID)
		require.NoError(s.T(), err)

		assert.Equal(s.T(), expectedDuplicates[i], isDuplicate,
			"Event %s (index %d) duplicate check failed", eventID, i)

		if !isDuplicate {
			err = deduplib.MarkEventAsProcessed(s.ctx, s.deduplicator, eventID)
			require.NoError(s.T(), err)
			processedCount++
		}
	}

	// Assert - проверяем финальную статистику
	stats := s.deduplicator.GetStats()
	assert.Equal(s.T(), int64(len(events)), stats.TotalChecks, "Should check all events")
	assert.Equal(s.T(), int64(processedCount), stats.ProcessedEvents, "Should process non-duplicate events")
	assert.Equal(s.T(), int64(2), stats.DuplicatesDetected, "Should detect 2 duplicates")
	assert.Equal(s.T(), int64(processedCount), stats.HotCacheSize, "Hot cache should contain processed events")
}

func (s *DeduplicationIntegrationSuite) TestBalanceUpdaterDeduplication_ColdCacheIntegration() {
	// Arrange - предварительно заполняем "БД" (cold cache)
	existingEvents := map[string]time.Time{
		"692_existing_1": time.Now().Add(12 * time.Hour),
		"692_existing_2": time.Now().Add(6 * time.Hour),
		"692_expired":    time.Now().Add(-1 * time.Hour),
	}

	for eventID, dieTime := range existingEvents {
		s.mockCacheTable.addEntry(eventID, dieTime)
	}

	// Act & Assert - проверяем активные события (должны быть дубликатами)
	isDuplicate1, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, "692_existing_1")
	require.NoError(s.T(), err)
	assert.True(s.T(), isDuplicate1, "Existing active event should be duplicate")

	isDuplicate2, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, "692_existing_2")
	require.NoError(s.T(), err)
	assert.True(s.T(), isDuplicate2, "Existing active event should be duplicate")

	// Act & Assert - проверяем истекшее событие (не должно быть дубликатом)
	isDuplicateExpired, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, "692_expired")
	require.NoError(s.T(), err)
	assert.False(s.T(), isDuplicateExpired, "Expired event should not be duplicate")

	// Проверяем что активные события добавились в hot cache
	stats := s.deduplicator.GetStats()
	assert.Equal(s.T(), int64(2), stats.ColdCacheHits, "Should have 2 cold cache hits")
	assert.Equal(s.T(), int64(2), stats.HotCacheSize, "Active events should be added to hot cache")
}

func (s *DeduplicationIntegrationSuite) TestBalanceUpdaterDeduplication_InitializationFromDatabase() {
	// Arrange - заполняем mock БД активными записями
	activeEvents := map[string]time.Time{
		"692_startup_1": time.Now().Add(10 * time.Hour),
		"692_startup_2": time.Now().Add(8 * time.Hour),
		"692_startup_3": time.Now().Add(5 * time.Hour),
		"692_expired":   time.Now().Add(-2 * time.Hour),
	}

	for eventID, dieTime := range activeEvents {
		s.mockCacheTable.addEntry(eventID, dieTime)
	}

	// Act - инициализируем cache из БД
	err := s.deduplicator.InitializeCache(s.ctx)
	require.NoError(s.T(), err)

	// Assert - проверяем что активные события загружены в hot cache
	stats := s.deduplicator.GetStats()
	assert.Equal(s.T(), int64(3), stats.HotCacheSize, "Should load 3 active events to hot cache")

	// Проверяем что загруженные события считаются дубликатами
	isDuplicate1, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, "692_startup_1")
	require.NoError(s.T(), err)
	assert.True(s.T(), isDuplicate1, "Loaded event should be duplicate")

	// Проверяем что истекшее событие не загрузилось
	isDuplicateExpired, err := deduplib.IsDuplicateEvent(s.ctx, s.deduplicator, "692_expired")
	require.NoError(s.T(), err)
	assert.False(s.T(), isDuplicateExpired, "Expired event should not be loaded")

	// Последняя проверка должна быть hit в hot cache (не cold cache)
	finalStats := s.deduplicator.GetStats()
	assert.Equal(s.T(), int64(1), finalStats.HotCacheHits, "Should hit hot cache for loaded event")
}

// mockCacheTable - упрощенная версия для интеграционных тестов
type mockCacheTable struct {
	entries                 map[string]time.Time
	shouldFailCreate        bool
	shouldFailExists        bool
	shouldFailLoadActive    bool
	shouldFailDeleteExpired bool
}

func newMockCacheTable() *mockCacheTable {
	return &mockCacheTable{
		entries: make(map[string]time.Time),
	}
}

func (m *mockCacheTable) Create(ctx context.Context, eventID string, dieTime time.Time) error {
	if m.shouldFailCreate {
		return assert.AnError
	}
	m.entries[eventID] = dieTime
	return nil
}

func (m *mockCacheTable) Exists(ctx context.Context, eventID string) (bool, error) {
	if m.shouldFailExists {
		return false, assert.AnError
	}
	dieTime, exists := m.entries[eventID]
	if !exists {
		return false, nil
	}
	return time.Now().Before(dieTime), nil
}

func (m *mockCacheTable) LoadActive(ctx context.Context, batchSize int) ([]deduplicator.CacheEntry, error) {
	if m.shouldFailLoadActive {
		return nil, assert.AnError
	}

	now := time.Now()
	var result []deduplicator.CacheEntry

	for eventID, dieTime := range m.entries {
		if now.Before(dieTime) {
			result = append(result, deduplicator.CacheEntry{
				EventID:   eventID,
				DieTime:   dieTime,
				CreatedAt: now.Add(-time.Hour),
			})
		}
	}

	return result, nil
}

func (m *mockCacheTable) DeleteExpired(ctx context.Context, windowStart, windowEnd time.Time) (int64, error) {
	if m.shouldFailDeleteExpired {
		return 0, assert.AnError
	}

	deletedCount := int64(0)
	now := time.Now()

	for eventID, dieTime := range m.entries {
		if now.After(dieTime) && dieTime.After(windowStart) && dieTime.Before(windowEnd) {
			delete(m.entries, eventID)
			deletedCount++
		}
	}

	return deletedCount, nil
}

func (m *mockCacheTable) addEntry(eventID string, dieTime time.Time) {
	m.entries[eventID] = dieTime
}

func (m *mockCacheTable) setFailure(operation string, shouldFail bool) {
	switch operation {
	case "create":
		m.shouldFailCreate = shouldFail
	case "exists":
		m.shouldFailExists = shouldFail
	case "loadActive":
		m.shouldFailLoadActive = shouldFail
	case "deleteExpired":
		m.shouldFailDeleteExpired = shouldFail
	}
}

func TestDeduplicationIntegrationSuite(t *testing.T) {
	suite.Run(t, new(DeduplicationIntegrationSuite))
}
