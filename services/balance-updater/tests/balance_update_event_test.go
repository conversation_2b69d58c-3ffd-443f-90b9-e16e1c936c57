package tests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

func TestBalanceUpdateEvent_IsDebit(t *testing.T) {
	tests := []struct {
		name     string
		incomFL  string
		expected bool
	}{
		{
			name:     "Debit operation - incomfl = 0",
			incomFL:  "0",
			expected: true,
		},
		{
			name:     "Credit operation - incomfl = 1",
			incomFL:  "1",
			expected: false,
		},
		{
			name:     "Unknown operation - empty string",
			incomFL:  "",
			expected: false,
		},
		{
			name:     "Unknown operation - invalid value",
			incomFL:  "2",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				IncomFL: tt.incomFL,
			}

			result := event.IsDebit()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestBalanceUpdateEvent_IsCredit(t *testing.T) {
	tests := []struct {
		name     string
		incomFL  string
		expected bool
	}{
		{
			name:     "Credit operation - incomfl = 1",
			incomFL:  "1",
			expected: true,
		},
		{
			name:     "Debit operation - incomfl = 0",
			incomFL:  "0",
			expected: false,
		},
		{
			name:     "Unknown operation - empty string",
			incomFL:  "",
			expected: false,
		},
		{
			name:     "Unknown operation - invalid value",
			incomFL:  "2",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				IncomFL: tt.incomFL,
			}

			result := event.IsCredit()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestBalanceUpdateEvent_GetOperationType(t *testing.T) {
	tests := []struct {
		name     string
		incomFL  string
		expected string
	}{
		{
			name:     "Debit operation",
			incomFL:  "0",
			expected: "debit",
		},
		{
			name:     "Credit operation",
			incomFL:  "1",
			expected: "credit",
		},
		{
			name:     "Unknown operation - empty",
			incomFL:  "",
			expected: "unknown",
		},
		{
			name:     "Unknown operation - invalid value",
			incomFL:  "5",
			expected: "unknown",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				IncomFL: tt.incomFL,
			}

			result := event.GetOperationType()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestBalanceUpdateEvent_ParseDocDate(t *testing.T) {
	tests := []struct {
		name        string
		docDate     string
		expectErr   bool
		expected    time.Time
		errContains string
	}{
		{
			name:      "Valid date - mm.dd.yyyy format",
			docDate:   "12.25.2023",
			expectErr: false,
			expected:  time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC),
		},
		{
			name:        "Invalid date - wrong format",
			docDate:     "invalid-date",
			expectErr:   true,
			errContains: "не удалось распарсить дату документа",
		},
		{
			name:        "Invalid date - empty string",
			docDate:     "",
			expectErr:   true,
			errContains: "не удалось распарсить дату документа",
		},
		{
			name:        "Invalid date - partial date",
			docDate:     "12.25",
			expectErr:   true,
			errContains: "не удалось распарсить дату документа",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				DocDate: tt.docDate,
			}

			result, err := event.ParseDocDate()

			if tt.expectErr {
				require.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestBalanceUpdateEvent_ParseSendDate(t *testing.T) {
	tests := []struct {
		name        string
		sendDate    string
		expectErr   bool
		expected    time.Time
		errContains string
	}{
		{
			name:      "Valid datetime - dd.mm.yyyy hh:mm:ss format",
			sendDate:  "25.12.2023 15:30:45",
			expectErr: false,
			expected:  time.Date(2023, 12, 25, 15, 30, 45, 0, time.UTC),
		},
		{
			name:      "Empty send date - should return zero time",
			sendDate:  "",
			expectErr: false,
			expected:  time.Time{},
		},
		{
			name:        "Invalid datetime - wrong format",
			sendDate:    "invalid-datetime",
			expectErr:   true,
			errContains: "не удалось распарсить дату отправки",
		},
		{
			name:      "Invalid datetime - partial datetime",
			sendDate:  "25.12.2023",
			expectErr: false,                                         // utils.ParseDate поддерживает этот формат
			expected:  time.Date(2023, 12, 25, 0, 0, 0, 0, time.UTC), // DD.MM.YYYY формат
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				SendDate: tt.sendDate,
			}

			result, err := event.ParseSendDate()

			if tt.expectErr {
				require.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestBalanceUpdateEvent_JSON_Serialization(t *testing.T) {
	originalEvent := &message.BalanceUpdateEvent{
		DepID:    692,
		ID:       1131433,
		IncomFL:  "1",
		AccCode:  "KZ51896KZT0522045417",
		SDok:     333000.50,
		ValCode:  "KZT",
		DocDate:  "12.26.2023",
		SendDate: "26.12.2023 10:15:30",
		OperCode: "PO8",
		TxtDscr:  "Расчеты по карточкам",
	}

	jsonData, err := originalEvent.ToJSON()
	require.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	var restoredEvent message.BalanceUpdateEvent
	err = restoredEvent.FromJSON(jsonData)
	require.NoError(t, err)

	assert.Equal(t, originalEvent.DepID, restoredEvent.DepID)
	assert.Equal(t, originalEvent.ID, restoredEvent.ID)
	assert.Equal(t, originalEvent.IncomFL, restoredEvent.IncomFL)
	assert.Equal(t, originalEvent.AccCode, restoredEvent.AccCode)
	assert.Equal(t, originalEvent.SDok, restoredEvent.SDok)
	assert.Equal(t, originalEvent.ValCode, restoredEvent.ValCode)
	assert.Equal(t, originalEvent.DocDate, restoredEvent.DocDate)
	assert.Equal(t, originalEvent.SendDate, restoredEvent.SendDate)
	assert.Equal(t, originalEvent.OperCode, restoredEvent.OperCode)
	assert.Equal(t, originalEvent.TxtDscr, restoredEvent.TxtDscr)
}

func TestBalanceUpdateEvent_NewFields(t *testing.T) {
	tests := []struct {
		name     string
		operCode string
		txtDscr  string
	}{
		{
			name:     "With oper_code and txt_dscr",
			operCode: "PO8",
			txtDscr:  "Расчеты по карточкам",
		},
		{
			name:     "With numeric oper_code",
			operCode: "92382983",
			txtDscr:  "Карточная операция",
		},
		{
			name:     "Empty fields",
			operCode: "",
			txtDscr:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				OperCode: tt.operCode,
				TxtDscr:  tt.txtDscr,
			}

			assert.Equal(t, tt.operCode, event.OperCode)
			assert.Equal(t, tt.txtDscr, event.TxtDscr)
		})
	}
}
