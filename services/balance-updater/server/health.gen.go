// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package server

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/entity"

	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/balance-updater"
)

func (s *Server) HealthCheck(ctx context.Context, req *pb.HealthCheckReq) (*pb.HealthCheckResp, error) {
	health, err := s.useCase.HealthCheck(ctx)
	if err != nil {
		return nil, err
	}
	return entity.MakeHealthEntityToPb(health), nil
}

func (s *Server) Check(ctx context.Context, _ *grpc_health_v1.HealthCheckRequest) (*grpc_health_v1.HealthCheckResponse, error) {
	check, err := s.useCase.HealthCheck(ctx)
	if err != nil {
		return nil, err
	}

	return entity.MakeCheckEntityToPb(check), nil
}

func (s *Server) Watch(_ *grpc_health_v1.HealthCheckRequest, _ grpc.ServerStreamingServer[grpc_health_v1.HealthCheckResponse]) error {
	// forward compatibility method
	return nil
}

func (s *Server) List(ctx context.Context, _ *grpc_health_v1.HealthListRequest) (*grpc_health_v1.HealthListResponse, error) {
	check, err := s.useCase.HealthCheck(ctx)
	if err != nil {
		return nil, err
	}

	return entity.MakeListEntityToPb(check), nil
}
