// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package server

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	balanceupdater "git.redmadrobot.com/zaman/backend/zaman/config/services/balance-updater"

	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/usecase"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/balance-updater"
)

type Server struct {
	pb.UnimplementedBalanceupdaterServer

	cfg     *balanceupdater.Config
	useCase usecase.BalanceUpdater
}

func NewServerOptions(useCases usecase.BalanceUpdater, cfg *balanceupdater.Config) *Server {
	return &Server{
		cfg:     cfg,
		useCase: useCases,
	}
}

func (s *Server) NewServer(cfg *grpcx.Config) (*grpc.Server, error) {
	options, err := grpcx.SetOptions(cfg)
	if err != nil {
		return nil, err
	}

	allOptions := []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(grpcx.SentryInterceptor),
	}
	allOptions = append(allOptions, options...)
	srv := grpc.NewServer(allOptions...)

	grpc_health_v1.RegisterHealthServer(srv, s)
	pb.RegisterBalanceupdaterServer(srv, s)

	return srv, nil
}
