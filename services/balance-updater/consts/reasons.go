package consts

type Reason struct {
	Code string
	Name string
}

type DropReasonsStruct struct {
	EmptyEvent                       Reason
	AmountIsZero                     Reason
	IDIsZero                         Reason
	IncomflEmpty                     Reason
	AccCodeEmpty                     Reason
	CurrencyEmpty                    Reason
	DocDateEmpty                     Reason
	PetitionNumberPresent            Reason // Причина для petitionNumber
	OperCode020381ExcludedByDesc     Reason
	OperCode112013Excluded           Reason
	OperCode000000Excluded           Reason
	OperCode105202ExcludedByDesc     Reason
	OperCode105202ExcludedByPetition Reason
	TrustManagementExcluded          Reason // Исключение операций доверительного управления
	// Временные моки карточных операций
	OperCodeCardPurchaseExcluded   Reason
	OperCodeCardWithdrawalExcluded Reason
	OperCodeCardTransferExcluded   Reason
	UnknownOperationType           Reason
}

var DropReasons = DropReasonsStruct{
	EmptyEvent:                       Reason{"empty_event", "Empty event"},
	AmountIsZero:                     Reason{"amount_is_zero", "Amount is zero"},
	IDIsZero:                         Reason{"id_is_zero", "Transaction ID is zero"},
	IncomflEmpty:                     Reason{"incomfl_empty", "Operation type (incomfl) is empty"},
	AccCodeEmpty:                     Reason{"acc_code_empty", "Account code is empty"},
	CurrencyEmpty:                    Reason{"currency_empty", "Currency is empty"},
	DocDateEmpty:                     Reason{"doc_date_empty", "Document date is empty"},
	PetitionNumberPresent:            Reason{"petition_number_present", "Petition number present - always excluded"},
	OperCode020381ExcludedByDesc:     Reason{"oper_code_020381_excluded_by_description", "Excluded by description for 020381"},
	OperCode112013Excluded:           Reason{"oper_code_112013_excluded", "Excluded by oper code 112013"},
	OperCode000000Excluded:           Reason{"oper_code_000000_excluded", "Excluded by oper code 000000"},
	OperCode105202ExcludedByDesc:     Reason{"oper_code_105202_excluded_by_description", "Excluded by description for 105202"},
	OperCode105202ExcludedByPetition: Reason{"oper_code_105202_excluded_by_petition", "Excluded by petition number for 105202"},
	TrustManagementExcluded:          Reason{"trust_management_excluded", "Excluded trust management operation by description"},
	// Временные моки карточных операций
	OperCodeCardPurchaseExcluded:   Reason{"oper_code_card_purchase_excluded", "Excluded card purchase operation (mock)"},
	OperCodeCardWithdrawalExcluded: Reason{"oper_code_card_withdrawal_excluded", "Excluded card withdrawal operation (mock)"},
	OperCodeCardTransferExcluded:   Reason{"oper_code_card_transfer_excluded", "Excluded card transfer operation (mock)"},
	UnknownOperationType:           Reason{"unknown_operation_type", "Unknown operation type"},
}

func (s DropReasonsStruct) ByCode(code string) (Reason, bool) {
	switch code {
	case s.EmptyEvent.Code:
		return s.EmptyEvent, true
	case s.AmountIsZero.Code:
		return s.AmountIsZero, true
	case s.IDIsZero.Code:
		return s.IDIsZero, true
	case s.IncomflEmpty.Code:
		return s.IncomflEmpty, true
	case s.AccCodeEmpty.Code:
		return s.AccCodeEmpty, true
	case s.CurrencyEmpty.Code:
		return s.CurrencyEmpty, true
	case s.DocDateEmpty.Code:
		return s.DocDateEmpty, true
	case s.PetitionNumberPresent.Code:
		return s.PetitionNumberPresent, true
	case s.OperCode020381ExcludedByDesc.Code:
		return s.OperCode020381ExcludedByDesc, true
	case s.OperCode112013Excluded.Code:
		return s.OperCode112013Excluded, true
	case s.OperCode000000Excluded.Code:
		return s.OperCode000000Excluded, true
	case s.OperCode105202ExcludedByDesc.Code:
		return s.OperCode105202ExcludedByDesc, true
	case s.OperCode105202ExcludedByPetition.Code:
		return s.OperCode105202ExcludedByPetition, true
	case s.TrustManagementExcluded.Code:
		return s.TrustManagementExcluded, true
	case s.OperCodeCardPurchaseExcluded.Code:
		return s.OperCodeCardPurchaseExcluded, true
	case s.OperCodeCardWithdrawalExcluded.Code:
		return s.OperCodeCardWithdrawalExcluded, true
	case s.OperCodeCardTransferExcluded.Code:
		return s.OperCodeCardTransferExcluded, true
	case s.UnknownOperationType.Code:
		return s.UnknownOperationType, true
	default:
		return Reason{}, false
	}
}
