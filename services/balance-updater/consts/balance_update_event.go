package consts

type (
	OperCode string
	DescTag  string
	ErrText  string
)

const (
	OperCode020381 OperCode = "020381"
	OperCode112013 OperCode = "112013"
	OperCode000000 OperCode = "000000"
	OperCode105202 OperCode = "105202"
	// Временные моки карточных операций (до получения реальных кодов)
	OperCodeCardPurchase   OperCode = "CARD01" // мок: покупка по карте
	OperCodeCardWithdrawal OperCode = "CARD02" // мок: снятие с карты
	OperCodeCardTransfer   OperCode = "CARD03" // мок: карточный перевод
)

const (
	DescInternalTransferByPhone DescTag = "Внутренний перевод по номеру телефона"
	DescPaymentByKaspi          DescTag = "PAYMENT_BY_KASPI"
	DescMobileServices          DescTag = "Оплата за мобильную связь"
	DescWshdFull                DescTag = "WSHD_FULL"
	DescWshdDecEqual            DescTag = "WSHD_DEC_EQUAL"
	DescOdOnly                  DescTag = "ODONLY"
	DescTrustManagementPrefix   DescTag = "Перевод денег в доверительное управление по договору"
)

var ColvirErrKafkaMsg = struct {
	DuplicateRequest    string
	ValidationFailed    string
	CustomerContract    string
	NotFound            string
	DebitEdgeCaseD      string
	DebitEdgeCaseG      string
	CreditEdgeCaseF     string
	DeadlineExceeded    string
	Timeout             string
	InternalServerError string
}{
	DuplicateRequest:    "duplicate request",
	ValidationFailed:    "validation failed",
	CustomerContract:    "customer contract",
	NotFound:            "not found",
	DebitEdgeCaseD:      "error code d",
	DebitEdgeCaseG:      "error code g",
	CreditEdgeCaseF:     "error code f",
	DeadlineExceeded:    "deadline exceeded",
	Timeout:             "timeout",
	InternalServerError: "internal server error",
}

// ExternalIDPrefix префикс для ExternalID в balance-updater
const ExternalIDPrefix = "37"

const (
	NullValue = "NULL"
)
