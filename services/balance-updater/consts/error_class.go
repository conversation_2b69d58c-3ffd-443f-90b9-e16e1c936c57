package consts

// ErrorClass is a normalized class of processing error used for retry policy & metrics
type ErrorClass string

const (
	ErrDuplicateRequest       ErrorClass = "duplicate_request"
	ErrValidationFailed       ErrorClass = "validation_failed"
	ErrCustomerContractAbsent ErrorClass = "customer_contract_not_found"
	ErrDebitEdgeCase          ErrorClass = "debit_edge_case"  // d,g errors from PC
	ErrCreditEdgeCase         ErrorClass = "credit_edge_case" // f errors from PC
	ErrDeadlineExceeded       ErrorClass = "deadline_exceeded"
	ErrTimeout                ErrorClass = "timeout"
	ErrInternalServerError    ErrorClass = "internal_server_error"
	ErrOther                  ErrorClass = "other"
)
