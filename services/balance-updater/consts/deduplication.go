package consts

import "time"

const (
	// DeduplicationTTL - время жизни записи в кеше дедупликации (24 часа)
	DeduplicationTTL = 24 * time.Hour

	// CleanupInterval - интервал запуска фонового worker'а для очистки expired записей
	// Каждые 5 минут удаляем expired записи из hot cache и БД
	CleanupInterval = 5 * time.Minute

	// CleanupWindow - окно поиска expired записей в БД (±1 день от текущего времени)
	// SQL готовит выборку в этом окне для оптимизации cleanup процесса
	CleanupWindow = 24 * time.Hour

	// MaxHotCacheSize - максимальный размер hot cache в памяти
	// При превышении начинаем принудительную очистку expired записей
	// ВАЖНО: Нужно увеличение лимитов памяти пода в k8s!
	MaxHotCacheSize = 100000

	// InitialCacheLoadBatchSize - размер batch'а при загрузке cache из БД при старте
	// Загружаем записи пакетами для избежания блокировки БД
	InitialCacheLoadBatchSize = 5000
)

// HotCacheEntry представляет запись в hot cache
type HotCacheEntry struct {
	DieTime time.Time
}
