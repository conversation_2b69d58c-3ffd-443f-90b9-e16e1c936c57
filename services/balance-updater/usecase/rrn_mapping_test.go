package usecase

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

func TestGenerateRRN_SimplifiedLogic(t *testing.T) {
	tests := []struct {
		name        string
		event       *message.BalanceUpdateEvent
		expectedRRN string
		description string
	}{
		{
			name: "Simple event - should use event.ID",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************", // игнорируется в новой логике
				ColvirReferenceID: "ref123",       // игнорируется в новой логике
				IncomFL:           consts.BalanceOperationDebit.String(),
			},
			expectedRRN: "12345",
			description: "DocumentNumber и ColvirReferenceID игнорируются, используется только event.ID",
		},
		{
			name: "Event with empty fields - should use event.ID",
			event: &message.BalanceUpdateEvent{
				ID:                54321,
				DocumentNumber:    "", // игнорируется
				ColvirReferenceID: "", // игнорируется
				IncomFL:           consts.BalanceOperationCredit.String(),
			},
			expectedRRN: "54321",
			description: "Пустые поля игнорируются, используется только event.ID",
		},
		{
			name: "Event with UUID DocumentNumber - should use event.ID",
			event: &message.BalanceUpdateEvent{
				ID:                67890,
				DocumentNumber:    "550e8400-e29b-41d4-a716-************", // игнорируется
				ColvirReferenceID: "colvir_ref_789",                       // игнорируется
				IncomFL:           consts.BalanceOperationDebit.String(),
			},
			expectedRRN: "67890",
			description: "UUID формат DocumentNumber игнорируется, используется только event.ID",
		},
		{
			name: "Large event.ID - should work correctly",
			event: &message.BalanceUpdateEvent{
				ID:                1234567890,
				DocumentNumber:    "various_data", // игнорируется
				ColvirReferenceID: "more_data",    // игнорируется
				IncomFL:           consts.BalanceOperationCredit.String(),
			},
			expectedRRN: "1234567890",
			description: "Большие ID обрабатываются корректно",
		},
		{
			name: "Zero event.ID - should work correctly",
			event: &message.BalanceUpdateEvent{
				ID:                0,
				DocumentNumber:    "some_document", // игнорируется
				ColvirReferenceID: "some_ref",      // игнорируется
				IncomFL:           consts.BalanceOperationDebit.String(),
			},
			expectedRRN: "0",
			description: "Нулевой ID обрабатывается корректно",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Logf("Testing: %s", tt.description)

			// Act
			rrn := generateRRN(tt.event)

			// Assert
			assert.Equal(t, tt.expectedRRN, rrn, "RRN should always be event.ID")
		})
	}
}

func TestGenerateExternalID_UnitTests(t *testing.T) {
	tests := []struct {
		name           string
		event          *message.BalanceUpdateEvent
		expectedPrefix string
		expectedRRN    string
		expectError    bool
		description    string
	}{
		{
			name: "Debit operation - should use event.ID as RRN",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************", // игнорируется
				ColvirReferenceID: "ref123",       // игнорируется
				IncomFL:           consts.BalanceOperationDebit.String(),
			},
			expectedPrefix: "D",
			expectedRRN:    "12345",
			expectError:    false,
			description:    "Операция списания должна генерировать префикс D + event.ID",
		},
		{
			name: "Credit operation - should use event.ID as RRN",
			event: &message.BalanceUpdateEvent{
				ID:                54321,
				DocumentNumber:    "",               // игнорируется
				ColvirReferenceID: "colvir_ref_456", // игнорируется
				IncomFL:           consts.BalanceOperationCredit.String(),
			},
			expectedPrefix: "C",
			expectedRRN:    "54321",
			expectError:    false,
			description:    "Операция зачисления должна генерировать префикс C + event.ID",
		},
		{
			name: "Invalid operation type - should return error",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************",
				ColvirReferenceID: "",
				IncomFL:           "INVALID", // неопределенный тип
			},
			expectedPrefix: "",
			expectedRRN:    "",
			expectError:    true,
			description:    "Неопределенный тип операции должен возвращать ошибку",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Logf("Testing: %s", tt.description)

			// Act
			externalID, err := generateExternalID(tt.event)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, externalID)
				assert.Contains(t, err.Error(), "неопределенный тип операции")
			} else {
				require.NoError(t, err)
				expectedExternalID := tt.expectedPrefix + tt.expectedRRN
				assert.Equal(t, expectedExternalID, externalID)

				// Дополнительные проверки
				assert.NotEmpty(t, externalID, "ExternalID should not be empty")
				assert.True(t, len(externalID) > 1, "ExternalID should have prefix + RRN")
				assert.Equal(t, tt.expectedPrefix, string(externalID[0]), "Should have correct prefix")

				// Проверяем что RRN часть правильная (всегда должен быть event.ID)
				actualRRN := externalID[1:]
				assert.Equal(t, tt.expectedRRN, actualRRN, "Should have correct RRN part")
			}
		})
	}
}

func TestGenerateExternalID_DebitVsCredit(t *testing.T) {
	// Базовое событие для сравнения
	baseEvent := &message.BalanceUpdateEvent{
		ID:                98765,
		DocumentNumber:    "any_document", // игнорируется
		ColvirReferenceID: "any_ref",      // игнорируется
		AccCode:           "KZ************3456",
	}

	// Тест для списания (Debit)
	debitEvent := *baseEvent
	debitEvent.IncomFL = consts.BalanceOperationDebit.String()

	debitExternalID, err := generateExternalID(&debitEvent)
	require.NoError(t, err)

	// Тест для зачисления (Credit)
	creditEvent := *baseEvent
	creditEvent.IncomFL = consts.BalanceOperationCredit.String()

	creditExternalID, err := generateExternalID(&creditEvent)
	require.NoError(t, err)

	// Проверяем что ExternalID разные
	assert.NotEqual(t, debitExternalID, creditExternalID, "Debit and Credit should have different ExternalIDs")

	// Проверяем префиксы
	assert.True(t, debitExternalID[0] == 'D', "Debit should start with D")
	assert.True(t, creditExternalID[0] == 'C', "Credit should start with C")

	// Проверяем что RRN части одинаковые (должны быть event.ID)
	debitRRN := debitExternalID[1:]
	creditRRN := creditExternalID[1:]
	assert.Equal(t, debitRRN, creditRRN, "RRN parts should be the same")
	assert.Equal(t, "98765", debitRRN, "Should use event.ID as RRN")
}

func TestGenerateExternalID_EdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		event       *message.BalanceUpdateEvent
		expectError bool
		errorMsg    string
	}{
		{
			name: "Empty IncomFL - should error",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************",
				ColvirReferenceID: "",
				IncomFL:           "", // пустой
			},
			expectError: true,
			errorMsg:    "неопределенный тип операции",
		},
		{
			name: "Unknown IncomFL value - should error",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************",
				ColvirReferenceID: "",
				IncomFL:           "2", // неизвестное значение
			},
			expectError: true,
			errorMsg:    "неопределенный тип операции",
		},
		{
			name: "Whitespace only IncomFL - should error",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************",
				ColvirReferenceID: "",
				IncomFL:           "   ", // только пробелы
			},
			expectError: true,
			errorMsg:    "неопределенный тип операции",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Act
			externalID, err := generateExternalID(tt.event)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, externalID)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, externalID)
			}
		})
	}
}
