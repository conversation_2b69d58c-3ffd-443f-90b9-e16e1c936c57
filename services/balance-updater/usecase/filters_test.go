package usecase

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/consts"
	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

func TestShouldDropEvent_PetitionNumberPresent_Priority(t *testing.T) {
	// Arrange - событие с petitionNumber (должно отбрасываться ПЕРВЫМ)
	petitionValue := "petition_123"
	event := &message.BalanceUpdateEvent{
		ID:             12345,          // валидный ID
		SDok:           100.0,          // валидная сумма
		IncomFL:        "0",            // валидный тип операции
		AccCode:        "KZ123456789",  // валидный счет
		ValCode:        "KZT",          // валидная валюта
		DocDate:        "01.01.2023",   // валидная дата
		PetitionNumber: &petitionValue, // ЕСТЬ petitionNumber
		OperCode:       "112013",       // даже если это исключаемый код операции
	}

	// Act
	shouldDrop, reasonCode := shouldDropEvent(event)

	// Assert
	assert.True(t, shouldDrop, "Should drop event with petitionNumber")
	assert.Equal(t, consts.DropReasons.PetitionNumberPresent.Code, reasonCode)
}

func TestShouldDropEvent_PetitionNumberEmpty_ShouldNotDrop(t *testing.T) {
	tests := []struct {
		name           string
		petitionNumber *string
	}{
		{"nil petitionNumber", nil},
		{"empty string petitionNumber", strPtr("")},
		{"whitespace only petitionNumber", strPtr("   ")},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				ID:             12345,
				SDok:           100.0,
				IncomFL:        "0",
				AccCode:        "KZ123456789",
				ValCode:        "KZT",
				DocDate:        "01.01.2023",
				PetitionNumber: tt.petitionNumber,
				OperCode:       "999999", // валидный код операции
			}

			shouldDrop, reasonCode := shouldDropEvent(event)

			// Не должно отбрасываться по petitionNumber
			assert.False(t, shouldDrop, "Should not drop event with empty/nil petitionNumber")
			assert.NotEqual(t, consts.DropReasons.PetitionNumberPresent.Code, reasonCode)
		})
	}
}

func TestShouldDropEvent_OtherFilters_AfterPetitionNumber(t *testing.T) {
	// Проверяем что другие фильтры работают только если petitionNumber пустой
	tests := []struct {
		name         string
		event        *message.BalanceUpdateEvent
		expectedDrop bool
		expectedCode string
	}{
		{
			name: "Amount zero",
			event: &message.BalanceUpdateEvent{
				ID:             12345,
				SDok:           0, // нулевая сумма
				IncomFL:        "0",
				AccCode:        "KZ123456789",
				ValCode:        "KZT",
				DocDate:        "01.01.2023",
				PetitionNumber: nil, // petitionNumber пустой
			},
			expectedDrop: true,
			expectedCode: consts.DropReasons.AmountIsZero.Code,
		},
		{
			name: "ID zero",
			event: &message.BalanceUpdateEvent{
				ID:             0, // нулевой ID
				SDok:           100.0,
				IncomFL:        "0",
				AccCode:        "KZ123456789",
				ValCode:        "KZT",
				DocDate:        "01.01.2023",
				PetitionNumber: nil,
			},
			expectedDrop: true,
			expectedCode: consts.DropReasons.IDIsZero.Code,
		},
		{
			name: "IncomFL empty",
			event: &message.BalanceUpdateEvent{
				ID:             12345,
				SDok:           100.0,
				IncomFL:        "", // пустой тип операции
				AccCode:        "KZ123456789",
				ValCode:        "KZT",
				DocDate:        "01.01.2023",
				PetitionNumber: nil,
			},
			expectedDrop: true,
			expectedCode: consts.DropReasons.IncomflEmpty.Code,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			shouldDrop, reasonCode := shouldDropEvent(tt.event)

			assert.Equal(t, tt.expectedDrop, shouldDrop)
			if tt.expectedDrop {
				assert.Equal(t, tt.expectedCode, reasonCode)
			}
		})
	}
}

func TestShouldDropEvent_ValidEvent_ShouldNotDrop(t *testing.T) {
	// Arrange - полностью валидное событие
	event := &message.BalanceUpdateEvent{
		ID:             12345,
		SDok:           100.0,
		IncomFL:        "0",
		AccCode:        "KZ123456789",
		ValCode:        "KZT",
		DocDate:        "01.01.2023",
		PetitionNumber: nil,
		OperCode:       "999999", // не исключаемый код
		Description:    "Regular transaction",
	}

	// Act
	shouldDrop, reasonCode := shouldDropEvent(event)

	// Assert
	assert.False(t, shouldDrop, "Valid event should not be dropped")
	assert.Empty(t, reasonCode, "No reason code for valid event")
}

func TestShouldDropEvent_OperCode112013_ShouldDrop(t *testing.T) {
	// Arrange - событие с исключаемым кодом операции 112013
	event := &message.BalanceUpdateEvent{
		ID:             12345,
		SDok:           100.0,
		IncomFL:        "0",
		AccCode:        "KZ123456789",
		ValCode:        "KZT",
		DocDate:        "01.01.2023",
		PetitionNumber: nil,
		OperCode:       "112013", // исключаемый код
		Description:    "Deposit operation",
	}

	// Act
	shouldDrop, reasonCode := shouldDropEvent(event)

	// Assert
	assert.True(t, shouldDrop, "Should drop event with oper_code 112013")
	assert.Equal(t, consts.DropReasons.OperCode112013Excluded.Code, reasonCode)
}

func TestShouldDropEvent_OperCode020381_ExcludedByDescription(t *testing.T) {
	// Тестируем фильтрацию по описанию для кода 020381
	tests := []struct {
		name         string
		description  string
		txtDscr      string
		expectedDrop bool
	}{
		{
			name:         "Mobile services payment in TxtDscr - should drop",
			description:  "Some other description",
			txtDscr:      "Оплата за мобильную связь",
			expectedDrop: true,
		},
		{
			name:         "Kaspi payment in TxtDscr - should drop",
			description:  "Some other description",
			txtDscr:      "PAYMENT_BY_KASPI",
			expectedDrop: true,
		},
		{
			name:         "Phone transfer in Description - should not drop (no longer checked)",
			description:  "Внутренний перевод по номеру телефона",
			txtDscr:      "",
			expectedDrop: false,
		},
		{
			name:         "Phone transfer in TxtDscr - should not drop (no longer checked)",
			description:  "",
			txtDscr:      "Внутренний перевод по номеру телефона",
			expectedDrop: false,
		},
		{
			name:         "Kaspi payment in Description - should not drop (only TxtDscr checked)",
			description:  "PAYMENT_BY_KASPI",
			txtDscr:      "",
			expectedDrop: false,
		},
		{
			name:         "Regular transfer 020381 - should not drop",
			description:  "Regular transfer operation",
			txtDscr:      "",
			expectedDrop: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				ID:             12345,
				SDok:           100.0,
				IncomFL:        "0",
				AccCode:        "KZ123456789",
				ValCode:        "KZT",
				DocDate:        "01.01.2023",
				PetitionNumber: nil,
				OperCode:       "020381",
				Description:    tt.description,
				TxtDscr:        tt.txtDscr,
			}

			shouldDrop, reasonCode := shouldDropEvent(event)

			assert.Equal(t, tt.expectedDrop, shouldDrop)
			if tt.expectedDrop {
				assert.Equal(t, consts.DropReasons.OperCode020381ExcludedByDesc.Code, reasonCode)
			}
		})
	}
}

// Вспомогательная функция для создания указателя на строку
func strPtr(s string) *string {
	return &s
}

func TestDropReasonByCode_PetitionNumberPresent(t *testing.T) {
	// Arrange
	code := consts.DropReasons.PetitionNumberPresent.Code

	// Act
	reason, found := consts.DropReasons.ByCode(code)

	// Assert
	assert.True(t, found, "Should find PetitionNumberPresent reason by code")
	assert.Equal(t, "petition_number_present", reason.Code)
	assert.Equal(t, "Petition number present - always excluded", reason.Name)
}

func TestComputeDropFlags_PetitionNumber(t *testing.T) {
	// Arrange
	petitionValue := "petition_123"
	event := &message.BalanceUpdateEvent{
		ID:             12345,
		SDok:           100.0,
		IncomFL:        "0",
		AccCode:        "KZ123456789",
		ValCode:        "KZT",
		DocDate:        "01.01.2023",
		PetitionNumber: &petitionValue,
		OperCode:       "999999",
	}

	// Act
	flags, matchedRule := computeDropFlags(event)

	// Assert
	// Проверяем что флаги корректно устанавливаются для валидного события с petitionNumber
	assert.False(t, flags.IsAmountZero, "Amount should not be zero")
	assert.False(t, flags.IsIDZero, "ID should not be zero")
	assert.False(t, flags.IsIncomflEmpty, "IncomFL should not be empty")
	assert.False(t, flags.IsAccCodeEmpty, "AccCode should not be empty")
	assert.False(t, flags.IsCurrencyEmpty, "Currency should not be empty")
	assert.False(t, flags.IsDocDateEmpty, "DocDate should not be empty")

	// Примечание: computeDropFlags не проверяет petitionNumber, это делает shouldDropEvent
	// matchedRule тоже может быть пустым для валидного события
	_ = matchedRule // используем переменную чтобы избежать предупреждения
}

func TestShouldDropEvent_TrustManagement_ShouldDrop(t *testing.T) {
	// Тестируем фильтрацию операций доверительного управления по описанию
	// ВАЖНО: фильтр срабатывает ТОЛЬКО если oper_code = NULL/пустое И oper_name = NULL/пустое
	tests := []struct {
		name         string
		operCode     string
		operName     string
		description  string
		txtDscr      string
		expectedDrop bool
	}{
		{
			name:         "Trust management with empty oper_code and oper_name - should drop",
			operCode:     "",
			operName:     "",
			description:  "Перевод денег в доверительное управление по договору №123456",
			txtDscr:      "",
			expectedDrop: true,
		},
		{
			name:         "Trust management with NULL oper_code and oper_name - should drop",
			operCode:     "NULL",
			operName:     "NULL",
			description:  "Перевод денег в доверительное управление по договору №789",
			txtDscr:      "",
			expectedDrop: true,
		},
		{
			name:         "Trust management from TxtDscr fallback - should drop",
			operCode:     "",
			operName:     "",
			description:  "",
			txtDscr:      "Перевод денег в доверительное управление по договору №789",
			expectedDrop: true,
		},
		{
			name:         "Trust management WITHOUT contract number - should drop",
			operCode:     "",
			operName:     "",
			description:  "Перевод денег в доверительное управление по договору",
			txtDscr:      "",
			expectedDrop: true,
		},
		{
			name:         "Trust management with NON-empty oper_code - should NOT drop",
			operCode:     "123456",
			operName:     "",
			description:  "Перевод денег в доверительное управление по договору №123456",
			txtDscr:      "",
			expectedDrop: false,
		},
		{
			name:         "Trust management with NON-empty oper_name - should NOT drop",
			operCode:     "",
			operName:     "Some Operation",
			description:  "Перевод денег в доверительное управление по договору №123456",
			txtDscr:      "",
			expectedDrop: false,
		},
		{
			name:         "Similar description with empty codes - should not drop",
			operCode:     "",
			operName:     "",
			description:  "Перевод денег по договору банковского обслуживания",
			txtDscr:      "",
			expectedDrop: false,
		},
		{
			name:         "Regular operation with empty codes - should not drop",
			operCode:     "",
			operName:     "",
			description:  "Обычная операция перевода",
			txtDscr:      "",
			expectedDrop: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			event := &message.BalanceUpdateEvent{
				ID:          12345,
				SDok:        100.0,
				IncomFL:     "0",
				AccCode:     "KZ123456789",
				ValCode:     "KZT",
				DocDate:     "01.01.2023",
				OperCode:    tt.operCode,
				OperName:    tt.operName,
				Description: tt.description,
				TxtDscr:     tt.txtDscr,
			}

			shouldDrop, reasonCode := shouldDropEvent(event)

			if tt.expectedDrop {
				assert.True(t, shouldDrop, "Should drop trust management operation when oper_code and oper_name are empty/NULL")
				assert.Equal(t, consts.DropReasons.TrustManagementExcluded.Code, reasonCode)
			} else {
				assert.False(t, shouldDrop, "Should not drop operation")
				assert.NotEqual(t, consts.DropReasons.TrustManagementExcluded.Code, reasonCode)
			}
		})
	}
}

func TestShouldDropEvent_TrustManagementVsOperCode112013_BothDrop(t *testing.T) {
	// Проверяем что старый фильтр (oper_code = 112013) и новый (описание) работают независимо

	// Событие с кодом 112013 (старый фильтр)
	eventWithOperCode := &message.BalanceUpdateEvent{
		ID:          12345,
		SDok:        100.0,
		IncomFL:     "0",
		AccCode:     "KZ123456789",
		ValCode:     "KZT",
		DocDate:     "01.01.2023",
		OperCode:    "112013",
		Description: "Обычная операция",
	}

	shouldDrop, reasonCode := shouldDropEvent(eventWithOperCode)
	assert.True(t, shouldDrop, "Should drop by oper_code 112013")
	assert.Equal(t, consts.DropReasons.OperCode112013Excluded.Code, reasonCode)

	// Событие с описанием доверительного управления (новый фильтр)
	// ВАЖНО: новый фильтр срабатывает только при пустых oper_code и oper_name
	eventWithDescription := &message.BalanceUpdateEvent{
		ID:          12345,
		SDok:        100.0,
		IncomFL:     "0",
		AccCode:     "KZ123456789",
		ValCode:     "KZT",
		DocDate:     "01.01.2023",
		OperCode:    "", // ПУСТОЙ oper_code
		OperName:    "", // ПУСТОЙ oper_name
		Description: "Перевод денег в доверительное управление по договору №555",
	}

	shouldDrop2, reasonCode2 := shouldDropEvent(eventWithDescription)
	assert.True(t, shouldDrop2, "Should drop by trust management description")
	assert.Equal(t, consts.DropReasons.TrustManagementExcluded.Code, reasonCode2)
}
