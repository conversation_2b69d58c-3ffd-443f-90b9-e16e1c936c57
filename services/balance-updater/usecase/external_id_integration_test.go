package usecase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/consts"
	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

func TestGenerateExternalID_SimplifiedLogic(t *testing.T) {
	tests := []struct {
		name           string
		event          *message.BalanceUpdateEvent
		expectedPrefix string
		expectedRRN    string
		expectError    bool
	}{
		{
			name: "Debit operation - should use event.ID as RRN",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************", // игнорируется в новой логике
				ColvirReferenceID: "ref123",       // игнорируется в новой логике
				IncomFL:           consts.BalanceOperationDebit.String(),
			},
			expectedPrefix: "D",
			expectedRRN:    "12345", // всегда event.ID
			expectError:    false,
		},
		{
			name: "Credit operation - should use event.ID as RRN",
			event: &message.BalanceUpdateEvent{
				ID:                54321,
				DocumentNumber:    "",               // игнорируется в новой логике
				ColvirReferenceID: "colvir_ref_456", // игнорируется в новой логике
				IncomFL:           consts.BalanceOperationCredit.String(),
			},
			expectedPrefix: "C",
			expectedRRN:    "54321", // всегда event.ID
			expectError:    false,
		},
		{
			name: "Large event.ID - should work correctly",
			event: &message.BalanceUpdateEvent{
				ID:                1234567890,
				DocumentNumber:    "550e8400-e29b-41d4-a716-446655440000", // игнорируется
				ColvirReferenceID: "colvir_ref_789",                       // игнорируется
				IncomFL:           consts.BalanceOperationDebit.String(),
			},
			expectedPrefix: "D",
			expectedRRN:    "1234567890", // всегда event.ID
			expectError:    false,
		},
		{
			name: "Zero event.ID - should work correctly",
			event: &message.BalanceUpdateEvent{
				ID:                0,
				DocumentNumber:    "123e4567-e89b-12d3-a456-426614174000", // игнорируется
				ColvirReferenceID: "",                                     // игнорируется
				IncomFL:           consts.BalanceOperationCredit.String(),
			},
			expectedPrefix: "C",
			expectedRRN:    "0", // всегда event.ID
			expectError:    false,
		},
		{
			name: "Minimal data - should work correctly",
			event: &message.BalanceUpdateEvent{
				ID:                99999,
				DocumentNumber:    "", // игнорируется
				ColvirReferenceID: "", // игнорируется
				IncomFL:           consts.BalanceOperationDebit.String(),
			},
			expectedPrefix: "D",
			expectedRRN:    "99999", // всегда event.ID
			expectError:    false,
		},
		{
			name: "Invalid operation type - should return error",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************",
				ColvirReferenceID: "",
				IncomFL:           "INVALID", // неопределенный тип
			},
			expectedPrefix: "",
			expectedRRN:    "",
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Act
			externalID, err := generateExternalID(tt.event)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, externalID)
				assert.Contains(t, err.Error(), "неопределенный тип операции")
			} else {
				require.NoError(t, err)
				expectedExternalID := tt.expectedPrefix + tt.expectedRRN
				assert.Equal(t, expectedExternalID, externalID)

				// Проверяем что ExternalID не пустой
				assert.NotEmpty(t, externalID)

				// Проверяем что начинается с правильного префикса
				assert.True(t, len(externalID) > 1, "ExternalID should have prefix + RRN")
				assert.Equal(t, tt.expectedPrefix, string(externalID[0]), "Should have correct prefix")

				// Проверяем что RRN часть правильная (всегда должен быть event.ID)
				actualRRN := externalID[1:]
				assert.Equal(t, tt.expectedRRN, actualRRN, "Should have correct RRN part")
			}
		})
	}
}

func TestExternalID_DebitVsCredit_SameEvent(t *testing.T) {
	// Базовое событие
	baseEvent := &message.BalanceUpdateEvent{
		ID:                12345,
		DocumentNumber:    "************",
		ColvirReferenceID: "",
		AccCode:           "KZ************3456",
	}

	// Тест для списания (Debit)
	debitEvent := *baseEvent
	debitEvent.IncomFL = consts.BalanceOperationDebit.String()

	debitExternalID, err := generateExternalID(&debitEvent)
	require.NoError(t, err)

	// Тест для зачисления (Credit)
	creditEvent := *baseEvent
	creditEvent.IncomFL = consts.BalanceOperationCredit.String()

	creditExternalID, err := generateExternalID(&creditEvent)
	require.NoError(t, err)

	// Проверяем что ExternalID разные
	assert.NotEqual(t, debitExternalID, creditExternalID, "Debit and Credit should have different ExternalIDs")

	// Проверяем префиксы
	assert.True(t, debitExternalID[0] == 'D', "Debit should start with D")
	assert.True(t, creditExternalID[0] == 'C', "Credit should start with C")

	// Проверяем что RRN части одинаковые
	debitRRN := debitExternalID[1:]
	creditRRN := creditExternalID[1:]
	assert.Equal(t, debitRRN, creditRRN, "RRN parts should be the same")
	assert.Equal(t, "12345", debitRRN, "Should use event.ID as RRN")
}

func TestExternalID_RealWorldScenarios(t *testing.T) {
	tests := []struct {
		name        string
		description string
		event       *message.BalanceUpdateEvent
		expectedRRN string
		expectedID  string
	}{
		{
			name:        "Phone transfer scenario",
			description: "Перевод по номеру телефона - теперь использует только event.ID",
			event: &message.BalanceUpdateEvent{
				ID:                987654,
				DocumentNumber:    "a1b2c3d4-e5f6-7890-abcd-ef1234567890", // игнорируется
				ColvirReferenceID: "phone_transfer_ref_123",               // игнорируется
				IncomFL:           consts.BalanceOperationDebit.String(),
				AccCode:           "KZ************3456",
				OperCode:          "020381",
			},
			expectedRRN: "987654", // всегда event.ID
			expectedID:  "D987654",
		},
		{
			name:        "Legacy operation with only ID",
			description: "Старая операция только с ID - логика не изменилась",
			event: &message.BalanceUpdateEvent{
				ID:                555666,
				DocumentNumber:    "",
				ColvirReferenceID: "",
				IncomFL:           consts.BalanceOperationCredit.String(),
				AccCode:           "KZ9876543210987654",
				OperCode:          "999999",
			},
			expectedRRN: "555666", // всегда event.ID
			expectedID:  "C555666",
		},
		{
			name:        "Standard bank operation",
			description: "Стандартная банковская операция - теперь использует только event.ID",
			event: &message.BalanceUpdateEvent{
				ID:                777888,
				DocumentNumber:    "************", // игнорируется
				ColvirReferenceID: "bank_ref_456", // игнорируется
				IncomFL:           consts.BalanceOperationDebit.String(),
				AccCode:           "KZ5555666677778888",
				OperCode:          "010203",
			},
			expectedRRN: "777888", // всегда event.ID
			expectedID:  "D777888",
		},
		{
			name:        "Credit operation with trimmed fields",
			description: "Операция зачисления - поля игнорируются, используется только event.ID",
			event: &message.BalanceUpdateEvent{
				ID:                999111,
				DocumentNumber:    "  ",                  // игнорируется
				ColvirReferenceID: "  ref_with_spaces  ", // игнорируется
				IncomFL:           consts.BalanceOperationCredit.String(),
				AccCode:           "KZ1111222233334444",
			},
			expectedRRN: "999111", // всегда event.ID
			expectedID:  "C999111",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Logf("Testing: %s", tt.description)

			// Act
			externalID, err := generateExternalID(tt.event)

			// Assert
			require.NoError(t, err, "Should not return error for valid operation type")
			assert.Equal(t, tt.expectedID, externalID, "Should generate expected ExternalID")

			// Проверяем RRN часть отдельно
			actualRRN := externalID[1:]
			assert.Equal(t, tt.expectedRRN, actualRRN, "Should have correct RRN part")

			t.Logf("Generated ExternalID: %s (RRN: %s)", externalID, actualRRN)
		})
	}
}

func TestExternalID_EdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		event       *message.BalanceUpdateEvent
		expectError bool
		errorMsg    string
	}{
		{
			name: "Empty IncomFL - should error",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************",
				ColvirReferenceID: "",
				IncomFL:           "", // пустой
			},
			expectError: true,
			errorMsg:    "неопределенный тип операции",
		},
		{
			name: "Unknown IncomFL value - should error",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************",
				ColvirReferenceID: "",
				IncomFL:           "2", // неизвестное значение
			},
			expectError: true,
			errorMsg:    "неопределенный тип операции",
		},
		{
			name: "Whitespace only IncomFL - should error",
			event: &message.BalanceUpdateEvent{
				ID:                12345,
				DocumentNumber:    "************",
				ColvirReferenceID: "",
				IncomFL:           "   ", // только пробелы
			},
			expectError: true,
			errorMsg:    "неопределенный тип операции",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Act
			externalID, err := generateExternalID(tt.event)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, externalID)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, externalID)
			}
		})
	}
}

// Вспомогательная функция для создания контекста с логгером
func createTestContext() context.Context {
	logger := logs.Logger(context.Background(), &logs.Config{Level: "debug"}, logs.Options())
	return logger.WithContext(context.Background())
}
