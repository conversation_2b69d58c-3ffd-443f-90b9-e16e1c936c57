package usecase

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/common"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

const (
	singleCallTimeout            = 5 * time.Second
	maxAttempts                  = 3
	amountScale                  = 2
	handleBalanceUpdateEventOpID = "HandleBalanceUpdateEvent"
)

// HandleBalanceUpdateEvent обрабатывает события изменения баланса из АБИС Colvir
func (u *useCasesImpl) HandleBalanceUpdateEvent(ctx context.Context, msg *kafka.Message) {
	base := logs.FromContext(ctx)
	ctx = utils.PutInfoFromMsgHeadersIntoCtx(ctx, *msg)
	logger := utils.LoggerWithOperationID(*base, handleBalanceUpdateEventOpID)
	logger.Info().Msg("start")
	defer logger.Info().Msg("end")

	logger.Info().Msg("Получено сообщение из Kafka, начинаем десериализацию")

	// Пытаемся распарсить как существующую модель (snake_case)
	deserializeMode := "snake_case"
	event, err := kafka.ValueJSONToTyped[message.BalanceUpdateEvent](msg.Value)
	if err != nil {
		logger.Error().Err(err).Msg("Ошибка десериализации события баланса (snake_case)")
	}

	// Если ключевые поля пустые/нулевые, пробуем Debezium envelope (UPPER_CASE)
	if needsDebeziumFallback(&event) {
		if ev2, ok := parseDebeziumEnvelopeFromValue(msg.Value); ok {
			logger.Info().Msg("Десериализация через Debezium envelope (fallback)")
			deserializeMode = "debezium_fallback"
			event = ev2
		} else if err != nil {
			logger.Error().Msg("Не удалось десериализовать сообщение Kafka в известном формате")
			return
		}
	}

	if payloadBytes, marshalErr := event.ToJSON(); marshalErr == nil {
		payloadHash := fmt.Sprintf("%x", sha256.Sum256(payloadBytes))
		logger.Info().
			Int("payload_size", len(payloadBytes)).
			Str("payload_sha256", payloadHash).
			Str("deserialize_mode", deserializeMode).
			Msg("Получено событие изменения баланса (sanitized)")
	}

	// Логируем ключевые поля события для диагностики
	petitionNumber := ""
	if event.PetitionNumber != nil {
		petitionNumber = strings.TrimSpace(*event.PetitionNumber)
	}
	description := strings.TrimSpace(event.Description)
	if description == "" {
		description = strings.TrimSpace(event.TxtDscr)
	}
	descriptionHash := fmt.Sprintf("%x", sha256.Sum256([]byte(description)))

	logger.Info().
		Str("incomfl", strings.TrimSpace(event.IncomFL)).
		Str("oper_code", strings.TrimSpace(event.OperCode)).
		Str("oper_name", strings.TrimSpace(event.OperName)).
		Str("description", description).
		Int("description_size", len(description)).
		Str("description_sha256", descriptionHash).
		Str("document_number", strings.TrimSpace(event.DocumentNumber)).
		Str("petition_number", petitionNumber).
		Str("colvir_reference_id", strings.TrimSpace(event.ColvirReferenceID)).
		Msg("Parsed event fields (sanitized)")

	logger = logger.With().
		Int64("transaction_id", event.ID).
		Str("account_code_masked", maskAccountCodeForLog(event.AccCode)).
		Str("operation_type", event.GetOperationType()).
		Logger()

	logger.Info().
		Int64("dep_id", event.DepID).
		Float64("amount", event.SDok).
		Float64("sdok_raw", event.SDok).
		Str("sdok_str", fmt.Sprintf("%.10f", event.SDok)).
		Str("currency", event.ValCode).
		Str("doc_date", event.DocDate).
		Msg("Обработка события изменения баланса")

	flags, matchedRule := computeDropFlags(&event)
	if drop, reasonCode := shouldDropEvent(&event); drop {
		if r, ok := consts.DropReasons.ByCode(reasonCode); ok {
			logger.Info().
				Str("reason_code", reasonCode).
				Str("reason", r.Name).
				Float64("amount", event.SDok).
				Float64("sdok_raw", event.SDok).
				Str("sdok_str", fmt.Sprintf("%.10f", event.SDok)).
				Str("currency", strings.TrimSpace(event.ValCode)).
				Str("incomfl", strings.TrimSpace(event.IncomFL)).
				Str("oper_code", strings.TrimSpace(event.OperCode)).
				Str("description", description).
				Str("matched_rule", matchedRule).
				Bool("is_amount_zero", flags.IsAmountZero).
				Bool("is_id_zero", flags.IsIDZero).
				Bool("is_incomfl_empty", flags.IsIncomflEmpty).
				Bool("is_acc_code_empty", flags.IsAccCodeEmpty).
				Bool("is_currency_empty", flags.IsCurrencyEmpty).
				Bool("is_doc_date_empty", flags.IsDocDateEmpty).
				Bool("is_oper_020381_excluded_by_desc", flags.IsOper020381ExcludedByDesc).
				Bool("is_oper_112013_excluded", flags.IsOper112013Excluded).
				Bool("is_oper_000000_excluded", flags.IsOper000000Excluded).
				Bool("is_oper_105202_excluded_by_desc", flags.IsOper105202ExcludedByDesc).
				Bool("is_trust_management_excluded", flags.IsTrustManagementExcluded).
				Bool("is_oper_card_purchase_excluded", flags.IsOperCardPurchaseExcluded).
				Bool("is_oper_card_withdrawal_excluded", flags.IsOperCardWithdrawalExcluded).
				Bool("is_oper_card_transfer_excluded", flags.IsOperCardTransferExcluded).
				Msg("Event skipped by filter")
		} else {
			logger.Info().
				Str("reason_code", reasonCode).
				Float64("amount", event.SDok).
				Float64("sdok_raw", event.SDok).
				Str("sdok_str", fmt.Sprintf("%.10f", event.SDok)).
				Str("currency", strings.TrimSpace(event.ValCode)).
				Str("incomfl", strings.TrimSpace(event.IncomFL)).
				Str("oper_code", strings.TrimSpace(event.OperCode)).
				Str("description", description).
				Str("matched_rule", matchedRule).
				Bool("is_amount_zero", flags.IsAmountZero).
				Bool("is_id_zero", flags.IsIDZero).
				Bool("is_incomfl_empty", flags.IsIncomflEmpty).
				Bool("is_acc_code_empty", flags.IsAccCodeEmpty).
				Bool("is_currency_empty", flags.IsCurrencyEmpty).
				Bool("is_doc_date_empty", flags.IsDocDateEmpty).
				Bool("is_oper_020381_excluded_by_desc", flags.IsOper020381ExcludedByDesc).
				Bool("is_oper_112013_excluded", flags.IsOper112013Excluded).
				Bool("is_oper_000000_excluded", flags.IsOper000000Excluded).
				Bool("is_oper_105202_excluded_by_desc", flags.IsOper105202ExcludedByDesc).
				Bool("is_trust_management_excluded", flags.IsTrustManagementExcluded).
				Bool("is_oper_card_purchase_excluded", flags.IsOperCardPurchaseExcluded).
				Bool("is_oper_card_withdrawal_excluded", flags.IsOperCardWithdrawalExcluded).
				Bool("is_oper_card_transfer_excluded", flags.IsOperCardTransferExcluded).
				Msg("Event skipped by filter")
		}
		return
	}

	logger.Info().
		Bool("is_amount_zero", flags.IsAmountZero).
		Bool("is_id_zero", flags.IsIDZero).
		Bool("is_incomfl_empty", flags.IsIncomflEmpty).
		Bool("is_acc_code_empty", flags.IsAccCodeEmpty).
		Bool("is_currency_empty", flags.IsCurrencyEmpty).
		Bool("is_doc_date_empty", flags.IsDocDateEmpty).
		Bool("is_oper_020381_excluded_by_desc", flags.IsOper020381ExcludedByDesc).
		Bool("is_oper_112013_excluded", flags.IsOper112013Excluded).
		Bool("is_oper_000000_excluded", flags.IsOper000000Excluded).
		Bool("is_oper_105202_excluded_by_desc", flags.IsOper105202ExcludedByDesc).
		Bool("is_trust_management_excluded", flags.IsTrustManagementExcluded).
		Msg("Event passed filter")

	if !event.IsDebit() && !event.IsCredit() {
		logger.Warn().
			Str("incomfl", event.IncomFL).
			Msg("Неизвестный тип операции, событие пропущено")
		return
	}

	err = u.processBalanceUpdate(ctx, &event)
	if err != nil {
		logger.Error().Err(err).
			Int64("transaction_id", event.ID).
			Str("account_code", event.AccCode).
			Msg("Ошибка обработки события изменения баланса")
		return
	}

	logger.Info().
		Msg("Событие изменения баланса успешно обработано")
}

// processBalanceUpdate выполняет основную логику обработки события
func (u *useCasesImpl) processBalanceUpdate(ctx context.Context, event *message.BalanceUpdateEvent) error {
	logger := logs.FromContext(ctx)

	isDuplicate, err := u.checkForDuplicateEvent(ctx, event)
	if err != nil {
		logger.Error().Err(err).
			Str("colvir_ref_id", event.ColvirReferenceID).
			Str("operation_code", event.OperCode).
			Msg("Failed to check for duplicate event - processing anyway for safety")
	} else if isDuplicate {
		logger.Info().
			Str("colvir_ref_id", event.ColvirReferenceID).
			Msg("Duplicate event detected, skipping processing")
		return nil // Дубликат - пропускаем обработку
	}

	// Основная логика обработки события
	var processingErr error
	if event.IsDebit() {
		logger.Info().Msg("Вызываем PaymentDebit в processing-bridge")
		processingErr = u.processDebitOperation(ctx, event)
	} else {
		logger.Info().Msg("Вызываем PaymentCredit в processing-bridge")
		processingErr = u.processCreditOperation(ctx, event)
	}

	if processingErr == nil {
		if markErr := u.markEventAsProcessed(ctx, event); markErr != nil {
			logger.Error().Err(markErr).
				Str("colvir_ref_id", event.ColvirReferenceID).
				Msg("Failed to mark event as processed - this may lead to duplicate processing")
		}
	}

	return processingErr
}

func (u *useCasesImpl) processDebitOperation(ctx context.Context, event *message.BalanceUpdateEvent) error {
	logger := logs.FromContext(ctx)

	logger.Info().
		Int64("event_id", event.ID).
		Str("incomfl", event.IncomFL).
		Str("oper_code", event.OperCode).
		Msg("DEBIT OPERATION - Starting debit processing")

	// Используем новую логику RRN маппинга: ExternalID = префикс + RRN
	externalID, err := generateExternalID(event)
	if err != nil {
		logger.Error().Err(err).
			Int64("event_id", event.ID).
			Msg("DEBIT OPERATION - Failed to generate ExternalID")
		return fmt.Errorf("failed to generate ExternalID: %w", err)
	}
	if externalID == "" {
		logger.Error().
			Int64("event_id", event.ID).
			Msg("DEBIT OPERATION - Generated ExternalID is empty")
		return fmt.Errorf("generated ExternalID is empty")
	}

	req := &pb.PaymentDebitReq{
		Iban:         &event.AccCode,                                     // используем IBAN
		Amount:       utils.FormatAmountDecimal(event.SDok, amountScale), // sdok → amount (как строка)
		Currency:     event.ValCode,                                      // val_code → currency
		Rrn:          externalID[1:],                                     // rrn из ExternalID (без префикса)
		CategoryText: &event.Description,
		IsReversal:   false,
		ExternalID:   externalID, // используем сгенерированный ExternalID
	}

	// Санитизируем текст категории
	ct := strings.TrimSpace(event.Description)
	ctHash := fmt.Sprintf("%x", sha256.Sum256([]byte(ct)))
	identifierType := "iban"

	logger.Info().
		Str("identifier_type", identifierType).
		Str("identifier_masked", maskAccountCodeForLog(event.AccCode)).
		Str("amount", req.Amount).
		Str("currency", req.Currency).
		Str("rrn", externalID[1:]). // Убираем префикс D/C/U
		Str("external_id", externalID).
		Int("category_text_size", len(ct)).
		Str("category_text_sha256", ctHash).
		Str("oper_code", strings.TrimSpace(event.OperCode)).
		Str("txt_dscr", strings.TrimSpace(event.TxtDscr)).
		Int("rrn_length", len(externalID[1:])).
		Bool("rrn_contains_underscore", strings.Contains(externalID[1:], "_")).
		Bool("rrn_contains_hyphen", strings.Contains(externalID[1:], "-")).
		Msg("DEBIT OPERATION - Sending request to processing-bridge")

	call := func(callCtx context.Context) error {
		_, err := u.Providers.Processingbridge.PaymentDebit(callCtx, req)
		return err
	}

	if err := u.callWithRetry(ctx, "PaymentDebit", call); err != nil {
		logger.Error().Err(err).
			Int64("transaction_id", event.ID).
			Str("iban_masked", maskAccountCodeForLog(event.AccCode)).
			Str("rrn", externalID[1:]). // Убираем префикс D/C/U
			Str("external_id", externalID).
			Str("amount", req.Amount).
			Str("currency", req.Currency).
			Msg("DEBIT OPERATION - Failed to call PaymentDebit in processing-bridge")
		return fmt.Errorf("failed to call PaymentDebit: %w", err)
	}

	logger.Info().
		Int64("event_id", event.ID).
		Str("external_id", externalID).
		Str("rrn", externalID[1:]).
		Msg("DEBIT OPERATION - Successfully completed")

	return nil
}

func (u *useCasesImpl) processCreditOperation(ctx context.Context, event *message.BalanceUpdateEvent) error {
	logger := logs.FromContext(ctx)

	logger.Info().
		Int64("event_id", event.ID).
		Str("incomfl", event.IncomFL).
		Str("oper_code", event.OperCode).
		Msg("CREDIT OPERATION - Starting credit processing")

	// Используем новую логику RRN маппинга: ExternalID = префикс + RRN
	externalID, err := generateExternalID(event)
	if err != nil {
		logger.Error().Err(err).
			Int64("event_id", event.ID).
			Msg("CREDIT OPERATION - Failed to generate ExternalID")
		return fmt.Errorf("failed to generate ExternalID: %w", err)
	}
	if externalID == "" {
		logger.Error().
			Int64("event_id", event.ID).
			Msg("CREDIT OPERATION - Generated ExternalID is empty")
		return fmt.Errorf("generated ExternalID is empty")
	}

	req := &pb.PaymentCreditReq{
		Iban:         &event.AccCode,                                     // используем IBAN
		Amount:       utils.FormatAmountDecimal(event.SDok, amountScale), // sdok → amount (как строка)
		Currency:     event.ValCode,                                      // val_code → currency
		Rrn:          externalID[1:],                                     // rrn из ExternalID (без префикса)
		CategoryText: &event.Description,
		IsReversal:   false,      // по умолчанию false
		ExternalID:   externalID, // используем сгенерированный ExternalID
	}

	// Санитизируем текст категории
	ct := strings.TrimSpace(event.Description)
	ctHash := fmt.Sprintf("%x", sha256.Sum256([]byte(ct)))
	identifierType := "iban"

	logger.Info().
		Str("identifier_type", identifierType).
		Str("identifier_masked", maskAccountCodeForLog(event.AccCode)).
		Str("amount", req.Amount).
		Str("currency", req.Currency).
		Str("rrn", externalID[1:]). // Убираем префикс D/C/U
		Str("external_id", externalID).
		Int("category_text_size", len(ct)).
		Str("category_text_sha256", ctHash).
		Str("oper_code", strings.TrimSpace(event.OperCode)).
		Str("txt_dscr", strings.TrimSpace(event.TxtDscr)).
		Int("rrn_length", len(externalID[1:])).
		Bool("rrn_contains_underscore", strings.Contains(externalID[1:], "_")).
		Bool("rrn_contains_hyphen", strings.Contains(externalID[1:], "-")).
		Msg("CREDIT OPERATION - Sending request to processing-bridge")

	call := func(callCtx context.Context) error {
		_, err := u.Providers.Processingbridge.PaymentCredit(callCtx, req)
		return err
	}

	if err := u.callWithRetry(ctx, "PaymentCredit", call); err != nil {
		logger.Error().Err(err).
			Int64("transaction_id", event.ID).
			Str("iban_masked", maskAccountCodeForLog(event.AccCode)).
			Str("rrn", externalID[1:]). // Убираем префикс D/C/U
			Str("external_id", externalID).
			Str("amount", req.Amount).
			Str("currency", req.Currency).
			Msg("CREDIT OPERATION - Failed to call PaymentCredit in processing-bridge")
		return fmt.Errorf("failed to call PaymentCredit: %w", err)
	}

	logger.Info().
		Int64("event_id", event.ID).
		Str("external_id", externalID).
		Str("rrn", externalID[1:]).
		Msg("CREDIT OPERATION - Successfully completed")

	return nil
}

// needsDebeziumFallback решает, стоит ли пробовать альтернативный парсинг Debezium envelope
func needsDebeziumFallback(e *message.BalanceUpdateEvent) bool {
	if e == nil {
		return true
	}
	// Если ключевые поля пустые/нулевые — пробуем fallback
	if strings.TrimSpace(e.AccCode) == "" {
		return true
	}
	if strings.TrimSpace(e.IncomFL) == "" {
		return true
	}
	if strings.TrimSpace(e.ValCode) == "" {
		return true
	}
	if strings.TrimSpace(e.DocDate) == "" {
		return true
	}
	if e.SDok == 0 {
		return true
	}
	return false
}

// parseDebeziumEnvelope парсит сообщение Debezium вида {"schema":..., "payload":{...UPPER_CASE...}}
func parseDebeziumEnvelopeFromValue(v kafka.ValueProvider) (message.BalanceUpdateEvent, bool) {
	m, err := kafka.ValueJSONToTyped[map[string]any](v)
	if err != nil {
		return message.BalanceUpdateEvent{}, false
	}
	payload, ok := m["payload"].(map[string]any)
	if !ok || payload == nil {
		return message.BalanceUpdateEvent{}, false
	}

	getStr := func(key string) string {
		if v, ok := payload[key]; ok && v != nil {
			switch t := v.(type) {
			case string:
				return strings.TrimSpace(t)
			default:
				b, _ := json.Marshal(t)
				return strings.TrimSpace(string(b))
			}
		}
		return ""
	}
	getF := func(key string) float64 {
		if v, ok := payload[key]; ok && v != nil {
			switch t := v.(type) {
			case float64:
				return t
			case json.Number:
				f, _ := t.Float64()
				return f
			case string:
				f, _ := strconv.ParseFloat(strings.TrimSpace(t), 64)
				return f
			}
		}
		return 0
	}
	getI := func(key string) int64 {
		f := getF(key)
		return int64(f)
	}
	// DOC_DATE приходит как epoch millis (float/number)
	formatDocDate := func() string {
		// если в payload уже строка даты — вернём её
		if ds := getStr("DOC_DATE"); ds != "" && len(ds) <= 20 && strings.Contains(ds, "-") {
			return ds
		}
		// иначе воспринимаем как epoch millis
		if v, ok := payload["DOC_DATE"]; ok && v != nil {
			var ms int64
			switch t := v.(type) {
			case float64:
				ms = int64(t)
			case json.Number:
				ms, _ = t.Int64()
			case string:
				// иногда строка числа
				if parsed, err := strconv.ParseInt(strings.TrimSpace(t), 10, 64); err == nil {
					ms = parsed
				}
			}
			if ms > 0 {
				return time.UnixMilli(ms).UTC().Format("2006-01-02")
			}
		}
		return ""
	}

	// Опциональные поля, которые могут быть null
	petition := getStr("PETITIONNUMBER")
	var petitionPtr *string
	if petition != "" {
		petitionPtr = &petition
	}

	ev := message.BalanceUpdateEvent{
		DepID:             getI("DEP_ID"),
		ID:                getI("ID"),
		IncomFL:           getStr("INCOMFL"),
		AccCode:           getStr("ACC_CODE"),
		SDok:              getF("SDOK"),
		ValCode:           getStr("VAL_CODE"),
		DocDate:           formatDocDate(),
		SendDate:          getStr("SENDDATE"),
		OperCode:          getStr("OPER_CODE"),
		TxtDscr:           getStr("TXT_DSCR"),
		Description:       getStr("TXT_DSCR"),
		ColvirReferenceID: getStr("COLVIRREFERENCEID"),
		DocumentNumber:    getStr("DOCUMENT_NUMBER"),
		PetitionNumber:    petitionPtr,
		OperName:          getStr("OPER_NAME"),
	}
	return ev, true
}

// maskAccountCodeForLog маскирует номер счёта/IBAN для логов
func maskAccountCodeForLog(accountCode string) string {
	code := strings.TrimSpace(accountCode)
	if code == "" {
		return ""
	}
	n := len(code)
	if n <= 6 {
		return strings.Repeat("*", n)
	}
	if n <= 10 {
		return code[:2] + strings.Repeat("*", n-4) + code[n-2:]
	}
	return code[:6] + strings.Repeat("*", n-10) + code[n-4:]
}

// dropFlags набор детальных флагов для объяснимости фильтрации
type dropFlags struct {
	IsAmountZero               bool
	IsIDZero                   bool
	IsIncomflEmpty             bool
	IsAccCodeEmpty             bool
	IsCurrencyEmpty            bool
	IsDocDateEmpty             bool
	IsOper020381ExcludedByDesc bool
	IsOper112013Excluded       bool
	IsOper000000Excluded       bool
	IsOper105202ExcludedByDesc bool
	IsTrustManagementExcluded  bool
	// Временные моки карточных операций
	IsOperCardPurchaseExcluded   bool
	IsOperCardWithdrawalExcluded bool
	IsOperCardTransferExcluded   bool
}

// computeDropFlags вычисляет флаги и правило, сработавшее в shouldDropEvent, без дублирования логики исключений
func computeDropFlags(e *message.BalanceUpdateEvent) (dropFlags, string) {
	flags := dropFlags{}
	if e == nil {
		return flags, consts.DropReasons.EmptyEvent.Code
	}
	if e.SDok == 0 {
		flags.IsAmountZero = true
		return flags, consts.DropReasons.AmountIsZero.Code
	}
	if e.ID == 0 {
		flags.IsIDZero = true
		return flags, consts.DropReasons.IDIsZero.Code
	}
	if common.IsEmptyString(strings.TrimSpace(e.IncomFL)) {
		flags.IsIncomflEmpty = true
		return flags, consts.DropReasons.IncomflEmpty.Code
	}
	if common.IsEmptyString(strings.TrimSpace(e.AccCode)) {
		flags.IsAccCodeEmpty = true
		return flags, consts.DropReasons.AccCodeEmpty.Code
	}
	if common.IsEmptyString(strings.TrimSpace(e.ValCode)) {
		flags.IsCurrencyEmpty = true
		return flags, consts.DropReasons.CurrencyEmpty.Code
	}
	if common.IsEmptyString(strings.TrimSpace(e.DocDate)) {
		flags.IsDocDateEmpty = true
		return flags, consts.DropReasons.DocDateEmpty.Code
	}

	code := strings.TrimSpace(e.OperCode)
	description := strings.TrimSpace(e.Description)
	if description == "" {
		description = strings.TrimSpace(e.TxtDscr)
	}

	switch consts.OperCode(code) {
	case consts.OperCode020381:
		txtDscr := strings.TrimSpace(e.TxtDscr)
		if txtDscr == string(consts.DescMobileServices) ||
			txtDscr == string(consts.DescPaymentByKaspi) {
			flags.IsOper020381ExcludedByDesc = true
			return flags, consts.DropReasons.OperCode020381ExcludedByDesc.Code
		}
	case consts.OperCode112013:
		flags.IsOper112013Excluded = true
		return flags, consts.DropReasons.OperCode112013Excluded.Code
	case consts.OperCode000000:
		flags.IsOper000000Excluded = true
		return flags, consts.DropReasons.OperCode000000Excluded.Code
	case consts.OperCode105202:
		if e.PetitionNumber != nil {
			pn := strings.TrimSpace(*e.PetitionNumber)
			if pn != "" && pn != "0" {
				flags.IsOper105202ExcludedByDesc = true
				return flags, consts.DropReasons.OperCode105202ExcludedByPetition.Code
			}
		}
	// Временные моки карточных операций
	case consts.OperCodeCardPurchase:
		flags.IsOperCardPurchaseExcluded = true
		return flags, consts.DropReasons.OperCodeCardPurchaseExcluded.Code
	case consts.OperCodeCardWithdrawal:
		flags.IsOperCardWithdrawalExcluded = true
		return flags, consts.DropReasons.OperCodeCardWithdrawalExcluded.Code
	case consts.OperCodeCardTransfer:
		flags.IsOperCardTransferExcluded = true
		return flags, consts.DropReasons.OperCodeCardTransferExcluded.Code
	}

	// Проверяем операции доверительного управления по описанию (только если oper_code и oper_name пустые)
	operCode := strings.TrimSpace(code)
	operName := strings.TrimSpace(e.OperName)
	if (operCode == "" || operCode == consts.NullValue) && (operName == "" || operName == consts.NullValue) {
		if strings.HasPrefix(description, string(consts.DescTrustManagementPrefix)) {
			flags.IsTrustManagementExcluded = true
			return flags, consts.DropReasons.TrustManagementExcluded.Code
		}
	}

	return flags, ""
}

// shouldDropEvent определяет, нужно ли пропустить событие изменения баланса.
// Возвращает пару: признак пропуска и текстовая причина (для логов/метрик).
func shouldDropEvent(e *message.BalanceUpdateEvent) (bool, string) {
	switch {
	case e == nil:
		return true, consts.DropReasons.EmptyEvent.Code
	// ПРИОРИТЕТНАЯ ПРОВЕРКА: petitionNumber всегда отбрасываем
	case e.PetitionNumber != nil && strings.TrimSpace(*e.PetitionNumber) != "":
		return true, consts.DropReasons.PetitionNumberPresent.Code
	case e.SDok == 0:
		return true, consts.DropReasons.AmountIsZero.Code
	case e.ID == 0:
		return true, consts.DropReasons.IDIsZero.Code
	case common.IsEmptyString(strings.TrimSpace(e.IncomFL)):
		return true, consts.DropReasons.IncomflEmpty.Code
	case common.IsEmptyString(strings.TrimSpace(e.AccCode)):
		return true, consts.DropReasons.AccCodeEmpty.Code
	case common.IsEmptyString(strings.TrimSpace(e.ValCode)):
		return true, consts.DropReasons.CurrencyEmpty.Code
	case common.IsEmptyString(strings.TrimSpace(e.DocDate)):
		return true, consts.DropReasons.DocDateEmpty.Code
	}

	code := strings.TrimSpace(e.OperCode)
	description := strings.TrimSpace(e.Description)
	if description == "" {
		description = strings.TrimSpace(e.TxtDscr)
	}

	switch consts.OperCode(code) {
	case consts.OperCode020381:
		txtDscr := strings.TrimSpace(e.TxtDscr)
		if txtDscr == string(consts.DescMobileServices) ||
			txtDscr == string(consts.DescPaymentByKaspi) {
			return true, consts.DropReasons.OperCode020381ExcludedByDesc.Code
		}
	case consts.OperCode112013:
		return true, consts.DropReasons.OperCode112013Excluded.Code
	case consts.OperCode000000:
		return true, consts.DropReasons.OperCode000000Excluded.Code
	case consts.OperCode105202:
		if e.PetitionNumber != nil {
			pn := strings.TrimSpace(*e.PetitionNumber)
			if pn != "" && pn != "0" {
				return true, consts.DropReasons.OperCode105202ExcludedByPetition.Code
			}
		}
	// Временные моки карточных операций (исключаем до получения реальных кодов)
	case consts.OperCodeCardPurchase:
		return true, consts.DropReasons.OperCodeCardPurchaseExcluded.Code
	case consts.OperCodeCardWithdrawal:
		return true, consts.DropReasons.OperCodeCardWithdrawalExcluded.Code
	case consts.OperCodeCardTransfer:
		return true, consts.DropReasons.OperCodeCardTransferExcluded.Code
	}

	// Проверяем операции доверительного управления по описанию (только если oper_code и oper_name пустые)
	operCode := strings.TrimSpace(code)
	operName := strings.TrimSpace(e.OperName)
	if (operCode == "" || operCode == consts.NullValue) && (operName == "" || operName == consts.NullValue) {
		if strings.HasPrefix(description, string(consts.DescTrustManagementPrefix)) {
			return true, consts.DropReasons.TrustManagementExcluded.Code
		}
	}

	return false, ""
}

// callWithRetry вызывает переданную функцию do с ограничением по времени и политикой повторов.
func (u *useCasesImpl) callWithRetry(ctx context.Context, op string, do func(context.Context) error) error {
	logger := logs.FromContext(ctx)

	attempt := 0
	for {
		attempt++

		callCtx, cancel := context.WithTimeout(ctx, singleCallTimeout)
		err := do(callCtx)
		cancel()

		if err == nil {
			if attempt > 1 {
				logger.Info().Int("attempt", attempt).Str("op", op).Msg("Succeeded after retries")
			}
			return nil
		}

		class := classifyProcessingError(err)

		// no-retry classes
		if class == consts.ErrDuplicateRequest {
			logger.Warn().Str("op", op).Msg("Duplicate request, stop without retry")
			return nil
		}
		if class == consts.ErrCustomerContractAbsent {
			logger.Warn().Str("op", op).Msg("Customer contract not found, no retry")
			return err
		}
		if class == consts.ErrValidationFailed {
			logger.Warn().Str("op", op).Msg("Validation failed, no retry")
			return err
		}
		if class == consts.ErrDebitEdgeCase {
			logger.Warn().Str("op", op).Msg("Debit edge case error (d/g), no retry")
			return err
		}
		if class == consts.ErrCreditEdgeCase {
			logger.Warn().Str("op", op).Msg("Credit edge case error (f), no retry")
			return err
		}

		if attempt >= maxAttempts {
			incRetryExhausted(ctx, op, string(class))
			logger.Error().Int("attempt", attempt).Str("op", op).Str("class", string(class)).Msg("Retry limit reached")
			return err
		}

		backoff := computeBackoff(attempt)
		logger.Warn().
			Int("attempt", attempt).
			Str("op", op).
			Str("class", string(class)).
			Int64("backoff_ms", backoff.Milliseconds()).
			Err(err).
			Msg("Transient error, will retry with backoff")

		incRetry(ctx, op, string(class))
		// sleep with exponential backoff + jitter
		time.Sleep(backoff)
	}
}

func classifyProcessingError(err error) consts.ErrorClass {
	if err == nil {
		return consts.ErrOther
	}
	lower := strings.ToLower(err.Error())

	switch {
	case strings.Contains(lower, consts.ColvirErrKafkaMsg.DuplicateRequest):
		return consts.ErrDuplicateRequest
	case strings.Contains(lower, consts.ColvirErrKafkaMsg.ValidationFailed):
		return consts.ErrValidationFailed
	case strings.Contains(lower, consts.ColvirErrKafkaMsg.CustomerContract) && strings.Contains(lower, consts.ColvirErrKafkaMsg.NotFound):
		return consts.ErrCustomerContractAbsent
	case strings.Contains(lower, consts.ColvirErrKafkaMsg.DebitEdgeCaseD) || strings.Contains(lower, consts.ColvirErrKafkaMsg.DebitEdgeCaseG):
		return consts.ErrDebitEdgeCase
	case strings.Contains(lower, consts.ColvirErrKafkaMsg.CreditEdgeCaseF):
		return consts.ErrCreditEdgeCase
	case errors.Is(err, context.DeadlineExceeded) || strings.Contains(lower, consts.ColvirErrKafkaMsg.DeadlineExceeded):
		return consts.ErrDeadlineExceeded
	case strings.Contains(lower, consts.ColvirErrKafkaMsg.Timeout):
		return consts.ErrTimeout
	case strings.Contains(lower, consts.ColvirErrKafkaMsg.InternalServerError):
		return consts.ErrInternalServerError
	default:
		return consts.ErrOther
	}
}

func computeBackoff(attempt int) time.Duration {
	mult := 1.0
	for i := 1; i < attempt; i++ {
		mult *= consts.BackoffFactor
		if time.Duration(mult*float64(consts.BackoffBase)) >= consts.BackoffMax {
			return addJitter(consts.BackoffMax)
		}
	}
	d := time.Duration(mult * float64(consts.BackoffBase))
	if d > consts.BackoffMax {
		d = consts.BackoffMax
	}
	return addJitter(d)
}

func addJitter(d time.Duration) time.Duration {
	if consts.BackoffJitter <= 0 {
		return d
	}
	n := time.Now().UnixNano()
	sign := int64(1)
	if n%2 == 0 {
		sign = -1
	}
	jitterNs := int64(float64(d.Nanoseconds()) * consts.BackoffJitter * 0.5) // +/- 10% effectively
	return d + time.Duration(sign*jitterNs)
}
