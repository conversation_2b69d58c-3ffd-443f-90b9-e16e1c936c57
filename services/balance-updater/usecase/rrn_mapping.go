package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

// generateRRN создает RRN согласно упрощенной логике: RRN = event.ID
// Согласно новой документации убраны все сложные варианты маппинга,
// теперь используется только внутренний ID операции из Colvir
func generateRRN(event *message.BalanceUpdateEvent) string {
	logger := logs.FromContext(context.Background())

	// Упрощенная логика: RRN = event.ID (внутренний уникальный код операции в Колвире)
	rrn := fmt.Sprintf("%d", event.ID)

	logger.Info().
		Int64("event_id", event.ID).
		Str("rrn", rrn).
		Msg("RRN GENERATION - Using simplified logic: RRN = event.ID")

	return rrn
}

// generateExternalID создает ExternalID по формуле: префикс + RRN
// Префикс: D (списание), C (зачисление)
// Возвращает ошибку если операция неопределенная
func generateExternalID(event *message.BalanceUpdateEvent) (string, error) {
	logger := logs.FromContext(context.Background())

	rrn := generateRRN(event)

	var prefix string
	switch {
	case event.IsDebit():
		prefix = "D"
	case event.IsCredit():
		prefix = "C"
	default:
		logger.Error().
			Str("incomfl", event.IncomFL).
			Str("operation_type", event.GetOperationType()).
			Msg("EXTERNALID GENERATION - Unknown operation type")
		return "", fmt.Errorf("неопределенный тип операции: incomfl=%s", event.IncomFL)
	}

	externalID := prefix + rrn

	logger.Info().
		Str("prefix", prefix).
		Str("rrn", rrn).
		Str("external_id", externalID).
		Str("operation_type", event.GetOperationType()).
		Msg("EXTERNALID GENERATION - Generated ExternalID")

	return externalID, nil
}
