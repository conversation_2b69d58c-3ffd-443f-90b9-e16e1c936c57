// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package usecase

import (
	"context"

	balanceupdater "git.redmadrobot.com/zaman/backend/zaman/config/services/balance-updater"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/bus"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/backend-go/rmr-pkg/proxy/hooks"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/balance-updater/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/entity"
)

var _ BalanceUpdater = (*useCasesImpl)(nil)

type BalanceUpdater interface {
	HealthCheck(ctx context.Context) (*entity.Health, error)
	HealthEvent(ctx context.Context, message *kafka.Message)
	InitConsumer(ctx context.Context)
}

type useCasesImpl struct {
	BalanceUpdater
	cfg       *balanceupdater.Config
	Providers providers.ServiceLocatorImpl
}

func New(ctx context.Context, locator providers.ServiceLocatorImpl, cfg *balanceupdater.Config) *BalanceUpdaterHook {
	useCases := &useCasesImpl{
		cfg:       cfg,
		Providers: locator,
	}

	logger := logs.FromContext(ctx)

	hook := NewBalanceUpdaterHook(
		useCases,
		hooks.GrpcServiceLogBeforeCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPostCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPanic(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
	)
	useCases.BalanceUpdater = hook

	return hook
}
func (u *useCasesImpl) InitConsumer(ctx context.Context) {
	register := func(ctx context.Context, busName string, handler func(context.Context, *kafka.Message)) error {
		subscriber := bus.SubscriberFn[*kafka.Message](func(ctx context.Context, messages ...*kafka.Message) error {
			for _, message := range messages {
				handler(ctx, message)
				message.Ack(ctx)
			}
			return nil
		})

		err := u.Providers.Event.Subscribe(ctx, busName, subscriber)
		if err != nil {
			return err
		}
		return nil
	}

	eventRegistry := u.EventRegistry()
	for busName, handler := range eventRegistry {
		err := register(ctx, busName, handler)
		if err != nil {
			logs.FromContext(ctx).Err(err).Msg("unable to register handler")
		}
	}
}
