package usecase

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	balanceUpdaterTopics "git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater"
)

func (u *useCasesImpl) EventRegistry() map[string]func(context.Context, *kafka.Message) {
	// Если Kafka отключен, возвращаем пустой реестр
	if !u.cfg.App.KafkaEnabled {
		return map[string]func(context.Context, *kafka.Message){}
	}

	if u.cfg.App.KafkaTopic == "" {
		return map[string]func(context.Context, *kafka.Message){}
	}

	balanceUpdaterTopics.SetBalanceUpdateTopic(u.cfg.App.KafkaTopic)

	return map[string]func(context.Context, *kafka.Message){
		balanceUpdaterTopics.BalanceUpdate.FullName(): u.HandleBalanceUpdateEvent,
	}
}
