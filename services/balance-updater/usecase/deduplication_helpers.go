package usecase

import (
	"context"
	"fmt"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/deduplicator"
	deduplib "git.redmadrobot.com/zaman/backend/zaman/services/balance-updater"
	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

// generateDeduplicationKey создает уникальный ключ для дедупликации
// Использует ID операции + IncomFL, так как в Колвире одна операция перевода
// создает два события: списание (IncomFL="0") и зачисление (IncomFL="1") с одним ID
func generateDeduplicationKey(ctx context.Context, event *message.BalanceUpdateEvent) string {
	// Ключ дедупликации = ID операции + тип операции (IncomFL)
	// Это позволяет обрабатывать и списание, и зачисление для одной операции
	dedupKey := fmt.Sprintf("%d|%s", event.ID, event.IncomFL)

	// Метрика: отслеживаем события с нулевым ID (не должно быть)
	if event.ID == 0 {
		incDedupEmptyRefID(ctx)
		incDedupKeyGenerationErrors(ctx, "zero_operation_id")
		return dedupKey
	}

	// Метрика: успешная генерация ключа дедупликации
	opTypeForMetrics := getOperationTypeForMetrics(event)
	incDedupCompositeKey(ctx, opTypeForMetrics)

	// Отслеживаем операции по составному ключу (ID|IncomFL)
	incDedupRelatedOperations(ctx, dedupKey)

	return dedupKey
}

// checkForDuplicateEvent проверяет является ли событие дубликатом
// Возвращает true если событие дубликат и его нужно пропустить
func (u *useCasesImpl) checkForDuplicateEvent(ctx context.Context, event *message.BalanceUpdateEvent) (bool, error) {
	logger := logs.FromContext(ctx)
	startTime := time.Now()

	// Получаем тип операции для метрик
	opTypeForMetrics := getOperationTypeForMetrics(event)

	// Метрика: увеличиваем счетчик проверок дедупликации
	incDedupChecks(ctx, opTypeForMetrics)

	// Генерируем ключ дедупликации (только OperationID)
	dedupKey := generateDeduplicationKey(ctx, event)

	logger.Info().
		Str("colvir_ref_id", event.ColvirReferenceID).
		Str("dedup_key", dedupKey).
		Str("operation_code", event.OperCode).
		Str("operation_type", event.IncomFL).
		Float64("amount", event.SDok).
		Str("currency", event.ValCode).
		Str("account_masked", maskAccountCodeForLog(event.AccCode)).
		Msg("STARTING DEDUPLICATION CHECK with OperationID only (AccCode removed)")

	// Получаем deduplicator из providers
	dedup := u.getDeduplicator()
	if dedup == nil {
		logger.Warn().
			Str("colvir_ref_id", event.ColvirReferenceID).
			Str("dedup_key", dedupKey).
			Msg("Deduplicator not available, processing event without deduplication")

		// Метрика: событие обработано без дедупликации
		incDedupBypassed(ctx, "deduplicator_unavailable")
		return false, nil
	}

	logger.Debug().
		Str("colvir_ref_id", event.ColvirReferenceID).
		Str("dedup_key", dedupKey).
		Msg("Deduplicator available, checking for duplicate")

	// Проверяем дубликат используя ключ (только OperationID)
	isDuplicate, err := deduplib.IsDuplicateEvent(ctx, dedup, dedupKey)

	// Метрика: записываем время проверки дедупликации
	duration := time.Since(startTime)
	recordDedupCheckDuration(ctx, duration, opTypeForMetrics)

	if err != nil {
		logger.Error().Err(err).
			Str("colvir_ref_id", event.ColvirReferenceID).
			Str("dedup_key", dedupKey).
			Str("operation_code", event.OperCode).
			Msg("Failed to check for duplicate")
		return false, err
	}

	if isDuplicate {
		// Метрика: увеличиваем счетчик дубликатов
		incDedupDuplicates(ctx, opTypeForMetrics)

		logger.Warn().
			Str("colvir_ref_id", event.ColvirReferenceID).
			Str("dedup_key", dedupKey).
			Str("operation_code", event.OperCode).
			Str("operation_type", event.IncomFL).
			Float64("amount", event.SDok).
			Str("currency", event.ValCode).
			Msg("DUPLICATE EVENT DETECTED - SKIPPING PROCESSING")
	} else {
		// Метрика: увеличиваем счетчик уникальных событий
		incDedupUnique(ctx, opTypeForMetrics)

		logger.Info().
			Str("colvir_ref_id", event.ColvirReferenceID).
			Str("dedup_key", dedupKey).
			Str("operation_code", event.OperCode).
			Str("operation_type", event.IncomFL).
			Msg("EVENT IS UNIQUE - PROCEEDING WITH PROCESSING")
	}

	return isDuplicate, nil
}

// markEventAsProcessed маркирует событие как обработанное
func (u *useCasesImpl) markEventAsProcessed(ctx context.Context, event *message.BalanceUpdateEvent) error {
	logger := logs.FromContext(ctx)
	startTime := time.Now()

	// Получаем тип операции для метрик
	opTypeForMetrics := getOperationTypeForMetrics(event)

	// Генерируем тот же ключ что и при проверке (только OperationID)
	dedupKey := generateDeduplicationKey(ctx, event)

	logger.Info().
		Str("colvir_ref_id", event.ColvirReferenceID).
		Str("dedup_key", dedupKey).
		Str("operation_code", event.OperCode).
		Str("operation_type", event.IncomFL).
		Float64("amount", event.SDok).
		Str("currency", event.ValCode).
		Msg("MARKING EVENT AS PROCESSED with OperationID only (AccCode removed)")

	// Получаем deduplicator из providers
	dedup := u.getDeduplicator()
	if dedup == nil {
		logger.Warn().
			Str("colvir_ref_id", event.ColvirReferenceID).
			Str("dedup_key", dedupKey).
			Msg("Deduplicator not available, cannot mark event as processed")
		return fmt.Errorf("deduplicator not available for event %s (key: %s)", event.ColvirReferenceID, dedupKey)
	}

	logger.Debug().
		Str("colvir_ref_id", event.ColvirReferenceID).
		Str("dedup_key", dedupKey).
		Msg("Deduplicator available, marking event")

	// Маркируем как обработанное используя ключ (только OperationID)
	err := deduplib.MarkEventAsProcessed(ctx, dedup, dedupKey)

	// Метрика: записываем время маркировки как обработанного
	duration := time.Since(startTime)
	recordDedupMarkDuration(ctx, duration, opTypeForMetrics)

	if err != nil {
		logger.Error().Err(err).
			Str("colvir_ref_id", event.ColvirReferenceID).
			Str("dedup_key", dedupKey).
			Str("operation_code", event.OperCode).
			Str("operation_type", event.IncomFL).
			Float64("amount", event.SDok).
			Msg("Failed to mark event as processed")
		return fmt.Errorf("failed to mark event as processed: %w", err)
	}

	logger.Info().
		Str("colvir_ref_id", event.ColvirReferenceID).
		Str("dedup_key", dedupKey).
		Str("operation_code", event.OperCode).
		Str("operation_type", event.IncomFL).
		Msg("Event successfully marked as processed")

	return nil
}

// getDeduplicator безопасно получает deduplicator из providers
func (u *useCasesImpl) getDeduplicator() deduplicator.Deduplicator {
	if u.Providers.Deduplicator == nil {
		return nil
	}
	return u.Providers.Deduplicator
}
