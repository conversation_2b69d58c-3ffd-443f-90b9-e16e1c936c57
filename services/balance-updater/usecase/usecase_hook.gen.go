// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/usecase -i BalanceUpdater -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ BalanceUpdater = (*BalanceUpdaterHook)(nil)

// BalanceUpdaterHook implements BalanceUpdater interface wrapper
type BalanceUpdaterHook struct {
	BalanceUpdater
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// HealthCheck implements BalanceUpdater
func (_w *BalanceUpdaterHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.BalanceUpdater, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.BalanceUpdater, "HealthCheck", _params)

	hp1, err = _w.BalanceUpdater.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.BalanceUpdater, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements BalanceUpdater
func (_w *BalanceUpdaterHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.BalanceUpdater, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.BalanceUpdater, "HealthEvent", _params)

	_w.BalanceUpdater.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.BalanceUpdater, "HealthEvent", []any{})
	return
}

// InitConsumer implements BalanceUpdater
func (_w *BalanceUpdaterHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.BalanceUpdater, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.BalanceUpdater, "InitConsumer", _params)

	_w.BalanceUpdater.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.BalanceUpdater, "InitConsumer", []any{})
	return
}

// NewBalanceUpdaterHook returns BalanceUpdaterHook
func NewBalanceUpdaterHook(object BalanceUpdater, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *BalanceUpdaterHook {
	return &BalanceUpdaterHook{
		BalanceUpdater: object,
		_beforeCall:    beforeCall,
		_postCall:      postCall,
		_onPanic:       onPanic,
	}
}
