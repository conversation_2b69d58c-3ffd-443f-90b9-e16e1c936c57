package usecase

import (
	"context"
	"crypto/sha256"
	"fmt"
	"strings"
	"time"

	balmetrics "git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/metrics"
	"git.redmadrobot.com/zaman/backend/zaman/topics/balance-updater/message"
)

func incRetry(ctx context.Context, op, class string) {
	balmetrics.IncRetry(ctx, op, class)
}

func incRetryExhausted(ctx context.Context, op, class string) {
	balmetrics.IncRetryExhausted(ctx, op, class)
}

// Метрики дедупликации

func incDedupChecks(ctx context.Context, operationType string) {
	balmetrics.IncDedupChecks(ctx, operationType)
}

func incDedupDuplicates(ctx context.Context, operationType string) {
	balmetrics.IncDedupDuplicates(ctx, operationType)
}

func incDedupUnique(ctx context.Context, operationType string) {
	balmetrics.IncDedupUnique(ctx, operationType)
}

func incDedupEmptyRefID(ctx context.Context) {
	balmetrics.IncDedupEmptyRefID(ctx)
}

func incDedupCompositeKey(ctx context.Context, operationType string) {
	balmetrics.IncDedupCompositeKey(ctx, operationType)
}

func incDedupKeyGenerationErrors(ctx context.Context, reason string) {
	balmetrics.IncDedupKeyGenerationErrors(ctx, reason)
}

func recordDedupCheckDuration(ctx context.Context, duration time.Duration, operationType string) {
	balmetrics.RecordDedupCheckDuration(ctx, duration.Seconds(), operationType)
}

func recordDedupMarkDuration(ctx context.Context, duration time.Duration, operationType string) {
	balmetrics.RecordDedupMarkDuration(ctx, duration.Seconds(), operationType)
}

func incDedupRelatedOperations(ctx context.Context, refID string) {
	// Создаем хеш для безопасного логирования
	hash := fmt.Sprintf("%x", sha256.Sum256([]byte(strings.TrimSpace(refID))))
	balmetrics.IncDedupRelatedOperations(ctx, hash[:8]) // Первые 8 символов хеша
}

func incDedupBypassed(ctx context.Context, reason string) {
	balmetrics.IncDedupBypassed(ctx, reason)
}

// getOperationTypeForMetrics возвращает нормализованный тип операции для метрик
func getOperationTypeForMetrics(event *message.BalanceUpdateEvent) string {
	if event.IsDebit() {
		return "debit"
	}
	if event.IsCredit() {
		return "credit"
	}
	return "unknown"
}
