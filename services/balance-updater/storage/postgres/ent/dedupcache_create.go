// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/dedupcache"
)

// DedupCacheCreate is the builder for creating a DedupCache entity.
type DedupCacheCreate struct {
	config
	mutation *DedupCacheMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *DedupCacheCreate) SetCreateTime(v time.Time) *DedupCacheCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *DedupCacheCreate) SetNillableCreateTime(v *time.Time) *DedupCacheCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *DedupCacheCreate) SetUpdateTime(v time.Time) *DedupCacheCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *DedupCacheCreate) SetNillableUpdateTime(v *time.Time) *DedupCacheCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetEventID sets the "event_id" field.
func (_c *DedupCacheCreate) SetEventID(v string) *DedupCacheCreate {
	_c.mutation.SetEventID(v)
	return _c
}

// SetDieTime sets the "die_time" field.
func (_c *DedupCacheCreate) SetDieTime(v time.Time) *DedupCacheCreate {
	_c.mutation.SetDieTime(v)
	return _c
}

// SetNillableDieTime sets the "die_time" field if the given value is not nil.
func (_c *DedupCacheCreate) SetNillableDieTime(v *time.Time) *DedupCacheCreate {
	if v != nil {
		_c.SetDieTime(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *DedupCacheCreate) SetID(v uuid.UUID) *DedupCacheCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *DedupCacheCreate) SetNillableID(v *uuid.UUID) *DedupCacheCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// Mutation returns the DedupCacheMutation object of the builder.
func (_c *DedupCacheCreate) Mutation() *DedupCacheMutation {
	return _c.mutation
}

// Save creates the DedupCache in the database.
func (_c *DedupCacheCreate) Save(ctx context.Context) (*DedupCache, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *DedupCacheCreate) SaveX(ctx context.Context) *DedupCache {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DedupCacheCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DedupCacheCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *DedupCacheCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := dedupcache.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := dedupcache.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.DieTime(); !ok {
		v := dedupcache.DefaultDieTime()
		_c.mutation.SetDieTime(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := dedupcache.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *DedupCacheCreate) check() error {
	if _, ok := _c.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "DedupCache.create_time"`)}
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "DedupCache.update_time"`)}
	}
	if _, ok := _c.mutation.EventID(); !ok {
		return &ValidationError{Name: "event_id", err: errors.New(`ent: missing required field "DedupCache.event_id"`)}
	}
	if v, ok := _c.mutation.EventID(); ok {
		if err := dedupcache.EventIDValidator(v); err != nil {
			return &ValidationError{Name: "event_id", err: fmt.Errorf(`ent: validator failed for field "DedupCache.event_id": %w`, err)}
		}
	}
	if _, ok := _c.mutation.DieTime(); !ok {
		return &ValidationError{Name: "die_time", err: errors.New(`ent: missing required field "DedupCache.die_time"`)}
	}
	return nil
}

func (_c *DedupCacheCreate) sqlSave(ctx context.Context) (*DedupCache, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *DedupCacheCreate) createSpec() (*DedupCache, *sqlgraph.CreateSpec) {
	var (
		_node = &DedupCache{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(dedupcache.Table, sqlgraph.NewFieldSpec(dedupcache.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(dedupcache.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(dedupcache.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.EventID(); ok {
		_spec.SetField(dedupcache.FieldEventID, field.TypeString, value)
		_node.EventID = value
	}
	if value, ok := _c.mutation.DieTime(); ok {
		_spec.SetField(dedupcache.FieldDieTime, field.TypeTime, value)
		_node.DieTime = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DedupCache.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DedupCacheUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *DedupCacheCreate) OnConflict(opts ...sql.ConflictOption) *DedupCacheUpsertOne {
	_c.conflict = opts
	return &DedupCacheUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DedupCache.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DedupCacheCreate) OnConflictColumns(columns ...string) *DedupCacheUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DedupCacheUpsertOne{
		create: _c,
	}
}

type (
	// DedupCacheUpsertOne is the builder for "upsert"-ing
	//  one DedupCache node.
	DedupCacheUpsertOne struct {
		create *DedupCacheCreate
	}

	// DedupCacheUpsert is the "OnConflict" setter.
	DedupCacheUpsert struct {
		*sql.UpdateSet
	}
)

// SetEventID sets the "event_id" field.
func (u *DedupCacheUpsert) SetEventID(v string) *DedupCacheUpsert {
	u.Set(dedupcache.FieldEventID, v)
	return u
}

// UpdateEventID sets the "event_id" field to the value that was provided on create.
func (u *DedupCacheUpsert) UpdateEventID() *DedupCacheUpsert {
	u.SetExcluded(dedupcache.FieldEventID)
	return u
}

// SetDieTime sets the "die_time" field.
func (u *DedupCacheUpsert) SetDieTime(v time.Time) *DedupCacheUpsert {
	u.Set(dedupcache.FieldDieTime, v)
	return u
}

// UpdateDieTime sets the "die_time" field to the value that was provided on create.
func (u *DedupCacheUpsert) UpdateDieTime() *DedupCacheUpsert {
	u.SetExcluded(dedupcache.FieldDieTime)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.DedupCache.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(dedupcache.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DedupCacheUpsertOne) UpdateNewValues() *DedupCacheUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(dedupcache.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(dedupcache.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(dedupcache.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DedupCache.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *DedupCacheUpsertOne) Ignore() *DedupCacheUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DedupCacheUpsertOne) DoNothing() *DedupCacheUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DedupCacheCreate.OnConflict
// documentation for more info.
func (u *DedupCacheUpsertOne) Update(set func(*DedupCacheUpsert)) *DedupCacheUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DedupCacheUpsert{UpdateSet: update})
	}))
	return u
}

// SetEventID sets the "event_id" field.
func (u *DedupCacheUpsertOne) SetEventID(v string) *DedupCacheUpsertOne {
	return u.Update(func(s *DedupCacheUpsert) {
		s.SetEventID(v)
	})
}

// UpdateEventID sets the "event_id" field to the value that was provided on create.
func (u *DedupCacheUpsertOne) UpdateEventID() *DedupCacheUpsertOne {
	return u.Update(func(s *DedupCacheUpsert) {
		s.UpdateEventID()
	})
}

// SetDieTime sets the "die_time" field.
func (u *DedupCacheUpsertOne) SetDieTime(v time.Time) *DedupCacheUpsertOne {
	return u.Update(func(s *DedupCacheUpsert) {
		s.SetDieTime(v)
	})
}

// UpdateDieTime sets the "die_time" field to the value that was provided on create.
func (u *DedupCacheUpsertOne) UpdateDieTime() *DedupCacheUpsertOne {
	return u.Update(func(s *DedupCacheUpsert) {
		s.UpdateDieTime()
	})
}

// Exec executes the query.
func (u *DedupCacheUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DedupCacheCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DedupCacheUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *DedupCacheUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: DedupCacheUpsertOne.ID is not supported by MySQL driver. Use DedupCacheUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *DedupCacheUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// DedupCacheCreateBulk is the builder for creating many DedupCache entities in bulk.
type DedupCacheCreateBulk struct {
	config
	err      error
	builders []*DedupCacheCreate
	conflict []sql.ConflictOption
}

// Save creates the DedupCache entities in the database.
func (_c *DedupCacheCreateBulk) Save(ctx context.Context) ([]*DedupCache, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*DedupCache, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DedupCacheMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *DedupCacheCreateBulk) SaveX(ctx context.Context) []*DedupCache {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DedupCacheCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DedupCacheCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DedupCache.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DedupCacheUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *DedupCacheCreateBulk) OnConflict(opts ...sql.ConflictOption) *DedupCacheUpsertBulk {
	_c.conflict = opts
	return &DedupCacheUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DedupCache.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DedupCacheCreateBulk) OnConflictColumns(columns ...string) *DedupCacheUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DedupCacheUpsertBulk{
		create: _c,
	}
}

// DedupCacheUpsertBulk is the builder for "upsert"-ing
// a bulk of DedupCache nodes.
type DedupCacheUpsertBulk struct {
	create *DedupCacheCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.DedupCache.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(dedupcache.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DedupCacheUpsertBulk) UpdateNewValues() *DedupCacheUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(dedupcache.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(dedupcache.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(dedupcache.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DedupCache.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *DedupCacheUpsertBulk) Ignore() *DedupCacheUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DedupCacheUpsertBulk) DoNothing() *DedupCacheUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DedupCacheCreateBulk.OnConflict
// documentation for more info.
func (u *DedupCacheUpsertBulk) Update(set func(*DedupCacheUpsert)) *DedupCacheUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DedupCacheUpsert{UpdateSet: update})
	}))
	return u
}

// SetEventID sets the "event_id" field.
func (u *DedupCacheUpsertBulk) SetEventID(v string) *DedupCacheUpsertBulk {
	return u.Update(func(s *DedupCacheUpsert) {
		s.SetEventID(v)
	})
}

// UpdateEventID sets the "event_id" field to the value that was provided on create.
func (u *DedupCacheUpsertBulk) UpdateEventID() *DedupCacheUpsertBulk {
	return u.Update(func(s *DedupCacheUpsert) {
		s.UpdateEventID()
	})
}

// SetDieTime sets the "die_time" field.
func (u *DedupCacheUpsertBulk) SetDieTime(v time.Time) *DedupCacheUpsertBulk {
	return u.Update(func(s *DedupCacheUpsert) {
		s.SetDieTime(v)
	})
}

// UpdateDieTime sets the "die_time" field to the value that was provided on create.
func (u *DedupCacheUpsertBulk) UpdateDieTime() *DedupCacheUpsertBulk {
	return u.Update(func(s *DedupCacheUpsert) {
		s.UpdateDieTime()
	})
}

// Exec executes the query.
func (u *DedupCacheUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the DedupCacheCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DedupCacheCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DedupCacheUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
