// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/dedupcache"
)

// Таблица для дедупликации событий с hot/cold cache архитектурой
type DedupCache struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// Уникальный идентификатор события для дедупликации
	EventID string `json:"event_id,omitempty"`
	// Время истечения записи в кеше дедупликации
	DieTime      time.Time `json:"die_time,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DedupCache) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case dedupcache.FieldEventID:
			values[i] = new(sql.NullString)
		case dedupcache.FieldCreateTime, dedupcache.FieldUpdateTime, dedupcache.FieldDieTime:
			values[i] = new(sql.NullTime)
		case dedupcache.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DedupCache fields.
func (_m *DedupCache) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case dedupcache.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case dedupcache.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case dedupcache.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case dedupcache.FieldEventID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field event_id", values[i])
			} else if value.Valid {
				_m.EventID = value.String
			}
		case dedupcache.FieldDieTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field die_time", values[i])
			} else if value.Valid {
				_m.DieTime = value.Time
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DedupCache.
// This includes values selected through modifiers, order, etc.
func (_m *DedupCache) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this DedupCache.
// Note that you need to call DedupCache.Unwrap() before calling this method if this DedupCache
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *DedupCache) Update() *DedupCacheUpdateOne {
	return NewDedupCacheClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the DedupCache entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *DedupCache) Unwrap() *DedupCache {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: DedupCache is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *DedupCache) String() string {
	var builder strings.Builder
	builder.WriteString("DedupCache(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("event_id=")
	builder.WriteString(_m.EventID)
	builder.WriteString(", ")
	builder.WriteString("die_time=")
	builder.WriteString(_m.DieTime.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// DedupCaches is a parsable slice of DedupCache.
type DedupCaches []*DedupCache
