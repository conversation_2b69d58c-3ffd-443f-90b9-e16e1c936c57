// Code generated by ent, DO NOT EDIT.

package dedupcache

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the dedupcache type in the database.
	Label = "dedup_cache"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldEventID holds the string denoting the event_id field in the database.
	FieldEventID = "event_id"
	// FieldDieTime holds the string denoting the die_time field in the database.
	FieldDieTime = "die_time"
	// Table holds the table name of the dedupcache in the database.
	Table = "dedup_caches"
)

// Columns holds all SQL columns for dedupcache fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldEventID,
	FieldDieTime,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// EventIDValidator is a validator for the "event_id" field. It is called by the builders before save.
	EventIDValidator func(string) error
	// DefaultDieTime holds the default value on creation for the "die_time" field.
	DefaultDieTime func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the DedupCache queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByEventID orders the results by the event_id field.
func ByEventID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEventID, opts...).ToFunc()
}

// ByDieTime orders the results by the die_time field.
func ByDieTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDieTime, opts...).ToFunc()
}
