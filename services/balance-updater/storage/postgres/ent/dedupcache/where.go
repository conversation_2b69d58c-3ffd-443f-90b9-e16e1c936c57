// Code generated by ent, DO NOT EDIT.

package dedupcache

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldUpdateTime, v))
}

// EventID applies equality check predicate on the "event_id" field. It's identical to EventIDEQ.
func EventID(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldEventID, v))
}

// DieTime applies equality check predicate on the "die_time" field. It's identical to DieTimeEQ.
func DieTime(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldDieTime, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLTE(FieldUpdateTime, v))
}

// EventIDEQ applies the EQ predicate on the "event_id" field.
func EventIDEQ(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldEventID, v))
}

// EventIDNEQ applies the NEQ predicate on the "event_id" field.
func EventIDNEQ(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNEQ(FieldEventID, v))
}

// EventIDIn applies the In predicate on the "event_id" field.
func EventIDIn(vs ...string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldIn(FieldEventID, vs...))
}

// EventIDNotIn applies the NotIn predicate on the "event_id" field.
func EventIDNotIn(vs ...string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNotIn(FieldEventID, vs...))
}

// EventIDGT applies the GT predicate on the "event_id" field.
func EventIDGT(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGT(FieldEventID, v))
}

// EventIDGTE applies the GTE predicate on the "event_id" field.
func EventIDGTE(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGTE(FieldEventID, v))
}

// EventIDLT applies the LT predicate on the "event_id" field.
func EventIDLT(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLT(FieldEventID, v))
}

// EventIDLTE applies the LTE predicate on the "event_id" field.
func EventIDLTE(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLTE(FieldEventID, v))
}

// EventIDContains applies the Contains predicate on the "event_id" field.
func EventIDContains(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldContains(FieldEventID, v))
}

// EventIDHasPrefix applies the HasPrefix predicate on the "event_id" field.
func EventIDHasPrefix(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldHasPrefix(FieldEventID, v))
}

// EventIDHasSuffix applies the HasSuffix predicate on the "event_id" field.
func EventIDHasSuffix(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldHasSuffix(FieldEventID, v))
}

// EventIDEqualFold applies the EqualFold predicate on the "event_id" field.
func EventIDEqualFold(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEqualFold(FieldEventID, v))
}

// EventIDContainsFold applies the ContainsFold predicate on the "event_id" field.
func EventIDContainsFold(v string) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldContainsFold(FieldEventID, v))
}

// DieTimeEQ applies the EQ predicate on the "die_time" field.
func DieTimeEQ(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldEQ(FieldDieTime, v))
}

// DieTimeNEQ applies the NEQ predicate on the "die_time" field.
func DieTimeNEQ(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNEQ(FieldDieTime, v))
}

// DieTimeIn applies the In predicate on the "die_time" field.
func DieTimeIn(vs ...time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldIn(FieldDieTime, vs...))
}

// DieTimeNotIn applies the NotIn predicate on the "die_time" field.
func DieTimeNotIn(vs ...time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldNotIn(FieldDieTime, vs...))
}

// DieTimeGT applies the GT predicate on the "die_time" field.
func DieTimeGT(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGT(FieldDieTime, v))
}

// DieTimeGTE applies the GTE predicate on the "die_time" field.
func DieTimeGTE(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldGTE(FieldDieTime, v))
}

// DieTimeLT applies the LT predicate on the "die_time" field.
func DieTimeLT(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLT(FieldDieTime, v))
}

// DieTimeLTE applies the LTE predicate on the "die_time" field.
func DieTimeLTE(v time.Time) predicate.DedupCache {
	return predicate.DedupCache(sql.FieldLTE(FieldDieTime, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DedupCache) predicate.DedupCache {
	return predicate.DedupCache(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DedupCache) predicate.DedupCache {
	return predicate.DedupCache(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DedupCache) predicate.DedupCache {
	return predicate.DedupCache(sql.NotPredicates(p))
}
