// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/dedupcache"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/predicate"
)

// DedupCacheUpdate is the builder for updating DedupCache entities.
type DedupCacheUpdate struct {
	config
	hooks     []Hook
	mutation  *DedupCacheMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DedupCacheUpdate builder.
func (_u *DedupCacheUpdate) Where(ps ...predicate.DedupCache) *DedupCacheUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetEventID sets the "event_id" field.
func (_u *DedupCacheUpdate) SetEventID(v string) *DedupCacheUpdate {
	_u.mutation.SetEventID(v)
	return _u
}

// SetNillableEventID sets the "event_id" field if the given value is not nil.
func (_u *DedupCacheUpdate) SetNillableEventID(v *string) *DedupCacheUpdate {
	if v != nil {
		_u.SetEventID(*v)
	}
	return _u
}

// SetDieTime sets the "die_time" field.
func (_u *DedupCacheUpdate) SetDieTime(v time.Time) *DedupCacheUpdate {
	_u.mutation.SetDieTime(v)
	return _u
}

// SetNillableDieTime sets the "die_time" field if the given value is not nil.
func (_u *DedupCacheUpdate) SetNillableDieTime(v *time.Time) *DedupCacheUpdate {
	if v != nil {
		_u.SetDieTime(*v)
	}
	return _u
}

// Mutation returns the DedupCacheMutation object of the builder.
func (_u *DedupCacheUpdate) Mutation() *DedupCacheMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *DedupCacheUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DedupCacheUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *DedupCacheUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DedupCacheUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DedupCacheUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := dedupcache.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *DedupCacheUpdate) check() error {
	if v, ok := _u.mutation.EventID(); ok {
		if err := dedupcache.EventIDValidator(v); err != nil {
			return &ValidationError{Name: "event_id", err: fmt.Errorf(`ent: validator failed for field "DedupCache.event_id": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DedupCacheUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DedupCacheUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DedupCacheUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(dedupcache.Table, dedupcache.Columns, sqlgraph.NewFieldSpec(dedupcache.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(dedupcache.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.EventID(); ok {
		_spec.SetField(dedupcache.FieldEventID, field.TypeString, value)
	}
	if value, ok := _u.mutation.DieTime(); ok {
		_spec.SetField(dedupcache.FieldDieTime, field.TypeTime, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{dedupcache.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// DedupCacheUpdateOne is the builder for updating a single DedupCache entity.
type DedupCacheUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DedupCacheMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetEventID sets the "event_id" field.
func (_u *DedupCacheUpdateOne) SetEventID(v string) *DedupCacheUpdateOne {
	_u.mutation.SetEventID(v)
	return _u
}

// SetNillableEventID sets the "event_id" field if the given value is not nil.
func (_u *DedupCacheUpdateOne) SetNillableEventID(v *string) *DedupCacheUpdateOne {
	if v != nil {
		_u.SetEventID(*v)
	}
	return _u
}

// SetDieTime sets the "die_time" field.
func (_u *DedupCacheUpdateOne) SetDieTime(v time.Time) *DedupCacheUpdateOne {
	_u.mutation.SetDieTime(v)
	return _u
}

// SetNillableDieTime sets the "die_time" field if the given value is not nil.
func (_u *DedupCacheUpdateOne) SetNillableDieTime(v *time.Time) *DedupCacheUpdateOne {
	if v != nil {
		_u.SetDieTime(*v)
	}
	return _u
}

// Mutation returns the DedupCacheMutation object of the builder.
func (_u *DedupCacheUpdateOne) Mutation() *DedupCacheMutation {
	return _u.mutation
}

// Where appends a list predicates to the DedupCacheUpdate builder.
func (_u *DedupCacheUpdateOne) Where(ps ...predicate.DedupCache) *DedupCacheUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *DedupCacheUpdateOne) Select(field string, fields ...string) *DedupCacheUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated DedupCache entity.
func (_u *DedupCacheUpdateOne) Save(ctx context.Context) (*DedupCache, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DedupCacheUpdateOne) SaveX(ctx context.Context) *DedupCache {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *DedupCacheUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DedupCacheUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DedupCacheUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := dedupcache.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *DedupCacheUpdateOne) check() error {
	if v, ok := _u.mutation.EventID(); ok {
		if err := dedupcache.EventIDValidator(v); err != nil {
			return &ValidationError{Name: "event_id", err: fmt.Errorf(`ent: validator failed for field "DedupCache.event_id": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DedupCacheUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DedupCacheUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DedupCacheUpdateOne) sqlSave(ctx context.Context) (_node *DedupCache, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(dedupcache.Table, dedupcache.Columns, sqlgraph.NewFieldSpec(dedupcache.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DedupCache.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, dedupcache.FieldID)
		for _, f := range fields {
			if !dedupcache.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != dedupcache.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(dedupcache.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.EventID(); ok {
		_spec.SetField(dedupcache.FieldEventID, field.TypeString, value)
	}
	if value, ok := _u.mutation.DieTime(); ok {
		_spec.SetField(dedupcache.FieldDieTime, field.TypeTime, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &DedupCache{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{dedupcache.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
