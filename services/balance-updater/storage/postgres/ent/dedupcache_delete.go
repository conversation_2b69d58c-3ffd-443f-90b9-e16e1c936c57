// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/dedupcache"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/predicate"
)

// DedupCacheDelete is the builder for deleting a DedupCache entity.
type DedupCacheDelete struct {
	config
	hooks    []Hook
	mutation *DedupCacheMutation
}

// Where appends a list predicates to the DedupCacheDelete builder.
func (_d *DedupCacheDelete) Where(ps ...predicate.DedupCache) *DedupCacheDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *DedupCacheDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *DedupCacheDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *DedupCacheDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(dedupcache.Table, sqlgraph.NewFieldSpec(dedupcache.FieldID, field.TypeUUID))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// DedupCacheDeleteOne is the builder for deleting a single DedupCache entity.
type DedupCacheDeleteOne struct {
	_d *DedupCacheDelete
}

// Where appends a list predicates to the DedupCacheDelete builder.
func (_d *DedupCacheDeleteOne) Where(ps ...predicate.DedupCache) *DedupCacheDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *DedupCacheDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{dedupcache.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *DedupCacheDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
