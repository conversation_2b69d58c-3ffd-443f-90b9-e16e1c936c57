// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/dedupcache"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	dedupcacheMixin := schema.DedupCache{}.Mixin()
	dedupcacheMixinFields0 := dedupcacheMixin[0].Fields()
	_ = dedupcacheMixinFields0
	dedupcacheFields := schema.DedupCache{}.Fields()
	_ = dedupcacheFields
	// dedupcacheDescCreateTime is the schema descriptor for create_time field.
	dedupcacheDescCreateTime := dedupcacheMixinFields0[0].Descriptor()
	// dedupcache.DefaultCreateTime holds the default value on creation for the create_time field.
	dedupcache.DefaultCreateTime = dedupcacheDescCreateTime.Default.(func() time.Time)
	// dedupcacheDescUpdateTime is the schema descriptor for update_time field.
	dedupcacheDescUpdateTime := dedupcacheMixinFields0[1].Descriptor()
	// dedupcache.DefaultUpdateTime holds the default value on creation for the update_time field.
	dedupcache.DefaultUpdateTime = dedupcacheDescUpdateTime.Default.(func() time.Time)
	// dedupcache.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	dedupcache.UpdateDefaultUpdateTime = dedupcacheDescUpdateTime.UpdateDefault.(func() time.Time)
	// dedupcacheDescEventID is the schema descriptor for event_id field.
	dedupcacheDescEventID := dedupcacheFields[1].Descriptor()
	// dedupcache.EventIDValidator is a validator for the "event_id" field. It is called by the builders before save.
	dedupcache.EventIDValidator = dedupcacheDescEventID.Validators[0].(func(string) error)
	// dedupcacheDescDieTime is the schema descriptor for die_time field.
	dedupcacheDescDieTime := dedupcacheFields[2].Descriptor()
	// dedupcache.DefaultDieTime holds the default value on creation for the die_time field.
	dedupcache.DefaultDieTime = dedupcacheDescDieTime.Default.(func() time.Time)
	// dedupcacheDescID is the schema descriptor for id field.
	dedupcacheDescID := dedupcacheFields[0].Descriptor()
	// dedupcache.DefaultID holds the default value on creation for the id field.
	dedupcache.DefaultID = dedupcacheDescID.Default.(func() uuid.UUID)
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
}
