// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/dedupcache"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/predicate"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeDedupCache = "DedupCache"
	TypeHealth     = "Health"
)

// DedupCacheMutation represents an operation that mutates the DedupCache nodes in the graph.
type DedupCacheMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	create_time   *time.Time
	update_time   *time.Time
	event_id      *string
	die_time      *time.Time
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*DedupCache, error)
	predicates    []predicate.DedupCache
}

var _ ent.Mutation = (*DedupCacheMutation)(nil)

// dedupcacheOption allows management of the mutation configuration using functional options.
type dedupcacheOption func(*DedupCacheMutation)

// newDedupCacheMutation creates new mutation for the DedupCache entity.
func newDedupCacheMutation(c config, op Op, opts ...dedupcacheOption) *DedupCacheMutation {
	m := &DedupCacheMutation{
		config:        c,
		op:            op,
		typ:           TypeDedupCache,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDedupCacheID sets the ID field of the mutation.
func withDedupCacheID(id uuid.UUID) dedupcacheOption {
	return func(m *DedupCacheMutation) {
		var (
			err   error
			once  sync.Once
			value *DedupCache
		)
		m.oldValue = func(ctx context.Context) (*DedupCache, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().DedupCache.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withDedupCache sets the old DedupCache of the mutation.
func withDedupCache(node *DedupCache) dedupcacheOption {
	return func(m *DedupCacheMutation) {
		m.oldValue = func(context.Context) (*DedupCache, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DedupCacheMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DedupCacheMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of DedupCache entities.
func (m *DedupCacheMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DedupCacheMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DedupCacheMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().DedupCache.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *DedupCacheMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *DedupCacheMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the DedupCache entity.
// If the DedupCache object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DedupCacheMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *DedupCacheMutation) ResetCreateTime() {
	m.create_time = nil
}

// SetUpdateTime sets the "update_time" field.
func (m *DedupCacheMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *DedupCacheMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the DedupCache entity.
// If the DedupCache object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DedupCacheMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *DedupCacheMutation) ResetUpdateTime() {
	m.update_time = nil
}

// SetEventID sets the "event_id" field.
func (m *DedupCacheMutation) SetEventID(s string) {
	m.event_id = &s
}

// EventID returns the value of the "event_id" field in the mutation.
func (m *DedupCacheMutation) EventID() (r string, exists bool) {
	v := m.event_id
	if v == nil {
		return
	}
	return *v, true
}

// OldEventID returns the old "event_id" field's value of the DedupCache entity.
// If the DedupCache object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DedupCacheMutation) OldEventID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEventID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEventID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEventID: %w", err)
	}
	return oldValue.EventID, nil
}

// ResetEventID resets all changes to the "event_id" field.
func (m *DedupCacheMutation) ResetEventID() {
	m.event_id = nil
}

// SetDieTime sets the "die_time" field.
func (m *DedupCacheMutation) SetDieTime(t time.Time) {
	m.die_time = &t
}

// DieTime returns the value of the "die_time" field in the mutation.
func (m *DedupCacheMutation) DieTime() (r time.Time, exists bool) {
	v := m.die_time
	if v == nil {
		return
	}
	return *v, true
}

// OldDieTime returns the old "die_time" field's value of the DedupCache entity.
// If the DedupCache object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DedupCacheMutation) OldDieTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDieTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDieTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDieTime: %w", err)
	}
	return oldValue.DieTime, nil
}

// ResetDieTime resets all changes to the "die_time" field.
func (m *DedupCacheMutation) ResetDieTime() {
	m.die_time = nil
}

// Where appends a list predicates to the DedupCacheMutation builder.
func (m *DedupCacheMutation) Where(ps ...predicate.DedupCache) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DedupCacheMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DedupCacheMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.DedupCache, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DedupCacheMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DedupCacheMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (DedupCache).
func (m *DedupCacheMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DedupCacheMutation) Fields() []string {
	fields := make([]string, 0, 4)
	if m.create_time != nil {
		fields = append(fields, dedupcache.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, dedupcache.FieldUpdateTime)
	}
	if m.event_id != nil {
		fields = append(fields, dedupcache.FieldEventID)
	}
	if m.die_time != nil {
		fields = append(fields, dedupcache.FieldDieTime)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DedupCacheMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case dedupcache.FieldCreateTime:
		return m.CreateTime()
	case dedupcache.FieldUpdateTime:
		return m.UpdateTime()
	case dedupcache.FieldEventID:
		return m.EventID()
	case dedupcache.FieldDieTime:
		return m.DieTime()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DedupCacheMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case dedupcache.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case dedupcache.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case dedupcache.FieldEventID:
		return m.OldEventID(ctx)
	case dedupcache.FieldDieTime:
		return m.OldDieTime(ctx)
	}
	return nil, fmt.Errorf("unknown DedupCache field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DedupCacheMutation) SetField(name string, value ent.Value) error {
	switch name {
	case dedupcache.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case dedupcache.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case dedupcache.FieldEventID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEventID(v)
		return nil
	case dedupcache.FieldDieTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDieTime(v)
		return nil
	}
	return fmt.Errorf("unknown DedupCache field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DedupCacheMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DedupCacheMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DedupCacheMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown DedupCache numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DedupCacheMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DedupCacheMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DedupCacheMutation) ClearField(name string) error {
	return fmt.Errorf("unknown DedupCache nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DedupCacheMutation) ResetField(name string) error {
	switch name {
	case dedupcache.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case dedupcache.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case dedupcache.FieldEventID:
		m.ResetEventID()
		return nil
	case dedupcache.FieldDieTime:
		m.ResetDieTime()
		return nil
	}
	return fmt.Errorf("unknown DedupCache field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DedupCacheMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DedupCacheMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DedupCacheMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DedupCacheMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DedupCacheMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DedupCacheMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DedupCacheMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown DedupCache unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DedupCacheMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown DedupCache edge %s", name)
}

// HealthMutation represents an operation that mutates the Health nodes in the graph.
type HealthMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Health, error)
	predicates    []predicate.Health
}

var _ ent.Mutation = (*HealthMutation)(nil)

// healthOption allows management of the mutation configuration using functional options.
type healthOption func(*HealthMutation)

// newHealthMutation creates new mutation for the Health entity.
func newHealthMutation(c config, op Op, opts ...healthOption) *HealthMutation {
	m := &HealthMutation{
		config:        c,
		op:            op,
		typ:           TypeHealth,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withHealthID sets the ID field of the mutation.
func withHealthID(id uuid.UUID) healthOption {
	return func(m *HealthMutation) {
		var (
			err   error
			once  sync.Once
			value *Health
		)
		m.oldValue = func(ctx context.Context) (*Health, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Health.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withHealth sets the old Health of the mutation.
func withHealth(node *Health) healthOption {
	return func(m *HealthMutation) {
		m.oldValue = func(context.Context) (*Health, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m HealthMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m HealthMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Health entities.
func (m *HealthMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *HealthMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *HealthMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Health.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the HealthMutation builder.
func (m *HealthMutation) Where(ps ...predicate.Health) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the HealthMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *HealthMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Health, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *HealthMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *HealthMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Health).
func (m *HealthMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *HealthMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *HealthMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *HealthMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Health field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *HealthMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *HealthMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Health numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *HealthMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *HealthMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *HealthMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Health nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *HealthMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *HealthMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *HealthMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *HealthMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *HealthMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *HealthMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *HealthMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *HealthMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Health unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *HealthMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Health edge %s", name)
}
