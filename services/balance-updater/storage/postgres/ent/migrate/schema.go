// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// DedupCachesColumns holds the columns for the "dedup_caches" table.
	DedupCachesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime},
		{Name: "update_time", Type: field.TypeTime},
		{Name: "event_id", Type: field.TypeString},
		{Name: "die_time", Type: field.TypeTime},
	}
	// DedupCachesTable holds the schema information for the "dedup_caches" table.
	DedupCachesTable = &schema.Table{
		Name:       "dedup_caches",
		Columns:    DedupCachesColumns,
		PrimaryKey: []*schema.Column{DedupCachesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "dedupcache_event_id",
				Unique:  true,
				Columns: []*schema.Column{DedupCachesColumns[3]},
			},
			{
				Name:    "dedupcache_die_time",
				Unique:  false,
				Columns: []*schema.Column{DedupCachesColumns[4]},
			},
			{
				Name:    "dedupcache_die_time_event_id",
				Unique:  false,
				Columns: []*schema.Column{DedupCachesColumns[4], DedupCachesColumns[3]},
			},
		},
	}
	// HealthsColumns holds the columns for the "healths" table.
	HealthsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
	}
	// HealthsTable holds the schema information for the "healths" table.
	HealthsTable = &schema.Table{
		Name:       "healths",
		Columns:    HealthsColumns,
		PrimaryKey: []*schema.Column{HealthsColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		DedupCachesTable,
		HealthsTable,
	}
)

func init() {
}
