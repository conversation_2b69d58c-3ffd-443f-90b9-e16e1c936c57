package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/gen"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/mixin"
	"github.com/google/uuid"
)

// DedupCache схема для дедупликации событий balance-updater
// Содержит кеш обработанных событий с TTL
type DedupCache struct {
	ent.Schema
}

// Fields определяет поля таблицы по требованиям Олега:
// - UUID (pk) + auto index
// - create_time, update_time (через mixin.Time)
// - die_time (время истечения) + index
// - event_id (идентификатор события для дедупликации)
func (DedupCache) Fields() []ent.Field {
	return []ent.Field{
		// UUID primary key с автоматической генерацией
		field.UUID("id", uuid.UUID{}).
			Default((func() uuid.UUID)(gen.UUID())),

		// Идентификатор события для дедупликации (например, colvirReferenceId)
		field.String("event_id").
			NotEmpty().
			Comment("Уникальный идентификатор события для дедупликации"),

		// Время истечения записи (TTL)
		field.Time("die_time").
			Default(func() time.Time {
				return time.Now().Add(24 * time.Hour) // TTL по умолчанию 24 часа
			}).
			Comment("Время истечения записи в кеше дедупликации"),
	}
}

// Mixin добавляет автоматические поля create_time и update_time
func (DedupCache) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{}, // Добавляет create_time и update_time автоматически
	}
}

// Indexes определяет индексы для оптимизации запросов
func (DedupCache) Indexes() []ent.Index {
	return []ent.Index{
		// Уникальный индекс по event_id для быстрого поиска дубликатов
		index.Fields("event_id").Unique(),

		// Индекс по die_time для эффективной очистки expired записей
		// ВАЖНО: по требованию Олега обязательно нужен индекс на die_time!
		index.Fields("die_time"),

		// Композитный индекс для оптимизации cleanup запросов
		index.Fields("die_time", "event_id"),
	}
}

// Annotations определяет дополнительные аннотации схемы
func (DedupCache) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Добавляем комментарий к таблице
		schema.Comment("Таблица для дедупликации событий с hot/cold cache архитектурой"),
	}
}
