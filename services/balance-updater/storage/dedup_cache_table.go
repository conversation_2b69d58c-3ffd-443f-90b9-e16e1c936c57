package storage

import (
	"context"
	"fmt"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/deduplicator"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage/postgres/ent/dedupcache"
)

// Проверяем что dedupCacheTable реализует интерфейс CacheTable
var _ deduplicator.CacheTable = (*dedupCacheTable)(nil)

// dedupCacheTable реализация интерфейса CacheTable для работы с таблицей dedup_cache
type dedupCacheTable struct {
	client *ent.Client
}

// NewDedupCacheTable создает новый экземпляр CacheTable для работы с БД
func NewDedupCacheTable(client *ent.Client) deduplicator.CacheTable {
	return &dedupCacheTable{
		client: client,
	}
}

// Create создает новую запись в БД
func (t *dedupCacheTable) Create(ctx context.Context, eventID string, dieTime time.Time) error {
	logger := logs.FromContext(ctx)

	_, err := t.client.DedupCache.
		Create().
		SetEventID(eventID).
		SetDieTime(dieTime).
		Save(ctx)
	if err != nil {
		logger.Error().Err(err).
			Str("event_id", eventID).
			Time("die_time", dieTime).
			Msg("Failed to create dedup cache entry")
		return fmt.Errorf("failed to create dedup cache entry: %w", err)
	}

	logger.Debug().
		Str("event_id", eventID).
		Time("die_time", dieTime).
		Msg("Created dedup cache entry")

	return nil
}

// Exists проверяет существование записи в БД
func (t *dedupCacheTable) Exists(ctx context.Context, eventID string) (bool, error) {
	logger := logs.FromContext(ctx)

	exists, err := t.client.DedupCache.
		Query().
		Where(
			dedupcache.EventIDEQ(eventID),
			dedupcache.DieTimeGT(time.Now()), // Только активные записи
		).
		Exist(ctx)
	if err != nil {
		logger.Error().Err(err).
			Str("event_id", eventID).
			Msg("Failed to check dedup cache existence")
		return false, fmt.Errorf("failed to check existence: %w", err)
	}

	logger.Debug().
		Str("event_id", eventID).
		Bool("exists", exists).
		Msg("Checked dedup cache existence")

	return exists, nil
}

// LoadActive загружает все активные записи из БД (где die_time > NOW())
func (t *dedupCacheTable) LoadActive(ctx context.Context, batchSize int) ([]deduplicator.CacheEntry, error) {
	logger := logs.FromContext(ctx)

	// Загружаем записи батчами
	var allEntries []deduplicator.CacheEntry
	offset := 0

	for {
		entries, err := t.client.DedupCache.
			Query().
			Where(dedupcache.DieTimeGT(time.Now())).
			Order(ent.Asc(dedupcache.FieldID)).
			Limit(batchSize).
			Offset(offset).
			All(ctx)
		if err != nil {
			logger.Error().Err(err).
				Int("offset", offset).
				Int("batch_size", batchSize).
				Msg("Failed to load active dedup cache entries")
			return nil, fmt.Errorf("failed to load active entries: %w", err)
		}

		if len(entries) == 0 {
			break // Больше записей нет
		}

		// Конвертируем в формат deduplicator.CacheEntry
		for _, entry := range entries {
			allEntries = append(allEntries, deduplicator.CacheEntry{
				EventID:   entry.EventID,
				DieTime:   entry.DieTime,
				CreatedAt: entry.CreateTime,
			})
		}

		offset += len(entries)

		// Если получили меньше чем batchSize, значит это последний батч
		if len(entries) < batchSize {
			break
		}
	}

	logger.Info().
		Int("total_loaded", len(allEntries)).
		Int("batch_size", batchSize).
		Msg("Loaded active dedup cache entries")

	return allEntries, nil
}

// DeleteExpired удаляет expired записи из БД в указанном временном окне
func (t *dedupCacheTable) DeleteExpired(ctx context.Context, windowStart, windowEnd time.Time) (int64, error) {
	logger := logs.FromContext(ctx)

	// Удаляем записи где die_time < NOW() и die_time в указанном окне
	deletedCount, err := t.client.DedupCache.
		Delete().
		Where(
			dedupcache.DieTimeLT(time.Now()),   // Expired записи
			dedupcache.DieTimeGTE(windowStart), // В окне поиска
			dedupcache.DieTimeLTE(windowEnd),   // В окне поиска
		).
		Exec(ctx)
	if err != nil {
		logger.Error().Err(err).
			Time("window_start", windowStart).
			Time("window_end", windowEnd).
			Msg("Failed to delete expired dedup cache entries")
		return 0, fmt.Errorf("failed to delete expired entries: %w", err)
	}

	logger.Info().
		Int("deleted_count", deletedCount).
		Time("window_start", windowStart).
		Time("window_end", windowEnd).
		Msg("Deleted expired dedup cache entries")

	return int64(deletedCount), nil
}
