// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/deduplicator"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// GetDedupCacheTable implements Storage
func (_w *StorageHook) GetDedupCacheTable() (c1 deduplicator.CacheTable) {
	_params := []any{}
	defer _w._onPanic.Hook(_w.Storage, "GetDedupCacheTable", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetDedupCacheTable", _params)

	c1 = _w.Storage.GetDedupCacheTable()
	_w._postCall.Hook(_ctx, _w.Storage, "GetDedupCacheTable", []any{c1})
	return c1
}

// GetNextRRNCounter implements Storage
func (_w *StorageHook) GetNextRRNCounter(ctx context.Context) (u1 uint64, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "GetNextRRNCounter", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetNextRRNCounter", _params)

	u1, err = _w.Storage.GetNextRRNCounter(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "GetNextRRNCounter", []any{u1, err})
	return u1, err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
