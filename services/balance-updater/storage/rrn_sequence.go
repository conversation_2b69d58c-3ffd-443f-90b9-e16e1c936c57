package storage

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
)

var _ RRNSequence = (*storageImpl)(nil)

type RRNSequence interface {
	GetNextRRNCounter(ctx context.Context) (uint64, error)
}

const (
	// CREATE SEQUENCE rrn_balance_updater_seq AS BIGINT;
	RRNCounterSequenceName = "rrn_balance_updater_seq"
)

// GetNextRRNCounter получает следующее значение из PostgreSQL sequence.
func (s *storageImpl) GetNextRRNCounter(ctx context.Context) (uint64, error) {
	logger := logs.FromContext(ctx)

	var counter uint64
	query := fmt.Sprintf("SELECT nextval('%s')", RRNCounterSequenceName)

	rows, err := s.SQLClient.QueryContext(ctx, query)
	if err != nil {
		return 0, fmt.Errorf("failed to query next counter from sequence %q: %w", RRNCounterSequenceName, err)
	}
	defer func() {
		if cerr := rows.Close(); cerr != nil {
			logger.Err(cerr).Msg("failed to close rows")
		}
	}()

	if !rows.Next() {
		if err := rows.Err(); err != nil {
			return 0, fmt.Errorf("rows iteration error for sequence %q: %w", RRNCounterSequenceName, err)
		}
		return 0, fmt.Errorf("no result returned for sequence %q", RRNCounterSequenceName)
	}

	if err := rows.Scan(&counter); err != nil {
		return 0, fmt.Errorf("failed to scan counter from sequence %q: %w", RRNCounterSequenceName, err)
	}

	return counter, nil
}
