package storage

import (
	"git.redmadrobot.com/zaman/backend/zaman/pkg/deduplicator"
)

// Проверяем что storageImpl реализует методы для дедупликации
var _ Storage = (*storageImpl)(nil)

// GetDedupCacheTable возвращает объект для работы с таблицей дедупликации
// Реализация интерфейса Storage для storageImpl
func (s *storageImpl) GetDedupCacheTable() deduplicator.CacheTable {
	return NewDedupCacheTable(s.PostgresClient)
}
