package metrics

import (
	"context"
	"sync"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
)

var (
	RetryCounter          metric.Int64Counter
	RetryExhaustedCounter metric.Int64Counter
	initOnce              sync.Once
)

func ensureInit(ctx context.Context) { initOnce.Do(func() { InitBalanceUpdateEvent(ctx) }) }

func InitBalanceUpdateEvent(ctx context.Context) {
	var err error
	RetryCounter, err = meter.Int64Counter(
		"balance_updater_retry_total",
		metric.WithDescription("Number of retries performed by balance-updater"),
	)
	if err != nil {
		_ = err
	}
	RetryExhaustedCounter, err = meter.Int64Counter(
		"balance_updater_retry_exhausted_total",
		metric.WithDescription("Number of times retry limit was reached by balance-updater"),
	)
	if err != nil {
		_ = err
	}
}

func IncRetry(ctx context.Context, op, class string) {
	ensureInit(ctx)
	if RetryCounter != nil {
		RetryCounter.Add(ctx, 1, metric.WithAttributes(attribute.String("op", op), attribute.String("class", class)))
	}
}

func IncRetryExhausted(ctx context.Context, op, class string) {
	ensureInit(ctx)
	if RetryExhaustedCounter != nil {
		RetryExhaustedCounter.Add(ctx, 1, metric.WithAttributes(attribute.String("op", op), attribute.String("class", class)))
	}
}
