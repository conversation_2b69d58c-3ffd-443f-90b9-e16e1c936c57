package metrics

import (
	"context"
	"sync"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
)

var (
	// Основные счетчики дедупликации
	DedupChecksCounter     metric.Int64Counter
	DedupDuplicatesCounter metric.Int64Counter
	DedupUniqueCounter     metric.Int64Counter

	// Метрики составного ключа
	DedupEmptyRefIDCounter   metric.Int64Counter
	DedupCompositeKeyCounter metric.Int64Counter
	DedupKeyGenerationErrors metric.Int64Counter

	// Производительность
	DedupCheckDuration metric.Float64Histogram
	DedupMarkDuration  metric.Float64Histogram

	// Мониторинг критических сценариев
	DedupRelatedOperationsCounter metric.Int64Counter
	DedupBypassedCounter          metric.Int64Counter

	dedupInitOnce sync.Once
)

func ensureDedupInit(ctx context.Context) { dedupInitOnce.Do(func() { InitDeduplication(ctx) }) }

func InitDeduplication(ctx context.Context) {
	var err error

	// Основные счетчики дедупликации
	DedupChecksCounter, err = meter.Int64Counter(
		"balance_updater_dedup_checks_total",
		metric.WithDescription("Total number of deduplication checks performed"),
	)
	if err != nil {
		_ = err
	}

	DedupDuplicatesCounter, err = meter.Int64Counter(
		"balance_updater_dedup_duplicates_total",
		metric.WithDescription("Total number of duplicate events detected"),
	)
	if err != nil {
		_ = err
	}

	DedupUniqueCounter, err = meter.Int64Counter(
		"balance_updater_dedup_unique_total",
		metric.WithDescription("Total number of unique events processed"),
	)
	if err != nil {
		_ = err
	}

	// Метрики составного ключа
	DedupEmptyRefIDCounter, err = meter.Int64Counter(
		"balance_updater_dedup_empty_ref_id_total",
		metric.WithDescription("Events with empty ColvirReferenceID"),
	)
	if err != nil {
		_ = err
	}

	DedupCompositeKeyCounter, err = meter.Int64Counter(
		"balance_updater_dedup_composite_key_generated_total",
		metric.WithDescription("Successfully generated composite deduplication keys"),
	)
	if err != nil {
		_ = err
	}

	DedupKeyGenerationErrors, err = meter.Int64Counter(
		"balance_updater_dedup_key_generation_errors_total",
		metric.WithDescription("Errors during deduplication key generation"),
	)
	if err != nil {
		_ = err
	}

	// Производительность
	DedupCheckDuration, err = meter.Float64Histogram(
		"balance_updater_dedup_check_duration_seconds",
		metric.WithDescription("Duration of deduplication check operations"),
		metric.WithUnit("s"),
	)
	if err != nil {
		_ = err
	}

	DedupMarkDuration, err = meter.Float64Histogram(
		"balance_updater_dedup_mark_duration_seconds",
		metric.WithDescription("Duration of mark as processed operations"),
		metric.WithUnit("s"),
	)
	if err != nil {
		_ = err
	}

	// Мониторинг критических сценариев
	DedupRelatedOperationsCounter, err = meter.Int64Counter(
		"balance_updater_dedup_related_operations_total",
		metric.WithDescription("Related operations (debit+credit) with same ColvirReferenceID"),
	)
	if err != nil {
		_ = err
	}

	DedupBypassedCounter, err = meter.Int64Counter(
		"balance_updater_dedup_bypassed_total",
		metric.WithDescription("Events processed without deduplication"),
	)
	if err != nil {
		_ = err
	}
}

// IncDedupChecks увеличивает счетчик проверок дедупликации
func IncDedupChecks(ctx context.Context, operationType string) {
	ensureDedupInit(ctx)
	if DedupChecksCounter != nil {
		DedupChecksCounter.Add(ctx, 1, metric.WithAttributes(
			attribute.String("operation_type", operationType),
		))
	}
}

// IncDedupDuplicates увеличивает счетчик найденных дубликатов
func IncDedupDuplicates(ctx context.Context, operationType string) {
	ensureDedupInit(ctx)
	if DedupDuplicatesCounter != nil {
		DedupDuplicatesCounter.Add(ctx, 1, metric.WithAttributes(
			attribute.String("operation_type", operationType),
		))
	}
}

// IncDedupUnique увеличивает счетчик уникальных событий
func IncDedupUnique(ctx context.Context, operationType string) {
	ensureDedupInit(ctx)
	if DedupUniqueCounter != nil {
		DedupUniqueCounter.Add(ctx, 1, metric.WithAttributes(
			attribute.String("operation_type", operationType),
		))
	}
}

// IncDedupEmptyRefID увеличивает счетчик событий с пустым ColvirReferenceID
func IncDedupEmptyRefID(ctx context.Context) {
	ensureDedupInit(ctx)
	if DedupEmptyRefIDCounter != nil {
		DedupEmptyRefIDCounter.Add(ctx, 1)
	}
}

// IncDedupCompositeKey увеличивает счетчик сгенерированных составных ключей
func IncDedupCompositeKey(ctx context.Context, operationType string) {
	ensureDedupInit(ctx)
	if DedupCompositeKeyCounter != nil {
		DedupCompositeKeyCounter.Add(ctx, 1, metric.WithAttributes(
			attribute.String("operation_type", operationType),
		))
	}
}

// IncDedupKeyGenerationErrors увеличивает счетчик ошибок генерации ключей
func IncDedupKeyGenerationErrors(ctx context.Context, reason string) {
	ensureDedupInit(ctx)
	if DedupKeyGenerationErrors != nil {
		DedupKeyGenerationErrors.Add(ctx, 1, metric.WithAttributes(
			attribute.String("reason", reason),
		))
	}
}

// RecordDedupCheckDuration записывает время проверки дедупликации
func RecordDedupCheckDuration(ctx context.Context, duration float64, operationType string) {
	ensureDedupInit(ctx)
	if DedupCheckDuration != nil {
		DedupCheckDuration.Record(ctx, duration, metric.WithAttributes(
			attribute.String("operation_type", operationType),
		))
	}
}

// RecordDedupMarkDuration записывает время маркировки как обработанного
func RecordDedupMarkDuration(ctx context.Context, duration float64, operationType string) {
	ensureDedupInit(ctx)
	if DedupMarkDuration != nil {
		DedupMarkDuration.Record(ctx, duration, metric.WithAttributes(
			attribute.String("operation_type", operationType),
		))
	}
}

// IncDedupRelatedOperations увеличивает счетчик связанных операций
func IncDedupRelatedOperations(ctx context.Context, refIDHash string) {
	ensureDedupInit(ctx)
	if DedupRelatedOperationsCounter != nil {
		DedupRelatedOperationsCounter.Add(ctx, 1, metric.WithAttributes(
			attribute.String("ref_id_hash", refIDHash),
		))
	}
}

// IncDedupBypassed увеличивает счетчик пропущенных дедупликаций
func IncDedupBypassed(ctx context.Context, reason string) {
	ensureDedupInit(ctx)
	if DedupBypassedCounter != nil {
		DedupBypassedCounter.Add(ctx, 1, metric.WithAttributes(
			attribute.String("reason", reason),
		))
	}
}
