package balanceupdater

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/deduplicator"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/balance-updater/storage"
)

// BalanceUpdaterDeduplicator создает настроенный deduplicator для balance-updater
func NewBalanceUpdaterDeduplicator(ctx context.Context, storage storage.Storage) (deduplicator.Deduplicator, error) {
	logger := logs.FromContext(ctx)

	// Получаем CacheTable из storage
	cacheTable := storage.GetDedupCacheTable()
	if cacheTable == nil {
		return nil, fmt.Errorf("cache table is nil")
	}

	// Создаем конфигурацию специфичную для balance-updater
	config := deduplicator.Config{
		TTL:                  consts.DeduplicationTTL,          // 24 часа
		CleanupInterval:      consts.CleanupInterval,           // 5 минут
		CleanupWindow:        consts.CleanupWindow,             // ±1 день
		MaxHotCacheSize:      consts.MaxHotCacheSize,           // 100k записей
		InitialLoadBatchSize: consts.InitialCacheLoadBatchSize, // 5k батч
		EnableMetrics:        true,                             // включаем метрики
	}

	// Создаем deduplicator с нашей конфигурацией
	dedup := deduplicator.NewDeduplicatorWithConfig(cacheTable, config)

	logger.Info().
		Dur("ttl", config.TTL).
		Dur("cleanup_interval", config.CleanupInterval).
		Int64("max_hot_cache_size", config.MaxHotCacheSize).
		Msg("Balance updater deduplicator created with configuration")

	return dedup, nil
}

// InitializeDeduplicator инициализирует deduplicator для balance-updater
// Вызывается при старте сервиса
func InitializeDeduplicator(ctx context.Context, dedup deduplicator.Deduplicator) error {
	logger := logs.FromContext(ctx)

	logger.Info().Msg("Initializing balance updater deduplicator...")

	// 1. Загружаем hot cache из БД
	if err := dedup.InitializeCache(ctx); err != nil {
		logger.Error().Err(err).Msg("Failed to initialize deduplicator cache")
		return fmt.Errorf("failed to initialize deduplicator cache: %w", err)
	}

	// 2. Запускаем фоновые задачи (cleanup worker через chronos)
	if err := dedup.Start(ctx); err != nil {
		logger.Error().Err(err).Msg("Failed to start deduplicator background tasks")
		return fmt.Errorf("failed to start deduplicator: %w", err)
	}

	logger.Info().Msg("Balance updater deduplicator initialized successfully")
	return nil
}

// IsDuplicateEvent проверяет является ли событие дубликатом
// Wrapper для использования в usecase
func IsDuplicateEvent(ctx context.Context, dedup deduplicator.Deduplicator, colvirReferenceID string) (bool, error) {
	logger := logs.FromContext(ctx)

	if colvirReferenceID == "" {
		logger.Warn().Msg("Empty colvirReferenceID provided for duplicate check")
		return false, fmt.Errorf("colvirReferenceID cannot be empty")
	}

	isDuplicate, err := dedup.IsDuplicate(ctx, colvirReferenceID)
	if err != nil {
		logger.Error().Err(err).
			Str("colvir_ref_id", colvirReferenceID).
			Msg("Failed to check if event is duplicate")
		return false, fmt.Errorf("failed to check duplicate: %w", err)
	}

	logger.Debug().
		Str("colvir_ref_id", colvirReferenceID).
		Bool("is_duplicate", isDuplicate).
		Msg("Checked event for duplication")

	return isDuplicate, nil
}

// MarkEventAsProcessed маркирует событие как обработанное
// Wrapper для использования в usecase
func MarkEventAsProcessed(ctx context.Context, dedup deduplicator.Deduplicator, colvirReferenceID string) error {
	logger := logs.FromContext(ctx)

	if colvirReferenceID == "" {
		logger.Warn().Msg("Empty colvirReferenceID provided for marking as processed")
		return fmt.Errorf("colvirReferenceID cannot be empty")
	}

	if err := dedup.MarkAsProcessed(ctx, colvirReferenceID); err != nil {
		logger.Error().Err(err).
			Str("colvir_ref_id", colvirReferenceID).
			Msg("Failed to mark event as processed")
		return fmt.Errorf("failed to mark as processed: %w", err)
	}

	logger.Debug().
		Str("colvir_ref_id", colvirReferenceID).
		Msg("Marked event as processed")

	return nil
}
