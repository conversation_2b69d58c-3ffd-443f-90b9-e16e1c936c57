package tests

import (
	"github.com/golang/mock/gomock"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

func (s *Suite) TestRevokeRefreshToken_Success() {
	s.mocks.Providers.KeycloakProvider.EXPECT().RevokeRefreshToken(gomock.Any(), realm, gomock.Any()).Return(nil).Times(1)

	resp, err := s.grpc.RevokeRefreshToken(s.ctx, &pb.RevokeRefreshTokenReq{
		Token: "test-refresh-token",
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().True(resp.Succeed)
}
