// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package tests

import (
	"encoding/json"
	"os"
	"path/filepath"
	"reflect"
)

// LoadJSONFixture loads a fixture from a JSON file, using the struct's name in lowercase as the filename.
func (s *Suite) LoadJSONFixture(structType interface{}) interface{} {
	s.T().Helper()

	// Get the name of the struct type using reflection
	typeName := reflect.TypeOf(structType).Name()

	// Convert the struct name and add the .json extension
	filename := typeName + ".json"

	// Get the current test name to construct the path to the fixture
	testName := s.getCurrentTestName()

	// Construct the path to the fixture file
	filePath := filepath.Join("./fixtures", testName, filename)

	// Open the file
	file, err := os.Open(filePath)
	if err != nil {
		s.T().Fatalf("Failed to open file %s: %v", filePath, err)
	}
	defer file.Close()

	// Create a new instance of the passed type using reflection
	fixture := reflect.New(reflect.TypeOf(structType)).Interface()

	// Read and decode the JSON into the created struct
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(fixture); err != nil {
		s.T().Fatalf("Failed to decode JSON from file %s: %v", filePath, err)
	}

	// Return the decoded struct
	return fixture
}
