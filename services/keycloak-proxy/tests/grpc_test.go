// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package tests

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	keycloakproxy "git.redmadrobot.com/zaman/backend/zaman/config/services/keycloak-proxy"
	pbKeycloakProxy "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"
)

type ConnectRetryConfig struct {
	MaxRetries int
	RetryDelay time.Duration
	Timeout    time.Duration
}

func DefaultRetryConfig() ConnectRetryConfig {
	return ConnectRetryConfig{
		MaxRetries: 3,
		RetryDelay: 100 * time.Millisecond,
		Timeout:    5 * time.Second,
	}
}

func ConnectWithRetries[T any](connectFunc func() (T, error), config ConnectRetryConfig) (T, error) {
	ctx, cancel := context.WithTimeout(context.Background(), config.Timeout)
	defer cancel()

	var result T
	var lastErr error
	for attempt := 0; attempt < config.MaxRetries; attempt++ {
		var err error
		result, err = connectFunc()
		if err == nil {
			return result, nil // connection established
		}
		lastErr = err

		select {
		case <-ctx.Done():
			return result, fmt.Errorf("connection timeout: %w", ctx.Err())
		case <-time.After(config.RetryDelay):
			// next try
		}
	}

	return result, fmt.Errorf("failed to connect after %d attempts: %w", config.MaxRetries, lastErr)
}

// CallGRPCMethod Wrapper for gRPC methods that auto-loads request and compares response.
func (s *Suite) CallGRPCMethod(method interface{}) (proto.Message, error) {
	methodValue := reflect.ValueOf(method)
	methodType := methodValue.Type()

	// Ensure the first argument is of type context.Context
	if methodType.NumIn() < 1 || !methodType.In(0).Implements(reflect.TypeOf((*context.Context)(nil)).Elem()) {
		s.T().Fatalf("First input must be context.Context, got %v", methodType.In(0))
	}

	// Prepare arguments: Always pass context
	args := []reflect.Value{reflect.ValueOf(s.ctx)}

	// Handle fixed arguments (non-variadic)
	numIn := methodType.NumIn()
	numFixedArgs := numIn
	if methodType.IsVariadic() {
		numFixedArgs = numIn - 1 // Exclude the variadic parameter
	}

	// Process fixed arguments
	for i := 1; i < numFixedArgs; i++ {
		argType := methodType.In(i)

		// If argument implements proto.Message, auto-load golden data
		if argType.Kind() == reflect.Ptr && reflect.PtrTo(argType.Elem()).Implements(reflect.TypeOf((*proto.Message)(nil)).Elem()) {
			reqInstance := reflect.New(argType.Elem()).Interface()
			req, ok := reqInstance.(proto.Message)
			if !ok {
				s.T().Fatalf("Argument %d does not implement proto.Message: %v", i, reqInstance)
			}

			// Load golden request automatically
			s.loadGolden(req)

			// Add the request as the next argument to the method call
			args = append(args, reflect.ValueOf(req))
		} else {
			// For other types, create zero value
			zeroValue := reflect.Zero(argType)
			args = append(args, zeroValue)
		}
	}

	// Handle variadic arguments
	if methodType.IsVariadic() {
		variadicType := methodType.In(numIn - 1).Elem()

		// Prepare variadic arguments - for now, we pass zero variadic arguments
		var variadicArgs []reflect.Value
		variadicSlice := reflect.MakeSlice(reflect.SliceOf(variadicType), len(variadicArgs), len(variadicArgs))
		for i, v := range variadicArgs {
			variadicSlice.Index(i).Set(v)
		}
		args = append(args, variadicSlice)

		// Call the method with variadic arguments
		results := methodValue.CallSlice(args)

		// Extract response and error
		respInstance := results[0].Interface()
		err, _ := results[1].Interface().(error)

		resp, ok := respInstance.(proto.Message)
		if !ok {
			s.T().Fatalf("Response object does not implement proto.Message: %v", respInstance)
		}

		// Compare the response automatically with golden data
		s.CompareGolden(resp)

		return resp, err
	} else {
		// Non-variadic method
		fmt.Printf("Calling method with %d arguments: %v\n", len(args), args)
		results := methodValue.Call(args)

		// Extract response and error
		respInstance := results[0].Interface()
		err, _ := results[1].Interface().(error)

		resp, ok := respInstance.(proto.Message)
		if !ok {
			s.T().Fatalf("Response object does not implement proto.Message: %v", respInstance)
		}

		// Compare the response automatically with golden data
		s.CompareGolden(resp)

		return resp, err
	}
}

// SetupGRPCClient Set up the gRPC client for communicating with the KeycloakProxy service
func (s *Suite) SetupGRPCClient(port string) {
	config, _ := keycloakproxy.LoadFromEnv()
	config.GRPC.Port = port

	keycloakProxyServer, err := ConnectWithRetries(func() (*grpc.ClientConn, error) {
		return grpcx.ConnectServer(config.GRPC)
	}, DefaultRetryConfig())

	if err != nil {
		s.dieOnErr(err)
	}

	s.T().Cleanup(func() {
		fmt.Printf("Close GRPC connect on port :%s\n", port)
		keycloakProxyServer.Close()
	})

	s.grpc = pbKeycloakProxy.NewKeycloakproxyClient(keycloakProxyServer)
	s.grpcServer = keycloakProxyServer
}
