package tests

import (
	"errors"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

var (
	testPhoneNumber = "+77770001122"
	testUserID      = "af502cfb-a1dc-4d73-9e9c-aa7babf3350b"
)

func (s *Suite) TestActivateUser_Success() {
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID, false, []string{
		keycloak.KeycloakRoleNotIdentified,
		keycloak.KeycloakRoleBlocked,
	}).Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID,
		true, keycloak.KeycloakRoleActive).Return(nil).Times(1)

	resp, err := s.grpc.ActivateUser(s.ctx, &pb.ActivateUserReq{
		Phone:  testPhoneNumber,
		UserID: testUserID,
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
}

func (s *Suite) TestActivateUser_KeycloakErr() {
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID, false, []string{
		keycloak.KeycloakRoleNotIdentified,
		keycloak.KeycloakRoleBlocked,
	}).Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID,
		true, keycloak.KeycloakRoleActive).Return(errors.New("some err")).Times(1)

	_, err := s.grpc.ActivateUser(s.ctx, &pb.ActivateUserReq{
		Phone:  testPhoneNumber,
		UserID: testUserID,
	})
	s.Require().NotNil(err)
}
