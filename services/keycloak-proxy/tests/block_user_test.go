package tests

import (
	"errors"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

func (s *Suite) TestBlockUser_Success() {
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID, false, []string{
		keycloak.KeycloakRoleNotIdentified,
		keycloak.KeycloakRoleActive,
	}).Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID,
		true, keycloak.KeycloakRoleBlocked).Return(nil).Times(1)

	resp, err := s.grpc.BlockUser(s.ctx, &pb.BlockUserReq{
		Phone:  testPhoneNumber,
		UserID: testUserID,
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
}

func (s *Suite) TestBlockUser_KeycloakErr() {
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID, false, []string{
		keycloak.KeycloakRoleNotIdentified,
		keycloak.KeycloakRoleActive,
	}).Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID,
		true, keycloak.KeycloakRoleBlocked).Return(errors.New("keycloak err")).Times(1)

	_, err := s.grpc.BlockUser(s.ctx, &pb.BlockUserReq{
		Phone:  testPhoneNumber,
		UserID: testUserID,
	})
	s.Require().NotNil(err)
}
