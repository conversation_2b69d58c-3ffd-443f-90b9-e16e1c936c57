package tests

import (
	"errors"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

const realm = "test"

func (s *Suite) TestCreateUser_Success() {
	newUserID := "fab3d426-99a3-4097-85f4-9f085c55b8df"
	s.mocks.Providers.KeycloakProvider.EXPECT().CreateUser(gomock.Any(), realm, testPhoneNumber).Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().GetUserByPhone(gomock.Any(), realm, testPhoneNumber).Return(keycloak.User{
		ID: newUserID,
	}, nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, newUserID, false, []string{
		keycloak.KeycloakRoleBlocked,
		keycloak.KeycloakRoleActive,
	}).
		Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, newUserID,
		true, keycloak.KeycloakRoleNotIdentified).Return(nil).Times(1)

	resp, err := s.grpc.CreateUser(s.ctx, &pb.CreateUserReq{Phone: testPhoneNumber})
	s.Require().NotNil(resp)
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.UserID)
	s.Require().True(resp.Succeed)
}

func (s *Suite) TestCreateUser_KeycloakCreateUserErr() {
	s.mocks.Providers.KeycloakProvider.EXPECT().CreateUser(gomock.Any(), realm, testPhoneNumber).
		Return(errors.New("keycloak user create err")).Times(1)

	resp, err := s.grpc.CreateUser(s.ctx, &pb.CreateUserReq{Phone: testPhoneNumber})
	s.Require().Nil(resp)
	s.Require().NotNil(err)
}

func (s *Suite) TestCreateUser_KeycloakGetUserErr() {
	s.mocks.Providers.KeycloakProvider.EXPECT().CreateUser(gomock.Any(), realm, testPhoneNumber).Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().GetUserByPhone(gomock.Any(), realm, testPhoneNumber).
		Return(keycloak.User{}, errors.New("keycloak ge user err")).Times(1)

	resp, err := s.grpc.CreateUser(s.ctx, &pb.CreateUserReq{Phone: testPhoneNumber})
	s.Require().Nil(resp)
	s.Require().NotNil(err)
}

func (s *Suite) TestCreateUser_KeycloakSetRoleErr() {
	newUserID := "fab3d426-99a3-4097-85f4-9f085c55b8df"
	s.mocks.Providers.KeycloakProvider.EXPECT().CreateUser(gomock.Any(), realm, testPhoneNumber).Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().GetUserByPhone(gomock.Any(), realm, testPhoneNumber).Return(keycloak.User{
		ID: newUserID,
	}, nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, newUserID, false, []string{
		keycloak.KeycloakRoleBlocked,
		keycloak.KeycloakRoleActive,
	}).
		Return(errors.New("keycloak set user role err")).Times(1)

	resp, err := s.grpc.CreateUser(s.ctx, &pb.CreateUserReq{Phone: testPhoneNumber})
	s.Require().Nil(resp)
	s.Require().NotNil(err)
}
