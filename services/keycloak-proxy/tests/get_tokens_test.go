package tests

import (
	"errors"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

func (s *Suite) TestGetTokens_Success() {
	s.mocks.Providers.KeycloakProvider.EXPECT().GetUserTokens(gomock.Any(), realm, testPhoneNumber).Return(keycloak.TokensResponse{
		AccessToken:  "some-access-token",
		RefreshToken: "some-refresh-token",
		SessionID:    uuid.New().String(),
	}, nil).Times(1)

	resp, err := s.grpc.GetTokens(s.ctx, &pb.GetTokensReq{
		Phone: testPhoneNumber,
	})
	s.Require().NotNil(resp)
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.RefreshToken)
	s.Require().NotEmpty(resp.AccessToken)
	s.Require().NotEmpty(resp.SessionID)
}

func (s *Suite) TestGetTokens_KeycloakTokensReqErr() {
	s.mocks.Providers.KeycloakProvider.EXPECT().GetUserTokens(gomock.Any(), realm, testPhoneNumber).
		Return(keycloak.TokensResponse{}, errors.New("keycloak tokens req err")).Times(1)

	resp, err := s.grpc.GetTokens(s.ctx, &pb.GetTokensReq{
		Phone: testPhoneNumber,
	})
	s.Require().Nil(resp)
	s.Require().NotNil(err)
}
