package tests

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

func (s *Suite) TestRefreshTokens_Success() {
	s.mocks.Providers.KeycloakProvider.EXPECT().RefreshUserTokens(gomock.Any(), realm, gomock.Any()).Return(keycloak.TokensResponse{
		AccessToken:  "new-access-token",
		RefreshToken: "new-refresh-token",
		SessionID:    uuid.New().String(),
	}, nil).Times(1)

	resp, err := s.grpc.RefreshTokens(s.ctx, &pb.RefreshTokensReq{
		RefreshToken: "test-refresh-token",
	})
	s.Require().NotNil(resp)
	s.Require().NoError(err)
	s.Require().NotEmpty(resp.RefreshToken)
	s.Require().NotEmpty(resp.AccessToken)
	s.Require().NotEmpty(resp.SessionID)
}

func (s *Suite) TestRefreshTokens_KeycloakInvalidTokenErr() {
	s.mocks.Providers.KeycloakProvider.EXPECT().RefreshUserTokens(gomock.Any(), realm, gomock.Any()).
		Return(keycloak.TokensResponse{}, keycloak.ErrRefreshTokenExpired).Times(1)

	resp, err := s.grpc.RefreshTokens(s.ctx, &pb.RefreshTokensReq{
		RefreshToken: "invalid-test-refresh-token",
	})
	s.Require().Nil(resp)
	s.Require().NotNil(err)
	parsedErr := grpcx.ErrorFromGRPC(err)
	s.Require().Equal("6.0", parsedErr.ErrNum)
}
