package tests

import (
	"errors"

	"github.com/golang/mock/gomock"

	keycloakContants "git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

func (s *Suite) TestDeleteSessionsByOrigin_Success() {
	s.mocks.Providers.KeycloakProvider.EXPECT().DeleteSessions(gomock.Any(), realm, gomock.Any()).Return(nil).Times(1)

	resp, err := s.grpc.DeleteSessionsByOrigin(s.ctx, &pb.DeleteSessionsByOriginReq{
		UserID:     testUserID,
		SessionIDs: []string{"e19d2995-1cc7-45ec-930d-10c52bd0349f"},
		Origin:     keycloakContants.TestOrigin,
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().True(resp.Succeed)
}

func (s *Suite) TestDeleteSessionsByOrigin_KeycloakErr() {
	s.mocks.Providers.KeycloakProvider.EXPECT().DeleteSessions(gomock.Any(), realm, gomock.Any()).
		Return(errors.New("keycloak session delete err")).Times(1)

	resp, err := s.grpc.DeleteSessionsByOrigin(s.ctx, &pb.DeleteSessionsByOriginReq{
		UserID:     testUserID,
		SessionIDs: []string{"e19d2995-1cc7-45ec-930d-10c52bd0349f"},
		Origin:     keycloakContants.TestOrigin,
	})
	s.Require().Nil(resp)
	s.Require().NotNil(err)
}
