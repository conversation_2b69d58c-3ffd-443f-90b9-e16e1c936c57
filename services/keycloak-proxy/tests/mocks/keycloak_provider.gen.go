// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak (interfaces: KeycloakProvider)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	keycloak "git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
)

// MockKeycloakProvider is a mock of KeycloakProvider interface.
type MockKeycloakProvider struct {
	ctrl     *gomock.Controller
	recorder *MockKeycloakProviderMockRecorder
}

// MockKeycloakProviderMockRecorder is the mock recorder for MockKeycloakProvider.
type MockKeycloakProviderMockRecorder struct {
	mock *MockKeycloakProvider
}

// NewMockKeycloakProvider creates a new mock instance.
func NewMockKeycloakProvider(ctrl *gomock.Controller) *MockKeycloakProvider {
	mock := &MockKeycloakProvider{ctrl: ctrl}
	mock.recorder = &MockKeycloakProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKeycloakProvider) EXPECT() *MockKeycloakProviderMockRecorder {
	return m.recorder
}

// CreateUser mocks base method.
func (m *MockKeycloakProvider) CreateUser(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockKeycloakProviderMockRecorder) CreateUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockKeycloakProvider)(nil).CreateUser), arg0, arg1, arg2)
}

// DeleteSessions mocks base method.
func (m *MockKeycloakProvider) DeleteSessions(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSessions", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSessions indicates an expected call of DeleteSessions.
func (mr *MockKeycloakProviderMockRecorder) DeleteSessions(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSessions", reflect.TypeOf((*MockKeycloakProvider)(nil).DeleteSessions), arg0, arg1, arg2)
}

// GetAdminToken mocks base method.
func (m *MockKeycloakProvider) GetAdminToken(arg0 context.Context, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdminToken", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdminToken indicates an expected call of GetAdminToken.
func (mr *MockKeycloakProviderMockRecorder) GetAdminToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdminToken", reflect.TypeOf((*MockKeycloakProvider)(nil).GetAdminToken), arg0, arg1)
}

// GetRealmRoles mocks base method.
func (m *MockKeycloakProvider) GetRealmRoles(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRealmRoles", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetRealmRoles indicates an expected call of GetRealmRoles.
func (mr *MockKeycloakProviderMockRecorder) GetRealmRoles(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRealmRoles", reflect.TypeOf((*MockKeycloakProvider)(nil).GetRealmRoles), arg0, arg1)
}

// GetUserByPhone mocks base method.
func (m *MockKeycloakProvider) GetUserByPhone(arg0 context.Context, arg1, arg2 string) (keycloak.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByPhone", arg0, arg1, arg2)
	ret0, _ := ret[0].(keycloak.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserByPhone indicates an expected call of GetUserByPhone.
func (mr *MockKeycloakProviderMockRecorder) GetUserByPhone(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByPhone", reflect.TypeOf((*MockKeycloakProvider)(nil).GetUserByPhone), arg0, arg1, arg2)
}

// GetUserTokens mocks base method.
func (m *MockKeycloakProvider) GetUserTokens(arg0 context.Context, arg1, arg2 string) (keycloak.TokensResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].(keycloak.TokensResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTokens indicates an expected call of GetUserTokens.
func (mr *MockKeycloakProviderMockRecorder) GetUserTokens(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTokens", reflect.TypeOf((*MockKeycloakProvider)(nil).GetUserTokens), arg0, arg1, arg2)
}

// RefreshUserTokens mocks base method.
func (m *MockKeycloakProvider) RefreshUserTokens(arg0 context.Context, arg1, arg2 string) (keycloak.TokensResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshUserTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].(keycloak.TokensResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshUserTokens indicates an expected call of RefreshUserTokens.
func (mr *MockKeycloakProviderMockRecorder) RefreshUserTokens(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshUserTokens", reflect.TypeOf((*MockKeycloakProvider)(nil).RefreshUserTokens), arg0, arg1, arg2)
}

// RevokeRefreshToken mocks base method.
func (m *MockKeycloakProvider) RevokeRefreshToken(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeRefreshToken", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RevokeRefreshToken indicates an expected call of RevokeRefreshToken.
func (mr *MockKeycloakProviderMockRecorder) RevokeRefreshToken(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeRefreshToken", reflect.TypeOf((*MockKeycloakProvider)(nil).RevokeRefreshToken), arg0, arg1, arg2)
}

// SetUserRoles mocks base method.
func (m *MockKeycloakProvider) SetUserRoles(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserRoles", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserRoles indicates an expected call of SetUserRoles.
func (mr *MockKeycloakProviderMockRecorder) SetUserRoles(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRoles", reflect.TypeOf((*MockKeycloakProvider)(nil).SetUserRoles), varargs...)
}
