package tests

import (
	"errors"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
	keycloakContants "git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

func (s *Suite) TestSetUserRoleByOrigin_Success() {
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID, false, []string{
		keycloak.KeycloakRoleNotIdentified,
		keycloak.KeycloakRoleBlocked,
	}).
		Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID,
		true, keycloak.KeycloakRoleActive).Return(nil).Times(1)

	resp, err := s.grpc.SetUserRoleByOrigin(s.ctx, &pb.SetUserRoleByOriginReq{
		UserID: testUserID,
		Role:   "active",
		Origin: keycloakContants.TestOrigin,
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().True(resp.Succeed)
}

func (s *Suite) TestSetUserRoleByOrigin_KeycloakSetUserRolesErr() {
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID, false, []string{
		keycloak.KeycloakRoleNotIdentified,
		keycloak.KeycloakRoleBlocked,
	}).
		Return(nil).Times(1)
	s.mocks.Providers.KeycloakProvider.EXPECT().SetUserRoles(gomock.Any(), realm, testUserID,
		true, keycloak.KeycloakRoleActive).Return(errors.New("keycloak user roles set err")).Times(1)

	resp, err := s.grpc.SetUserRoleByOrigin(s.ctx, &pb.SetUserRoleByOriginReq{
		UserID: testUserID,
		Role:   "active",
		Origin: keycloakContants.TestOrigin,
	})
	s.Require().NotNil(err)
	s.Require().Nil(resp)
}
