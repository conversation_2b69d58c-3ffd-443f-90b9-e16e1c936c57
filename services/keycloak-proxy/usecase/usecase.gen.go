// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package usecase

import (
	"context"

	keycloakproxy "git.redmadrobot.com/zaman/backend/zaman/config/services/keycloak-proxy"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"

	"git.redmadrobot.com/backend-go/rmr-pkg/proxy/hooks"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/keycloak-proxy/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

var _ KeycloakProxy = (*useCasesImpl)(nil)

type KeycloakProxy interface {
	HealthCheck(ctx context.Context) (*entity.Health, error)
	CreateUser(ctx context.Context, req *entity.CreateUserReq) (*entity.CreateUserResult, error)
	ActivateUser(ctx context.Context, req *entity.ActivateUserReq) (*entity.ActivateUserResult, error)
	BlockUser(ctx context.Context, req *entity.BlockUserReq) (*entity.BlockUserResult, error)
	GetTokens(ctx context.Context, req *entity.GetTokensReq) (*entity.GetTokensResult, error)
	RefreshTokens(ctx context.Context, req *entity.RefreshTokensReq) (*entity.RefreshTokensResult, error)
	DeleteSessions(ctx context.Context, req *entity.DeleteSessionsReq) (*entity.DeleteSessionsResult, error)
	SetUserRole(ctx context.Context, req *entity.SetUserRoleReq) (*entity.SetUserRoleResult, error)
	RevokeRefreshToken(ctx context.Context, req *entity.RevokeRefreshTokenReq) (*entity.RevokeRefreshTokenResult, error)
	DeleteSessionsByOrigin(ctx context.Context, req *entity.DeleteSessionsByOriginReq) (*entity.DeleteSessionsByOriginResult, error)
	SetUserRoleByOrigin(ctx context.Context, req *entity.SetUserRoleByOriginReq) (*entity.SetUserRoleByOriginResult, error)
}

type useCasesImpl struct {
	KeycloakProxy
	cfg       *keycloakproxy.Config
	Providers providers.ServiceLocatorImpl
}

func New(ctx context.Context, locator providers.ServiceLocatorImpl, cfg *keycloakproxy.Config) *KeycloakProxyHook {
	useCases := &useCasesImpl{
		cfg:       cfg,
		Providers: locator,
	}

	logger := logs.FromContext(ctx)

	hook := NewKeycloakProxyHook(
		useCases,
		hooks.GrpcServiceLogBeforeCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPostCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPanic(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
	)
	useCases.KeycloakProxy = hook

	return hook
}
