package usecase

import (
	"context"

	keycloackErrs "git.redmadrobot.com/zaman/backend/zaman/errs/keycloakProxy"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) CreateUser(ctx context.Context, req *entity.CreateUserReq) (*entity.CreateUserResult, error) {
	realm, err := u.getRealm(ctx)
	if err != nil {
		return nil, err
	}

	err = u.Providers.KeycloakProvider.CreateUser(ctx, realm, req.Phone)
	if err != nil {
		return nil, err
	}

	user, err := u.Providers.KeycloakProvider.GetUserByPhone(ctx, realm, req.Phone)
	if err != nil {
		return nil, err
	}

	_, err = u.SetUserRole(ctx, &entity.SetUserRoleReq{
		UserID: user.ID,
		Role:   keycloak.KeycloakRoleNotIdentified,
	})
	if err != nil {
		return nil, err
	}

	return entity.MakeCreateUser(user.ID), nil
}

func (u *useCasesImpl) getRealm(ctx context.Context) (string, error) {
	gateway := ctx.Value("reqSource")
	if gateway == nil {
		return "", keycloackErrs.KeycloakProxyErrs().RealmNotFoundNotFoundInCtxError()
	}

	switch gateway.(string) {
	case "MOBILE":
		return u.cfg.App.KeycloakProvider.KcMobileGwRealm, nil
	case "SME":
		return u.cfg.App.KeycloakProvider.KcSmeGwRealm, nil
	case "TEST":
		return "test", nil
	default:
		return "", keycloackErrs.KeycloakProxyErrs().RealmNotFoundNotFoundInCtxError()
	}
}
