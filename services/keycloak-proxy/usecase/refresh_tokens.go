package usecase

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/errs/keycloakProxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) RefreshTokens(ctx context.Context, req *entity.RefreshTokensReq) (*entity.RefreshTokensResult, error) {
	realm, err := u.getRealm(ctx)
	if err != nil {
		return nil, err
	}
	tokens, err := u.Providers.KeycloakProvider.RefreshUserTokens(ctx, realm, req.RefreshToken)
	if err != nil {
		return nil, keycloakProxy.NewKeycloakServiceErr(err)
	}
	return entity.MakeRefreshTokens(tokens), nil
}
