package usecase

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	keycloackErrs "git.redmadrobot.com/zaman/backend/zaman/errs/keycloakProxy"
	keycloakContants "git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/consts"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) DeleteSessionsByOrigin(ctx context.Context, req *entity.DeleteSessionsByOriginReq) (*entity.DeleteSessionsByOriginResult, error) {
	logger := logs.FromContext(ctx)

	logger.Debug().Msgf("getting realm for origin:%s ; userId: %s", req.Origin, req.UserID)

	realm, err := u.getRealmFromOrigin(req.Origin)
	if err != nil {
		logger.Warn().Err(err).Msg("failed to get realm by origin")
		return nil, err
	}

	logger.Debug().Msg("deleting user sessions in keycloack")

	err = u.Providers.KeycloakProvider.DeleteSessions(ctx, realm, req.SessionIDs)
	if err != nil {
		return nil, err
	}
	return entity.MakeDeleteSessionsByOrigin(), nil
}

func (u *useCasesImpl) getRealmFromOrigin(origin string) (string, error) {
	switch origin {
	case keycloakContants.MobileOrigin:
		return u.cfg.App.KeycloakProvider.KcMobileGwRealm, nil
	case keycloakContants.SmeOrigin:
		return u.cfg.App.KeycloakProvider.KcSmeGwRealm, nil
	case keycloakContants.TestOrigin:
		return "test", nil
	default:
		return "", keycloackErrs.KeycloakProxyErrs().RealmNotFoundError()
	}
}
