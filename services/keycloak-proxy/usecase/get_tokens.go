package usecase

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) GetTokens(ctx context.Context, req *entity.GetTokensReq) (*entity.GetTokensResult, error) {
	realm, err := u.getRealm(ctx)
	if err != nil {
		return nil, err
	}
	if utils.IsReviewer(ctx) {
		// Получаем UserID по номеру телефона в имени
		user, err := u.Providers.KeycloakProvider.GetUserByPhone(ctx, realm, req.Phone)
		if err != nil {
			return nil, err
		}
		err = u.Providers.KeycloakProvider.SetUserRoles(ctx, realm, user.ID, true, "reviewer", "active")
		if err != nil {
			return nil, err
		}
		err = u.Providers.KeycloakProvider.SetUserRoles(ctx, realm, user.ID, false, "not_identified")
		if err != nil {
			return nil, err
		}
	}

	tokens, err := u.Providers.KeycloakProvider.GetUserTokens(ctx, realm, req.Phone)
	if err != nil {
		return nil, err
	}
	return entity.MakeGetTokens(tokens), nil
}
