package usecase

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) ActivateUser(ctx context.Context, req *entity.ActivateUserReq) (*entity.ActivateUserResult, error) {
	// не проверяем логику смены статусов, т.к. это задача вызывающего бизнес-сервиса.
	_, err := u.SetUserRole(ctx, &entity.SetUserRoleReq{
		UserID: req.UserID,
		Role:   keycloak.KeycloakRoleActive,
	})
	if err != nil {
		return nil, err
	}
	return entity.MakeActivateUser(), nil
}
