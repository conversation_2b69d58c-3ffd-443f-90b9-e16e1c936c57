package usecase

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) DeleteSessions(ctx context.Context, req *entity.DeleteSessionsReq) (*entity.DeleteSessionsResult, error) {
	realm, err := u.getRealm(ctx)
	if err != nil {
		return nil, err
	}

	err = u.Providers.KeycloakProvider.DeleteSessions(ctx, realm, req.SessionIDs)
	if err != nil {
		return nil, err
	}
	return entity.MakeDeleteSessions(), nil
}
