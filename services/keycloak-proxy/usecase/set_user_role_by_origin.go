package usecase

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) SetUserRoleByOrigin(ctx context.Context, req *entity.SetUserRoleByOriginReq) (*entity.SetUserRoleByOriginResult, error) {
	logger := logs.FromContext(ctx)

	logger.Debug().Msgf("getting realm for origin: %s ; userId: %s", req.Origin, req.UserID)

	realm, err := u.getRealmFromOrigin(req.Origin)
	if err != nil {
		logger.Warn().Err(err).Msg("failed to get realm by origin")
		return nil, err
	}

	logger.Debug().Msg("setting user roles in keycloack")

	if err := u.Providers.KeycloakProvider.SetUserRoles(ctx, realm, req.UserID, false, getRolesToDelete(req.Role)...); err != nil {
		return nil, err
	}

	if err := u.Providers.KeycloakProvider.SetUserRoles(ctx, realm, req.UserID, true, req.Role); err != nil {
		return nil, err
	}

	return entity.MakeSetUserRoleByOrigin(), nil
}
