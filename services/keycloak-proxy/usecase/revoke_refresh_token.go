package usecase

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) RevokeRefreshToken(ctx context.Context, req *entity.RevokeRefreshTokenReq) (*entity.RevokeRefreshTokenResult, error) {
	realm, err := u.getRealm(ctx)
	if err != nil {
		return nil, err
	}
	if err := u.Providers.KeycloakProvider.RevokeRefreshToken(ctx, realm, req.RefreshToken); err != nil {
		return nil, err
	}

	return entity.MakeRevokeRefreshTokenResult(), nil
}
