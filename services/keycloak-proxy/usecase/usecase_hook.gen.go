// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/usecase -i KeycloakProxy -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ KeycloakProxy = (*KeycloakProxyHook)(nil)

// KeycloakProxyHook implements KeycloakProxy interface wrapper
type KeycloakProxyHook struct {
	KeycloakProxy
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// ActivateUser implements KeycloakProxy
func (_w *KeycloakProxyHook) ActivateUser(ctx context.Context, req *entity.ActivateUserReq) (ap1 *entity.ActivateUserResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "ActivateUser", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "ActivateUser", _params)

	ap1, err = _w.KeycloakProxy.ActivateUser(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "ActivateUser", []any{ap1, err})
	return ap1, err
}

// BlockUser implements KeycloakProxy
func (_w *KeycloakProxyHook) BlockUser(ctx context.Context, req *entity.BlockUserReq) (bp1 *entity.BlockUserResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "BlockUser", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "BlockUser", _params)

	bp1, err = _w.KeycloakProxy.BlockUser(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "BlockUser", []any{bp1, err})
	return bp1, err
}

// CreateUser implements KeycloakProxy
func (_w *KeycloakProxyHook) CreateUser(ctx context.Context, req *entity.CreateUserReq) (cp1 *entity.CreateUserResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "CreateUser", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "CreateUser", _params)

	cp1, err = _w.KeycloakProxy.CreateUser(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "CreateUser", []any{cp1, err})
	return cp1, err
}

// DeleteSessions implements KeycloakProxy
func (_w *KeycloakProxyHook) DeleteSessions(ctx context.Context, req *entity.DeleteSessionsReq) (dp1 *entity.DeleteSessionsResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "DeleteSessions", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "DeleteSessions", _params)

	dp1, err = _w.KeycloakProxy.DeleteSessions(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "DeleteSessions", []any{dp1, err})
	return dp1, err
}

// DeleteSessionsByOrigin implements KeycloakProxy
func (_w *KeycloakProxyHook) DeleteSessionsByOrigin(ctx context.Context, req *entity.DeleteSessionsByOriginReq) (dp1 *entity.DeleteSessionsByOriginResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "DeleteSessionsByOrigin", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "DeleteSessionsByOrigin", _params)

	dp1, err = _w.KeycloakProxy.DeleteSessionsByOrigin(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "DeleteSessionsByOrigin", []any{dp1, err})
	return dp1, err
}

// GetTokens implements KeycloakProxy
func (_w *KeycloakProxyHook) GetTokens(ctx context.Context, req *entity.GetTokensReq) (gp1 *entity.GetTokensResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "GetTokens", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "GetTokens", _params)

	gp1, err = _w.KeycloakProxy.GetTokens(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "GetTokens", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements KeycloakProxy
func (_w *KeycloakProxyHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "HealthCheck", _params)

	hp1, err = _w.KeycloakProxy.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// RefreshTokens implements KeycloakProxy
func (_w *KeycloakProxyHook) RefreshTokens(ctx context.Context, req *entity.RefreshTokensReq) (rp1 *entity.RefreshTokensResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "RefreshTokens", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "RefreshTokens", _params)

	rp1, err = _w.KeycloakProxy.RefreshTokens(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "RefreshTokens", []any{rp1, err})
	return rp1, err
}

// RevokeRefreshToken implements KeycloakProxy
func (_w *KeycloakProxyHook) RevokeRefreshToken(ctx context.Context, req *entity.RevokeRefreshTokenReq) (rp1 *entity.RevokeRefreshTokenResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "RevokeRefreshToken", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "RevokeRefreshToken", _params)

	rp1, err = _w.KeycloakProxy.RevokeRefreshToken(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "RevokeRefreshToken", []any{rp1, err})
	return rp1, err
}

// SetUserRole implements KeycloakProxy
func (_w *KeycloakProxyHook) SetUserRole(ctx context.Context, req *entity.SetUserRoleReq) (sp1 *entity.SetUserRoleResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "SetUserRole", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "SetUserRole", _params)

	sp1, err = _w.KeycloakProxy.SetUserRole(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "SetUserRole", []any{sp1, err})
	return sp1, err
}

// SetUserRoleByOrigin implements KeycloakProxy
func (_w *KeycloakProxyHook) SetUserRoleByOrigin(ctx context.Context, req *entity.SetUserRoleByOriginReq) (sp1 *entity.SetUserRoleByOriginResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.KeycloakProxy, "SetUserRoleByOrigin", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.KeycloakProxy, "SetUserRoleByOrigin", _params)

	sp1, err = _w.KeycloakProxy.SetUserRoleByOrigin(_ctx, req)
	_w._postCall.Hook(_ctx, _w.KeycloakProxy, "SetUserRoleByOrigin", []any{sp1, err})
	return sp1, err
}

// NewKeycloakProxyHook returns KeycloakProxyHook
func NewKeycloakProxyHook(object KeycloakProxy, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *KeycloakProxyHook {
	return &KeycloakProxyHook{
		KeycloakProxy: object,
		_beforeCall:   beforeCall,
		_postCall:     postCall,
		_onPanic:      onPanic,
	}
}
