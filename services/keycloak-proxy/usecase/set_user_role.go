package usecase

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) SetUserRole(ctx context.Context, req *entity.SetUserRoleReq) (*entity.SetUserRoleResult, error) {
	realm, err := u.getRealm(ctx)
	if err != nil {
		return nil, err
	}
	if err := u.Providers.KeycloakProvider.SetUserRoles(ctx, realm, req.UserID, false, getRolesToDelete(req.Role)...); err != nil {
		return nil, err
	}

	if err := u.Providers.KeycloakProvider.SetUserRoles(ctx, realm, req.UserID, true, req.Role); err != nil {
		return nil, err
	}

	return entity.MakeSetUserRole(), nil
}

func getRolesToDelete(targetRole string) []string {
	switch targetRole {
	case keycloak.KeycloakRoleNotIdentified:
		return []string{
			keycloak.KeycloakRoleBlocked,
			keycloak.KeycloakRoleActive,
		}
	case keycloak.KeycloakRoleBlocked:
		return []string{
			keycloak.KeycloakRoleNotIdentified,
			keycloak.KeycloakRoleActive,
		}
	default:
		return []string{
			keycloak.KeycloakRoleNotIdentified,
			keycloak.KeycloakRoleBlocked,
		}
	}
}
