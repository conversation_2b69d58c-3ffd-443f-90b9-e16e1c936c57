package usecase

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (u *useCasesImpl) BlockUser(ctx context.Context, req *entity.BlockUserReq) (*entity.BlockUserResult, error) {
	// не проверяем логику смены статусов, т.к. это задача вызывающего бизнес-сервиса.
	_, err := u.SetUserRole(ctx, &entity.SetUserRoleReq{
		UserID: req.UserID,
		Role:   keycloak.KeycloakRoleBlocked,
	})
	if err != nil {
		return nil, err
	}

	return entity.MakeBlockUser(), nil
}
