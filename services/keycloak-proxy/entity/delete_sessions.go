package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	DeleteSessionsReq struct {
		UserID     string
		SessionIDs []string
	}

	DeleteSessionsResult struct{}
)

func MakeDeleteSessions() *DeleteSessionsResult {
	return &DeleteSessionsResult{}
}

// MakeDeleteSessionsPbToEntity создает объект из pb.DeleteSessionsReq в DeleteSessionsReq
func MakeDeleteSessionsPbToEntity(req *pb.DeleteSessionsReq) *DeleteSessionsReq {
	return &DeleteSessionsReq{
		UserID:     req.UserID,
		SessionIDs: req.SessionIDs,
	}
}

// MakeDeleteSessionsEntityToPb создает объект из ActivateUserResult в pb.ActivateUserResp
func MakeDeleteSessionsEntityToPb(req *DeleteSessionsResult) *pb.DeleteSessionsResp {
	return &pb.DeleteSessionsResp{
		Succeed: req != nil,
	}
}
