package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	ActivateUserReq struct {
		Phone  string
		UserID string
	}

	ActivateUserResult struct{}
)

func MakeActivateUser() *ActivateUserResult {
	return &ActivateUserResult{}
}

// MakeActivateUserPbToEntity создает объект из pb.ActivateUserReq в ActivateUserReq
func MakeActivateUserPbToEntity(req *pb.ActivateUserReq) *ActivateUserReq {
	return &ActivateUserReq{
		Phone:  req.Phone,
		UserID: req.UserID,
	}
}

// MakeActivateUserEntityToPb создает объект из ActivateUserResult в pb.ActivateUserResp
func MakeActivateUserEntityToPb(req *ActivateUserResult) *pb.ActivateUserResp {
	return &pb.ActivateUserResp{
		Succeed: req != nil,
	}
}
