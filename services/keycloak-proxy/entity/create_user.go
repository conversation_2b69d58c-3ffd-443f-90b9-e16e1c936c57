package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	CreateUserReq struct {
		Phone string
	}

	CreateUserResult struct {
		UserID string
	}
)

func MakeCreateUser(userID string) *CreateUserResult {
	return &CreateUserResult{
		UserID: userID,
	}
}

// MakeCreateUserPbToEntity создает объект из pb.CreateUserReq в CreateUserReq
func MakeCreateUserPbToEntity(req *pb.CreateUserReq) *CreateUserReq {
	return &CreateUserReq{
		Phone: req.Phone,
	}
}

// MakeCreateUserEntityToPb создает объект из CreateUserResult в pb.CreateUserResp
func MakeCreateUserEntityToPb(req *CreateUserResult) *pb.CreateUserResp {
	return &pb.CreateUserResp{Succeed: req != nil, UserID: req.UserID}
}
