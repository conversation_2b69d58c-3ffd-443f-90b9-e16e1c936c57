package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	SetUserRoleReq struct {
		UserID string
		Role   string
	}

	SetUserRoleResult struct{}
)

func MakeSetUserRole() *SetUserRoleResult {
	return &SetUserRoleResult{}
}

// MakeSetUserRolePbToEntity создает объект из pb.SetUserRolesReq в SetUserRolesReq
func MakeSetUserRolePbToEntity(req *pb.SetUserRoleReq) *SetUserRoleReq {
	return &SetUserRoleReq{
		UserID: req.UserID,
		Role:   req.Role,
	}
}

// MakeSetUserRoleEntityToPb создает объект из SetUserRoles в pb.SetUserRolesResp
func MakeSetUserRoleEntityToPb(req *SetUserRoleResult) *pb.SetUserRoleResp {
	return &pb.SetUserRoleResp{
		Succeed: req != nil,
	}
}
