package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	BlockUserReq struct {
		Phone  string
		UserID string
	}

	BlockUserResult struct{}
)

func MakeBlockUser() *BlockUserResult {
	return &BlockUserResult{}
}

// MakeBlockUserPbToEntity создает объект из pb.BlockUserReq в BlockUserReq
func MakeBlockUserPbToEntity(req *pb.BlockUserReq) *BlockUserReq {
	return &BlockUserReq{
		Phone:  req.Phone,
		UserID: req.UserID,
	}
}

// MakeBlockUserEntityToPb создает объект из BlockUserResult в pb.BlockUserResp
func MakeBlockUserEntityToPb(req *BlockUserResult) *pb.BlockUserResp {
	return &pb.BlockUserResp{
		Succeed: req != nil,
	}
}
