package entity

import (
	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	RefreshTokensReq struct {
		RefreshToken string
	}

	RefreshTokensResult struct {
		AccessToken  string
		RefreshToken string
		SessionID    string
	}
)

func MakeRefreshTokens(tokens keycloak.TokensResponse) *RefreshTokensResult {
	return &RefreshTokensResult{
		AccessToken:  tokens.AccessToken,
		RefreshToken: tokens.RefreshToken,
		SessionID:    tokens.SessionID,
	}
}

// MakeRefreshTokensPbToEntity создает объект из pb.RefreshTokensReq в RefreshTokensReq
func MakeRefreshTokensPbToEntity(req *pb.RefreshTokensReq) *RefreshTokensReq {
	return &RefreshTokensReq{
		RefreshToken: req.RefreshToken,
	}
}

// MakeRefreshTokensEntityToPb создает объект из RefreshTokensResult в pb.RefreshTokensResp
func MakeRefreshTokensEntityToPb(req *RefreshTokensResult) *pb.RefreshTokensResp {
	return &pb.RefreshTokensResp{
		AccessToken:  req.AccessToken,
		RefreshToken: req.RefreshToken,
		SessionID:    req.SessionID,
	}
}
