package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	SetUserRoleByOriginReq struct {
		UserID string
		Role   string
		Origin string
	}

	SetUserRoleByOriginResult struct{}
)

func MakeSetUserRoleByOrigin() *SetUserRoleByOriginResult {
	return &SetUserRoleByOriginResult{}
}

// MakeSetUserRoleByOriginPbToEntity создает объект из pb.SetUserRoleByOriginReq в SetUserRoleByOriginReq для передачи в usecase
func MakeSetUserRoleByOriginPbToEntity(req *pb.SetUserRoleByOriginReq) *SetUserRoleByOriginReq {
	if req == nil {
		return &SetUserRoleByOriginReq{}
	}

	return &SetUserRoleByOriginReq{
		UserID: req.UserID,
		Role:   req.Role,
		Origin: req.Origin,
	}
}

// MakeSetUserRoleByOriginEntityToPb создает объект из SetUserRoleByOrigin в pb.SetUserRoleByOriginResp для возврата ответа из сервиса
func MakeSetUserRoleByOriginEntityToPb(res *SetUserRoleByOriginResult) *pb.SetUserRoleByOriginResp {
	return &pb.SetUserRoleByOriginResp{
		Succeed: res != nil,
	}
}
