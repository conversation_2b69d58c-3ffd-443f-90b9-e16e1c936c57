package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	RevokeRefreshTokenReq struct {
		RefreshToken string
	}

	RevokeRefreshTokenResult struct{}
)

// MakeRevokeRefreshTokenPbToEntity создает объект из pb.RevokeRefreshTokenReq в RevokeRefreshTokenReq для передачи в usecase
func MakeRevokeRefreshTokenPbToEntity(req *pb.RevokeRefreshTokenReq) *RevokeRefreshTokenReq {
	if req == nil {
		return &RevokeRefreshTokenReq{}
	}
	return &RevokeRefreshTokenReq{
		RefreshToken: req.Token,
	}
}

// MakeRevokeRefreshTokenEntityToPb создает объект из RevokeRefreshTokenResult в pb.RevokeRefreshTokenResp для возврата ответа из сервиса
func MakeRevokeRefreshTokenEntityToPb(res *RevokeRefreshTokenResult) *pb.RevokeRefreshTokenResp {
	return &pb.RevokeRefreshTokenResp{
		Succeed: res != nil,
	}
}

func MakeRevokeRefreshTokenResult() *RevokeRefreshTokenResult {
	return &RevokeRefreshTokenResult{}
}
