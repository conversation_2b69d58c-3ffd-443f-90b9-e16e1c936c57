package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	DeleteSessionsByOriginReq struct {
		UserID     string
		SessionIDs []string
		Origin     string
	}

	DeleteSessionsByOriginResult struct{}
)

func MakeDeleteSessionsByOrigin() *DeleteSessionsByOriginResult {
	return &DeleteSessionsByOriginResult{}
}

// MakeDeleteSessionsByOriginPbToEntity создает объект из pb.DeleteSessionsByOriginReq в DeleteSessionsByOriginReq для передачи в usecase
func MakeDeleteSessionsByOriginPbToEntity(req *pb.DeleteSessionsByOriginReq) *DeleteSessionsByOriginReq {
	if req == nil {
		return &DeleteSessionsByOriginReq{}
	}

	return &DeleteSessionsByOriginReq{
		UserID:     req.UserID,
		SessionIDs: req.SessionIDs,
		Origin:     req.Origin,
	}
}

// MakeDeleteSessionsByOriginEntityToPb создает объект из DeleteSessionsByOrigin в pb.DeleteSessionsByOriginResp для возврата ответа из сервиса
func MakeDeleteSessionsByOriginEntityToPb(res *DeleteSessionsByOriginResult) *pb.DeleteSessionsByOriginResp {
	return &pb.DeleteSessionsByOriginResp{
		Succeed: res != nil,
	}
}
