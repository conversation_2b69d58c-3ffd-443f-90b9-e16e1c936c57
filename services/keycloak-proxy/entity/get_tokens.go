package entity

import (
	"git.redmadrobot.com/zaman/backend/zaman/pkg/keycloak"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"
)

type (
	GetTokensReq struct {
		Phone string
	}

	GetTokensResult struct {
		AccessToken  string
		RefreshToken string
		SessionID    string
	}
)

func MakeGetTokens(tokens keycloak.TokensResponse) *GetTokensResult {
	return &GetTokensResult{
		AccessToken:  tokens.AccessToken,
		RefreshToken: tokens.RefreshToken,
		SessionID:    tokens.SessionID,
	}
}

// MakeGetTokensPbToEntity создает объект из pb.GetTokensReq в GetTokensReq
func MakeGetTokensPbToEntity(req *pb.GetTokensReq) *GetTokensReq {
	return &GetTokensReq{
		Phone: req.Phone,
	}
}

// MakeGetTokensEntityToPb создает объект из GetTokensResult в pb.GetTokensResp
func MakeGetTokensEntityToPb(req *GetTokensResult) *pb.GetTokensResp {
	return &pb.GetTokensResp{
		AccessToken:  req.AccessToken,
		RefreshToken: req.RefreshToken,
		SessionID:    req.SessionID,
	}
}
