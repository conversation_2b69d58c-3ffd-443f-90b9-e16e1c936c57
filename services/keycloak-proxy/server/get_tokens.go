package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) GetTokens(ctx context.Context, req *pb.GetTokensReq) (*pb.GetTokensResp, error) {
	getTokensEntity := entity.MakeGetTokensPbToEntity(req)

	getTokens, err := s.useCase.GetTokens(ctx, getTokensEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetTokensEntityToPb(getTokens), nil
}
