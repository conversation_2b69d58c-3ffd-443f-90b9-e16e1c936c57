package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) RevokeRefreshToken(ctx context.Context, req *pb.RevokeRefreshTokenReq) (*pb.RevokeRefreshTokenResp, error) {
	revokeRefreshTokenEntity := entity.MakeRevokeRefreshTokenPbToEntity(req)

	revokeRefreshToken, err := s.useCase.RevokeRefreshToken(ctx, revokeRefreshTokenEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeRevokeRefreshTokenEntityToPb(revokeRefreshToken), nil
}
