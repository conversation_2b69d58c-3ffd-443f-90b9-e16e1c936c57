package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) DeleteSessionsByOrigin(ctx context.Context, req *pb.DeleteSessionsByOriginReq) (*pb.DeleteSessionsByOriginResp, error) {
	deleteSessionsByOriginEntity := entity.MakeDeleteSessionsByOriginPbToEntity(req)

	deleteSessionsByOrigin, err := s.useCase.DeleteSessionsByOrigin(ctx, deleteSessionsByOriginEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeDeleteSessionsByOriginEntityToPb(deleteSessionsByOrigin), nil
}
