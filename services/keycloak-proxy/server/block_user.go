package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) BlockUser(ctx context.Context, req *pb.BlockUserReq) (*pb.BlockUserResp, error) {
	blockUserEntity := entity.MakeBlockUserPbToEntity(req)

	blockUser, err := s.useCase.BlockUser(ctx, blockUserEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeBlockUserEntityToPb(blockUser), nil
}
