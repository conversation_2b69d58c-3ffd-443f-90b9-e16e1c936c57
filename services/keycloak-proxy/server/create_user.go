package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) CreateUser(ctx context.Context, req *pb.CreateUserReq) (*pb.CreateUserResp, error) {
	createUserEntity := entity.MakeCreateUserPbToEntity(req)

	createUser, err := s.useCase.CreateUser(ctx, createUserEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeCreateUserEntityToPb(createUser), nil
}
