package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) DeleteSessions(ctx context.Context, req *pb.DeleteSessionsReq) (*pb.DeleteSessionsResp, error) {
	deleteSessionsEntity := entity.MakeDeleteSessionsPbToEntity(req)

	deleteSessions, err := s.useCase.DeleteSessions(ctx, deleteSessionsEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeDeleteSessionsEntityToPb(deleteSessions), nil
}
