package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) ActivateUser(ctx context.Context, req *pb.ActivateUserReq) (*pb.ActivateUserResp, error) {
	activateUserEntity := entity.MakeActivateUserPbToEntity(req)

	activateUser, err := s.useCase.ActivateUser(ctx, activateUserEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeActivateUserEntityToPb(activateUser), nil
}
