package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) SetUserRole(ctx context.Context, req *pb.SetUserRoleReq) (*pb.SetUserRoleResp, error) {
	setUserRoleEntity := entity.MakeSetUserRolePbToEntity(req)

	setUserRole, err := s.useCase.SetUserRole(ctx, setUserRoleEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeSetUserRoleEntityToPb(setUserRole), nil
}
