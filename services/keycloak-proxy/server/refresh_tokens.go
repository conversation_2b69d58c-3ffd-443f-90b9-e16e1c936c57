package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) RefreshTokens(ctx context.Context, req *pb.RefreshTokensReq) (*pb.RefreshTokensResp, error) {
	refreshTokensEntity := entity.MakeRefreshTokensPbToEntity(req)

	refreshTokens, err := s.useCase.RefreshTokens(ctx, refreshTokensEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeRefreshTokensEntityToPb(refreshTokens), nil
}
