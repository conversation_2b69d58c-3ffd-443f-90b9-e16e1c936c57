package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/keycloak-proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/keycloak-proxy/entity"
)

func (s *Server) SetUserRoleByOrigin(ctx context.Context, req *pb.SetUserRoleByOriginReq) (*pb.SetUserRoleByOriginResp, error) {
	setUserRoleByOriginEntity := entity.MakeSetUserRoleByOriginPbToEntity(req)

	setUserRoleByOrigin, err := s.useCase.SetUserRoleByOrigin(ctx, setUserRoleByOriginEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeSetUserRoleByOriginEntityToPb(setUserRoleByOrigin), nil
}
