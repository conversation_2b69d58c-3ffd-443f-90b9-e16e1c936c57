// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/usecase -i JuicyscoreBridge -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ JuicyscoreBridge = (*JuicyscoreBridgeHook)(nil)

// JuicyscoreBridgeHook implements JuicyscoreBridge interface wrapper
type JuicyscoreBridgeHook struct {
	JuicyscoreBridge
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// GetJuicyScore implements JuicyscoreBridge
func (_w *JuicyscoreBridgeHook) GetJuicyScore(ctx context.Context, req *entity.GetJuicyScoreReq) (gp1 *entity.GetJuicyScoreResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.JuicyscoreBridge, "GetJuicyScore", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.JuicyscoreBridge, "GetJuicyScore", _params)

	gp1, err = _w.JuicyscoreBridge.GetJuicyScore(_ctx, req)
	_w._postCall.Hook(_ctx, _w.JuicyscoreBridge, "GetJuicyScore", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements JuicyscoreBridge
func (_w *JuicyscoreBridgeHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.JuicyscoreBridge, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.JuicyscoreBridge, "HealthCheck", _params)

	hp1, err = _w.JuicyscoreBridge.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.JuicyscoreBridge, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements JuicyscoreBridge
func (_w *JuicyscoreBridgeHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.JuicyscoreBridge, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.JuicyscoreBridge, "HealthEvent", _params)

	_w.JuicyscoreBridge.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.JuicyscoreBridge, "HealthEvent", []any{})
	return
}

// InitConsumer implements JuicyscoreBridge
func (_w *JuicyscoreBridgeHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.JuicyscoreBridge, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.JuicyscoreBridge, "InitConsumer", _params)

	_w.JuicyscoreBridge.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.JuicyscoreBridge, "InitConsumer", []any{})
	return
}

// NewJuicyscoreBridgeHook returns JuicyscoreBridgeHook
func NewJuicyscoreBridgeHook(object JuicyscoreBridge, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *JuicyscoreBridgeHook {
	return &JuicyscoreBridgeHook{
		JuicyscoreBridge: object,
		_beforeCall:      beforeCall,
		_postCall:        postCall,
		_onPanic:         onPanic,
	}
}
