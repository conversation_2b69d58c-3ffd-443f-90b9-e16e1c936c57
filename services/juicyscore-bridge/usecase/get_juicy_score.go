package usecase

import (
	"context"
	"errors"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/generic"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/mongo"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/juicyscore"
	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/entity"
)

const defaultJuicyScoreRetries = 3

func (u *useCasesImpl) GetJuicyScore(ctx context.Context, req *entity.GetJuicyScoreReq) (*entity.GetJuicyScoreResult, error) {
	resp, err := u.requestJuicyScore(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (u *useCasesImpl) requestJuicyScore(ctx context.Context, req *entity.GetJuicyScoreReq) (*entity.GetJuicyScoreResult, error) {
	retryParams := generic.ParamsRetryWithBackoff[entity.GetJuicyScoreResult]{
		MaxRetries: defaultJuicyScoreRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: checkRetryNecessity,
		Operation: func() (*entity.GetJuicyScoreResult, error) {
			startTime := time.Now().Unix()

			resp, reqLog, juicyScoreErr := u.Providers.JuicyScoreProvider.GetJuicyScore(ctx, *entity.MakeGetJuicyScoreReqParamsToPkgEntity(&req.Params))

			rawData := entity.NewGetJuicyScoreRawData(ctx, req, resp, reqLog, startTime, juicyScoreErr)
			reqLogID, saveErr := u.Providers.Storage.SaveRawDataWithID(ctx, rawData, mongo.CollectionRawJuicyScore)
			if saveErr != nil {
				return nil, saveErr
			}

			result := &entity.GetJuicyScoreResult{}
			if resp != nil {
				result.Response = resp
			}

			if reqLog != nil && req.ClientInfo != nil {
				result.RequestLogID = &reqLogID
			}

			return result, juicyScoreErr
		},
	}

	return generic.RetryWithBackoff(retryParams)
}

func checkRetryNecessity(err error) bool {
	errorsList := []error{
		juicyscore.ErrJuicyScoreInternal,
	}
	for _, e := range errorsList {
		if errors.Is(err, e) {
			return true
		}
	}
	return false
}
