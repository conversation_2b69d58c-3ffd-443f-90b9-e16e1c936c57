package entity

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"

	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/juicyscore"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/juicyscore-bridge"
)

type (
	GetJuicyScoreReq struct {
		Params     Params      `json:"params"`
		ClientInfo *ClientInfo `json:"client_info"`
	}

	Params struct {
		ApplicationID string    `json:"application_id" bson:"application_id"`
		Amount        int64     `json:"amount" bson:"amount"`
		UserID        string    `json:"user_id" bson:"user_id"`
		SessionID     string    `json:"session_id" bson:"session_id"`
		RequestTime   time.Time `json:"request_time" bson:"request_time"`
		TimeZone      string    `json:"time_zone" bson:"time_zone"`
		IP            string    `json:"ip" bson:"ip"`
		PhoneNumber   string    `json:"phone_number" bson:"phone_number"`
		Tenor         int64     `json:"tenor" bson:"tenor"`
	}

	ClientInfo struct {
		Iin           string `json:"iin"`
		FullName      string `json:"full_name"`
		UserType      string `json:"user_type"`
		ApplicationID uint64 `json:"application_id"`
		ClientID      uint64 `json:"client_id"`
		TrackID       uint64 `json:"track_id"`
	}

	GetJuicyScoreResult struct {
		Response     *juicyscore.GetJuicyScoreResponse `json:"response"`
		RequestLogID *string                           `json:"request_log_id"`
	}

	GetJuicyScoreRawData = utils.RawData[*Params, *juicyscore.GetJuicyScoreResponse, *utils.RequestLog]
)

func NewGetJuicyScoreRawData(
	ctx context.Context,
	req *GetJuicyScoreReq,
	res *juicyscore.GetJuicyScoreResponse,
	rl *utils.RequestLog,
	startTime int64,
	err error,
) *GetJuicyScoreRawData {
	rd := &utils.RawData[*Params, *juicyscore.GetJuicyScoreResponse, *utils.RequestLog]{
		Request:   &req.Params,
		StartTime: startTime,
	}

	userID, _ := utils.GetUserIDFromContext(ctx)
	requestID := utils.ExtractRequestID(ctx)

	if rl != nil {
		if req.ClientInfo != nil {
			rl.FillClientInfo(req.ClientInfo)
		}

		rd.WithPayload(&rl)
	}

	rd.WithUserID(userID).
		WithRequestID(requestID).
		WithTimeStamp().
		WithResponse(&res).
		WithError(err)

	return rd
}

func (c *ClientInfo) GetIIN() string           { return c.Iin }
func (c *ClientInfo) GetFullName() string      { return c.FullName }
func (c *ClientInfo) GetUserType() string      { return c.UserType }
func (c *ClientInfo) GetApplicationID() uint64 { return c.ApplicationID }
func (c *ClientInfo) GetClientID() uint64      { return c.ClientID }
func (c *ClientInfo) GetTrackID() uint64       { return c.TrackID }

func MakeGetJuicyScoreReqParamsToPkgEntity(params *Params) *juicyscore.ParamsGetJuicyScore {
	if params == nil {
		return &juicyscore.ParamsGetJuicyScore{}
	}

	return &juicyscore.ParamsGetJuicyScore{
		ApplicationID: params.ApplicationID,
		Amount:        int(params.Amount),
		ClientID:      params.UserID,
		SessionID:     params.SessionID,
		RequestTime:   params.RequestTime,
		TimeZone:      params.TimeZone,
		IP:            params.IP,
		PhoneNumber:   params.PhoneNumber,
		Tenor:         int(params.Tenor),
	}
}

// MakeGetJuicyScorePbToEntity создает объект из pb.GetJuicyScoreReq в GetJuicyScoreReq для передачи в usecase
func MakeGetJuicyScorePbToEntity(req *pb.GetJuicyScoreReq) *GetJuicyScoreReq {
	if req == nil {
		return &GetJuicyScoreReq{}
	}

	result := &GetJuicyScoreReq{
		Params: Params{
			ApplicationID: req.Params.ApplicationId,
			Amount:        req.Params.Amount,
			UserID:        req.Params.UserId,
			SessionID:     req.Params.SessionId,
			RequestTime:   req.Params.RequestTime.AsTime(),
			TimeZone:      req.Params.TimeZone,
			IP:            req.Params.Ip,
			PhoneNumber:   req.Params.PhoneNumber,
			Tenor:         req.Params.Tenor,
		},
	}

	if req.ClientInfo != nil {
		result.ClientInfo = &ClientInfo{
			Iin:           req.ClientInfo.Iin,
			FullName:      req.ClientInfo.FullName,
			UserType:      req.ClientInfo.UserType,
			ApplicationID: req.ClientInfo.ApplicationId,
			ClientID:      req.ClientInfo.ClientId,
			TrackID:       req.ClientInfo.TrackId,
		}
	}

	return result
}

func MakeGetJuicyScoreEntityToPb(res *GetJuicyScoreResult) *pb.GetJuicyScoreResp { //nolint: funlen
	parseInt := func(s string) int64 {
		if s == "" {
			return 0
		}
		val, err := strconv.ParseInt(s, 10, 64)
		if err != nil {
			return 0
		}
		return val
	}

	parseFloat := func(s string) float64 {
		if s == "" {
			return 0
		}
		val, err := strconv.ParseFloat(s, 64)
		if err != nil {
			return 0
		}
		return val
	}

	if res != nil {
		r := res.Response

		IPFirstSeenDate, err := time.Parse("2006-01-02 15:04:05", r.Predictors.IPFirstSeenDate)
		if err != nil {
			log.Err(fmt.Errorf("failed to parse IPFirstSeenDate: %w", err))
		}
		parsedIPFirstSeenDate := timestamppb.New(IPFirstSeenDate)

		result := &pb.GetJuicyScoreResp{
			AntiFraudScore:                    parseFloat(r.AntiFraudScore),
			AdditionalInfo:                    r.AdditionalInfo,
			DeviceId:                          parseInt(r.DeviceID),
			ExactDeviceId:                     parseInt(r.ExactDeviceID),
			BrowserHash:                       r.BrowserHash,
			UserId:                            parseInt(r.UserID),
			Success:                           r.Success,
			Time:                              r.Time,
			Idx1StopMarkers:                   parseInt(r.Predictors.IDX1StopMarkers),
			Idx2UserBehaviourMarkers:          parseInt(r.Predictors.IDX2UserBehaviourMarkers),
			Idx3DeviceMarkers:                 parseInt(r.Predictors.IDX3DeviceMarkers),
			Idx4ConnectionMarkers:             parseInt(r.Predictors.IDX4ConnectionMarkers),
			Idx5DeviceQuality:                 parseInt(r.Predictors.IDX5DeviceQuality),
			Idx6InternetInfrastructureQuality: parseInt(r.Predictors.IDX6InternetInfrastructureQuality),
			Idx7DeviceApplicationsQuality:     parseInt(r.Predictors.IDX7DeviceApplicationsQuality),
			Idx8DeviceCredentialsVariability:  parseInt(r.Predictors.IDX8DeviceCredentialsVariability),
			Idx9DeviceApplicationsRisk:        parseInt(r.Predictors.IDX9DeviceApplicationsRisk),
			Idx10EiEstimation:                 parseInt(r.Predictors.IDX10EIEstimation),
			JavaScriptExecutable:              parseInt(r.Predictors.JavaScriptExecutable),
			UseragentAvailable:                parseInt(r.Predictors.UseragentAvailable),
			SupportingDataAvailable:           parseInt(r.Predictors.SupportingDataAvailable),
			BrowserMimesLength:                parseInt(r.Predictors.BrowserMimesLength),
			IsWifiSwitchedOn:                  parseInt(r.Predictors.IsWifiSwitchedOn),
			IsCellularSwitchedOn:              parseInt(r.Predictors.IsCellularSwitchedOn),
			IsBluetoothSwitchedOn:             parseInt(r.Predictors.IsBluetoothSwitchedOn),
			IsWebRtcSwitchedOff:               parseInt(r.Predictors.IsWebRTCSwitchedOff),
			IsWebGlSwitchedOff:                parseInt(r.Predictors.IsWebGLSwitchedOff),
			IsLocalCountry:                    parseInt(r.Predictors.IsLocalCountry),
			IpZipBillingCountriesMatch:        parseInt(r.Predictors.IPZipBillingCountriesMatch),
			TimezoneMismatch:                  parseInt(r.Predictors.TimezoneMismatch),
			IsLocalTimeZone:                   parseInt(r.Predictors.IsLocalTimeZone),
			IsLocalTimeZoneName:               parseInt(r.Predictors.IsLocalTimeZoneName),
			IpSimRegionsMatch:                 parseInt(r.Predictors.IPSimRegionsMatch),
			DuplicatingDevice:                 parseInt(r.Predictors.DuplicatingDevice),
			DuplicatingExactDeviceId:          parseInt(r.Predictors.DuplicatingExactDeviceID),
			DuplicatingIp:                     parseInt(r.Predictors.DuplicatingIP),
			DuplicatingUser:                   parseInt(r.Predictors.DuplicatingUser),
			SamePhone:                         parseInt(r.Predictors.SamePhone),
			SameRegion:                        parseInt(r.Predictors.SameRegion),
			SameDevice:                        parseInt(r.Predictors.SameDevice),
			SameZipBilling:                    parseInt(r.Predictors.SameZipBilling),
			SeenBefore:                        parseInt(r.Predictors.SeenBefore),
			TotalApplicationsNumber:           parseInt(r.Predictors.TotalApplicationsNumber),
			ApplicationsNumber:                parseInt(r.Predictors.ApplicationsNumber),
			ApplicationsNumberOnExactDeviceId: parseInt(r.Predictors.ApplicationsNumberOnExactDeviceID),
			TotalNumOfApplicationsWithBrowserHashIn_1Hour:  parseInt(r.Predictors.TotalNumOfApplicationsWithBrowserHashIn1Hour),
			TotalNumOfApplicationsWithBrowserHashIn_1Day:   parseInt(r.Predictors.TotalNumOfApplicationsWithBrowserHashIn1Day),
			TotalNumOfApplicationsWithBrowserHashIn_7Days:  parseInt(r.Predictors.TotalNumOfApplicationsWithBrowserHashIn7Days),
			TotalNumOfApplicationsWithBrowserHashIn_30Days: parseInt(r.Predictors.TotalNumOfApplicationsWithBrowserHashIn30Days),
			PhonesNumber:                                         parseInt(r.Predictors.PhonesNumber),
			DevicesNumber:                                        parseInt(r.Predictors.DevicesNumber),
			CitiesNumber:                                         parseInt(r.Predictors.CitiesNumber),
			ZipCodesNumber:                                       parseInt(r.Predictors.ZipCodesNumber),
			CardsNumber:                                          parseInt(r.Predictors.CardsNumber),
			LessTenorDays:                                        parseInt(r.Predictors.LessTenorDays),
			DeviceAgeingWithUser:                                 parseInt(r.Predictors.DeviceAgeingWithUser),
			NumOfDaysFromPrevSession:                             parseInt(r.Predictors.NumOfDaysFromPrevSession),
			TotalNumOfShortTermSessionsIn_1Hour:                  parseInt(r.Predictors.TotalNumOfShortTermSessionsIn1Hour),
			TotalNumOfBankingSessionsIn_1Hour:                    parseInt(r.Predictors.TotalNumOfBankingSessionsIn1Hour),
			TotalNumOfInsuranceSessionsIn_1Hour:                  parseInt(r.Predictors.TotalNumOfInsuranceSessionsIn1Hour),
			TotalNumOfShorttermCreditApplicationsIn_1Hour:        parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsIn1Hour),
			TotalNumOfShortTermCreditApplicationsIn_1Day:         parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsIn1Day),
			TotalNumOfShortTermCreditApplicationsIn_7Days:        parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsIn7Days),
			TotalNumOfShortTermCreditApplicationsIn_30Days:       parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsIn30Days),
			TotalNumOfShortTermCreditApplicationsFromIpIn_1Day:   parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsIn1Day),
			TotalNumOfShortTermCreditApplicationsFromIpIn_7Days:  parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsIn7Days),
			TotalNumOfShortTermCreditApplicationsFromIpIn_30Days: parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsIn30Days),
			TotalNumOfBankingCreditApplicationsIn_1Hour:          parseInt(r.Predictors.TotalNumOfBankingCreditApplicationsIn1Hour),
			TotalNumOfBankingCreditApplicationsIn_1Day:           parseInt(r.Predictors.TotalNumOfBankingCreditApplicationsIn1Day),
			TotalNumOfBankingCreditApplicationsIn_7Days:          parseInt(r.Predictors.TotalNumOfBankingCreditApplicationsIn7Days),
			TotalNumOfBankingCreditApplicationsIn_30Days:         parseInt(r.Predictors.TotalNumOfBankingCreditApplicationsIn30Days),
			TotalNumOfBankingCreditApplicationsFromIpIn_1Day:     parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsFromIPIn1Day),
			TotalNumOfBankingCreditApplicationsFromIpIn_7Days:    parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsFromIPIn7Days),
			TotalNumOfBankingCreditApplicationsFromIpIn_30Days:   parseInt(r.Predictors.TotalNumOfShortTermCreditApplicationsFromIPIn30Days),
			TotalNumOf_InsuranceApplicationsIn_1Hour:             parseInt(r.Predictors.TotalNumOfInsuranceApplicationsIn1Hour),
			TotalNumOfInsuranceApplicationsIn_1Day:               parseInt(r.Predictors.TotalNumOfInsuranceApplicationsIn1Day),
			TotalNumOfInsuranceApplicationsIn_7Days:              parseInt(r.Predictors.TotalNumOfInsuranceApplicationsIn7Days),
			TotalNumOfInsuranceApplicationsIn_30Days:             parseInt(r.Predictors.TotalNumOfInsuranceApplicationsIn30Days),
			TotalNumOfInsuranceApplicationsFromIpIn_1Day:         parseInt(r.Predictors.TotalNumOfInsuranceApplicationsFromIPIn1Day),
			TotalNumOfInsuranceApplicationsFromIpIn_7Days:        parseInt(r.Predictors.TotalNumOfInsuranceApplicationsFromIPIn7Days),
			TotalNumOfInsuranceApplicationsFromIpIn_30Days:       parseInt(r.Predictors.TotalNumOfInsuranceApplicationsFromIPIn30Days),
			TotalNumOfGamblingApplicationsIn_1Hour:               parseInt(r.Predictors.TotalNumOfGamblingApplicationsIn1Hour),
			ForeignLanguageUsed:                                  parseInt(r.Predictors.ForeignLanguageUsed),
			UncommonLanguageUsedInBrowser:                        parseInt(r.Predictors.UncommonLanguageUsedInBrowser),
			SuspiciousLanguageUsed:                               parseInt(r.Predictors.SuspiciousLanguageUsed),
			BrowserLanguagesLength:                               parseInt(r.Predictors.BrowserLanguagesLength),
			Frd:                                                  parseInt(r.Predictors.FRD),
			FrdFromDiffAcc:                                       parseInt(r.Predictors.FRDFromDiffAcc),
			Npl90Plus:                                            parseInt(r.Predictors.NPL90),
			Npl90PlusFromDiffAcc:                                 parseInt(r.Predictors.NPL90FromDiffAcc),
			Npl30Plus:                                            parseInt(r.Predictors.NPL30),
			Npl30PlusFromDiffAcc:                                 parseInt(r.Predictors.NPL30FromDiffAcc),
			TotalNumOfFrdAndNpl90PlusApplicationsFromDevice:      parseInt(r.Predictors.TotalNumOfFRDAndNPL90ApplicationsFromDevice),
			IsEverFrd: parseInt(r.Predictors.IsEverFRD),
			IsMassFrd: parseInt(r.Predictors.IsMassFRD),
			TotalNumOfFrdAndNpl90PlusApplicationsFromIp:   parseInt(r.Predictors.TotalNumOfFRDAndNPL90ApplicationsFromIP),
			TotalNumOfFrdAndNpl90PlusApplicationsFromIpv3: parseInt(r.Predictors.TotalNumOfFRDAndNPL90ApplicationsFromIpv3),
			GlobalIpBlacklist:                       parseInt(r.Predictors.GlobalIPBlacklist),
			GlobalIspBlacklist:                      parseInt(r.Predictors.GlobalISPBlacklist),
			UserDefinedIpBlacklist:                  parseInt(r.Predictors.UserDefinedIPBlacklist),
			UserDefinedDeviceIdBlacklist:            parseInt(r.Predictors.UserDefinedDeviceIDBlacklist),
			UserDefinedUserIdBlacklist:              parseInt(r.Predictors.UserDefinedUserIDBlacklist),
			UserDefinedIspBlacklist:                 parseInt(r.Predictors.UserDefinedISPBlacklist),
			UserDefinedSimilarDeviceIdBlacklist:     parseInt(r.Predictors.UserDefinedSimilarDeviceIDBlacklist),
			UserDefinedClientIdBlacklist:            parseInt(r.Predictors.UserDefinedClientIDBlacklist),
			SuspiciousReferral:                      parseInt(r.Predictors.SuspiciousReferral),
			SuspiciousPlugins:                       parseInt(r.Predictors.SuspiciousPlugins),
			SuspiciousFonts:                         parseInt(r.Predictors.SuspiciousFonts),
			IsSessionClone:                          parseInt(r.Predictors.IsSessionClone),
			SocialVectorLength:                      parseInt(r.Predictors.SocialVectorLength),
			ShoppingVectorLength:                    parseInt(r.Predictors.ShoppingVectorLength),
			MicrolendingVectorLength:                parseInt(r.Predictors.MicrolendingVectorLength),
			GamblingVectorLength:                    parseInt(r.Predictors.GamblingVectorLength),
			ProxyVectorLength:                       parseInt(r.Predictors.ProxyVectorLength),
			AntivirusVectorLength:                   parseInt(r.Predictors.AntivirusVectorLength),
			RdpVectorLength:                         parseInt(r.Predictors.RDPVectorLength),
			NetfilterVectorLength:                   parseInt(r.Predictors.NetFilterVectorLength),
			TimeOnPage:                              parseFloat(r.Predictors.TimeOnPage),
			NumberOfHotKeys:                         parseInt(r.Predictors.NumberOfHotKeys),
			NumberOfCorrections:                     parseInt(r.Predictors.NumberOfCorrections),
			BrowserHistoryCount:                     parseInt(r.Predictors.BrowserHistoryCount),
			IsConsoleOpen:                           parseInt(r.Predictors.IsConsoleOpen),
			RamProductivity:                         parseInt(r.Predictors.RAMProductivity),
			StorageProductivity:                     parseInt(r.Predictors.StorageProductivity),
			HddUtils:                                parseFloat(r.Predictors.HDDUtils),
			SingleClick:                             parseInt(r.Predictors.SingleClick),
			DoubleClick:                             parseInt(r.Predictors.DoubleClick),
			ContextMenu:                             parseInt(r.Predictors.ContextMenu),
			LeavePage:                               parseInt(r.Predictors.LeavePage),
			ObservedQuarters:                        parseInt(r.Predictors.ObservedQuarters),
			CursorMovementSpeed:                     parseFloat(r.Predictors.CursorMovementSpeed),
			CursorDistanceCovered:                   parseFloat(r.Predictors.CursorDistanceCovered),
			ScrollMovementSpeed:                     parseFloat(r.Predictors.ScrollMovementSpeed),
			ScrollDistanceCovered:                   parseFloat(r.Predictors.ScrollDistanceCovered),
			AvgScreenIdleTime:                       parseFloat(r.Predictors.AvgScreenIdleTime),
			TouchDev:                                parseFloat(r.Predictors.TouchDev),
			AvgTouchSize:                            parseFloat(r.Predictors.AvgTouchSize),
			KeystrokeDwellTime:                      parseFloat(r.Predictors.KeyStrokeDwellTime),
			KeystrokeFlightTime:                     parseFloat(r.Predictors.KeyStrokeFlightTime),
			AvgTypingSpeed:                          parseFloat(r.Predictors.AvgTypingSpeed),
			IsDataMatching:                          parseInt(r.Predictors.IsDataMatching),
			IsWebrtcDataMatching:                    parseInt(r.Predictors.IsWebRTCDataMatching),
			LoanLimitUtilization:                    parseFloat(r.Predictors.LoanLimitUtilization),
			NumberOfSecondaryAntiFraudMarker:        parseInt(r.Predictors.NumberOfSecondaryAntiFraudMarkers),
			IpCountryCode:                           r.Predictors.IPCountryCode,
			IpCountryName:                           r.Predictors.IPCountryName,
			IpRegionName:                            r.Predictors.IPRegionName,
			IpCity:                                  r.Predictors.IPCity,
			IpLongitude:                             parseFloat(r.Predictors.IPLongitude),
			IpLatitude:                              parseFloat(r.Predictors.IPLatitude),
			IpOwner:                                 r.Predictors.IPOwner,
			IpDomain:                                r.Predictors.IPDomain,
			IpMobileBrand:                           r.Predictors.IPMobileBrand,
			IpUsageType:                             r.Predictors.IPUsageType,
			IpNetSpeed:                              r.Predictors.IPNetSpeed,
			Proxy:                                   parseInt(r.Predictors.Proxy),
			Tor:                                     parseInt(r.Predictors.TOR),
			ProxyType:                               r.Predictors.ProxyType,
			RealIp:                                  r.Predictors.RealIP,
			RealIpv6:                                r.Predictors.RealIPv6,
			Ipv6Usage:                               parseInt(r.Predictors.IPv6Usage),
			IpMismatch:                              parseInt(r.Predictors.IPMismatch),
			IpFirstSeenDate:                         parsedIPFirstSeenDate,
			IpAgeingInMonths:                        parseInt(r.Predictors.IPAgeingInMonths),
			InternetConnectionType:                  r.Predictors.InternetConnectionType,
			InternetConnectionSpeed:                 r.Predictors.InternetConnectionSpeed,
			ChDownlink:                              parseInt(r.Predictors.CHDownlink),
			JsDownlink:                              parseInt(r.Predictors.JSDownlink),
			DnsName:                                 r.Predictors.DNSName,
			DnsIp:                                   r.Predictors.DNSIP,
			IsDnsLocal:                              parseInt(r.Predictors.IsDNSLocal),
			IpZipCode:                               r.Predictors.IPZipCode,
			IpZipCodeDistance:                       parseInt(r.Predictors.IPZipCodeDistance),
			VirtualMachine:                          parseInt(r.Predictors.VirtualMachine),
			IsPoorVcSupport:                         parseInt(r.Predictors.IsPoorVCSupport),
			MobileOsVirtualMachine:                  parseInt(r.Predictors.MobileOSVirtualMachine),
			IsUseragentStructureIssue:               parseInt(r.Predictors.IsUserAgentStructureIssue),
			IsRandomizerIssue:                       parseInt(r.Predictors.IsRandomizerIssue),
			IsCanvasBlocker:                         parseInt(r.Predictors.IsCanvasBlocker),
			NumberOfBrowserAnomalies:                parseInt(r.Predictors.NumberOfBrowserAnomalies),
			FontsRandomizationLevel:                 parseInt(r.Predictors.FontsRandomizationLevel),
			NumberOfMissingDeviceStaticFingerprints: parseInt(r.Predictors.NumberOfMissingDeviceStaticFingerprints),
			IsStatDfprUnique:                        parseInt(r.Predictors.IsStatDfprUnique),
			IsSuspAspectRatio:                       parseInt(r.Predictors.IsSuspAspectRatio),
			IsHighRiskTls:                           parseInt(r.Predictors.IsHighRiskTLS),
			IsTlsNotUnique:                          parseInt(r.Predictors.IsTLSNOTUnique),
			IsTlsRandomizer:                         parseInt(r.Predictors.IsTLSRandomizer),
			IsLimitedNoiseRandomizer:                parseInt(r.Predictors.IsLimitedNoiseRandomizer),
			IsRemoteAccess:                          parseInt(r.Predictors.IsRemoteAccess),
			IsActiveCall:                            parseInt(r.Predictors.IsActiveCall),
			IsActiveVoipCall:                        parseInt(r.Predictors.IsActiveVoipCall),
			IsOnholdCall:                            parseInt(r.Predictors.IsOnholdCall),
			SuspiciousScripts:                       parseInt(r.Predictors.SuspiciousScripts),
			IsRootedDevice:                          parseInt(r.Predictors.IsRootedDevice),
			IsRootedDeviceCurrent:                   parseInt(r.Predictors.IsRootedDeviceCurrent),
			IsRootedDeviceBefore:                    parseInt(r.Predictors.IsRootedDeviceBefore),
			IsFactoryReset:                          parseInt(r.Predictors.IsFactoryReset),
			IsFakeGeoLocationUsed:                   parseInt(r.Predictors.IsFakeGeoLocationUsed),
			NonOfficialMobileApplication:            parseInt(r.Predictors.NonOfficialMobileApplication),
			IsPhoneCarrierLocal:                     parseInt(r.Predictors.IsPhoneCarrierLocal),
			NumberOfSimCardsOnDevice:                parseInt(r.Predictors.NumberOfSIMCardsOnDevice),
			WifiLevel:                               parseInt(r.Predictors.WifiLevel),
			BatteryLevel:                            parseInt(r.Predictors.BatteryLevel),
			BatteryStatus:                           parseInt(r.Predictors.BatteryStatus),
			DeviceUptimeSinceLastReboot:             parseInt(r.Predictors.DeviceUptimeSinceLastReboot),
			OsBootCount:                             parseInt(r.Predictors.OSBootCount),
			AvgReceivedTrafficViaWifi:               parseFloat(r.Predictors.AvgReceivedTrafficViaWifi),
			AvgTransmittedTrafficViaWifi:            parseFloat(r.Predictors.AvgTransmittedTrafficViaWifi),
			AvgMobileReceivedTrafficWoWif:           parseFloat(r.Predictors.AvgMobileTransmittedTrafficWoWifi),
			AvgMobileTransmittedTrafficWoWifi:       parseFloat(r.Predictors.AvgMobileTransmittedTrafficWoWifi),
			MaxSimTrfPerSec:                         parseFloat(r.Predictors.MaxSIMTrfPerSec),
			MaxWifiTrfPerSec:                        parseFloat(r.Predictors.MaxWifiTrfPerSec),
			DntModeUsed:                             parseInt(r.Predictors.DNTModeUsed),
			IsPrivateMode:                           parseInt(r.Predictors.IsPrivateMode),
			IsLockDownMode:                          parseInt(r.Predictors.IsLockDownMode),
			Botnet:                                  parseInt(r.Predictors.Botnet),
			NumberOfBotBehAnomalies:                 parseInt(r.Predictors.NumberOfBotBehAnomalies),
			ConnectionViaApplication:                parseInt(r.Predictors.ConnectionViaApplication),
			IsWebViewApplicationUsed:                parseInt(r.Predictors.IsWebViewApplicationUsed),
			DeviceType:                              r.Predictors.DeviceType,
			DeviceVendor:                            r.Predictors.DeviceVendor,
			DeviceModel:                             r.Predictors.DeviceModel,
			DeviceYearReleased:                      parseInt(r.Predictors.DeviceYearReleased),
			DisplayWidthViaPixel:                    parseInt(r.Predictors.DisplayWidthViaPixel),
			DisplayHeightViaPixel:                   parseInt(r.Predictors.DisplayHeightViaPixel),
			ScreenGeometryMismatch:                  parseInt(r.Predictors.ScreenGeometryMismatch),
			IsRegularScreenResolution:               parseInt(r.Predictors.IsRegularScreenResolution),
			SuspiciousGeometryUsed:                  parseInt(r.Predictors.SuspiciousGeometryUsed),
			DisplayPixelRatio:                       parseFloat(r.Predictors.DisplayPixelRatio),
			ColorDepthViaPixel:                      parseInt(r.Predictors.ColorDepthViaPixel),
			RamSize:                                 parseFloat(r.Predictors.RAMSize),
			HardwareConcurrency:                     parseInt(r.Predictors.HardwareConcurrency),
			Nfc:                                     parseInt(r.Predictors.NFC),
			Esim:                                    parseInt(r.Predictors.ESim),
			IpTimeZone:                              parseFloat(r.Predictors.IPTimeZone),
			IpTimeZoneName:                          r.Predictors.IPTimeZoneName,
			PixelTimeZone:                           parseFloat(r.Predictors.PixelTimeZone),
			PixelTimezoneName:                       r.Predictors.PixelTimeZoneName,
			DeviceLanguage:                          r.Predictors.DeviceLanguage,
			PixelLanguage:                           r.Predictors.PixelLanguage,
			OsName:                                  r.Predictors.OSName,
			OsVersion:                               r.Predictors.OSVersion,
			BrowserName:                             r.Predictors.BrowserName,
			BrowserVersion:                          r.Predictors.BrowserVersion,
			RenderingEngine:                         r.Predictors.RenderingEngine,
			BrowserVersionAgeing:                    parseInt(r.Predictors.BrowserVersionAgeing),
			RequestLogId:                            res.RequestLogID,
		}

		return result
	}
	return &pb.GetJuicyScoreResp{}
}
