package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/juicyscore-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/entity"
)

func (s *Server) GetJuicyScore(ctx context.Context, req *pb.GetJuicyScoreReq) (*pb.GetJuicyScoreResp, error) {
	getJuicyScoreEntity := entity.MakeGetJuicyScorePbToEntity(req)

	getJuicyScore, err := s.useCase.GetJuicyScore(ctx, getJuicyScoreEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetJuicyScoreEntityToPb(getJuicyScore), nil
}
