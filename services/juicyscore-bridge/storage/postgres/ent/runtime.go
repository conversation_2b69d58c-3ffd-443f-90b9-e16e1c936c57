// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/requestlog"
	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
	requestlogMixin := schema.RequestLog{}.Mixin()
	requestlogMixinFields0 := requestlogMixin[0].Fields()
	_ = requestlogMixinFields0
	requestlogFields := schema.RequestLog{}.Fields()
	_ = requestlogFields
	// requestlogDescCreateTime is the schema descriptor for create_time field.
	requestlogDescCreateTime := requestlogMixinFields0[0].Descriptor()
	// requestlog.DefaultCreateTime holds the default value on creation for the create_time field.
	requestlog.DefaultCreateTime = requestlogDescCreateTime.Default.(func() time.Time)
	// requestlogDescUpdateTime is the schema descriptor for update_time field.
	requestlogDescUpdateTime := requestlogMixinFields0[1].Descriptor()
	// requestlog.DefaultUpdateTime holds the default value on creation for the update_time field.
	requestlog.DefaultUpdateTime = requestlogDescUpdateTime.Default.(func() time.Time)
	// requestlog.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	requestlog.UpdateDefaultUpdateTime = requestlogDescUpdateTime.UpdateDefault.(func() time.Time)
	// requestlogDescExternalID is the schema descriptor for external_id field.
	requestlogDescExternalID := requestlogFields[1].Descriptor()
	// requestlog.ExternalIDValidator is a validator for the "external_id" field. It is called by the builders before save.
	requestlog.ExternalIDValidator = requestlogDescExternalID.Validators[0].(func(string) error)
	// requestlogDescID is the schema descriptor for id field.
	requestlogDescID := requestlogFields[0].Descriptor()
	// requestlog.IDValidator is a validator for the "id" field. It is called by the builders before save.
	requestlog.IDValidator = requestlogDescID.Validators[0].(func(int64) error)
}
