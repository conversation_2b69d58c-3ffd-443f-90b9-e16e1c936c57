// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/requestlog"
)

// RequestLogCreate is the builder for creating a RequestLog entity.
type RequestLogCreate struct {
	config
	mutation *RequestLogMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *RequestLogCreate) SetCreateTime(v time.Time) *RequestLogCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *RequestLogCreate) SetNillableCreateTime(v *time.Time) *RequestLogCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *RequestLogCreate) SetUpdateTime(v time.Time) *RequestLogCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *RequestLogCreate) SetNillableUpdateTime(v *time.Time) *RequestLogCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetExternalID sets the "external_id" field.
func (_c *RequestLogCreate) SetExternalID(v string) *RequestLogCreate {
	_c.mutation.SetExternalID(v)
	return _c
}

// SetID sets the "id" field.
func (_c *RequestLogCreate) SetID(v int64) *RequestLogCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the RequestLogMutation object of the builder.
func (_c *RequestLogCreate) Mutation() *RequestLogMutation {
	return _c.mutation
}

// Save creates the RequestLog in the database.
func (_c *RequestLogCreate) Save(ctx context.Context) (*RequestLog, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *RequestLogCreate) SaveX(ctx context.Context) *RequestLog {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *RequestLogCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *RequestLogCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *RequestLogCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := requestlog.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := requestlog.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *RequestLogCreate) check() error {
	if _, ok := _c.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "RequestLog.create_time"`)}
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "RequestLog.update_time"`)}
	}
	if _, ok := _c.mutation.ExternalID(); !ok {
		return &ValidationError{Name: "external_id", err: errors.New(`ent: missing required field "RequestLog.external_id"`)}
	}
	if v, ok := _c.mutation.ExternalID(); ok {
		if err := requestlog.ExternalIDValidator(v); err != nil {
			return &ValidationError{Name: "external_id", err: fmt.Errorf(`ent: validator failed for field "RequestLog.external_id": %w`, err)}
		}
	}
	if v, ok := _c.mutation.ID(); ok {
		if err := requestlog.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "RequestLog.id": %w`, err)}
		}
	}
	return nil
}

func (_c *RequestLogCreate) sqlSave(ctx context.Context) (*RequestLog, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *RequestLogCreate) createSpec() (*RequestLog, *sqlgraph.CreateSpec) {
	var (
		_node = &RequestLog{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(requestlog.Table, sqlgraph.NewFieldSpec(requestlog.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(requestlog.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(requestlog.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.ExternalID(); ok {
		_spec.SetField(requestlog.FieldExternalID, field.TypeString, value)
		_node.ExternalID = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.RequestLog.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RequestLogUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *RequestLogCreate) OnConflict(opts ...sql.ConflictOption) *RequestLogUpsertOne {
	_c.conflict = opts
	return &RequestLogUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.RequestLog.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *RequestLogCreate) OnConflictColumns(columns ...string) *RequestLogUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &RequestLogUpsertOne{
		create: _c,
	}
}

type (
	// RequestLogUpsertOne is the builder for "upsert"-ing
	//  one RequestLog node.
	RequestLogUpsertOne struct {
		create *RequestLogCreate
	}

	// RequestLogUpsert is the "OnConflict" setter.
	RequestLogUpsert struct {
		*sql.UpdateSet
	}
)

// SetExternalID sets the "external_id" field.
func (u *RequestLogUpsert) SetExternalID(v string) *RequestLogUpsert {
	u.Set(requestlog.FieldExternalID, v)
	return u
}

// UpdateExternalID sets the "external_id" field to the value that was provided on create.
func (u *RequestLogUpsert) UpdateExternalID() *RequestLogUpsert {
	u.SetExcluded(requestlog.FieldExternalID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.RequestLog.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(requestlog.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RequestLogUpsertOne) UpdateNewValues() *RequestLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(requestlog.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(requestlog.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(requestlog.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.RequestLog.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *RequestLogUpsertOne) Ignore() *RequestLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RequestLogUpsertOne) DoNothing() *RequestLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RequestLogCreate.OnConflict
// documentation for more info.
func (u *RequestLogUpsertOne) Update(set func(*RequestLogUpsert)) *RequestLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RequestLogUpsert{UpdateSet: update})
	}))
	return u
}

// SetExternalID sets the "external_id" field.
func (u *RequestLogUpsertOne) SetExternalID(v string) *RequestLogUpsertOne {
	return u.Update(func(s *RequestLogUpsert) {
		s.SetExternalID(v)
	})
}

// UpdateExternalID sets the "external_id" field to the value that was provided on create.
func (u *RequestLogUpsertOne) UpdateExternalID() *RequestLogUpsertOne {
	return u.Update(func(s *RequestLogUpsert) {
		s.UpdateExternalID()
	})
}

// Exec executes the query.
func (u *RequestLogUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for RequestLogCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RequestLogUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *RequestLogUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *RequestLogUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// RequestLogCreateBulk is the builder for creating many RequestLog entities in bulk.
type RequestLogCreateBulk struct {
	config
	err      error
	builders []*RequestLogCreate
	conflict []sql.ConflictOption
}

// Save creates the RequestLog entities in the database.
func (_c *RequestLogCreateBulk) Save(ctx context.Context) ([]*RequestLog, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*RequestLog, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*RequestLogMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *RequestLogCreateBulk) SaveX(ctx context.Context) []*RequestLog {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *RequestLogCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *RequestLogCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.RequestLog.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RequestLogUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *RequestLogCreateBulk) OnConflict(opts ...sql.ConflictOption) *RequestLogUpsertBulk {
	_c.conflict = opts
	return &RequestLogUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.RequestLog.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *RequestLogCreateBulk) OnConflictColumns(columns ...string) *RequestLogUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &RequestLogUpsertBulk{
		create: _c,
	}
}

// RequestLogUpsertBulk is the builder for "upsert"-ing
// a bulk of RequestLog nodes.
type RequestLogUpsertBulk struct {
	create *RequestLogCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.RequestLog.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(requestlog.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RequestLogUpsertBulk) UpdateNewValues() *RequestLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(requestlog.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(requestlog.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(requestlog.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.RequestLog.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *RequestLogUpsertBulk) Ignore() *RequestLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RequestLogUpsertBulk) DoNothing() *RequestLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RequestLogCreateBulk.OnConflict
// documentation for more info.
func (u *RequestLogUpsertBulk) Update(set func(*RequestLogUpsert)) *RequestLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RequestLogUpsert{UpdateSet: update})
	}))
	return u
}

// SetExternalID sets the "external_id" field.
func (u *RequestLogUpsertBulk) SetExternalID(v string) *RequestLogUpsertBulk {
	return u.Update(func(s *RequestLogUpsert) {
		s.SetExternalID(v)
	})
}

// UpdateExternalID sets the "external_id" field to the value that was provided on create.
func (u *RequestLogUpsertBulk) UpdateExternalID() *RequestLogUpsertBulk {
	return u.Update(func(s *RequestLogUpsert) {
		s.UpdateExternalID()
	})
}

// Exec executes the query.
func (u *RequestLogUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the RequestLogCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for RequestLogCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RequestLogUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
