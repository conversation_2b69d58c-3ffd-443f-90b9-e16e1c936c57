// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/migrate"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/requestlog"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Health is the client for interacting with the Health builders.
	Health *HealthClient
	// RequestLog is the client for interacting with the RequestLog builders.
	RequestLog *RequestLogClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Health = NewHealthClient(c.config)
	c.RequestLog = NewRequestLogClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:        ctx,
		config:     cfg,
		Health:     NewHealthClient(cfg),
		RequestLog: NewRequestLogClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:        ctx,
		config:     cfg,
		Health:     NewHealthClient(cfg),
		RequestLog: NewRequestLogClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Health.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.Health.Use(hooks...)
	c.RequestLog.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.Health.Intercept(interceptors...)
	c.RequestLog.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *HealthMutation:
		return c.Health.mutate(ctx, m)
	case *RequestLogMutation:
		return c.RequestLog.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// HealthClient is a client for the Health schema.
type HealthClient struct {
	config
}

// NewHealthClient returns a client for the Health from the given config.
func NewHealthClient(c config) *HealthClient {
	return &HealthClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `health.Hooks(f(g(h())))`.
func (c *HealthClient) Use(hooks ...Hook) {
	c.hooks.Health = append(c.hooks.Health, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `health.Intercept(f(g(h())))`.
func (c *HealthClient) Intercept(interceptors ...Interceptor) {
	c.inters.Health = append(c.inters.Health, interceptors...)
}

// Create returns a builder for creating a Health entity.
func (c *HealthClient) Create() *HealthCreate {
	mutation := newHealthMutation(c.config, OpCreate)
	return &HealthCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Health entities.
func (c *HealthClient) CreateBulk(builders ...*HealthCreate) *HealthCreateBulk {
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *HealthClient) MapCreateBulk(slice any, setFunc func(*HealthCreate, int)) *HealthCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &HealthCreateBulk{err: fmt.Errorf("calling to HealthClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*HealthCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Health.
func (c *HealthClient) Update() *HealthUpdate {
	mutation := newHealthMutation(c.config, OpUpdate)
	return &HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *HealthClient) UpdateOne(_m *Health) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealth(_m))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *HealthClient) UpdateOneID(id uuid.UUID) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealthID(id))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Health.
func (c *HealthClient) Delete() *HealthDelete {
	mutation := newHealthMutation(c.config, OpDelete)
	return &HealthDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *HealthClient) DeleteOne(_m *Health) *HealthDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *HealthClient) DeleteOneID(id uuid.UUID) *HealthDeleteOne {
	builder := c.Delete().Where(health.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &HealthDeleteOne{builder}
}

// Query returns a query builder for Health.
func (c *HealthClient) Query() *HealthQuery {
	return &HealthQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeHealth},
		inters: c.Interceptors(),
	}
}

// Get returns a Health entity by its id.
func (c *HealthClient) Get(ctx context.Context, id uuid.UUID) (*Health, error) {
	return c.Query().Where(health.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *HealthClient) GetX(ctx context.Context, id uuid.UUID) *Health {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *HealthClient) Hooks() []Hook {
	return c.hooks.Health
}

// Interceptors returns the client interceptors.
func (c *HealthClient) Interceptors() []Interceptor {
	return c.inters.Health
}

func (c *HealthClient) mutate(ctx context.Context, m *HealthMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&HealthCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&HealthDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Health mutation op: %q", m.Op())
	}
}

// RequestLogClient is a client for the RequestLog schema.
type RequestLogClient struct {
	config
}

// NewRequestLogClient returns a client for the RequestLog from the given config.
func NewRequestLogClient(c config) *RequestLogClient {
	return &RequestLogClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `requestlog.Hooks(f(g(h())))`.
func (c *RequestLogClient) Use(hooks ...Hook) {
	c.hooks.RequestLog = append(c.hooks.RequestLog, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `requestlog.Intercept(f(g(h())))`.
func (c *RequestLogClient) Intercept(interceptors ...Interceptor) {
	c.inters.RequestLog = append(c.inters.RequestLog, interceptors...)
}

// Create returns a builder for creating a RequestLog entity.
func (c *RequestLogClient) Create() *RequestLogCreate {
	mutation := newRequestLogMutation(c.config, OpCreate)
	return &RequestLogCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of RequestLog entities.
func (c *RequestLogClient) CreateBulk(builders ...*RequestLogCreate) *RequestLogCreateBulk {
	return &RequestLogCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *RequestLogClient) MapCreateBulk(slice any, setFunc func(*RequestLogCreate, int)) *RequestLogCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &RequestLogCreateBulk{err: fmt.Errorf("calling to RequestLogClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*RequestLogCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &RequestLogCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for RequestLog.
func (c *RequestLogClient) Update() *RequestLogUpdate {
	mutation := newRequestLogMutation(c.config, OpUpdate)
	return &RequestLogUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *RequestLogClient) UpdateOne(_m *RequestLog) *RequestLogUpdateOne {
	mutation := newRequestLogMutation(c.config, OpUpdateOne, withRequestLog(_m))
	return &RequestLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *RequestLogClient) UpdateOneID(id int64) *RequestLogUpdateOne {
	mutation := newRequestLogMutation(c.config, OpUpdateOne, withRequestLogID(id))
	return &RequestLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for RequestLog.
func (c *RequestLogClient) Delete() *RequestLogDelete {
	mutation := newRequestLogMutation(c.config, OpDelete)
	return &RequestLogDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *RequestLogClient) DeleteOne(_m *RequestLog) *RequestLogDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *RequestLogClient) DeleteOneID(id int64) *RequestLogDeleteOne {
	builder := c.Delete().Where(requestlog.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &RequestLogDeleteOne{builder}
}

// Query returns a query builder for RequestLog.
func (c *RequestLogClient) Query() *RequestLogQuery {
	return &RequestLogQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeRequestLog},
		inters: c.Interceptors(),
	}
}

// Get returns a RequestLog entity by its id.
func (c *RequestLogClient) Get(ctx context.Context, id int64) (*RequestLog, error) {
	return c.Query().Where(requestlog.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *RequestLogClient) GetX(ctx context.Context, id int64) *RequestLog {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *RequestLogClient) Hooks() []Hook {
	return c.hooks.RequestLog
}

// Interceptors returns the client interceptors.
func (c *RequestLogClient) Interceptors() []Interceptor {
	return c.inters.RequestLog
}

func (c *RequestLogClient) mutate(ctx context.Context, m *RequestLogMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&RequestLogCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&RequestLogUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&RequestLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&RequestLogDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown RequestLog mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Health, RequestLog []ent.Hook
	}
	inters struct {
		Health, RequestLog []ent.Interceptor
	}
)
