// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/requestlog"
)

// RequestLogUpdate is the builder for updating RequestLog entities.
type RequestLogUpdate struct {
	config
	hooks    []Hook
	mutation *RequestLogMutation
}

// Where appends a list predicates to the RequestLogUpdate builder.
func (_u *RequestLogUpdate) Where(ps ...predicate.RequestLog) *RequestLogUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetExternalID sets the "external_id" field.
func (_u *RequestLogUpdate) SetExternalID(v string) *RequestLogUpdate {
	_u.mutation.SetExternalID(v)
	return _u
}

// SetNillableExternalID sets the "external_id" field if the given value is not nil.
func (_u *RequestLogUpdate) SetNillableExternalID(v *string) *RequestLogUpdate {
	if v != nil {
		_u.SetExternalID(*v)
	}
	return _u
}

// Mutation returns the RequestLogMutation object of the builder.
func (_u *RequestLogUpdate) Mutation() *RequestLogMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *RequestLogUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *RequestLogUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *RequestLogUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *RequestLogUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *RequestLogUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := requestlog.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *RequestLogUpdate) check() error {
	if v, ok := _u.mutation.ExternalID(); ok {
		if err := requestlog.ExternalIDValidator(v); err != nil {
			return &ValidationError{Name: "external_id", err: fmt.Errorf(`ent: validator failed for field "RequestLog.external_id": %w`, err)}
		}
	}
	return nil
}

func (_u *RequestLogUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(requestlog.Table, requestlog.Columns, sqlgraph.NewFieldSpec(requestlog.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(requestlog.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ExternalID(); ok {
		_spec.SetField(requestlog.FieldExternalID, field.TypeString, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{requestlog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// RequestLogUpdateOne is the builder for updating a single RequestLog entity.
type RequestLogUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *RequestLogMutation
}

// SetExternalID sets the "external_id" field.
func (_u *RequestLogUpdateOne) SetExternalID(v string) *RequestLogUpdateOne {
	_u.mutation.SetExternalID(v)
	return _u
}

// SetNillableExternalID sets the "external_id" field if the given value is not nil.
func (_u *RequestLogUpdateOne) SetNillableExternalID(v *string) *RequestLogUpdateOne {
	if v != nil {
		_u.SetExternalID(*v)
	}
	return _u
}

// Mutation returns the RequestLogMutation object of the builder.
func (_u *RequestLogUpdateOne) Mutation() *RequestLogMutation {
	return _u.mutation
}

// Where appends a list predicates to the RequestLogUpdate builder.
func (_u *RequestLogUpdateOne) Where(ps ...predicate.RequestLog) *RequestLogUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *RequestLogUpdateOne) Select(field string, fields ...string) *RequestLogUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated RequestLog entity.
func (_u *RequestLogUpdateOne) Save(ctx context.Context) (*RequestLog, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *RequestLogUpdateOne) SaveX(ctx context.Context) *RequestLog {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *RequestLogUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *RequestLogUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *RequestLogUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := requestlog.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *RequestLogUpdateOne) check() error {
	if v, ok := _u.mutation.ExternalID(); ok {
		if err := requestlog.ExternalIDValidator(v); err != nil {
			return &ValidationError{Name: "external_id", err: fmt.Errorf(`ent: validator failed for field "RequestLog.external_id": %w`, err)}
		}
	}
	return nil
}

func (_u *RequestLogUpdateOne) sqlSave(ctx context.Context) (_node *RequestLog, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(requestlog.Table, requestlog.Columns, sqlgraph.NewFieldSpec(requestlog.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "RequestLog.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, requestlog.FieldID)
		for _, f := range fields {
			if !requestlog.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != requestlog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(requestlog.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.ExternalID(); ok {
		_spec.SetField(requestlog.FieldExternalID, field.TypeString, value)
	}
	_node = &RequestLog{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{requestlog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
