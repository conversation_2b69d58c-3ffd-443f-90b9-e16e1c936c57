// Code generated by ent, DO NOT EDIT.

package requestlog

import (
	"time"

	"entgo.io/ent/dialect/sql"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEQ(FieldUpdateTime, v))
}

// ExternalID applies equality check predicate on the "external_id" field. It's identical to ExternalIDEQ.
func ExternalID(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEQ(FieldExternalID, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldLTE(FieldUpdateTime, v))
}

// ExternalIDEQ applies the EQ predicate on the "external_id" field.
func ExternalIDEQ(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEQ(FieldExternalID, v))
}

// ExternalIDNEQ applies the NEQ predicate on the "external_id" field.
func ExternalIDNEQ(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldNEQ(FieldExternalID, v))
}

// ExternalIDIn applies the In predicate on the "external_id" field.
func ExternalIDIn(vs ...string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldIn(FieldExternalID, vs...))
}

// ExternalIDNotIn applies the NotIn predicate on the "external_id" field.
func ExternalIDNotIn(vs ...string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldNotIn(FieldExternalID, vs...))
}

// ExternalIDGT applies the GT predicate on the "external_id" field.
func ExternalIDGT(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldGT(FieldExternalID, v))
}

// ExternalIDGTE applies the GTE predicate on the "external_id" field.
func ExternalIDGTE(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldGTE(FieldExternalID, v))
}

// ExternalIDLT applies the LT predicate on the "external_id" field.
func ExternalIDLT(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldLT(FieldExternalID, v))
}

// ExternalIDLTE applies the LTE predicate on the "external_id" field.
func ExternalIDLTE(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldLTE(FieldExternalID, v))
}

// ExternalIDContains applies the Contains predicate on the "external_id" field.
func ExternalIDContains(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldContains(FieldExternalID, v))
}

// ExternalIDHasPrefix applies the HasPrefix predicate on the "external_id" field.
func ExternalIDHasPrefix(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldHasPrefix(FieldExternalID, v))
}

// ExternalIDHasSuffix applies the HasSuffix predicate on the "external_id" field.
func ExternalIDHasSuffix(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldHasSuffix(FieldExternalID, v))
}

// ExternalIDEqualFold applies the EqualFold predicate on the "external_id" field.
func ExternalIDEqualFold(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldEqualFold(FieldExternalID, v))
}

// ExternalIDContainsFold applies the ContainsFold predicate on the "external_id" field.
func ExternalIDContainsFold(v string) predicate.RequestLog {
	return predicate.RequestLog(sql.FieldContainsFold(FieldExternalID, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.RequestLog) predicate.RequestLog {
	return predicate.RequestLog(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.RequestLog) predicate.RequestLog {
	return predicate.RequestLog(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.RequestLog) predicate.RequestLog {
	return predicate.RequestLog(sql.NotPredicates(p))
}
