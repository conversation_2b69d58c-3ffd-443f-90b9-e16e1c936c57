// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// HealthsColumns holds the columns for the "healths" table.
	HealthsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
	}
	// HealthsTable holds the schema information for the "healths" table.
	HealthsTable = &schema.Table{
		Name:       "healths",
		Columns:    HealthsColumns,
		PrimaryKey: []*schema.Column{HealthsColumns[0]},
	}
	// RequestLogsColumns holds the columns for the "request_logs" table.
	RequestLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "create_time", Type: field.TypeTime},
		{Name: "update_time", Type: field.TypeTime},
		{Name: "external_id", Type: field.TypeString},
	}
	// RequestLogsTable holds the schema information for the "request_logs" table.
	RequestLogsTable = &schema.Table{
		Name:       "request_logs",
		Columns:    RequestLogsColumns,
		PrimaryKey: []*schema.Column{RequestLogsColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		HealthsTable,
		RequestLogsTable,
	}
)

func init() {
}
