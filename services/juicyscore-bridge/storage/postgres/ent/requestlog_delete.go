// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent/requestlog"
)

// RequestLogDelete is the builder for deleting a RequestLog entity.
type RequestLogDelete struct {
	config
	hooks    []Hook
	mutation *RequestLogMutation
}

// Where appends a list predicates to the RequestLogDelete builder.
func (_d *RequestLogDelete) Where(ps ...predicate.RequestLog) *RequestLogDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *RequestLogDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *RequestLogDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *RequestLogDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(requestlog.Table, sqlgraph.NewFieldSpec(requestlog.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// RequestLogDeleteOne is the builder for deleting a single RequestLog entity.
type RequestLogDeleteOne struct {
	_d *RequestLogDelete
}

// Where appends a list predicates to the RequestLogDelete builder.
func (_d *RequestLogDeleteOne) Where(ps ...predicate.RequestLog) *RequestLogDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *RequestLogDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{requestlog.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *RequestLogDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
