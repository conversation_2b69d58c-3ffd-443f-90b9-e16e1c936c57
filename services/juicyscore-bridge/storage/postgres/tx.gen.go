// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package storage

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/transaction"

	"git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres/ent"
)

func MakeTxFn(client *ent.Client) func(ctx context.Context, options *sql.TxOptions) (*transaction.Tx[ent.Client], error) {
	return func(ctx context.Context, options *sql.TxOptions) (*transaction.Tx[ent.Client], error) {
		transactionClient, err := client.BeginTx(ctx, options)
		if err != nil {
			logs.FromContext(ctx).Err(err).Msg("failed to open transaction")

			return nil, err
		}

		return &transaction.Tx[ent.Client]{
			Transaction: transactionClient,
			Client:      client,
		}, nil
	}
}
