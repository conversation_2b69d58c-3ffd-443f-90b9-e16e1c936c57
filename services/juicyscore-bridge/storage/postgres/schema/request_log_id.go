package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/mixin"
)

type RequestLog struct {
	ent.Schema
}

func (RequestLog) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").
			Unique().
			Positive().
			Immutable().
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор записи для мапинга идентификатора запроса СПР"}),
		field.String("external_id").
			NotEmpty().
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор запроса к системе JuicyScore"}),
	}
}

func (RequestLog) Annotations() []schema.Annotation {
	return []schema.Annotation{}
}

func (RequestLog) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
