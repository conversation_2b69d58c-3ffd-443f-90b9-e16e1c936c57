package storage

import (
	"context"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

var _ RawDataMongoWithID = (*storageImpl)(nil)

type RawDataMongoWithID interface {
	SaveRawDataWithID(ctx context.Context, data interface{}, collection string) (string, error)
}

func (s *storageImpl) SaveRawDataWithID(ctx context.Context, data interface{}, collection string) (string, error) {
	coll := s.MongoClient.DB.Collection(collection)

	res, err := coll.InsertOne(ctx, data)
	if err != nil {
		return "", err
	}

	id, ok := res.InsertedID.(primitive.ObjectID)
	if !ok {
		return "", err
	}

	return id.Hex(), nil
}
