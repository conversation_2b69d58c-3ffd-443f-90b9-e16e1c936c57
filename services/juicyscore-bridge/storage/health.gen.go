// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package storage

import (
	"context"
	"database/sql"

	pg "git.redmadrobot.com/zaman/backend/zaman/services/juicyscore-bridge/storage/postgres"
)

var _ Health = (*storageImpl)(nil)

type Health interface {
	Check(ctx context.Context) error
}

func (s *storageImpl) Check(ctx context.Context) error {
	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelRepeatableRead})
	if err != nil {
		return err
	}
	defer tx.Done(ctx)

	_, err = tx.Client.Health.Query().All(ctx)
	if err != nil {
		return err
	}

	if err := s.MongoClient.HealthCheck(ctx); err != nil {
		return err
	}

	return nil
}
