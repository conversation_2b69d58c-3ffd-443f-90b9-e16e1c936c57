// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
}

type Providers struct {
	JuicyScoreProvider *MockJuicyScoreProvider
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{},
		Providers: Providers{
			JuicyScoreProvider: NewMockJuicyScoreProvider(gomock.NewController(t)),
		},
	}
}
