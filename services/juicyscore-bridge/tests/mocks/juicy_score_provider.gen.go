// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/juicyscore (interfaces: JuicyScoreProvider)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	juicyscore "git.redmadrobot.com/zaman/backend/zaman/pkg/juicyscore"
	utils "git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
)

// MockJuicyScoreProvider is a mock of JuicyScoreProvider interface.
type MockJuicyScoreProvider struct {
	ctrl     *gomock.Controller
	recorder *MockJuicyScoreProviderMockRecorder
}

// MockJuicyScoreProviderMockRecorder is the mock recorder for MockJuicyScoreProvider.
type MockJuicyScoreProviderMockRecorder struct {
	mock *MockJuicyScoreProvider
}

// NewMockJuicyScoreProvider creates a new mock instance.
func NewMockJuicyScoreProvider(ctrl *gomock.Controller) *MockJuicyScoreProvider {
	mock := &MockJuicyScoreProvider{ctrl: ctrl}
	mock.recorder = &MockJuicyScoreProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJuicyScoreProvider) EXPECT() *MockJuicyScoreProviderMockRecorder {
	return m.recorder
}

// GetJuicyScore mocks base method.
func (m *MockJuicyScoreProvider) GetJuicyScore(arg0 context.Context, arg1 juicyscore.ParamsGetJuicyScore) (*juicyscore.GetJuicyScoreResponse, *utils.RequestLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetJuicyScore", arg0, arg1)
	ret0, _ := ret[0].(*juicyscore.GetJuicyScoreResponse)
	ret1, _ := ret[1].(*utils.RequestLog)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetJuicyScore indicates an expected call of GetJuicyScore.
func (mr *MockJuicyScoreProviderMockRecorder) GetJuicyScore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJuicyScore", reflect.TypeOf((*MockJuicyScoreProvider)(nil).GetJuicyScore), arg0, arg1)
}
