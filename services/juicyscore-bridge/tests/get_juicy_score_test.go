package tests

import (
	"errors"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/juicyscore"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/juicyscore-bridge"
)

var (
	additionalInfo             = "test info"
	successJuiscyScoreResponse = &juicyscore.GetJuicyScoreResponse{
		AntiFraudScore: "0.85",
		AdditionalInfo: additionalInfo,
		DeviceID:       "123456789",
		ExactDeviceID:  "987654321",
		BrowserHash:    "test-browser-hash",
		UserID:         "12345",
		Success:        true,
		Time:           **********.0, // 2024-03-15 12:00:00 UTC
	}
)

func (s *Suite) TestGetJuicyScore_Success() {
	s.mocks.Providers.JuicyScoreProvider.EXPECT().
		GetJuicyScore(gomock.Any(), gomock.Any()).
		Return(successJuiscyScoreResponse, nil, nil).Times(1)

	resp, err := s.grpc.GetJuicyScore(s.ctx, &pb.GetJuicyScoreReq{
		Params: &pb.Params{
			ApplicationId: "test-app-id",
			UserId:        "test-user-id",
			Amount:        1000,
			SessionId:     "test-session-id",
			RequestTime:   timestamppb.New(time.Date(2024, 3, 15, 12, 0, 0, 0, time.UTC)),
			TimeZone:      "UTC",
			Ip:            "127.0.0.1",
			UserAgent:     "test-user-agent",
			PhoneNumber:   "77777777777",
			Tenor:         30,
		},
	})

	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(0.85, resp.AntiFraudScore)
	s.Require().NotNil(resp.AdditionalInfo)
	s.Require().Equal(successJuiscyScoreResponse.AdditionalInfo, resp.AdditionalInfo)
	s.Require().Equal(int64(123456789), resp.DeviceId)
	s.Require().Equal(int64(987654321), resp.ExactDeviceId)
	s.Require().Equal(successJuiscyScoreResponse.BrowserHash, resp.BrowserHash)
	s.Require().Equal(int64(12345), resp.UserId)
	s.Require().Equal(successJuiscyScoreResponse.Success, resp.Success)
}

func (s *Suite) TestGetJuicyScore_ProviderError() {
	s.mocks.Providers.JuicyScoreProvider.EXPECT().
		GetJuicyScore(gomock.Any(), gomock.Any()).
		Return(nil, nil, errors.New("provider error")).Times(1)

	resp, err := s.grpc.GetJuicyScore(s.ctx, &pb.GetJuicyScoreReq{
		Params: &pb.Params{
			ApplicationId: "test-app-id",
			Amount:        1000,
			SessionId:     "test-session-id",
			UserId:        "test-user-id",
			RequestTime:   timestamppb.New(time.Date(2024, 3, 15, 12, 0, 0, 0, time.UTC)),
			TimeZone:      "UTC",
			Ip:            "127.0.0.1",
			UserAgent:     "test-user-agent",
			PhoneNumber:   "77777777777",
			Tenor:         30,
		},
	})

	s.Require().Error(err)
	s.Require().Nil(resp)
}

func (s *Suite) TestGetJuicyScore_RetryOnInternalError() {
	s.mocks.Providers.JuicyScoreProvider.EXPECT().
		GetJuicyScore(gomock.Any(), gomock.Any()).
		Return(nil, nil, juicyscore.ErrJuicyScoreInternal).Times(1)

	s.mocks.Providers.JuicyScoreProvider.EXPECT().
		GetJuicyScore(gomock.Any(), gomock.Any()).
		Return(successJuiscyScoreResponse, nil, nil).Times(1)

	resp, err := s.grpc.GetJuicyScore(s.ctx, &pb.GetJuicyScoreReq{
		Params: &pb.Params{
			ApplicationId: "test-app-id",
			Amount:        1000,
			SessionId:     "test-session-id",
			UserId:        "test-user-id",
			RequestTime:   timestamppb.New(time.Date(2024, 3, 15, 12, 0, 0, 0, time.UTC)),
			TimeZone:      "UTC",
			Ip:            "127.0.0.1",
			UserAgent:     "test-user-agent",
			PhoneNumber:   "77777777777",
			Tenor:         30,
		},
	})

	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(0.85, resp.AntiFraudScore)
}
