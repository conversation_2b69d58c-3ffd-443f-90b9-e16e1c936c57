// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/qazpost (interfaces: QazPostProvider)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	qazpost "git.redmadrobot.com/zaman/backend/zaman/pkg/qazpost"
)

// MockQazPostProvider is a mock of QazPostProvider interface.
type MockQazPostProvider struct {
	ctrl     *gomock.Controller
	recorder *MockQazPostProviderMockRecorder
}

// MockQazPostProviderMockRecorder is the mock recorder for MockQazPostProvider.
type MockQazPostProviderMockRecorder struct {
	mock *MockQazPostProvider
}

// NewMockQazPostProvider creates a new mock instance.
func NewMockQazPostProvider(ctrl *gomock.Controller) *MockQazPostProvider {
	mock := &MockQazPostProvider{ctrl: ctrl}
	mock.recorder = &MockQazPostProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQazPostProvider) EXPECT() *MockQazPostProviderMockRecorder {
	return m.recorder
}

// GetAuthToken mocks base method.
func (m *MockQazPostProvider) GetAuthToken(arg0 context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthToken", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthToken indicates an expected call of GetAuthToken.
func (mr *MockQazPostProviderMockRecorder) GetAuthToken(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthToken", reflect.TypeOf((*MockQazPostProvider)(nil).GetAuthToken), arg0)
}

// GetOldIndex mocks base method.
func (m *MockQazPostProvider) GetOldIndex(arg0 context.Context, arg1, arg2 string) (*qazpost.OldIndexResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOldIndex", arg0, arg1, arg2)
	ret0, _ := ret[0].(*qazpost.OldIndexResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOldIndex indicates an expected call of GetOldIndex.
func (mr *MockQazPostProviderMockRecorder) GetOldIndex(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOldIndex", reflect.TypeOf((*MockQazPostProvider)(nil).GetOldIndex), arg0, arg1, arg2)
}

// SearchNPI mocks base method.
func (m *MockQazPostProvider) SearchNPI(arg0 context.Context, arg1, arg2 string) ([]qazpost.NpiSearchResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchNPI", arg0, arg1, arg2)
	ret0, _ := ret[0].([]qazpost.NpiSearchResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchNPI indicates an expected call of SearchNPI.
func (mr *MockQazPostProviderMockRecorder) SearchNPI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchNPI", reflect.TypeOf((*MockQazPostProvider)(nil).SearchNPI), arg0, arg1, arg2)
}
