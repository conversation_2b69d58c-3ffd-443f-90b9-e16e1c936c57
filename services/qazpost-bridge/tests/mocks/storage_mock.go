package mocks

import (
	"context"
	"reflect"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
)

// MockStorage is a mock of Storage interface
type MockStorage struct {
	ctrl     *gomock.Controller
	recorder *MockStorageMockRecorder
}

// MockStorageMockRecorder is the mock recorder for MockStorage
type MockStorageMockRecorder struct {
	mock *MockStorage
}

// NewMockStorage creates a new mock instance
func NewMockStorage(ctrl *gomock.Controller) *MockStorage {
	mock := &MockStorage{ctrl: ctrl}
	mock.recorder = &MockStorageMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockStorage) EXPECT() *MockStorageMockRecorder {
	return m.recorder
}

// SaveQazPostLog mocks base method
func (m *MockStorage) SaveQazPostLog(ctx context.Context, logEntry *entity.QazPostLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveQazPostLog", ctx, logEntry)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveQazPostLog indicates an expected call of SaveQazPostLog
func (mr *MockStorageMockRecorder) SaveQazPostLog(ctx, logEntry interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveQazPostLog",
		reflect.TypeOf((*MockStorage)(nil).SaveQazPostLog), ctx, logEntry)
}

// FindSPIByAddress mocks base method
func (m *MockStorage) FindSPIByAddress(ctx context.Context, address string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindSPIByAddress", ctx, address)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSPIByAddress indicates an expected call of FindSPIByAddress
func (mr *MockStorageMockRecorder) FindSPIByAddress(ctx, address interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSPIByAddress",
		reflect.TypeOf((*MockStorage)(nil).FindSPIByAddress), ctx, address)
}

// HealthCheck mocks base method
func (m *MockStorage) HealthCheck(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HealthCheck", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// HealthCheck indicates an expected call of HealthCheck
func (mr *MockStorageMockRecorder) HealthCheck(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck",
		reflect.TypeOf((*MockStorage)(nil).HealthCheck), ctx)
}
