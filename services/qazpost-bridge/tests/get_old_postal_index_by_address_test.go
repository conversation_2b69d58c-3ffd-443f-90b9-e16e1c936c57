package tests

import (
	"context"
	"errors"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/acceptlanguage"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"

	"git.redmadrobot.com/zaman/backend/zaman/errs/qazpostBridge"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage/mongo"

	mongoLib "git.redmadrobot.com/backend-go/rmr-pkg/providers/mongo"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/qazpost"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/qazpost-bridge"
)

type UserInfoCtx struct {
	UserID    string
	SessionID string
	Phone     string
	Origin    string
	Locale    string
}

// TestGetOldPostalIndexByAddress_FromLocalDictionary - info not found in db
func (s *Suite) TestGetOldPostalIndexByAddress_FromLocalDictionary() {
	const (
		testAddress     = "Алматы, ул. Абая 52"
		testSPI         = "050000"
		testOldPostCode = "123456"
		testAuthToken   = "test-token-123"
	)
	s.ctx = s.addUserDataToContext(s.ctx, UserInfoCtx{
		Origin: "TEST",
		Locale: locale.Kk.String(),
	})

	s.ctx = s.addUserDataToContext(s.ctx, UserInfoCtx{
		Origin: "TEST",
		Locale: locale.Kk.String(),
	})

	s.mocks.Providers.QazPostProvider.EXPECT().GetAuthToken(gomock.Any()).Return(testAuthToken, nil).Times(1)
	s.mocks.Providers.QazPostProvider.EXPECT().SearchNPI(gomock.Any(), gomock.Any(), gomock.Any()).Return([]qazpost.NpiSearchResult{{
		Postcode:   testSPI,
		AddressRus: testAddress,
	}}, nil).Times(1)
	s.mocks.Providers.QazPostProvider.EXPECT().GetOldIndex(gomock.Any(), gomock.Any(), gomock.Any()).Return(&qazpost.OldIndexResult{OldPostcode: testOldPostCode}, nil).
		Times(1)

	resp, err := s.grpc.GetOldPostalIndexByAddress(s.ctx, &pb.GetOldPostalIndexByAddressRequest{
		AddressQuery: testAddress,
	})

	// Assert results
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(testOldPostCode, resp.OldPostalIndex)
	s.Require().Equal(testAddress, resp.QazpostAddress)
	s.Require().Equal(testSPI, resp.NewPostalIndex)
}

func (s *Suite) TestGetOldPostalIndexByAddress_FromQazpostAPI() {
	const (
		testAddress = "Нур-Султан, ул. Кунаева 10"
		testSPI     = "473000"
	)

	_, err := s.mongodb.InsertOne(s.ctx, mongo.AddressDictionaryCollection, mongoLib.Document{"address": testAddress, "spi": testSPI})
	s.Require().NoError(err)

	resp, err := s.grpc.GetOldPostalIndexByAddress(s.ctx, &pb.GetOldPostalIndexByAddressRequest{
		AddressQuery: testAddress,
	})

	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(testSPI, resp.OldPostalIndex)
}

func (s *Suite) TestGetOldPostalIndexByAddress_WithRetry() {
	const (
		testAddress     = "Астана, пр. Мангилик Ел 55"
		testAuthToken   = "test-token-456"
		testNPI         = "020000"
		testSPI         = "573000"
		testQazpostAddr = "Казахстан, г. Астана, пр. Мангилик Ел, д. 55"
	)
	s.ctx = s.addUserDataToContext(s.ctx, UserInfoCtx{
		Origin: "TEST",
		Locale: locale.Kk.String(),
	})

	s.ctx = s.addUserDataToContext(s.ctx, UserInfoCtx{
		Origin: "TEST",
		Locale: locale.Kk.String(),
	})

	httpErr := &qazpost.HTTPError{
		StatusCode: 503,
		Status:     "Service Unavailable",
		Message:    "The server is temporarily unavailable",
	}

	npiResults := []qazpost.NpiSearchResult{
		{
			Postcode:   testNPI,
			AddressRus: testQazpostAddr,
		},
	}

	oldIndexResult := &qazpost.OldIndexResult{
		OldPostcode: testSPI,
	}

	gomock.InOrder(
		s.mocks.Providers.QazPostProvider.EXPECT().GetAuthToken(gomock.Any()).Return("", httpErr),
		s.mocks.Providers.QazPostProvider.EXPECT().GetAuthToken(gomock.Any()).Return(testAuthToken, nil),
	)

	gomock.InOrder(
		s.mocks.Providers.QazPostProvider.EXPECT().SearchNPI(gomock.Any(), testAuthToken, testAddress).Return(nil, httpErr),
		s.mocks.Providers.QazPostProvider.EXPECT().SearchNPI(gomock.Any(), testAuthToken, testAddress).Return(npiResults, nil),
	)

	s.mocks.Providers.QazPostProvider.EXPECT().GetOldIndex(gomock.Any(), testAuthToken, testNPI).Return(oldIndexResult, nil).Times(1)

	resp, err := s.grpc.GetOldPostalIndexByAddress(s.ctx, &pb.GetOldPostalIndexByAddressRequest{
		AddressQuery: testAddress,
	})

	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(testSPI, resp.OldPostalIndex)
	s.Require().Equal(testNPI, resp.NewPostalIndex)
	s.Require().Equal(testQazpostAddr, resp.QazpostAddress)
}

// TestGetOldPostalIndexByAddress_AddressNotFound tests when an address is not found anywhere
func (s *Suite) TestGetOldPostalIndexByAddress_AddressNotFound() {
	const (
		testAddress   = "Несуществующий адрес 123"
		testAuthToken = "test-token-123"
	)

	s.ctx = s.addUserDataToContext(s.ctx, UserInfoCtx{
		Origin: "TEST",
		Locale: locale.Kk.String(),
	})

	s.mocks.Providers.QazPostProvider.EXPECT().GetAuthToken(gomock.Any()).Return(testAuthToken, nil).Times(1)
	s.mocks.Providers.QazPostProvider.EXPECT().SearchNPI(gomock.Any(), testAuthToken, testAddress).Return([]qazpost.NpiSearchResult{}, nil).Times(1)

	_, err := s.grpc.GetOldPostalIndexByAddress(s.ctx, &pb.GetOldPostalIndexByAddressRequest{
		AddressQuery: testAddress,
	})

	s.Require().Error(err)
	s.Require().Contains(err.Error(), qazpostBridge.CodeQazpostBridgeNotFound)
}

func (s *Suite) TestGetOldPostalIndexByAddress_EmptyAddress() {
	s.ctx = s.addUserDataToContext(s.ctx, UserInfoCtx{
		Origin: "TEST",
		Locale: locale.Kk.String(),
	})

	_, err := s.grpc.GetOldPostalIndexByAddress(s.ctx, &pb.GetOldPostalIndexByAddressRequest{
		AddressQuery: "",
	})

	s.Require().Error(err)
	s.Require().Contains(err.Error(), qazpostBridge.CodeQazpostBridgeInvalidInput)
}

func (s *Suite) TestGetOldPostalIndexByAddress_AuthError() {
	const testAddress = "Алматы, проспект Аль-Фараби 77"
	authError := errors.New("authentication failed")
	s.ctx = s.addUserDataToContext(s.ctx, UserInfoCtx{
		Origin: "TEST",
		Locale: locale.Kk.String(),
	})

	s.mocks.Providers.QazPostProvider.EXPECT().GetAuthToken(gomock.Any()).Return("", authError).MinTimes(1).MaxTimes(3)

	_, err := s.grpc.GetOldPostalIndexByAddress(s.ctx, &pb.GetOldPostalIndexByAddressRequest{
		AddressQuery: testAddress,
	})

	s.Require().Error(err)
	s.Require().Contains(err.Error(), "failed to authenticate")
}

func (s *Suite) TestGetOldPostalIndexByAddress_SearchError() {
	const (
		testAddress   = "Алматы, проспект Абая 52"
		testAuthToken = "test-token-123"
	)
	searchError := errors.New("search failed")

	s.mocks.Providers.QazPostProvider.EXPECT().GetAuthToken(gomock.Any()).Return(testAuthToken, nil).Times(1)
	s.mocks.Providers.QazPostProvider.EXPECT().SearchNPI(gomock.Any(), testAuthToken, testAddress).Return(nil, searchError).MinTimes(1).MaxTimes(3)

	_, err := s.grpc.GetOldPostalIndexByAddress(s.ctx, &pb.GetOldPostalIndexByAddressRequest{
		AddressQuery: testAddress,
	})

	s.Require().Error(err)
	s.Require().Contains(err.Error(), "failed to search for address")
}

func (s *Suite) TestGetOldPostalIndexByAddress_GetOldIndexError() {
	const (
		testAddress     = "Караганда, ул. Ерубаева 35"
		testAuthToken   = "test-token-123"
		testNPI         = "100000"
		testQazpostAddr = "Казахстан, г. Караганда, ул. Ерубаева, д. 35"
	)
	getOldIndexError := errors.New("get old index failed")

	s.ctx = s.addUserDataToContext(s.ctx, UserInfoCtx{
		Origin: "TEST",
		Locale: locale.Kk.String(),
	})

	npiResults := []qazpost.NpiSearchResult{
		{
			Postcode:   testNPI,
			AddressRus: testQazpostAddr,
		},
	}

	s.mocks.Providers.QazPostProvider.EXPECT().GetAuthToken(gomock.Any()).Return(testAuthToken, nil).Times(1)
	s.mocks.Providers.QazPostProvider.EXPECT().SearchNPI(gomock.Any(), testAuthToken, testAddress).Return(npiResults, nil).Times(1)
	s.mocks.Providers.QazPostProvider.EXPECT().GetOldIndex(gomock.Any(), testAuthToken, testNPI).Return(nil, getOldIndexError).MinTimes(1).MaxTimes(3)

	_, err := s.grpc.GetOldPostalIndexByAddress(s.ctx, &pb.GetOldPostalIndexByAddressRequest{
		AddressQuery: testAddress,
	})

	// Assert results
	s.Require().Error(err)
	s.Require().Contains(err.Error(), "failed to get old postal index")
}

func (s *Suite) addUserDataToContext(ctx context.Context, data UserInfoCtx) context.Context {
	userInfo := map[string]interface{}{
		"user_id":      data.UserID,
		"session_id":   data.SessionID,
		"phone_number": data.Phone,
	}

	// SA1029: should not use built-in type string as key for value; define your own type to avoid collisions (staticcheck)
	//nolint:staticcheck
	ctx = context.WithValue(ctx, "userInfo", userInfo)
	ctx = context.WithValue(ctx, reqSourceKey, data.Origin)
	ctx = context.WithValue(ctx, acceptlanguage.AcceptLanguageKey, data.Locale)
	return ctx
}
