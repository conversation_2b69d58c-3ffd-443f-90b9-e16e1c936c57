// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package tests

import (
	"fmt"
	"io/ioutil"
	"os"
	"runtime"
	"strings"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// Load the golden file and map it to the message type without additional parameters
func (s *Suite) loadGolden(message proto.Message) {
	s.T().Helper()

	// Determine the message type based on the name of the calling function
	messageType := "req"
	if s.isCalledFromCompareGolden() {
		messageType = "resp"
	}

	// Get the current test name
	testName := s.getCurrentTestName()

	// Determine the file path, including the message type
	filePath := s.getGoldenFilePath(testName, messageType)

	// Read the file contents
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		s.T().Fatalf("Error reading golden file: %v", err)
	}

	// Map JSON to the gRPC structure
	if err := protojson.Unmarshal(data, message); err != nil {
		s.T().Fatalf("Error unmarshalling golden file into proto: %v", err)
	}
}

// Check if the function was called from CompareGolden
func (s *Suite) isCalledFromCompareGolden() bool {
	// Skip two frames of the call stack
	pc, _, _, ok := runtime.Caller(2)
	if !ok {
		return false
	}
	callerFunc := runtime.FuncForPC(pc)
	if callerFunc == nil {
		return false
	}
	return strings.Contains(callerFunc.Name(), "CompareGolden")
}

// Compare with the golden file
func (s *Suite) CompareGolden(actual proto.Message) {
	s.T().Helper()

	testName := s.getCurrentTestName()

	// Optionally update the golden file if needed
	s.updateGolden(actual, testName)

	expected := proto.Clone(actual)
	s.loadGolden(expected)

	// Compare the structures
	if !proto.Equal(expected, actual) {
		s.T().Fatalf("Actual response does not match golden file.\nExpected: %v\nActual: %v", expected, actual)
	}
}

// Update the golden file
func (s *Suite) updateGolden(message proto.Message, testName string) {
	s.T().Helper()

	if os.Getenv("UPDATE_GOLDEN") == "true" {
		// Convert gRPC structure to JSON
		data, err := protojson.Marshal(message)
		if err != nil {
			s.T().Fatalf("Error marshalling proto to JSON: %v", err)
		}

		// Write to the file
		filePath := s.getGoldenFilePath(testName, "resp")
		if err := ioutil.WriteFile(filePath, data, 0644); err != nil {
			s.T().Fatalf("Error writing golden file: %v", err)
		}
	}
}

// Get the current test name without the "TestRunner_" prefix
func (s *Suite) getCurrentTestName() string {
	// Get the current test name
	fullTestName := s.T().Name()

	// Remove the "TestRunner_" prefix
	testName := strings.TrimPrefix(fullTestName, "TestRunner/")

	// Convert the test name to a format without "/"
	return strings.ReplaceAll(testName, "/", "_")
}

// Get the path to the golden file based on the test name and message type
func (s *Suite) getGoldenFilePath(testName, messageType string) string {
	return fmt.Sprintf("./goldenfiles/%s/%s.json", testName, messageType)
}
