package tests

import (
	"context"
	"sync"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage"
)

// TestStorage is a simple implementation of Storage for testing
type TestStorage struct {
	mu              sync.Mutex
	postalIndices   map[string]string
	healthErr       error
	saveLogErr      error
	findSPIByAddrFn func(ctx context.Context, addr string) (string, error)
}

// NewTestStorage creates a new test storage
func NewTestStorage() *TestStorage {
	return &TestStorage{
		postalIndices: make(map[string]string),
		findSPIByAddrFn: func(ctx context.Context, addr string) (string, error) {
			return "", storage.ErrNotFound
		},
	}
}

// HealthCheck implements Storage.Health interface
func (s *TestStorage) HealthCheck(ctx context.Context) error {
	return s.healthErr
}

// SaveQazPostLog implements Storage.SaveQazPostLog
func (s *TestStorage) SaveQazPostLog(ctx context.Context, logEntry *entity.QazPostLog) error {
	return s.saveLogErr
}

// FindSPIByAddress implements Storage.FindSPIByAddress
func (s *TestStorage) FindSPIByAddress(ctx context.Context, address string) (string, error) {
	return s.findSPIByAddrFn(ctx, address)
}

// SetFindSPIByAddressFunc sets a custom function for FindSPIByAddress
func (s *TestStorage) SetFindSPIByAddressFunc(fn func(ctx context.Context, addr string) (string, error)) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.findSPIByAddrFn = fn
}

// SetPostalIndex sets a mapping from address to postal index
func (s *TestStorage) SetPostalIndex(address, index string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.postalIndices[address] = index
	s.findSPIByAddrFn = func(ctx context.Context, addr string) (string, error) {
		if idx, ok := s.postalIndices[addr]; ok {
			return idx, nil
		}
		return "", storage.ErrNotFound
	}
}

// Check implements Storage.Health interface
func (s *TestStorage) Check(ctx context.Context) error {
	return s.healthErr
}
