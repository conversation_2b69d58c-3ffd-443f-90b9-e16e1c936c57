package entity

// QazPostLog represents a log entry for an interaction with the QazPost API.
type QazPostLog struct {
	ID            string `bson:"_id,omitempty"` // Use string for potential UUID if needed, or change to primitive.ObjectID
	Timestamp     int64  `bson:"timestamp"`
	StartTime     int64  `bson:"start_time"`
	RequestMethod string `bson:"request_method"` // e.g., "GetAuthToken", "SearchNPI", "GetOldIndex"
	RequestID     string `bson:"request_id"`
	UserID        string `bson:"user_id"`
	RequestURL    string `bson:"request_url"`
	RequestBody   string `bson:"request_body,omitempty"`  // Store marshaled JSON or relevant parts
	RequestToken  string `bson:"request_token,omitempty"` // Mask partially if sensitive
	ResponseCode  int    `bson:"response_code,omitempty"`
	ResponseBody  string `bson:"response_body,omitempty"` // Store marshaled JSON or relevant parts
	DurationMs    int64  `bson:"duration_ms"`
	Error         string `bson:"error,omitempty"`
}
