package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/qazpost-bridge"
)

type (
	// GetOldPostalIndexByAddressReq holds the request data for the use case.
	GetOldPostalIndexByAddressReq struct {
		AddressQuery string // The address string to search for.
	}

	// GetOldPostalIndexByAddressResult holds the result data from the use case.
	GetOldPostalIndexByAddressResult struct {
		OldPostalIndex string // The old postal index (SPI) found.
		NewPostalIndex string // The new postal index found.
		QazpostAddress string // The address returned by Qazpost.
	}

	AddressDictionaryEntry struct {
		Address string `bson:"address"`
		SPI     string `bson:"spi"`
	}
)

// MakeGetOldPostalIndexByAddressPbToEntity creates an entity request from a Protobuf request.
func MakeGetOldPostalIndexByAddressPbToEntity(req *pb.GetOldPostalIndexByAddressRequest) *GetOldPostalIndexByAddressReq {
	if req == nil {
		return &GetOldPostalIndexByAddressReq{}
	}
	// Map Protobuf field to entity field.
	return &GetOldPostalIndexByAddressReq{
		AddressQuery: req.GetAddressQuery(), // Use the getter generated by protoc
	}
}

// MakeGetOldPostalIndexByAddressEntityToPb creates a Protobuf response from an entity result.
func MakeGetOldPostalIndexByAddressEntityToPb(res *GetOldPostalIndexByAddressResult) *pb.GetOldPostalIndexByAddressResponse {
	if res == nil {
		return &pb.GetOldPostalIndexByAddressResponse{}
	}
	// Map entity fields to Protobuf fields.
	return &pb.GetOldPostalIndexByAddressResponse{
		OldPostalIndex: res.OldPostalIndex,
		NewPostalIndex: res.NewPostalIndex,
		QazpostAddress: res.QazpostAddress,
	}
}
