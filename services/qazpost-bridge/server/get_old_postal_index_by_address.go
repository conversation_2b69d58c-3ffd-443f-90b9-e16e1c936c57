package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/qazpost-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
)

func (s *Server) GetOldPostalIndexByAddress(ctx context.Context, req *pb.GetOldPostalIndexByAddressRequest) (*pb.GetOldPostalIndexByAddressResponse, error) {
	getOldPostalIndexByAddressEntity := entity.MakeGetOldPostalIndexByAddressPbToEntity(req)

	getOldPostalIndexByAddress, err := s.useCase.GetOldPostalIndexByAddress(ctx, getOldPostalIndexByAddressEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetOldPostalIndexByAddressEntityToPb(getOldPostalIndexByAddress), nil
}
