package usecase

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/generic"

	qazpostBridgeErrs "git.redmadrobot.com/zaman/backend/zaman/errs/qazpostBridge"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/qazpost"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
)

const (
	maxRetries = 3
)

func (u *useCasesImpl) GetOldPostalIndexByAddress(ctx context.Context, req *entity.GetOldPostalIndexByAddressReq) (*entity.GetOldPostalIndexByAddressResult, error) {
	l := logs.FromContext(ctx)

	// Получаем RequestID из контекста
	requestID := utils.ExtractRequestID(ctx)

	// Получаем UserID из контекста
	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if req == nil || req.AddressQuery == "" {
		// Use the specific error for invalid input
		return nil, qazpostBridgeErrs.QazpostBridgeErrs().InvalidInputError()
	}

	spi, err := u.Providers.Storage.FindSPIByAddress(ctx, req.AddressQuery)
	if err == nil && spi != "" {
		return &entity.GetOldPostalIndexByAddressResult{
			OldPostalIndex: spi,
		}, nil
	} else if !errors.Is(err, qazpostBridgeErrs.QazpostBridgeErrs().NotFoundError()) {
		return nil, fmt.Errorf("failed to query dictionary: %w", err)
	}

	// Get auth token with retry
	startTime := time.Now().UnixMilli()
	token, err := u.getAuthTokenWithRetry(ctx)
	duration := time.Duration(time.Now().UnixMilli()-startTime) * time.Millisecond
	if err != nil {
		logErr := u.logQazPostInteraction(ctx, "GetAuthToken", requestID, userID, req.AddressQuery, "", "", startTime, duration, err)
		if logErr != nil {
			l.Error().Err(logErr).Msg("Failed to save QazPost log for method GetAuthToken")
		}
		return nil, fmt.Errorf("failed to authenticate with QazPost API: %w", err)
	}

	// Search NPI with retry
	startTime = time.Now().UnixMilli()
	npiResults, err := u.searchNPIWithRetry(ctx, token, req.AddressQuery)
	duration = time.Duration(time.Now().UnixMilli()-startTime) * time.Millisecond
	if err != nil {
		logErr := u.logQazPostInteraction(ctx, "SearchNPI", requestID, userID, req.AddressQuery, token, "", startTime, duration, err)
		if logErr != nil {
			l.Error().Err(logErr).Msg("Failed to save QazPost log for method SearchNPI")
		}
		return nil, fmt.Errorf("failed to search for address: %w", err)
	}

	if len(npiResults) == 0 {
		logErr := u.logQazPostInteraction(ctx, "SearchNPI", requestID, userID, req.AddressQuery, token, "no results found", startTime, duration, qazpostBridgeErrs.QazpostBridgeErrs().NotFoundError())
		if logErr != nil {
			l.Error().Err(logErr).Msg("Failed to save QazPost log for method SearchNPI")
		}
		return nil, qazpostBridgeErrs.QazpostBridgeErrs().NotFoundError()
	}

	npi := npiResults[0].Postcode
	formattedAddress := npiResults[0].AddressRus

	// Get old index with retry
	startTime = time.Now().UnixMilli()
	oldIndexResult, err := u.getOldIndexWithRetry(ctx, token, npi)
	duration = time.Duration(time.Now().UnixMilli()-startTime) * time.Millisecond
	if err != nil {
		logErr := u.logQazPostInteraction(ctx, "GetOldIndex", requestID, userID, req.AddressQuery, token, npi, startTime, duration, err)
		if logErr != nil {
			l.Error().Err(logErr).Msg("Failed to save QazPost log for method GetOldIndex")
		}
		return nil, fmt.Errorf("failed to get old postal index: %w", err)
	}

	logErr := u.logQazPostInteraction(ctx,
		"GetOldPostalIndexByAddress",
		requestID,
		userID,
		req.AddressQuery,
		token,
		fmt.Sprintf("%s -> %s", npi, oldIndexResult.OldPostcode),
		startTime,
		duration,
		nil)
	if logErr != nil {
		l.Error().Err(logErr).Msg("Failed to save QazPost log for method GetOldPostalIndexByAddress")
	}

	return &entity.GetOldPostalIndexByAddressResult{
		OldPostalIndex: oldIndexResult.OldPostcode,
		NewPostalIndex: npi,
		QazpostAddress: formattedAddress,
	}, nil
}

func (u *useCasesImpl) getAuthTokenWithRetry(ctx context.Context) (string, error) {
	retryParams := generic.ParamsRetryWithBackoff[string]{
		MaxRetries: maxRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: func(err error) bool {
			if err == nil {
				return false
			}
			var httpErr *qazpost.HTTPError
			if errors.As(err, &httpErr) {
				return httpErr.StatusCode >= http.StatusInternalServerError
			}
			return true
		},
		Operation: func() (*string, error) {
			token, err := u.Providers.QazPostProvider.GetAuthToken(ctx)
			if err != nil {
				return nil, err
			}
			return &token, nil
		},
	}

	tokenPtr, err := generic.RetryWithBackoff(retryParams)
	if err != nil {
		return "", fmt.Errorf("auth request failed after retries: %w", err)
	}

	if tokenPtr == nil || *tokenPtr == "" {
		return "", errors.New("auth response missing access token after retries")
	}

	return *tokenPtr, nil
}

func (u *useCasesImpl) searchNPIWithRetry(ctx context.Context, token, addressQuery string) ([]qazpost.NpiSearchResult, error) {
	retryParams := generic.ParamsRetryWithBackoff[[]qazpost.NpiSearchResult]{
		MaxRetries: maxRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: func(err error) bool {
			if err == nil {
				return false
			}
			var httpErr *qazpost.HTTPError
			if errors.As(err, &httpErr) {
				return httpErr.StatusCode >= http.StatusInternalServerError
			}
			return true
		},
		Operation: func() (*[]qazpost.NpiSearchResult, error) {
			results, err := u.Providers.QazPostProvider.SearchNPI(ctx, token, addressQuery)
			if err != nil {
				return nil, err
			}
			return &results, nil
		},
	}

	resultsPtr, err := generic.RetryWithBackoff(retryParams)
	if err != nil {
		return nil, fmt.Errorf("search request failed after retries: %w", err)
	}

	if resultsPtr == nil {
		return []qazpost.NpiSearchResult{}, nil
	}

	return *resultsPtr, nil
}

func (u *useCasesImpl) getOldIndexWithRetry(ctx context.Context, token, npi string) (*qazpost.OldIndexResult, error) {
	retryParams := generic.ParamsRetryWithBackoff[qazpost.OldIndexResult]{
		MaxRetries: maxRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: func(err error) bool {
			if err == nil {
				return false
			}
			var httpErr *qazpost.HTTPError
			if errors.As(err, &httpErr) {
				return httpErr.StatusCode >= http.StatusInternalServerError
			}
			return true
		},
		Operation: func() (*qazpost.OldIndexResult, error) {
			return u.Providers.QazPostProvider.GetOldIndex(ctx, token, npi)
		},
	}

	result, err := generic.RetryWithBackoff(retryParams)
	if err != nil {
		return nil, fmt.Errorf("get old index request failed after retries: %w", err)
	}

	if result == nil {
		return nil, errors.New("failed to get old index after retries, response was nil")
	}

	return result, nil
}

func (u *useCasesImpl) logQazPostInteraction(ctx context.Context, method, requestID, userID, requestBody, token, responseBody string, startTime int64, duration time.Duration, err error) error {
	var errStr string
	if err != nil {
		errStr = err.Error()
	}

	logEntry := &entity.QazPostLog{
		Timestamp:     time.Now().Unix(),
		StartTime:     startTime,
		RequestMethod: method,
		RequestID:     requestID,
		UserID:        userID,
		RequestBody:   requestBody,
		RequestToken:  token,
		ResponseCode:  http.StatusOK, // Default to success
		ResponseBody:  responseBody,
		DurationMs:    duration.Milliseconds(),
		Error:         errStr,
	}

	if err != nil {
		logEntry.ResponseCode = 500
		logEntry.Error = err.Error()
	}

	return u.Providers.Storage.SaveQazPostLog(ctx, logEntry)
}
