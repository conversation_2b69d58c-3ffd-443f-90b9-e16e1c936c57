package usecase

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"
)

// EventRegistry Маппинг названий топиков в шине данных и методов, которые будут вызываться при получении сообщения.
// Названия топиков необходимо указывать и создавать в Apache Kafka самостоятельно.

// EventRegistry - registry of events
//
// TODO: Add events registration in this map.
//
// Example:
//
//	"my-topic-in":  u.HandleMyTopic,
//	"my-topic-out": u.HandleMyTopic,
func (u *useCasesImpl) EventRegistry() map[string]func(context.Context, *kafka.Message) {
	return map[string]func(context.Context, *kafka.Message){
		// Add events registration here
	}
}
