package usecase

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/qazpost"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage"
)

// Test implementation of Storage interface
type mockStorage struct {
	mock.Mock
}

func (m *mockStorage) FindSPIByAddress(ctx context.Context, addressQuery string) (string, error) {
	args := m.Called(ctx, addressQuery)
	return args.String(0), args.Error(1)
}

func (m *mockStorage) SaveQazPostLog(ctx context.Context, log *entity.QazPostLog) error {
	args := m.Called(ctx, log)
	return args.Error(0)
}

func (m *mockStorage) Check(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// Test implementation of QazPostProvider interface
type mockQazPostProvider struct {
	mock.Mock
}

func (m *mockQazPostProvider) GetAuthToken(ctx context.Context) (string, error) {
	args := m.Called(ctx)
	return args.String(0), args.Error(1)
}

func (m *mockQazPostProvider) SearchNPI(ctx context.Context, token, addressQuery string) ([]qazpost.NpiSearchResult, error) {
	args := m.Called(ctx, token, addressQuery)
	return args.Get(0).([]qazpost.NpiSearchResult), args.Error(1)
}

func (m *mockQazPostProvider) GetOldIndex(ctx context.Context, token, npi string) (*qazpost.OldIndexResult, error) {
	args := m.Called(ctx, token, npi)
	return args.Get(0).(*qazpost.OldIndexResult), args.Error(1)
}

func (m *mockQazPostProvider) LogInteraction(ctx context.Context, method string, err error) error {
	args := m.Called(ctx, method, err)
	return args.Error(0)
}

// Standalone implementation of GetOldPostalIndexByAddress function for testing
func getOldPostalIndexByAddress(
	ctx context.Context,
	req *entity.GetOldPostalIndexByAddressReq,
	storageImpl storage.Storage,
	qazpostImpl qazpost.QazPostProvider,
) (*entity.GetOldPostalIndexByAddressResult, error) {
	// Validate the request
	if req == nil || req.AddressQuery == "" {
		return nil, errors.New("INVALID_INPUT")
	}

	// First, try to find the Old Postal Index (SPI) in the local dictionary
	spi, err := storageImpl.FindSPIByAddress(ctx, req.AddressQuery)
	if err == nil && spi != "" {
		// SPI found in the dictionary, return it
		return &entity.GetOldPostalIndexByAddressResult{
			OldPostalIndex: spi,
		}, nil
	} else if !errors.Is(err, storage.ErrNotFound) {
		// An error occurred other than "not found"
		return nil, errors.New("failed to query dictionary: " + err.Error())
	}

	// If we get here, SPI was not found in dictionary, try QazPost API
	token, err := qazpostImpl.GetAuthToken(ctx)
	if err != nil {
		// Log the error
		logQazPostInteraction(ctx, storageImpl, "GetAuthToken", req.AddressQuery, token, "", err)
		return nil, errors.New("failed to authenticate with QazPost API: " + err.Error())
	}

	// Search for the address to get the New Postal Index (NPI)
	npiResults, err := qazpostImpl.SearchNPI(ctx, token, req.AddressQuery)
	if err != nil {
		// Log the error
		logQazPostInteraction(ctx, storageImpl, "SearchNPI", req.AddressQuery, token, "", err)
		return nil, errors.New("failed to search for address: " + err.Error())
	}

	// If no results, return error
	if len(npiResults) == 0 {
		// Log the interaction
		logQazPostInteraction(ctx, storageImpl, "SearchNPI", req.AddressQuery, token, "no results found", errors.New("ADDRESS_NOT_FOUND"))
		return nil, errors.New("ADDRESS_NOT_FOUND")
	}

	// Use the first result (most relevant)
	npi := npiResults[0].Postcode
	formattedAddress := npiResults[0].AddressRus

	// Get the Old Postal Index using the New Postal Index
	oldIndexResult, err := qazpostImpl.GetOldIndex(ctx, token, npi)
	if err != nil {
		// Log the error
		logQazPostInteraction(ctx, storageImpl, "GetOldIndex", req.AddressQuery, token, npi, err)
		return nil, errors.New("failed to get old postal index: " + err.Error())
	}

	// Log successful interaction
	logQazPostInteraction(ctx, storageImpl, "GetOldPostalIndexByAddress", req.AddressQuery, token,
		npi+"->"+oldIndexResult.OldPostcode, nil)

	// Return the result
	return &entity.GetOldPostalIndexByAddressResult{
		OldPostalIndex: oldIndexResult.OldPostcode,
		NewPostalIndex: npi,
		QazpostAddress: formattedAddress,
	}, nil
}

// Helper function to log QazPost interactions
func logQazPostInteraction(ctx context.Context, storageImpl storage.Storage, method, requestBody, token, responseBody string, err error) error {
	// Create log entry
	logEntry := &entity.QazPostLog{
		Timestamp:     time.Now().Unix(),
		RequestMethod: method,
		RequestBody:   requestBody,
		RequestToken:  token,
		ResponseCode:  200, // Default to success
		ResponseBody:  responseBody,
	}

	// If there was an error, update the log entry
	if err != nil {
		logEntry.ResponseCode = 500 // Generic error code
		logEntry.Error = err.Error()
	}

	// Save the log entry
	return storageImpl.SaveQazPostLog(ctx, logEntry)
}

func TestGetOldPostalIndexByAddressStandalone(t *testing.T) {
	// Test case for empty address query
	t.Run("Empty address query", func(t *testing.T) {
		mockStorage := new(mockStorage)
		mockQazPost := new(mockQazPostProvider)

		req := &entity.GetOldPostalIndexByAddressReq{
			AddressQuery: "",
		}

		result, err := getOldPostalIndexByAddress(context.Background(), req, mockStorage, mockQazPost)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "INVALID_INPUT")
		assert.Nil(t, result)
	})

	// Test case for found in local storage
	t.Run("Found in local storage", func(t *testing.T) {
		mockStorage := new(mockStorage)
		mockQazPost := new(mockQazPostProvider)

		mockStorage.On("FindSPIByAddress", mock.Anything, "Test Address").
			Return("123456", nil).Once()

		req := &entity.GetOldPostalIndexByAddressReq{
			AddressQuery: "Test Address",
		}

		result, err := getOldPostalIndexByAddress(context.Background(), req, mockStorage, mockQazPost)

		assert.NoError(t, err)
		assert.Equal(t, &entity.GetOldPostalIndexByAddressResult{
			OldPostalIndex: "123456",
		}, result)

		mockStorage.AssertExpectations(t)
		mockQazPost.AssertExpectations(t)
	})

	// Test case for error querying local storage
	t.Run("Error querying local storage", func(t *testing.T) {
		mockStorage := new(mockStorage)
		mockQazPost := new(mockQazPostProvider)

		mockStorage.On("FindSPIByAddress", mock.Anything, "Error Address").
			Return("", errors.New("database error")).Once()

		req := &entity.GetOldPostalIndexByAddressReq{
			AddressQuery: "Error Address",
		}

		result, err := getOldPostalIndexByAddress(context.Background(), req, mockStorage, mockQazPost)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to query dictionary")
		assert.Nil(t, result)

		mockStorage.AssertExpectations(t)
		mockQazPost.AssertExpectations(t)
	})

	// Test case for not found in local storage but found via API
	t.Run("Not found in local storage, found via API", func(t *testing.T) {
		mockStorage := new(mockStorage)
		mockQazPost := new(mockQazPostProvider)

		mockStorage.On("FindSPIByAddress", mock.Anything, "API Address").
			Return("", storage.ErrNotFound).Once()

		mockQazPost.On("GetAuthToken", mock.Anything).
			Return("test-token", nil).Once()

		npiResults := []qazpost.NpiSearchResult{
			{
				Postcode:   "654321",
				AddressRus: "Formatted API Address",
			},
		}
		mockQazPost.On("SearchNPI", mock.Anything, "test-token", "API Address").
			Return(npiResults, nil).Once()

		oldIndexResult := &qazpost.OldIndexResult{
			OldPostcode: "987654",
		}
		mockQazPost.On("GetOldIndex", mock.Anything, "test-token", "654321").
			Return(oldIndexResult, nil).Once()

		mockStorage.On("SaveQazPostLog", mock.Anything, mock.Anything).
			Return(nil).Times(1)

		req := &entity.GetOldPostalIndexByAddressReq{
			AddressQuery: "API Address",
		}

		result, err := getOldPostalIndexByAddress(context.Background(), req, mockStorage, mockQazPost)

		assert.NoError(t, err)
		assert.Equal(t, &entity.GetOldPostalIndexByAddressResult{
			OldPostalIndex: "987654",
			NewPostalIndex: "654321",
			QazpostAddress: "Formatted API Address",
		}, result)

		mockStorage.AssertExpectations(t)
		mockQazPost.AssertExpectations(t)
	})

	// Test case for API auth error
	t.Run("API auth error", func(t *testing.T) {
		mockStorage := new(mockStorage)
		mockQazPost := new(mockQazPostProvider)

		mockStorage.On("FindSPIByAddress", mock.Anything, "Auth Error Address").
			Return("", storage.ErrNotFound).Once()

		mockQazPost.On("GetAuthToken", mock.Anything).
			Return("", errors.New("auth error")).Once()

		mockStorage.On("SaveQazPostLog", mock.Anything, mock.Anything).
			Return(nil).Once()

		req := &entity.GetOldPostalIndexByAddressReq{
			AddressQuery: "Auth Error Address",
		}

		result, err := getOldPostalIndexByAddress(context.Background(), req, mockStorage, mockQazPost)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to authenticate with QazPost API")
		assert.Nil(t, result)

		mockStorage.AssertExpectations(t)
		mockQazPost.AssertExpectations(t)
	})

	// Test case for Search NPI error
	t.Run("Search NPI error", func(t *testing.T) {
		mockStorage := new(mockStorage)
		mockQazPost := new(mockQazPostProvider)

		mockStorage.On("FindSPIByAddress", mock.Anything, "Search Error Address").
			Return("", storage.ErrNotFound).Once()

		mockQazPost.On("GetAuthToken", mock.Anything).
			Return("test-token", nil).Once()

		var emptyResults []qazpost.NpiSearchResult
		mockQazPost.On("SearchNPI", mock.Anything, "test-token", "Search Error Address").
			Return(emptyResults, errors.New("search error")).Once()

		mockStorage.On("SaveQazPostLog", mock.Anything, mock.Anything).
			Return(nil).Once()

		req := &entity.GetOldPostalIndexByAddressReq{
			AddressQuery: "Search Error Address",
		}

		result, err := getOldPostalIndexByAddress(context.Background(), req, mockStorage, mockQazPost)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to search for address")
		assert.Nil(t, result)

		mockStorage.AssertExpectations(t)
		mockQazPost.AssertExpectations(t)
	})

	// Test case for no results found
	t.Run("No results found", func(t *testing.T) {
		mockStorage := new(mockStorage)
		mockQazPost := new(mockQazPostProvider)

		mockStorage.On("FindSPIByAddress", mock.Anything, "No Results Address").
			Return("", storage.ErrNotFound).Once()

		mockQazPost.On("GetAuthToken", mock.Anything).
			Return("test-token", nil).Once()

		var emptyResults []qazpost.NpiSearchResult
		mockQazPost.On("SearchNPI", mock.Anything, "test-token", "No Results Address").
			Return(emptyResults, nil).Once()

		mockStorage.On("SaveQazPostLog", mock.Anything, mock.Anything).
			Return(nil).Once()

		req := &entity.GetOldPostalIndexByAddressReq{
			AddressQuery: "No Results Address",
		}

		result, err := getOldPostalIndexByAddress(context.Background(), req, mockStorage, mockQazPost)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "ADDRESS_NOT_FOUND")
		assert.Nil(t, result)

		mockStorage.AssertExpectations(t)
		mockQazPost.AssertExpectations(t)
	})
}
