// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/usecase -i QazpostBridge -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ QazpostBridge = (*QazpostBridgeHook)(nil)

// QazpostBridgeHook implements QazpostBridge interface wrapper
type QazpostBridgeHook struct {
	QazpostBridge
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// GetOldPostalIndexByAddress implements QazpostBridge
func (_w *QazpostBridgeHook) GetOldPostalIndexByAddress(ctx context.Context, req *entity.GetOldPostalIndexByAddressReq) (gp1 *entity.GetOldPostalIndexByAddressResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.QazpostBridge, "GetOldPostalIndexByAddress", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.QazpostBridge, "GetOldPostalIndexByAddress", _params)

	gp1, err = _w.QazpostBridge.GetOldPostalIndexByAddress(_ctx, req)
	_w._postCall.Hook(_ctx, _w.QazpostBridge, "GetOldPostalIndexByAddress", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements QazpostBridge
func (_w *QazpostBridgeHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.QazpostBridge, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.QazpostBridge, "HealthCheck", _params)

	hp1, err = _w.QazpostBridge.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.QazpostBridge, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements QazpostBridge
func (_w *QazpostBridgeHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.QazpostBridge, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.QazpostBridge, "HealthEvent", _params)

	_w.QazpostBridge.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.QazpostBridge, "HealthEvent", []any{})
	return
}

// InitConsumer implements QazpostBridge
func (_w *QazpostBridgeHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.QazpostBridge, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.QazpostBridge, "InitConsumer", _params)

	_w.QazpostBridge.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.QazpostBridge, "InitConsumer", []any{})
	return
}

// NewQazpostBridgeHook returns QazpostBridgeHook
func NewQazpostBridgeHook(object QazpostBridge, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *QazpostBridgeHook {
	return &QazpostBridgeHook{
		QazpostBridge: object,
		_beforeCall:   beforeCall,
		_postCall:     postCall,
		_onPanic:      onPanic,
	}
}
