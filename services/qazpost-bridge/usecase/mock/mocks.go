package mock

import (
	"context"

	"github.com/stretchr/testify/mock"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
)

// Storage is a mock implementation of the storage interface
type Storage struct {
	mock.Mock
}

// FindSPIByAddress mocks the FindSPIByAddress method
func (m *Storage) FindSPIByAddress(ctx context.Context, address string) (string, error) {
	args := m.Called(ctx, address)
	return args.String(0), args.Error(1)
}

// SaveQazPostLog mocks the SaveQazPostLog method
func (m *Storage) SaveQazPostLog(ctx context.Context, log *entity.QazPostLog) error {
	args := m.Called(ctx, log)
	return args.Error(0)
}

// QazPostProvider is a mock implementation of the QazPost provider interface
type QazPostProvider struct {
	mock.Mock
}

// GetAuthToken mocks the GetAuthToken method
func (m *QazPostProvider) GetAuthToken(ctx context.Context) (string, error) {
	args := m.Called(ctx)
	return args.String(0), args.Error(1)
}

// SearchNPI mocks the SearchNPI method
func (m *QazPostProvider) SearchNPI(ctx context.Context, token, address string) ([]struct {
	Postcode   string
	AddressRus string
}, error,
) {
	args := m.Called(ctx, token, address)
	return args.Get(0).([]struct {
		Postcode   string
		AddressRus string
	}), args.Error(1)
}

// GetOldIndex mocks the GetOldIndex method
func (m *QazPostProvider) GetOldIndex(ctx context.Context, token, npi string) (struct {
	OldPostcode string
}, error,
) {
	args := m.Called(ctx, token, npi)
	return args.Get(0).(struct {
		OldPostcode string
	}), args.Error(1)
}
