// Code generated by MockGen. DO NOT EDIT.
// Source: usecase.gen.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	kafka "git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"
	gomock "github.com/golang/mock/gomock"

	entity "git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
)

// MockQazpostBridge is a mock of QazpostBridge interface.
type MockQazpostBridge struct {
	ctrl     *gomock.Controller
	recorder *MockQazpostBridgeMockRecorder
}

// MockQazpostBridgeMockRecorder is the mock recorder for MockQazpostBridge.
type MockQazpostBridgeMockRecorder struct {
	mock *MockQazpostBridge
}

// NewMockQazpostBridge creates a new mock instance.
func NewMockQazpostBridge(ctrl *gomock.Controller) *MockQazpostBridge {
	mock := &MockQazpostBridge{ctrl: ctrl}
	mock.recorder = &MockQazpostBridgeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQazpostBridge) EXPECT() *MockQazpostBridgeMockRecorder {
	return m.recorder
}

// GetOldPostalIndexByAddress mocks base method.
func (m *MockQazpostBridge) GetOldPostalIndexByAddress(ctx context.Context, req *entity.GetOldPostalIndexByAddressReq) (*entity.GetOldPostalIndexByAddressResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOldPostalIndexByAddress", ctx, req)
	ret0, _ := ret[0].(*entity.GetOldPostalIndexByAddressResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOldPostalIndexByAddress indicates an expected call of GetOldPostalIndexByAddress.
func (mr *MockQazpostBridgeMockRecorder) GetOldPostalIndexByAddress(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOldPostalIndexByAddress", reflect.TypeOf((*MockQazpostBridge)(nil).GetOldPostalIndexByAddress), ctx, req)
}

// HealthCheck mocks base method.
func (m *MockQazpostBridge) HealthCheck(ctx context.Context) (*entity.Health, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HealthCheck", ctx)
	ret0, _ := ret[0].(*entity.Health)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockQazpostBridgeMockRecorder) HealthCheck(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockQazpostBridge)(nil).HealthCheck), ctx)
}

// HealthEvent mocks base method.
func (m *MockQazpostBridge) HealthEvent(ctx context.Context, message *kafka.Message) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "HealthEvent", ctx, message)
}

// HealthEvent indicates an expected call of HealthEvent.
func (mr *MockQazpostBridgeMockRecorder) HealthEvent(ctx, message interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthEvent", reflect.TypeOf((*MockQazpostBridge)(nil).HealthEvent), ctx, message)
}

// InitConsumer mocks base method.
func (m *MockQazpostBridge) InitConsumer(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "InitConsumer", ctx)
}

// InitConsumer indicates an expected call of InitConsumer.
func (mr *MockQazpostBridgeMockRecorder) InitConsumer(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitConsumer", reflect.TypeOf((*MockQazpostBridge)(nil).InitConsumer), ctx)
}
