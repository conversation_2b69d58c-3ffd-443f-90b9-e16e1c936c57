package storage

import (
	"context"
	"errors"

	qazpostBridgeErrs "git.redmadrobot.com/zaman/backend/zaman/errs/qazpostBridge"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage/mongo"

	"go.mongodb.org/mongo-driver/bson"
	m "go.mongodb.org/mongo-driver/mongo"
)

func (s *storageImpl) FindSPIByAddress(ctx context.Context, address string) (string, error) {
	coll := s.MongoClient.DB.Collection(mongo.AddressDictionaryCollection)

	var result entity.AddressDictionaryEntry
	filter := bson.M{"address": address}

	err := coll.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		if errors.Is(err, m.ErrNoDocuments) {
			return "", qazpostBridgeErrs.QazpostBridgeErrs().NotFoundError()
		}
		return "", err
	}

	if result.SPI == "" {
		return "", qazpostBridgeErrs.QazpostBridgeErrs().NotFoundError()
	}

	return result.SPI, nil
}
