// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// FindSPIByAddress implements Storage
func (_w *StorageHook) FindSPIByAddress(ctx context.Context, address string) (s1 string, err error) {
	_params := []any{ctx, address}
	defer _w._onPanic.Hook(_w.Storage, "FindSPIByAddress", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "FindSPIByAddress", _params)

	s1, err = _w.Storage.FindSPIByAddress(_ctx, address)
	_w._postCall.Hook(_ctx, _w.Storage, "FindSPIByAddress", []any{s1, err})
	return s1, err
}

// SaveQazPostLog implements Storage
func (_w *StorageHook) SaveQazPostLog(ctx context.Context, logEntry *entity.QazPostLog) (err error) {
	_params := []any{ctx, logEntry}
	defer _w._onPanic.Hook(_w.Storage, "SaveQazPostLog", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveQazPostLog", _params)

	err = _w.Storage.SaveQazPostLog(_ctx, logEntry)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveQazPostLog", []any{err})
	return err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
