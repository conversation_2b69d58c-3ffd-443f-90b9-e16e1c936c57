// Code generated by ent, DO NOT EDIT.

package ent

import (
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
}
