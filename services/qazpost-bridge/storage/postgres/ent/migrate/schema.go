// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// HealthsColumns holds the columns for the "healths" table.
	HealthsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
	}
	// HealthsTable holds the schema information for the "healths" table.
	HealthsTable = &schema.Table{
		Name:       "healths",
		Columns:    HealthsColumns,
		PrimaryKey: []*schema.Column{HealthsColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		HealthsTable,
	}
)

func init() {
}
