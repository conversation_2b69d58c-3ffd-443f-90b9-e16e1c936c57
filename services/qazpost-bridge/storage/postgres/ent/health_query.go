// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage/postgres/ent/predicate"
)

// HealthQuery is the builder for querying Health entities.
type HealthQuery struct {
	config
	ctx        *QueryContext
	order      []health.OrderOption
	inters     []Interceptor
	predicates []predicate.Health
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the HealthQuery builder.
func (hq *HealthQuery) Where(ps ...predicate.Health) *HealthQuery {
	hq.predicates = append(hq.predicates, ps...)
	return hq
}

// Limit the number of records to be returned by this query.
func (hq *HealthQuery) Limit(limit int) *HealthQuery {
	hq.ctx.Limit = &limit
	return hq
}

// Offset to start from.
func (hq *HealthQuery) Offset(offset int) *HealthQuery {
	hq.ctx.Offset = &offset
	return hq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (hq *HealthQuery) Unique(unique bool) *HealthQuery {
	hq.ctx.Unique = &unique
	return hq
}

// Order specifies how the records should be ordered.
func (hq *HealthQuery) Order(o ...health.OrderOption) *HealthQuery {
	hq.order = append(hq.order, o...)
	return hq
}

// First returns the first Health entity from the query.
// Returns a *NotFoundError when no Health was found.
func (hq *HealthQuery) First(ctx context.Context) (*Health, error) {
	nodes, err := hq.Limit(1).All(setContextOp(ctx, hq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{health.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (hq *HealthQuery) FirstX(ctx context.Context) *Health {
	node, err := hq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Health ID from the query.
// Returns a *NotFoundError when no Health ID was found.
func (hq *HealthQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = hq.Limit(1).IDs(setContextOp(ctx, hq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{health.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (hq *HealthQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := hq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Health entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Health entity is found.
// Returns a *NotFoundError when no Health entities are found.
func (hq *HealthQuery) Only(ctx context.Context) (*Health, error) {
	nodes, err := hq.Limit(2).All(setContextOp(ctx, hq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{health.Label}
	default:
		return nil, &NotSingularError{health.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (hq *HealthQuery) OnlyX(ctx context.Context) *Health {
	node, err := hq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Health ID in the query.
// Returns a *NotSingularError when more than one Health ID is found.
// Returns a *NotFoundError when no entities are found.
func (hq *HealthQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = hq.Limit(2).IDs(setContextOp(ctx, hq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{health.Label}
	default:
		err = &NotSingularError{health.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (hq *HealthQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := hq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Healths.
func (hq *HealthQuery) All(ctx context.Context) ([]*Health, error) {
	ctx = setContextOp(ctx, hq.ctx, ent.OpQueryAll)
	if err := hq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Health, *HealthQuery]()
	return withInterceptors[[]*Health](ctx, hq, qr, hq.inters)
}

// AllX is like All, but panics if an error occurs.
func (hq *HealthQuery) AllX(ctx context.Context) []*Health {
	nodes, err := hq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Health IDs.
func (hq *HealthQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if hq.ctx.Unique == nil && hq.path != nil {
		hq.Unique(true)
	}
	ctx = setContextOp(ctx, hq.ctx, ent.OpQueryIDs)
	if err = hq.Select(health.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (hq *HealthQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := hq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (hq *HealthQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, hq.ctx, ent.OpQueryCount)
	if err := hq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, hq, querierCount[*HealthQuery](), hq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (hq *HealthQuery) CountX(ctx context.Context) int {
	count, err := hq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (hq *HealthQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, hq.ctx, ent.OpQueryExist)
	switch _, err := hq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (hq *HealthQuery) ExistX(ctx context.Context) bool {
	exist, err := hq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the HealthQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (hq *HealthQuery) Clone() *HealthQuery {
	if hq == nil {
		return nil
	}
	return &HealthQuery{
		config:     hq.config,
		ctx:        hq.ctx.Clone(),
		order:      append([]health.OrderOption{}, hq.order...),
		inters:     append([]Interceptor{}, hq.inters...),
		predicates: append([]predicate.Health{}, hq.predicates...),
		// clone intermediate query.
		sql:  hq.sql.Clone(),
		path: hq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
func (hq *HealthQuery) GroupBy(field string, fields ...string) *HealthGroupBy {
	hq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &HealthGroupBy{build: hq}
	grbuild.flds = &hq.ctx.Fields
	grbuild.label = health.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
func (hq *HealthQuery) Select(fields ...string) *HealthSelect {
	hq.ctx.Fields = append(hq.ctx.Fields, fields...)
	sbuild := &HealthSelect{HealthQuery: hq}
	sbuild.label = health.Label
	sbuild.flds, sbuild.scan = &hq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a HealthSelect configured with the given aggregations.
func (hq *HealthQuery) Aggregate(fns ...AggregateFunc) *HealthSelect {
	return hq.Select().Aggregate(fns...)
}

func (hq *HealthQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range hq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, hq); err != nil {
				return err
			}
		}
	}
	for _, f := range hq.ctx.Fields {
		if !health.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if hq.path != nil {
		prev, err := hq.path(ctx)
		if err != nil {
			return err
		}
		hq.sql = prev
	}
	return nil
}

func (hq *HealthQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Health, error) {
	var (
		nodes = []*Health{}
		_spec = hq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Health).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Health{config: hq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, hq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (hq *HealthQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := hq.querySpec()
	_spec.Node.Columns = hq.ctx.Fields
	if len(hq.ctx.Fields) > 0 {
		_spec.Unique = hq.ctx.Unique != nil && *hq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, hq.driver, _spec)
}

func (hq *HealthQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(health.Table, health.Columns, sqlgraph.NewFieldSpec(health.FieldID, field.TypeUUID))
	_spec.From = hq.sql
	if unique := hq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if hq.path != nil {
		_spec.Unique = true
	}
	if fields := hq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, health.FieldID)
		for i := range fields {
			if fields[i] != health.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := hq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := hq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := hq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := hq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (hq *HealthQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(hq.driver.Dialect())
	t1 := builder.Table(health.Table)
	columns := hq.ctx.Fields
	if len(columns) == 0 {
		columns = health.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if hq.sql != nil {
		selector = hq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if hq.ctx.Unique != nil && *hq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range hq.predicates {
		p(selector)
	}
	for _, p := range hq.order {
		p(selector)
	}
	if offset := hq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := hq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// HealthGroupBy is the group-by builder for Health entities.
type HealthGroupBy struct {
	selector
	build *HealthQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (hgb *HealthGroupBy) Aggregate(fns ...AggregateFunc) *HealthGroupBy {
	hgb.fns = append(hgb.fns, fns...)
	return hgb
}

// Scan applies the selector query and scans the result into the given value.
func (hgb *HealthGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, hgb.build.ctx, ent.OpQueryGroupBy)
	if err := hgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*HealthQuery, *HealthGroupBy](ctx, hgb.build, hgb, hgb.build.inters, v)
}

func (hgb *HealthGroupBy) sqlScan(ctx context.Context, root *HealthQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(hgb.fns))
	for _, fn := range hgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*hgb.flds)+len(hgb.fns))
		for _, f := range *hgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*hgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := hgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// HealthSelect is the builder for selecting fields of Health entities.
type HealthSelect struct {
	*HealthQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (hs *HealthSelect) Aggregate(fns ...AggregateFunc) *HealthSelect {
	hs.fns = append(hs.fns, fns...)
	return hs
}

// Scan applies the selector query and scans the result into the given value.
func (hs *HealthSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, hs.ctx, ent.OpQuerySelect)
	if err := hs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*HealthQuery, *HealthSelect](ctx, hs.HealthQuery, hs, hs.inters, v)
}

func (hs *HealthSelect) sqlScan(ctx context.Context, root *HealthQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(hs.fns))
	for _, fn := range hs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*hs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := hs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
