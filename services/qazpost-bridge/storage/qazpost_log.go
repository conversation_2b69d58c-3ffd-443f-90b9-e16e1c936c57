package storage

import (
	"context"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/storage/mongo"
)

// SaveQazPostLog inserts a log entry into the MongoDB collection.
// Receiver is *storageImpl, which holds the MongoClient.
func (s *storageImpl) SaveQazPostLog(ctx context.Context, logEntry *entity.QazPostLog) error {
	// Access MongoClient via the receiver 's', then the DB field
	coll := s.MongoClient.DB.Collection(mongo.QazpostLogCollection)

	// Generate a new ID if it's empty (using string UUID or ObjectID)
	if logEntry.ID == "" {
		logEntry.ID = primitive.NewObjectID().Hex() // Or use uuid.NewString()
	}

	_, err := coll.InsertOne(ctx, logEntry)
	if err != nil {
		// TODO: Add structured logging for the error
		return err // Wrap error potentially?
	}
	return nil
}
