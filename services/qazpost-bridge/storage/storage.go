package storage

import (
	"context"
	"errors"

	"git.redmadrobot.com/zaman/backend/zaman/services/qazpost-bridge/entity"
)

// ErrNotFound indicates that a requested item was not found in storage.
var ErrNotFound = errors.New("item not found in storage")

type Storage interface {
	Health

	// SaveQazPostLog saves a log entry for a QazPost API interaction.
	SaveQazPostLog(ctx context.Context, logEntry *entity.QazPostLog) error

	// FindSPIByAddress looks up the Old Postal Index (SPI) for a given address string
	// in the local dictionary. It returns the SPI if found, or ErrNotFound otherwise.
	FindSPIByAddress(ctx context.Context, address string) (string, error)
}
