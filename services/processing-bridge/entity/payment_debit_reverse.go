package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	PaymentDebitReverseReq struct {
		ContractRid    *string `json:"contractRid"`
		Iban           *string `json:"iban"`
		Amount         string  `json:"amount"`
		Currency       string  `json:"currency"`
		Rrn            string  `json:"rrn"`
		AuthExternalID string  `json:"authExternalId"`
		CategoryText   *string `json:"categoryText"`
	}

	PaymentDebitReverseResult struct {
		TransactionID int64 `json:"transactionId"`
	}
)

// MakePaymentDebitReversePbToEntity создает объект из pb.PaymentDebitReverseReq в PaymentDebitReverseReq для передачи в usecase
func MakePaymentDebitReversePbToEntity(req *pb.PaymentDebitReverseReq) *PaymentDebitReverseReq {
	if req == nil {
		return &PaymentDebitReverseReq{}
	}
	// write your mapping code here
	return &PaymentDebitReverseReq{
		ContractRid:    req.ContractRid,
		Iban:           req.Iban,
		Amount:         req.Amount,
		Currency:       req.Currency,
		Rrn:            req.Rrn,
		AuthExternalID: req.AuthExternalID,
		CategoryText:   req.CategoryText,
	}
}

// MakePaymentDebitReverseEntityToPb создает объект из PaymentDebitReverse в pb.PaymentDebitReverseResp для возврата ответа из сервиса
func MakePaymentDebitReverseEntityToPb(res *PaymentDebitReverseResult) *pb.PaymentDebitReverseResp {
	if res == nil {
		return &pb.PaymentDebitReverseResp{}
	}

	return &pb.PaymentDebitReverseResp{
		TransactionID: res.TransactionID,
	}
}
