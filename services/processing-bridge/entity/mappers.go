package entity

import (
	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent"
)

func MapEntTokenToEntity(entToken *ent.Tokens) *TokenInfo {
	if entToken == nil {
		return nil
	}

	return &TokenInfo{
		AccessToken: entToken.Token,
		TokenType:   entToken.TokenType,
		CreatedAt:   entToken.CreateTime,
		UpdatedAt:   entToken.UpdateTime,
		ExpiresIn:   entToken.ExpiresIn,
		RequestID:   entToken.RequestID,
		IsDeleted:   entToken.Deleted,
	}
}

func MapCreateClientEntityToRequest(reqData *CreateClientAccountAndCardReq, accessToken string) silkpay.CreateClientAccountAndCardRequest {
	if reqData == nil {
		return silkpay.CreateClientAccountAndCardRequest{}
	}

	return silkpay.CreateClientAccountAndCardRequest{
		Person: silkpay.Person{
			PersonType:   reqData.Person.PersonType,
			Iin:          reqData.Person.Iin,
			Rid:          reqData.Person.Rid,
			LastName:     reqData.Person.LastName,
			FirstName:    reqData.Person.FirstName,
			MiddleName:   reqData.Person.MiddleName,
			ScreenName:   reqData.Person.ScreenName,
			BirthDate:    reqData.Person.BirthDate,
			MobilePhone:  reqData.Person.MobilePhone,
			Citizenship:  reqData.Person.Citizenship,
			LastNameLat:  reqData.Person.LastNameLat,
			FirstNameLat: reqData.Person.FirstNameLat,
			BranchCode:   reqData.Person.BranchCode,
			RiskLevel:    reqData.Person.RiskLevel,
			Document: silkpay.Document{
				Type:      reqData.Person.Document.Type,
				Number:    reqData.Person.Document.Number,
				IssueDate: reqData.Person.Document.IssueDate,
				ExpDate:   reqData.Person.Document.ExpDate,
				Issuer:    reqData.Person.Document.Issuer,
			},
			ResidentAddress: silkpay.ResidentAddress{
				CountryID:     reqData.Person.ResidentAddress.CountryID,
				CityTitle:     reqData.Person.ResidentAddress.CityTitle,
				AddressInCity: reqData.Person.ResidentAddress.AddressInCity,
				Zip:           reqData.Person.ResidentAddress.Zip,
			},
		},
		AccountDetails: silkpay.AccountDetails{
			ContractTypeRid:  reqData.AccountDetails.ContractTypeRid,
			ContractRid:      reqData.AccountDetails.ContractRid,
			ContractCurrency: reqData.AccountDetails.ContractCurrency,
			Iban:             reqData.AccountDetails.Iban,
		},
		CardDetails: silkpay.CardDetails{
			ContractTypeRid: reqData.CardDetails.ContractTypeRid,
			ContractRid:     reqData.CardDetails.ContractRid,
			CardParams: silkpay.CardParams{
				EmbossName: reqData.CardDetails.CardParams.EmbossName,
			},
		},
		AccessToken: accessToken,
	}
}

func MapSilkpayCreateClientAndCardRespToEntity(resp *silkpay.CreateClientAccountAndCardResponse) *CreateClientAccountAndCardResult {
	if resp == nil {
		return &CreateClientAccountAndCardResult{}
	}

	return &CreateClientAccountAndCardResult{
		EmbossName: resp.EmbossName,
		Pan:        resp.Pan,
		ExpiryDate: resp.ExpiryDate,
		Status:     resp.Status,
	}
}
