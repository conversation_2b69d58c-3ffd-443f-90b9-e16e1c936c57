package entity

import (
	"time"

	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	GetPaymentFinDocReq struct {
		ExternalID          string     `json:"externalId"`
		PartyIban           *string    `json:"partyIban"`
		PartyFinContractRid *string    `json:"partyFinContractRid"`
		DatetimeFrom        time.Time  `json:"datetimeFrom"`
		DatetimeTo          *time.Time `json:"datetimeTo"`
	}

	GetPaymentFinDocResult struct {
		TransactionID    int64           `json:"transactionId"`
		Type             string          `json:"type"`
		RegisteredTime   time.Time       `json:"registeredTime"`
		Amount           decimal.Decimal `json:"amount"`
		Currency         string          `json:"currency"`
		PayerContractRid string          `json:"payerContractRid"`
		IsReversal       bool            `json:"isReversal"`
		Result           string          `json:"result"`
		Rrn              string          `json:"rrn"`
		LifePhase        string          `json:"lifePhase"`
	}
)

// MakeGetPaymentFinDocPbToEntity создает объект из pb.GetPaymentFinDocReq в GetPaymentFinDocReq для передачи в usecase
func MakeGetPaymentFinDocPbToEntity(req *pb.GetPaymentFinDocReq) *GetPaymentFinDocReq {
	if req == nil {
		return &GetPaymentFinDocReq{}
	}

	datetimeTo := req.DatetimeTo.AsTime()

	// write your mapping code here
	return &GetPaymentFinDocReq{
		ExternalID:          req.ExternalID,
		PartyIban:           req.PartyIban,
		PartyFinContractRid: req.PartyFinContractRid,
		DatetimeFrom:        req.DatetimeFrom.AsTime(),
		DatetimeTo:          &datetimeTo,
	}
}

// MakeGetPaymentFinDocEntityToPb создает объект из GetPaymentFinDoc в pb.GetPaymentFinDocResp для возврата ответа из сервиса
func MakeGetPaymentFinDocEntityToPb(res *GetPaymentFinDocResult) *pb.GetPaymentFinDocResp {
	if res == nil {
		return &pb.GetPaymentFinDocResp{}
	}

	return &pb.GetPaymentFinDocResp{
		TransactionID:    res.TransactionID,
		Type:             res.Type,
		RegisteredTime:   timestamppb.New(res.RegisteredTime),
		Amount:           res.Amount.String(),
		Currency:         res.Currency,
		PayerContractRid: res.PayerContractRid,
		IsReversal:       res.IsReversal,
		Result:           res.Result,
		Rrn:              res.Rrn,
		LifePhase:        res.LifePhase,
	}
}
