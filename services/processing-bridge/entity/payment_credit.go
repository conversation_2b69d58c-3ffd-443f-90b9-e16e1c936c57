package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	PaymentCreditReq struct {
		ContractRid  *string `json:"contractRid"`
		Iban         *string `json:"iban"`
		Amount       string  `json:"amount"`
		Currency     string  `json:"currency"`
		Rrn          string  `json:"rrn"`
		IsReversal   *bool   `json:"isReversal"`
		CategoryText *string `json:"categoryText"`
		ExternalID   string  `json:"externalId"`
	}

	PaymentCreditResult struct{}
)

// MakePaymentCreditPbToEntity создает объект из pb.PaymentCreditReq в PaymentCreditReq для передачи в usecase
func MakePaymentCreditPbToEntity(req *pb.PaymentCreditReq) *PaymentCreditReq {
	if req == nil {
		return &PaymentCreditReq{}
	}
	// write your mapping code here
	return &PaymentCreditReq{
		ContractRid:  req.ContractRid,
		Iban:         req.<PERSON>ban,
		Amount:       req.Amount,
		Currency:     req.<PERSON><PERSON>rency,
		Rrn:          req.Rrn,
		IsReversal:   &req.IsReversal,
		CategoryText: req.CategoryText,
		ExternalID:   req.ExternalID,
	}
}

// MakePaymentCreditEntityToPb создает объект из PaymentCredit в pb.PaymentCreditResp для возврата ответа из сервиса
func MakePaymentCreditEntityToPb(res *PaymentCreditResult) *pb.PaymentCreditResp {
	return &pb.PaymentCreditResp{}
}
