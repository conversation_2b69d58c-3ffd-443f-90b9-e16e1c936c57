package entity

import (
	silkEntity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	GetCardAndFinContractStatusReq struct {
		CardRIDs     []string `json:"card_rids,omitempty"`
		ContractRids []string `json:"contract_rids,omitempty"`
		ClientID     string   `json:"client_id,omitempty"`
	}

	GetCardAndFinContractStatusResult struct {
		CardStatuses     []CardStatus     `json:"card_statuses,omitempty"`     // Массив статусов карт
		ContractStatuses []ContractStatus `json:"contract_statuses,omitempty"` // Массив статусов фин. контрактов
	}

	CardStatus struct {
		Status       string `json:"status,omitempty"`        // Статус карты
		ContractRid  string `json:"contract_rid,omitempty"`  // Идентификатор карты
		ErrorMessage string `json:"error_message,omitempty"` // Сообщение об ошибке при возникновении
		Successful   bool   `json:"successful,omitempty"`    // Успешность операции
	}

	ContractStatus struct {
		Status       string `json:"status,omitempty"`        // Статус фин. контракта
		ContractRid  string `json:"contract_rid,omitempty"`  // Идентификатор фин. контракта
		ErrorMessage string `json:"error_message,omitempty"` // Сообщение об ошибке при возникновении
		Successful   bool   `json:"successful,omitempty"`    // Успешность операции
	}
)

// MakeGetCardAndFinContractStatusPbToEntity создает объект из pb.GetFinContractStatusReq в GetCardAndFinContractStatusReq для передачи в usecase
func MakeGetCardAndFinContractStatusPbToEntity(req *pb.GetFinContractStatusReq) *GetCardAndFinContractStatusReq {
	if req == nil {
		return &GetCardAndFinContractStatusReq{}
	}

	return &GetCardAndFinContractStatusReq{
		CardRIDs:     req.CardRIDs,
		ContractRids: req.ContractRIDs,
		ClientID:     req.ClientID,
	}
}

// MakeGetCardAndFinContractStatusEntityToPb создает объект из GetCardAndFinContractStatus в pb.GetFinContractStatusResp для возврата ответа из сервиса
func MakeGetCardAndFinContractStatusEntityToPb(res *GetCardAndFinContractStatusResult) *pb.GetFinContractStatusResp {
	if res == nil {
		return &pb.GetFinContractStatusResp{}
	}
	// Преобразование статусов карт
	contractStatuses := make([]*pb.ContractStatus, len(res.ContractStatuses))
	for i, contractStatus := range res.ContractStatuses {
		contractStatuses[i] = &pb.ContractStatus{
			Status:       contractStatus.Status,
			ContractRid:  contractStatus.ContractRid,
			ErrorMessage: contractStatus.ErrorMessage,
			Successful:   contractStatus.Successful,
		}
	}
	// Преобразование статусов карт
	cardStatuses := make([]*pb.CardStatus, len(res.CardStatuses))
	for i, cardStatus := range res.CardStatuses {
		cardStatuses[i] = &pb.CardStatus{
			Status:       cardStatus.Status,
			ContractRid:  cardStatus.ContractRid,
			ErrorMessage: cardStatus.ErrorMessage,
			Successful:   cardStatus.Successful,
		}
	}

	return &pb.GetFinContractStatusResp{
		CardStatuses:     cardStatuses,
		ContractStatuses: contractStatuses,
	}
}

// MapGetCardAndFinContractStatusEntityToRequest преобразует GetCardAndFinContractStatusReq в silkEntity.GetCardAndFinContractStatusReq для запроса к SilkPay
func MapGetCardAndFinContractStatusEntityToRequest(req *GetCardAndFinContractStatusReq, requestID, accessToken string) *silkEntity.GetCardAndFinContractStatusReq {
	if req == nil {
		return &silkEntity.GetCardAndFinContractStatusReq{}
	}

	return &silkEntity.GetCardAndFinContractStatusReq{
		CardRids:     req.CardRIDs,
		ContractRids: req.ContractRids,
		ClientID:     req.ClientID,
		RequestID:    requestID,
		AccessToken:  accessToken,
	}
}

// MapSilkpayGetCardAndFinContractStatusRespToEntity преобразует silkEntity.GetCardAndFinContractStatusResponse в GetCardAndFinContractStatusResult
func MapSilkpayGetCardAndFinContractStatusRespToEntity(resp *silkEntity.GetCardAndFinContractStatusResponse) *GetCardAndFinContractStatusResult {
	if resp == nil {
		return &GetCardAndFinContractStatusResult{}
	}

	// Преобразование статусов карт
	cardStatuses := make([]CardStatus, len(resp.CardStatuses))
	for i, cardStatus := range resp.CardStatuses {
		cardStatuses[i] = CardStatus{
			Status:       cardStatus.Status,
			ContractRid:  cardStatus.ContractRid,
			ErrorMessage: cardStatus.ErrorMessage,
			Successful:   cardStatus.Successful,
		}
	}
	// Преобразование статусов контрактов
	contractStatuses := make([]ContractStatus, len(resp.ContractStatuses))
	for i, contractStatus := range resp.ContractStatuses {
		contractStatuses[i] = ContractStatus{
			Status:       contractStatus.Status,
			ContractRid:  contractStatus.ContractRid,
			ErrorMessage: contractStatus.ErrorMessage,
			Successful:   contractStatus.Successful,
		}
	}

	return &GetCardAndFinContractStatusResult{
		CardStatuses:     cardStatuses,
		ContractStatuses: contractStatuses,
	}
}
