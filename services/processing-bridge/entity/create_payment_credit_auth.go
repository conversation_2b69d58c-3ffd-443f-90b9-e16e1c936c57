package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	CreatePaymentCreditAuthReq struct {
		ContractRid  *string `json:"contractRid"`
		Iban         *string `json:"iban"`
		Amount       string  `json:"amount"`
		Currency     string  `json:"currency"`
		Rrn          string  `json:"rrn"`
		ExternalID   string  `json:"externalId"`
		CategoryText *string `json:"categoryText"`
	}

	CreatePaymentCreditAuthResult struct {
		TransactionID int64 `json:"transactionId"`
	}
)

// MakeCreatePaymentCreditAuthPbToEntity создает объект из pb.CreatePaymentCreditAuthReq в CreatePaymentCreditAuthReq для передачи в usecase
func MakeCreatePaymentCreditAuthPbToEntity(req *pb.CreatePaymentCreditAuthReq) *CreatePaymentCreditAuthReq {
	if req == nil {
		return &CreatePaymentCreditAuthReq{}
	}
	// write your mapping code here
	return &CreatePaymentCreditAuthReq{
		ContractRid:  req.ContractRid,
		Iban:         req.Iban,
		Amount:       req.Amount,
		Currency:     req.Currency,
		Rrn:          req.Rrn,
		ExternalID:   req.ExternalID,
		CategoryText: req.CategoryText,
	}
}

// MakeCreatePaymentCreditAuthEntityToPb создает объект из CreatePaymentCreditAuth в pb.CreatePaymentCreditAuthResp для возврата ответа из сервиса
func MakeCreatePaymentCreditAuthEntityToPb(res *CreatePaymentCreditAuthResult) *pb.CreatePaymentCreditAuthResp {
	if res == nil {
		return &pb.CreatePaymentCreditAuthResp{}
	}

	return &pb.CreatePaymentCreditAuthResp{
		TransactionID: res.TransactionID,
	}
}
