package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	PaymentDebitPresentReq struct {
		ContractRid    *string `json:"contractRid"`
		Iban           *string `json:"iban"`
		Amount         string  `json:"amount"`
		Currency       string  `json:"currency"`
		Rrn            string  `json:"rrn"`
		AuthExternalID string  `json:"authExternalId"`
		CategoryText   *string `json:"categoryText"`
		Token          string  `json:"token"`
	}

	PaymentDebitPresentResult struct {
		TransactionID int64 `json:"transactionId"`
	}
)

// MakePaymentDebitPresentPbToEntity создает объект из pb.PaymentDebitPresentReq в PaymentDebitPresentReq для передачи в usecase
func MakePaymentDebitPresentPbToEntity(req *pb.PaymentDebitPresentReq) *PaymentDebitPresentReq {
	if req == nil {
		return &PaymentDebitPresentReq{}
	}
	// write your mapping code here
	return &PaymentDebitPresentReq{
		ContractRid:    req.ContractRid,
		Iban:           req.Iban,
		Amount:         req.Amount,
		Currency:       req.Currency,
		Rrn:            req.Rrn,
		AuthExternalID: req.AuthExternalID,
		CategoryText:   req.CategoryText,
	}
}

// MakePaymentDebitPresentEntityToPb создает объект из PaymentDebitPresent в pb.PaymentDebitPresentResp для возврата ответа из сервиса
func MakePaymentDebitPresentEntityToPb(res *PaymentDebitPresentResult) *pb.PaymentDebitPresentResp {
	if res == nil {
		return &pb.PaymentDebitPresentResp{}
	}

	return &pb.PaymentDebitPresentResp{
		TransactionID: res.TransactionID,
	}
}
