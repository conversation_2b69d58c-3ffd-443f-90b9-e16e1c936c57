package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	PaymentDebitReq struct {
		ContractRid  *string `json:"contractRid"`
		Iban         *string `json:"iban"`
		Amount       string  `json:"amount"`
		Currency     string  `json:"currency"`
		Rrn          string  `json:"rrn"`
		IsReversal   *bool   `json:"isReversal"`
		CategoryText *string `json:"categoryText"`
		ExternalID   string  `json:"externalId"`
	}

	PaymentDebitResult struct{}
)

// MakePaymentDebitPbToEntity создает объект из pb.PaymentDebitReq в PaymentDebitReq для передачи в usecase
func MakePaymentDebitPbToEntity(req *pb.PaymentDebitReq) *PaymentDebitReq {
	if req == nil {
		return &PaymentDebitReq{}
	}

	return &PaymentDebitReq{
		ContractRid:  req.ContractRid,
		Iban:         req.<PERSON><PERSON>,
		Amount:       req.Amount,
		Currency:     req.<PERSON>urrency,
		Rrn:          req.Rrn,
		IsReversal:   &req.IsReversal,
		CategoryText: req.CategoryText,
		ExternalID:   req.ExternalID,
	}
}

// MakePaymentDebitEntityToPb создает объект из PaymentDebit в pb.PaymentDebitResp для возврата ответа из сервиса
func MakePaymentDebitEntityToPb(res *PaymentDebitResult) *pb.PaymentDebitResp {
	return &pb.PaymentDebitResp{}
}
