package entity

import (
	"net/http"
	"strings"
)

// HTTP статус коды для retry логики
const (
	HTTPBadRequestStart     = 400
	HTTPClientErrorEnd      = 500
	HTTPInternalServerError = 500
)

type RetryDecision int

const (
	RetryDecisionRetry RetryDecision = iota

	RetryDecisionStop

	RetryDecisionStopSuccess
)

type RetryErrorClassifier struct{}

func NewRetryErrorClassifier() *RetryErrorClassifier {
	return &RetryErrorClassifier{}
}

func (c *RetryErrorClassifier) ClassifyError(err error, httpStatusCode int, responseBody string) RetryDecision {
	if err == nil {
		return RetryDecisionStop
	}

	switch httpStatusCode {
	case http.StatusConflict:
		if strings.Contains(strings.ToLower(responseBody), "duplicate request on transaction") {
			return RetryDecisionStopSuccess
		}
		return RetryDecisionStop

	case http.StatusBadRequest:
		if strings.Contains(strings.ToLower(responseBody), "validation failed") {
			return RetryDecisionRetry
		}

		if strings.Contains(strings.ToLower(responseBody), "insufficient funds") {
			return RetryDecisionStop
		}

		if strings.Contains(strings.ToLower(responseBody), "customer contract") ||
			strings.Contains(strings.ToLower(responseBody), "contract not found") {
			return RetryDecisionStop
		}

		return RetryDecisionStop

	case http.StatusInternalServerError:
		return RetryDecisionRetry

	case http.StatusRequestTimeout,
		http.StatusGatewayTimeout,
		http.StatusServiceUnavailable:
		return RetryDecisionRetry

	case 0:
		if c.isNetworkError(err) {
			return RetryDecisionRetry
		}
		return RetryDecisionStop

	default:
		if httpStatusCode >= HTTPBadRequestStart && httpStatusCode < HTTPClientErrorEnd {
			return RetryDecisionStop
		}
		if httpStatusCode >= HTTPInternalServerError {
			return RetryDecisionRetry
		}
		return RetryDecisionStop
	}
}

func (c *RetryErrorClassifier) isNetworkError(err error) bool {
	errStr := strings.ToLower(err.Error())

	// Типичные сетевые ошибки
	networkErrors := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"no such host",
		"network unreachable",
		"context deadline exceeded",
		"i/o timeout",
		"connection timed out",
	}

	for _, netErr := range networkErrors {
		if strings.Contains(errStr, netErr) {
			return true
		}
	}

	return false
}

func (c *RetryErrorClassifier) ShouldRetryValidationError(attempt int) bool {
	return attempt <= 2
}
