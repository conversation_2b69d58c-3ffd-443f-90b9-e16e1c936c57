package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	CreatePaymentDebitAuthReq struct {
		ContractRid  *string `json:"contractRid"`
		Iban         *string `json:"iban"`
		Amount       string  `json:"amount"`
		Currency     string  `json:"currency"`
		Rrn          string  `json:"rrn"`
		ExternalID   string  `json:"externalId"`
		CategoryText *string `json:"categoryText"`
	}

	CreatePaymentDebitAuthResult struct {
		TransactionID int64 `json:"transactionId"`
	}
)

// MakeCreatePaymentDebitAuthPbToEntity создает объект из pb.CreatePaymentDebitAuthReq в CreatePaymentDebitAuthReq для передачи в usecase
func MakeCreatePaymentDebitAuthPbToEntity(req *pb.CreatePaymentDebitAuthReq) *CreatePaymentDebitAuthReq {
	if req == nil {
		return &CreatePaymentDebitAuthReq{}
	}
	// write your mapping code here
	return &CreatePaymentDebitAuthReq{
		ContractRid:  req.ContractRid,
		Iban:         req.Iban,
		Amount:       req.Amount,
		Currency:     req.Currency,
		Rrn:          req.Rrn,
		ExternalID:   req.ExternalID,
		CategoryText: req.CategoryText,
	}
}

// MakeCreatePaymentDebitAuthEntityToPb создает объект из CreatePaymentDebitAuth в pb.CreatePaymentDebitAuthResp для возврата ответа из сервиса
func MakeCreatePaymentDebitAuthEntityToPb(res *CreatePaymentDebitAuthResult) *pb.CreatePaymentDebitAuthResp {
	if res == nil {
		return &pb.CreatePaymentDebitAuthResp{}
	}

	return &pb.CreatePaymentDebitAuthResp{
		TransactionID: res.TransactionID,
	}
}
