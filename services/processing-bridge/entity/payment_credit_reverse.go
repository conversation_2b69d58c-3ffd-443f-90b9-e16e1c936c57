package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	PaymentCreditReverseReq struct {
		ContractRid    *string `json:"contractRid"`
		Iban           *string `json:"iban"`
		Amount         string  `json:"amount"`
		Currency       string  `json:"currency"`
		Rrn            string  `json:"rrn"`
		AuthExternalID string  `json:"authExternalId"`
		CategoryText   *string `json:"categoryText"`
	}

	PaymentCreditReverseResult struct {
		TransactionID int64 `json:"transactionId"`
	}
)

// MakePaymentCreditReversePbToEntity создает объект из pb.PaymentCreditReverseReq в PaymentCreditReverseReq для передачи в usecase
func MakePaymentCreditReversePbToEntity(req *pb.PaymentCreditReverseReq) *PaymentCreditReverseReq {
	if req == nil {
		return &PaymentCreditReverseReq{}
	}
	// write your mapping code here
	return &PaymentCreditReverseReq{
		ContractRid:    req.ContractRid,
		Iban:           req.Iban,
		Amount:         req.Amount,
		Currency:       req.Currency,
		Rrn:            req.Rrn,
		AuthExternalID: req.AuthExternalID,
		CategoryText:   req.CategoryText,
	}
}

// MakePaymentCreditReverseEntityToPb создает объект из PaymentCreditReverse в pb.PaymentCreditReverseResp для возврата ответа из сервиса
func MakePaymentCreditReverseEntityToPb(res *PaymentCreditReverseResult) *pb.PaymentCreditReverseResp {
	if res == nil {
		return &pb.PaymentCreditReverseResp{}
	}

	return &pb.PaymentCreditReverseResp{
		TransactionID: res.TransactionID,
	}
}
