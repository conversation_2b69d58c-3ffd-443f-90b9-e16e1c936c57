package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	CreateClientAccountAndCardReq struct {
		Person         Person
		AccountDetails AccountDetails
		CardDetails    CardDetails
	}

	AccountDetails struct {
		ContractTypeRid  string
		ContractRid      string
		ContractCurrency string
		Iban             string
	}

	CardDetails struct {
		ContractTypeRid string
		ContractRid     string
		CardParams      CardParams
		DeliveryAddress DeliveryAddress
		Attrs           []Attr
	}

	Attr struct {
		Key   string
		Value string
	}

	CardParams struct {
		ExpDate    string
		EmbossName string
		Status     string
	}

	DeliveryAddress struct {
		CountryID int64
		City      string
		Street    string
		House     string
	}

	Person struct {
		PersonType      string
		Iin             string
		Rid             string
		LastName        string
		FirstName       string
		MiddleName      string
		ScreenName      string
		Gender          string
		BirthDate       string
		MobilePhone     string
		WorkPhone       string
		Email           string
		Citizenship     int64
		BirthPlace      string
		BirthName       string
		LastNameLat     string
		FirstNameLat    string
		EmployerTitle   string
		Notes           string
		RiskLevel       string
		BranchCode      string
		Document        Document
		LanguageCode    string
		ResidentAddress ResidentAddress
	}

	Document struct {
		Type      string
		Number    string
		IssueDate string
		ExpDate   string
		Issuer    string
	}

	ResidentAddress struct {
		CountryID     int64
		CityTitle     string
		AddressInCity string
		Zip           string
	}

	CreateClientAccountAndCardResult struct {
		EmbossName string
		Pan        string
		ExpiryDate string
		Status     string
	}
)

// MakeCreateClientAccountAndCardPbToEntity создает объект из pb.CreateClientAccaontAndCardReq в CreateClientAccountAndCardReq для передачи в usecase
func MakeCreateClientAccountAndCardPbToEntity(req *pb.CreateClientAccountAndCardReq) *CreateClientAccountAndCardReq {
	if req == nil {
		return &CreateClientAccountAndCardReq{}
	}

	result := &CreateClientAccountAndCardReq{
		Person: Person{
			PersonType:    req.Person.GetPersonType().String(),
			Iin:           req.GetPerson().GetIin(),
			Rid:           req.GetPerson().GetRid(),
			LastName:      req.GetPerson().GetLastName(),
			FirstName:     req.GetPerson().GetFirstName(),
			MiddleName:    req.GetPerson().GetMiddleName(),
			ScreenName:    req.GetPerson().GetScreenName(),
			Gender:        req.GetPerson().GetGender(),
			BirthDate:     req.GetPerson().GetBirthDate(),
			MobilePhone:   req.GetPerson().GetMobilePhone(),
			WorkPhone:     req.GetPerson().GetWorkPhone(),
			Email:         req.GetPerson().GetEmail(),
			Citizenship:   req.GetPerson().GetCitizenship(),
			BirthPlace:    req.GetPerson().GetBirthPlace(),
			BirthName:     req.GetPerson().GetBirthName(),
			LastNameLat:   req.GetPerson().GetLastNameLat(),
			FirstNameLat:  req.GetPerson().GetFirstNameLat(),
			EmployerTitle: req.GetPerson().GetEmployerTitle(),
			Notes:         req.GetPerson().GetNotes(),
			RiskLevel:     req.GetPerson().GetRiskLevel(),
			BranchCode:    req.GetPerson().GetBranchCode(),
			Document: Document{
				Type:      req.GetPerson().GetDocument().GetType(),
				Number:    req.GetPerson().GetDocument().GetNumber(),
				IssueDate: req.GetPerson().GetDocument().GetIssueDate(),
				ExpDate:   req.GetPerson().GetDocument().GetExpDate(),
				Issuer:    req.GetPerson().GetDocument().GetIssuer(),
			},
			LanguageCode: req.GetPerson().GetLanguageCode(),
			ResidentAddress: ResidentAddress{
				CountryID:     req.GetPerson().GetResidentAddress().GetCountryId(),
				CityTitle:     req.GetPerson().GetResidentAddress().GetCityTitle(),
				AddressInCity: req.GetPerson().GetResidentAddress().GetAddressInCity(),
				Zip:           req.GetPerson().GetResidentAddress().GetZip(),
			},
		},
		AccountDetails: AccountDetails{
			ContractTypeRid:  req.GetAccountDetails().GetContractTypeRid(),
			ContractRid:      req.GetAccountDetails().GetContractRid(),
			ContractCurrency: req.GetAccountDetails().GetContractCurrency(),
			Iban:             req.GetAccountDetails().GetIban(),
		},
		CardDetails: CardDetails{
			ContractTypeRid: req.GetCardDetails().GetContractTypeRid(),
			ContractRid:     req.GetCardDetails().GetContractRid(),
			CardParams: CardParams{
				ExpDate:    req.GetCardDetails().GetCardParams().GetExpDate(),
				EmbossName: req.GetCardDetails().GetCardParams().GetEmbossName(),
				Status:     req.GetCardDetails().GetCardParams().GetStatus(),
			},
			DeliveryAddress: DeliveryAddress{
				CountryID: req.GetCardDetails().GetDeliveryAddress().GetCountryId(),
				City:      req.GetCardDetails().GetDeliveryAddress().GetCity(),
				Street:    req.GetCardDetails().GetDeliveryAddress().GetStreet(),
				House:     req.GetCardDetails().GetDeliveryAddress().GetHouse(),
			},
		},
	}

	attrs := make([]Attr, 0, len(req.GetCardDetails().GetAttrs()))
	for _, attr := range req.GetCardDetails().GetAttrs() {
		attrs = append(attrs, Attr{
			Key:   attr.GetKey(),
			Value: attr.GetValue(),
		})
	}
	result.CardDetails.Attrs = attrs

	return result
}

// MakeCreateClientAccountAndCardEntityToPb создает объект из CreateClientAccountAndCard в pb.CreateClientAccountAndCardResp для возврата ответа из сервиса
func MakeCreateClientAccountAndCardEntityToPb(res *CreateClientAccountAndCardResult) *pb.CreateClientAccountAndCardResp {
	if res == nil {
		return &pb.CreateClientAccountAndCardResp{}
	}

	return &pb.CreateClientAccountAndCardResp{
		Pan:        res.Pan,
		EmbossName: res.EmbossName,
		ExpiryDate: res.ExpiryDate,
		Status:     res.Status,
	}
}
