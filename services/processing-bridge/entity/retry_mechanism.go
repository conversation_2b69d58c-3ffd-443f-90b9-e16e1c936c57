package entity

import (
	"context"
	"fmt"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
)

// ASCII константы
const (
	ASCIICaseDifference = 32
)

type RetryableOperation[T any] func(ctx context.Context, attempt int) (T, int, string, error)

type RetryResult[T any] struct {
	Result             T
	Success            bool
	Attempts           int
	LastError          error
	IsSuccessDuplicate bool
}

type RetryMechanism struct {
	config     *RetryConfig
	classifier *RetryErrorClassifier
}

func NewRetryMechanism(config *RetryConfig) *RetryMechanism {
	if config == nil {
		config = DefaultRetryConfig()
	}

	return &RetryMechanism{
		config:     config,
		classifier: NewRetryErrorClassifier(),
	}
}

func ExecuteWithRetry[T any](
	ctx context.Context,
	retryMechanism *RetryMechanism,
	operationName string,
	operation RetryableOperation[T],
) *RetryResult[T] {
	logger := logs.FromContext(ctx)

	var lastResult T
	var lastError error

	for attempt := 1; attempt <= retryMechanism.config.MaxAttempts; attempt++ {
		logger.Info().
			Str("operation", operationName).
			Int("attempt", attempt).
			Int("max_attempts", retryMechanism.config.MaxAttempts).
			Msg("Выполняем попытку вызова к ПЦ")

		result, httpStatusCode, responseBody, err := operation(ctx, attempt)

		if err == nil {
			logger.Info().
				Str("operation", operationName).
				Int("attempt", attempt).
				Msg("Операция выполнена успешно")

			return &RetryResult[T]{
				Result:   result,
				Success:  true,
				Attempts: attempt,
			}
		}

		decision := retryMechanism.classifier.ClassifyError(err, httpStatusCode, responseBody)

		if httpStatusCode == 400 &&
			contains(responseBody, "validation failed") {
			if !retryMechanism.classifier.ShouldRetryValidationError(attempt) {
				logger.Warn().
					Str("operation", operationName).
					Int("attempt", attempt).
					Err(err).
					Msg("Validation error: превышено количество попыток")
				decision = RetryDecisionStop
			}
		}

		lastResult = result
		lastError = err

		switch decision {
		case RetryDecisionStopSuccess:
			logger.Info().
				Str("operation", operationName).
				Int("attempt", attempt).
				Int("http_status", httpStatusCode).
				Msg("Операция завершена: дублирование запроса (считаем успехом)")

			return &RetryResult[T]{
				Result:             result,
				Success:            true,
				Attempts:           attempt,
				IsSuccessDuplicate: true,
			}

		case RetryDecisionStop:
			logger.Error().
				Str("operation", operationName).
				Int("attempt", attempt).
				Int("http_status", httpStatusCode).
				Err(err).
				Msg("Операция завершена с окончательной ошибкой")

			return &RetryResult[T]{
				Result:    lastResult,
				Success:   false,
				Attempts:  attempt,
				LastError: lastError,
			}

		case RetryDecisionRetry:
			if attempt >= retryMechanism.config.MaxAttempts {
				logger.Error().
					Str("operation", operationName).
					Int("attempt", attempt).
					Int("max_attempts", retryMechanism.config.MaxAttempts).
					Err(err).
					Msg("Превышено максимальное количество попыток")

				return &RetryResult[T]{
					Result:    lastResult,
					Success:   false,
					Attempts:  attempt,
					LastError: lastError,
				}
			}

			interval := retryMechanism.config.NextInterval(attempt)

			logger.Warn().
				Str("operation", operationName).
				Int("attempt", attempt).
				Int("http_status", httpStatusCode).
				Dur("retry_in", interval).
				Err(err).
				Msg("Ошибка выполнения операции, повторяем через интервал")

			select {
			case <-ctx.Done():
				logger.Error().
					Str("operation", operationName).
					Int("attempt", attempt).
					Msg("Контекст отменён во время ожидания retry")

				return &RetryResult[T]{
					Result:    lastResult,
					Success:   false,
					Attempts:  attempt,
					LastError: ctx.Err(),
				}
			case <-time.After(interval):
			}
		}
	}

	return &RetryResult[T]{
		Result:    lastResult,
		Success:   false,
		Attempts:  retryMechanism.config.MaxAttempts,
		LastError: fmt.Errorf("неизвестная ошибка в retry механизме"),
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				containsCaseInsensitive(s, substr)))
}

func containsCaseInsensitive(s, substr string) bool {
	s = toLower(s)
	substr = toLower(substr)

	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func toLower(s string) string {
	result := make([]byte, len(s))
	for i, c := range []byte(s) {
		if c >= 'A' && c <= 'Z' {
			result[i] = c + ASCIICaseDifference
		} else {
			result[i] = c
		}
	}
	return string(result)
}
