package entity

import (
	"math"
	"time"
)

// Константы для retry конфигурации
const (
	DefaultMaxAttempts     = 3
	DefaultInitialInterval = 5 * time.Second
	DefaultMaxInterval     = 30 * time.Second
	DefaultMultiplier      = 2.0
)

// RetryConfig - конфигурация для retry логики при вызовах к ПЦ
type RetryConfig struct {
	MaxAttempts int `json:"max_attempts" yaml:"max_attempts"`

	InitialInterval time.Duration `json:"initial_interval" yaml:"initial_interval"`

	MaxInterval time.Duration `json:"max_interval" yaml:"max_interval"`

	Multiplier float64 `json:"multiplier" yaml:"multiplier"`
}

func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxAttempts:     DefaultMaxAttempts,
		InitialInterval: DefaultInitialInterval,
		MaxInterval:     DefaultMaxInterval,
		Multiplier:      DefaultMultiplier,
	}
}

func (c *RetryConfig) NextInterval(attempt int) time.Duration {
	if attempt <= 0 {
		return c.InitialInterval
	}

	interval := time.Duration(float64(c.InitialInterval) *
		math.Pow(c.Multiplier, float64(attempt-1)))

	if interval > c.MaxInterval {
		return c.MaxInterval
	}

	return interval
}
