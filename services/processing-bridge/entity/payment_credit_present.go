package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

type (
	PaymentCreditPresentReq struct {
		ContractRid    *string `json:"contractRid"`
		Iban           *string `json:"iban"`
		Amount         string  `json:"amount"`
		Currency       string  `json:"currency"`
		Rrn            string  `json:"rrn"`
		AuthExternalID string  `json:"authExternalId"`
		CategoryText   *string `json:"categoryText"`
		Token          string  `json:"token"`
	}

	PaymentCreditPresentResult struct {
		TransactionID int64 `json:"transactionId"`
	}
)

// MakePaymentCreditPresentPbToEntity создает объект из pb.PaymentCreditPresentReq в PaymentCreditPresentReq для передачи в usecase
func MakePaymentCreditPresentPbToEntity(req *pb.PaymentCreditPresentReq) *PaymentCreditPresentReq {
	if req == nil {
		return &PaymentCreditPresentReq{}
	}
	// write your mapping code here
	return &PaymentCreditPresentReq{
		ContractRid:    req.ContractRid,
		Iban:           req.Iban,
		Amount:         req.Amount,
		Currency:       req.Currency,
		Rrn:            req.Rrn,
		AuthExternalID: req.AuthExternalID,
		CategoryText:   req.CategoryText,
	}
}

// MakePaymentCreditPresentEntityToPb создает объект из PaymentCreditPresent в pb.PaymentCreditPresentResp для возврата ответа из сервиса
func MakePaymentCreditPresentEntityToPb(res *PaymentCreditPresentResult) *pb.PaymentCreditPresentResp {
	if res == nil {
		return &pb.PaymentCreditPresentResp{}
	}

	return &pb.PaymentCreditPresentResp{
		TransactionID: res.TransactionID,
	}
}
