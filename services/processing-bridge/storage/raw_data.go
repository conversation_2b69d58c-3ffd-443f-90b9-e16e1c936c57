package storage

import (
	"context"
)

var _ RawDataMongo = (*storageImpl)(nil)

type RawDataMongo interface {
	SaveRawData(ctx context.Context, data interface{}, collection string) error
}

func (s *storageImpl) SaveRawData(ctx context.Context, data interface{}, collection string) error {
	coll := s.MongoClient.DB.Collection(collection)

	_, err := coll.InsertOne(ctx, data)
	if err != nil {
		return err
	}

	return nil
}
