package storage

import (
	"context"
	"database/sql"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
	pg "git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/tokens"
)

var _ Tokens = (*storageImpl)(nil)

type Tokens interface {
	ReSaveToken(ctx context.Context, tokenData entity.SaveTokenParams) error
	GetToken(ctx context.Context) (*entity.TokenInfo, error)
}

func (s *storageImpl) ReSaveToken(ctx context.Context, tokenData entity.SaveTokenParams) error {
	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer tx.Done(ctx)

	if err = tx.Client.Tokens.Update().Where(tokens.DeletedEQ(false)).SetDeleted(true).Exec(ctx); err != nil {
		return fmt.Errorf("failed to set tokens as delete: %w", err)
	}

	if err = tx.Client.Tokens.Create().
		SetTokenType(tokenData.TokenType).
		SetToken(tokenData.AccessToken).
		SetRequestID(tokenData.RequestID).
		SetExpiresIn(tokenData.ExpiresIn).Exec(ctx); err != nil {
		return fmt.Errorf("failed to save new token: %w", err)
	}

	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func (s *storageImpl) GetToken(ctx context.Context) (*entity.TokenInfo, error) {
	entToken, err := s.PostgresClient.Tokens.Query().Where(tokens.DeletedEQ(false)).Only(ctx)
	if err != nil {
		logs.FromContext(ctx).Warn().AnErr("failed to get tokens, db err:", err)
	}

	return entity.MapEntTokenToEntity(entToken), nil
}
