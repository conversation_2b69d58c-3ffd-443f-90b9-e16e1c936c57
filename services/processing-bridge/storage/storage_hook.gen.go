// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// GetToken implements Storage
func (_w *StorageHook) GetToken(ctx context.Context) (tp1 *entity.TokenInfo, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "GetToken", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetToken", _params)

	tp1, err = _w.Storage.GetToken(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "GetToken", []any{tp1, err})
	return tp1, err
}

// ReSaveToken implements Storage
func (_w *StorageHook) ReSaveToken(ctx context.Context, tokenData entity.SaveTokenParams) (err error) {
	_params := []any{ctx, tokenData}
	defer _w._onPanic.Hook(_w.Storage, "ReSaveToken", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "ReSaveToken", _params)

	err = _w.Storage.ReSaveToken(_ctx, tokenData)
	_w._postCall.Hook(_ctx, _w.Storage, "ReSaveToken", []any{err})
	return err
}

// SaveRawData implements Storage
func (_w *StorageHook) SaveRawData(ctx context.Context, data interface{}, collection string) (err error) {
	_params := []any{ctx, data, collection}
	defer _w._onPanic.Hook(_w.Storage, "SaveRawData", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveRawData", _params)

	err = _w.Storage.SaveRawData(_ctx, data, collection)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveRawData", []any{err})
	return err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
