package mongo

const (
	CollectionSilkpayAuthenticate               = "silkpay_authenticate"
	CollectionSilkpayCreateClientAccountAndCard = "silkpay_create_client_account_and_card"
	// #nosec G101 -- not credentials, just a collection name
	CollectionSilkpayCreatePaymentCreditAuth = "silkpay_create_payment_credit_auth"
	CollectionSilkpayCreatePaymentDebitAuth  = "silkpay_create_payment_debit_auth"
	CollectionSilkpayPaymentCreditPresent    = "silkpay_payment_credit_present"
	CollectionSilkpayPaymentDebitPresent     = "silkpay_payment_debit_present"
	CollectionSilkpayPaymentFinDoc           = "silkpay_payment_fin_doc"
	CollectionSilkpayPaymentCredit           = "silkpay_payment_credit"
	CollectionSilkpayPaymentDebit            = "silkpay_payment_debit"
	CollectionSilkpayPaymentCreditReverse    = "silkpay_payment_credit_reverse"
	CollectionSilkpayPaymentDebitReverse     = "silkpay_payment_debit_reverse"
	CollectionSilkpayFinContractStatus       = "silkpay_fin_contract_status"
)
