package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/mixin"
)

type Tokens struct {
	ent.Schema
}

func (Tokens) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New),
		field.String("request_id").NotEmpty().
			Annotations(annotation.CustomAnnotation{Description: "id запроса, вернувшего токен"}),
		field.Time("expires_in").
			Annotations(annotation.CustomAnnotation{Description: "время истечения жизни токена"}),
		field.String("token").NotEmpty().Annotations(),
		field.String("token_type").NotEmpty().
			Annotations(annotation.CustomAnnotation{Description: "тип токена"}),
		field.Bool("deleted").Default(false).
			Annotations(annotation.CustomAnnotation{Description: "флаг удаления токена"}),
	}
}

func (Tokens) Annotations() []schema.Annotation {
	return []schema.Annotation{}
}

func (Tokens) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
