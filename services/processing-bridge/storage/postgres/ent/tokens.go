// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/tokens"
)

// Tokens is the model entity for the Tokens schema.
type Tokens struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// RequestID holds the value of the "request_id" field.
	RequestID string `json:"request_id,omitempty"`
	// ExpiresIn holds the value of the "expires_in" field.
	ExpiresIn time.Time `json:"expires_in,omitempty"`
	// Token holds the value of the "token" field.
	Token string `json:"token,omitempty"`
	// TokenType holds the value of the "token_type" field.
	TokenType string `json:"token_type,omitempty"`
	// Deleted holds the value of the "deleted" field.
	Deleted      bool `json:"deleted,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Tokens) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case tokens.FieldDeleted:
			values[i] = new(sql.NullBool)
		case tokens.FieldRequestID, tokens.FieldToken, tokens.FieldTokenType:
			values[i] = new(sql.NullString)
		case tokens.FieldCreateTime, tokens.FieldUpdateTime, tokens.FieldExpiresIn:
			values[i] = new(sql.NullTime)
		case tokens.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Tokens fields.
func (_m *Tokens) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case tokens.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case tokens.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case tokens.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case tokens.FieldRequestID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field request_id", values[i])
			} else if value.Valid {
				_m.RequestID = value.String
			}
		case tokens.FieldExpiresIn:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expires_in", values[i])
			} else if value.Valid {
				_m.ExpiresIn = value.Time
			}
		case tokens.FieldToken:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field token", values[i])
			} else if value.Valid {
				_m.Token = value.String
			}
		case tokens.FieldTokenType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field token_type", values[i])
			} else if value.Valid {
				_m.TokenType = value.String
			}
		case tokens.FieldDeleted:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field deleted", values[i])
			} else if value.Valid {
				_m.Deleted = value.Bool
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Tokens.
// This includes values selected through modifiers, order, etc.
func (_m *Tokens) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Tokens.
// Note that you need to call Tokens.Unwrap() before calling this method if this Tokens
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Tokens) Update() *TokensUpdateOne {
	return NewTokensClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Tokens entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Tokens) Unwrap() *Tokens {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Tokens is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Tokens) String() string {
	var builder strings.Builder
	builder.WriteString("Tokens(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("request_id=")
	builder.WriteString(_m.RequestID)
	builder.WriteString(", ")
	builder.WriteString("expires_in=")
	builder.WriteString(_m.ExpiresIn.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("token=")
	builder.WriteString(_m.Token)
	builder.WriteString(", ")
	builder.WriteString("token_type=")
	builder.WriteString(_m.TokenType)
	builder.WriteString(", ")
	builder.WriteString("deleted=")
	builder.WriteString(fmt.Sprintf("%v", _m.Deleted))
	builder.WriteByte(')')
	return builder.String()
}

// TokensSlice is a parsable slice of Tokens.
type TokensSlice []*Tokens
