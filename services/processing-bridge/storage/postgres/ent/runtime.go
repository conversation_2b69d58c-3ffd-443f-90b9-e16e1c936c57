// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/tokens"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
	tokensMixin := schema.Tokens{}.Mixin()
	tokensMixinFields0 := tokensMixin[0].Fields()
	_ = tokensMixinFields0
	tokensFields := schema.Tokens{}.Fields()
	_ = tokensFields
	// tokensDescCreateTime is the schema descriptor for create_time field.
	tokensDescCreateTime := tokensMixinFields0[0].Descriptor()
	// tokens.DefaultCreateTime holds the default value on creation for the create_time field.
	tokens.DefaultCreateTime = tokensDescCreateTime.Default.(func() time.Time)
	// tokensDescUpdateTime is the schema descriptor for update_time field.
	tokensDescUpdateTime := tokensMixinFields0[1].Descriptor()
	// tokens.DefaultUpdateTime holds the default value on creation for the update_time field.
	tokens.DefaultUpdateTime = tokensDescUpdateTime.Default.(func() time.Time)
	// tokens.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	tokens.UpdateDefaultUpdateTime = tokensDescUpdateTime.UpdateDefault.(func() time.Time)
	// tokensDescRequestID is the schema descriptor for request_id field.
	tokensDescRequestID := tokensFields[1].Descriptor()
	// tokens.RequestIDValidator is a validator for the "request_id" field. It is called by the builders before save.
	tokens.RequestIDValidator = tokensDescRequestID.Validators[0].(func(string) error)
	// tokensDescToken is the schema descriptor for token field.
	tokensDescToken := tokensFields[3].Descriptor()
	// tokens.TokenValidator is a validator for the "token" field. It is called by the builders before save.
	tokens.TokenValidator = tokensDescToken.Validators[0].(func(string) error)
	// tokensDescTokenType is the schema descriptor for token_type field.
	tokensDescTokenType := tokensFields[4].Descriptor()
	// tokens.TokenTypeValidator is a validator for the "token_type" field. It is called by the builders before save.
	tokens.TokenTypeValidator = tokensDescTokenType.Validators[0].(func(string) error)
	// tokensDescDeleted is the schema descriptor for deleted field.
	tokensDescDeleted := tokensFields[5].Descriptor()
	// tokens.DefaultDeleted holds the default value on creation for the deleted field.
	tokens.DefaultDeleted = tokensDescDeleted.Default.(bool)
	// tokensDescID is the schema descriptor for id field.
	tokensDescID := tokensFields[0].Descriptor()
	// tokens.DefaultID holds the default value on creation for the id field.
	tokens.DefaultID = tokensDescID.Default.(func() uuid.UUID)
}
