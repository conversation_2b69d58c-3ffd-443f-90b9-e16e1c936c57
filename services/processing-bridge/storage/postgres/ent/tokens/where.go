// Code generated by ent, DO NOT EDIT.

package tokens

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Tokens {
	return predicate.Tokens(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldUpdateTime, v))
}

// RequestID applies equality check predicate on the "request_id" field. It's identical to RequestIDEQ.
func RequestID(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldRequestID, v))
}

// ExpiresIn applies equality check predicate on the "expires_in" field. It's identical to ExpiresInEQ.
func ExpiresIn(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldExpiresIn, v))
}

// Token applies equality check predicate on the "token" field. It's identical to TokenEQ.
func Token(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldToken, v))
}

// TokenType applies equality check predicate on the "token_type" field. It's identical to TokenTypeEQ.
func TokenType(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldTokenType, v))
}

// Deleted applies equality check predicate on the "deleted" field. It's identical to DeletedEQ.
func Deleted(v bool) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldDeleted, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldLTE(FieldUpdateTime, v))
}

// RequestIDEQ applies the EQ predicate on the "request_id" field.
func RequestIDEQ(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldRequestID, v))
}

// RequestIDNEQ applies the NEQ predicate on the "request_id" field.
func RequestIDNEQ(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldNEQ(FieldRequestID, v))
}

// RequestIDIn applies the In predicate on the "request_id" field.
func RequestIDIn(vs ...string) predicate.Tokens {
	return predicate.Tokens(sql.FieldIn(FieldRequestID, vs...))
}

// RequestIDNotIn applies the NotIn predicate on the "request_id" field.
func RequestIDNotIn(vs ...string) predicate.Tokens {
	return predicate.Tokens(sql.FieldNotIn(FieldRequestID, vs...))
}

// RequestIDGT applies the GT predicate on the "request_id" field.
func RequestIDGT(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldGT(FieldRequestID, v))
}

// RequestIDGTE applies the GTE predicate on the "request_id" field.
func RequestIDGTE(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldGTE(FieldRequestID, v))
}

// RequestIDLT applies the LT predicate on the "request_id" field.
func RequestIDLT(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldLT(FieldRequestID, v))
}

// RequestIDLTE applies the LTE predicate on the "request_id" field.
func RequestIDLTE(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldLTE(FieldRequestID, v))
}

// RequestIDContains applies the Contains predicate on the "request_id" field.
func RequestIDContains(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldContains(FieldRequestID, v))
}

// RequestIDHasPrefix applies the HasPrefix predicate on the "request_id" field.
func RequestIDHasPrefix(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldHasPrefix(FieldRequestID, v))
}

// RequestIDHasSuffix applies the HasSuffix predicate on the "request_id" field.
func RequestIDHasSuffix(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldHasSuffix(FieldRequestID, v))
}

// RequestIDEqualFold applies the EqualFold predicate on the "request_id" field.
func RequestIDEqualFold(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEqualFold(FieldRequestID, v))
}

// RequestIDContainsFold applies the ContainsFold predicate on the "request_id" field.
func RequestIDContainsFold(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldContainsFold(FieldRequestID, v))
}

// ExpiresInEQ applies the EQ predicate on the "expires_in" field.
func ExpiresInEQ(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldExpiresIn, v))
}

// ExpiresInNEQ applies the NEQ predicate on the "expires_in" field.
func ExpiresInNEQ(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldNEQ(FieldExpiresIn, v))
}

// ExpiresInIn applies the In predicate on the "expires_in" field.
func ExpiresInIn(vs ...time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldIn(FieldExpiresIn, vs...))
}

// ExpiresInNotIn applies the NotIn predicate on the "expires_in" field.
func ExpiresInNotIn(vs ...time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldNotIn(FieldExpiresIn, vs...))
}

// ExpiresInGT applies the GT predicate on the "expires_in" field.
func ExpiresInGT(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldGT(FieldExpiresIn, v))
}

// ExpiresInGTE applies the GTE predicate on the "expires_in" field.
func ExpiresInGTE(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldGTE(FieldExpiresIn, v))
}

// ExpiresInLT applies the LT predicate on the "expires_in" field.
func ExpiresInLT(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldLT(FieldExpiresIn, v))
}

// ExpiresInLTE applies the LTE predicate on the "expires_in" field.
func ExpiresInLTE(v time.Time) predicate.Tokens {
	return predicate.Tokens(sql.FieldLTE(FieldExpiresIn, v))
}

// TokenEQ applies the EQ predicate on the "token" field.
func TokenEQ(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldToken, v))
}

// TokenNEQ applies the NEQ predicate on the "token" field.
func TokenNEQ(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldNEQ(FieldToken, v))
}

// TokenIn applies the In predicate on the "token" field.
func TokenIn(vs ...string) predicate.Tokens {
	return predicate.Tokens(sql.FieldIn(FieldToken, vs...))
}

// TokenNotIn applies the NotIn predicate on the "token" field.
func TokenNotIn(vs ...string) predicate.Tokens {
	return predicate.Tokens(sql.FieldNotIn(FieldToken, vs...))
}

// TokenGT applies the GT predicate on the "token" field.
func TokenGT(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldGT(FieldToken, v))
}

// TokenGTE applies the GTE predicate on the "token" field.
func TokenGTE(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldGTE(FieldToken, v))
}

// TokenLT applies the LT predicate on the "token" field.
func TokenLT(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldLT(FieldToken, v))
}

// TokenLTE applies the LTE predicate on the "token" field.
func TokenLTE(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldLTE(FieldToken, v))
}

// TokenContains applies the Contains predicate on the "token" field.
func TokenContains(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldContains(FieldToken, v))
}

// TokenHasPrefix applies the HasPrefix predicate on the "token" field.
func TokenHasPrefix(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldHasPrefix(FieldToken, v))
}

// TokenHasSuffix applies the HasSuffix predicate on the "token" field.
func TokenHasSuffix(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldHasSuffix(FieldToken, v))
}

// TokenEqualFold applies the EqualFold predicate on the "token" field.
func TokenEqualFold(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEqualFold(FieldToken, v))
}

// TokenContainsFold applies the ContainsFold predicate on the "token" field.
func TokenContainsFold(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldContainsFold(FieldToken, v))
}

// TokenTypeEQ applies the EQ predicate on the "token_type" field.
func TokenTypeEQ(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldTokenType, v))
}

// TokenTypeNEQ applies the NEQ predicate on the "token_type" field.
func TokenTypeNEQ(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldNEQ(FieldTokenType, v))
}

// TokenTypeIn applies the In predicate on the "token_type" field.
func TokenTypeIn(vs ...string) predicate.Tokens {
	return predicate.Tokens(sql.FieldIn(FieldTokenType, vs...))
}

// TokenTypeNotIn applies the NotIn predicate on the "token_type" field.
func TokenTypeNotIn(vs ...string) predicate.Tokens {
	return predicate.Tokens(sql.FieldNotIn(FieldTokenType, vs...))
}

// TokenTypeGT applies the GT predicate on the "token_type" field.
func TokenTypeGT(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldGT(FieldTokenType, v))
}

// TokenTypeGTE applies the GTE predicate on the "token_type" field.
func TokenTypeGTE(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldGTE(FieldTokenType, v))
}

// TokenTypeLT applies the LT predicate on the "token_type" field.
func TokenTypeLT(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldLT(FieldTokenType, v))
}

// TokenTypeLTE applies the LTE predicate on the "token_type" field.
func TokenTypeLTE(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldLTE(FieldTokenType, v))
}

// TokenTypeContains applies the Contains predicate on the "token_type" field.
func TokenTypeContains(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldContains(FieldTokenType, v))
}

// TokenTypeHasPrefix applies the HasPrefix predicate on the "token_type" field.
func TokenTypeHasPrefix(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldHasPrefix(FieldTokenType, v))
}

// TokenTypeHasSuffix applies the HasSuffix predicate on the "token_type" field.
func TokenTypeHasSuffix(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldHasSuffix(FieldTokenType, v))
}

// TokenTypeEqualFold applies the EqualFold predicate on the "token_type" field.
func TokenTypeEqualFold(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldEqualFold(FieldTokenType, v))
}

// TokenTypeContainsFold applies the ContainsFold predicate on the "token_type" field.
func TokenTypeContainsFold(v string) predicate.Tokens {
	return predicate.Tokens(sql.FieldContainsFold(FieldTokenType, v))
}

// DeletedEQ applies the EQ predicate on the "deleted" field.
func DeletedEQ(v bool) predicate.Tokens {
	return predicate.Tokens(sql.FieldEQ(FieldDeleted, v))
}

// DeletedNEQ applies the NEQ predicate on the "deleted" field.
func DeletedNEQ(v bool) predicate.Tokens {
	return predicate.Tokens(sql.FieldNEQ(FieldDeleted, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Tokens) predicate.Tokens {
	return predicate.Tokens(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Tokens) predicate.Tokens {
	return predicate.Tokens(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Tokens) predicate.Tokens {
	return predicate.Tokens(sql.NotPredicates(p))
}
