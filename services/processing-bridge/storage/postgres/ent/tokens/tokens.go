// Code generated by ent, DO NOT EDIT.

package tokens

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the tokens type in the database.
	Label = "tokens"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldRequestID holds the string denoting the request_id field in the database.
	FieldRequestID = "request_id"
	// FieldExpiresIn holds the string denoting the expires_in field in the database.
	FieldExpiresIn = "expires_in"
	// FieldToken holds the string denoting the token field in the database.
	FieldToken = "token"
	// FieldTokenType holds the string denoting the token_type field in the database.
	FieldTokenType = "token_type"
	// FieldDeleted holds the string denoting the deleted field in the database.
	FieldDeleted = "deleted"
	// Table holds the table name of the tokens in the database.
	Table = "tokens"
)

// Columns holds all SQL columns for tokens fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldRequestID,
	FieldExpiresIn,
	FieldToken,
	FieldTokenType,
	FieldDeleted,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// RequestIDValidator is a validator for the "request_id" field. It is called by the builders before save.
	RequestIDValidator func(string) error
	// TokenValidator is a validator for the "token" field. It is called by the builders before save.
	TokenValidator func(string) error
	// TokenTypeValidator is a validator for the "token_type" field. It is called by the builders before save.
	TokenTypeValidator func(string) error
	// DefaultDeleted holds the default value on creation for the "deleted" field.
	DefaultDeleted bool
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the Tokens queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByRequestID orders the results by the request_id field.
func ByRequestID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRequestID, opts...).ToFunc()
}

// ByExpiresIn orders the results by the expires_in field.
func ByExpiresIn(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpiresIn, opts...).ToFunc()
}

// ByToken orders the results by the token field.
func ByToken(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldToken, opts...).ToFunc()
}

// ByTokenType orders the results by the token_type field.
func ByTokenType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTokenType, opts...).ToFunc()
}

// ByDeleted orders the results by the deleted field.
func ByDeleted(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeleted, opts...).ToFunc()
}
