// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/tokens"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeHealth = "Health"
	TypeTokens = "Tokens"
)

// HealthMutation represents an operation that mutates the Health nodes in the graph.
type HealthMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Health, error)
	predicates    []predicate.Health
}

var _ ent.Mutation = (*HealthMutation)(nil)

// healthOption allows management of the mutation configuration using functional options.
type healthOption func(*HealthMutation)

// newHealthMutation creates new mutation for the Health entity.
func newHealthMutation(c config, op Op, opts ...healthOption) *HealthMutation {
	m := &HealthMutation{
		config:        c,
		op:            op,
		typ:           TypeHealth,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withHealthID sets the ID field of the mutation.
func withHealthID(id uuid.UUID) healthOption {
	return func(m *HealthMutation) {
		var (
			err   error
			once  sync.Once
			value *Health
		)
		m.oldValue = func(ctx context.Context) (*Health, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Health.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withHealth sets the old Health of the mutation.
func withHealth(node *Health) healthOption {
	return func(m *HealthMutation) {
		m.oldValue = func(context.Context) (*Health, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m HealthMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m HealthMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Health entities.
func (m *HealthMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *HealthMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *HealthMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Health.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the HealthMutation builder.
func (m *HealthMutation) Where(ps ...predicate.Health) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the HealthMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *HealthMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Health, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *HealthMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *HealthMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Health).
func (m *HealthMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *HealthMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *HealthMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *HealthMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Health field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *HealthMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *HealthMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Health numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *HealthMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *HealthMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *HealthMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Health nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *HealthMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *HealthMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *HealthMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *HealthMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *HealthMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *HealthMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *HealthMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *HealthMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Health unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *HealthMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Health edge %s", name)
}

// TokensMutation represents an operation that mutates the Tokens nodes in the graph.
type TokensMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	create_time   *time.Time
	update_time   *time.Time
	request_id    *string
	expires_in    *time.Time
	token         *string
	token_type    *string
	deleted       *bool
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Tokens, error)
	predicates    []predicate.Tokens
}

var _ ent.Mutation = (*TokensMutation)(nil)

// tokensOption allows management of the mutation configuration using functional options.
type tokensOption func(*TokensMutation)

// newTokensMutation creates new mutation for the Tokens entity.
func newTokensMutation(c config, op Op, opts ...tokensOption) *TokensMutation {
	m := &TokensMutation{
		config:        c,
		op:            op,
		typ:           TypeTokens,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTokensID sets the ID field of the mutation.
func withTokensID(id uuid.UUID) tokensOption {
	return func(m *TokensMutation) {
		var (
			err   error
			once  sync.Once
			value *Tokens
		)
		m.oldValue = func(ctx context.Context) (*Tokens, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Tokens.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTokens sets the old Tokens of the mutation.
func withTokens(node *Tokens) tokensOption {
	return func(m *TokensMutation) {
		m.oldValue = func(context.Context) (*Tokens, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TokensMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TokensMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Tokens entities.
func (m *TokensMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TokensMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TokensMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Tokens.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *TokensMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *TokensMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Tokens entity.
// If the Tokens object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TokensMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *TokensMutation) ResetCreateTime() {
	m.create_time = nil
}

// SetUpdateTime sets the "update_time" field.
func (m *TokensMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *TokensMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Tokens entity.
// If the Tokens object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TokensMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *TokensMutation) ResetUpdateTime() {
	m.update_time = nil
}

// SetRequestID sets the "request_id" field.
func (m *TokensMutation) SetRequestID(s string) {
	m.request_id = &s
}

// RequestID returns the value of the "request_id" field in the mutation.
func (m *TokensMutation) RequestID() (r string, exists bool) {
	v := m.request_id
	if v == nil {
		return
	}
	return *v, true
}

// OldRequestID returns the old "request_id" field's value of the Tokens entity.
// If the Tokens object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TokensMutation) OldRequestID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRequestID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRequestID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRequestID: %w", err)
	}
	return oldValue.RequestID, nil
}

// ResetRequestID resets all changes to the "request_id" field.
func (m *TokensMutation) ResetRequestID() {
	m.request_id = nil
}

// SetExpiresIn sets the "expires_in" field.
func (m *TokensMutation) SetExpiresIn(t time.Time) {
	m.expires_in = &t
}

// ExpiresIn returns the value of the "expires_in" field in the mutation.
func (m *TokensMutation) ExpiresIn() (r time.Time, exists bool) {
	v := m.expires_in
	if v == nil {
		return
	}
	return *v, true
}

// OldExpiresIn returns the old "expires_in" field's value of the Tokens entity.
// If the Tokens object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TokensMutation) OldExpiresIn(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExpiresIn is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExpiresIn requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExpiresIn: %w", err)
	}
	return oldValue.ExpiresIn, nil
}

// ResetExpiresIn resets all changes to the "expires_in" field.
func (m *TokensMutation) ResetExpiresIn() {
	m.expires_in = nil
}

// SetToken sets the "token" field.
func (m *TokensMutation) SetToken(s string) {
	m.token = &s
}

// Token returns the value of the "token" field in the mutation.
func (m *TokensMutation) Token() (r string, exists bool) {
	v := m.token
	if v == nil {
		return
	}
	return *v, true
}

// OldToken returns the old "token" field's value of the Tokens entity.
// If the Tokens object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TokensMutation) OldToken(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldToken is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldToken requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldToken: %w", err)
	}
	return oldValue.Token, nil
}

// ResetToken resets all changes to the "token" field.
func (m *TokensMutation) ResetToken() {
	m.token = nil
}

// SetTokenType sets the "token_type" field.
func (m *TokensMutation) SetTokenType(s string) {
	m.token_type = &s
}

// TokenType returns the value of the "token_type" field in the mutation.
func (m *TokensMutation) TokenType() (r string, exists bool) {
	v := m.token_type
	if v == nil {
		return
	}
	return *v, true
}

// OldTokenType returns the old "token_type" field's value of the Tokens entity.
// If the Tokens object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TokensMutation) OldTokenType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTokenType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTokenType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTokenType: %w", err)
	}
	return oldValue.TokenType, nil
}

// ResetTokenType resets all changes to the "token_type" field.
func (m *TokensMutation) ResetTokenType() {
	m.token_type = nil
}

// SetDeleted sets the "deleted" field.
func (m *TokensMutation) SetDeleted(b bool) {
	m.deleted = &b
}

// Deleted returns the value of the "deleted" field in the mutation.
func (m *TokensMutation) Deleted() (r bool, exists bool) {
	v := m.deleted
	if v == nil {
		return
	}
	return *v, true
}

// OldDeleted returns the old "deleted" field's value of the Tokens entity.
// If the Tokens object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TokensMutation) OldDeleted(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeleted is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeleted requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeleted: %w", err)
	}
	return oldValue.Deleted, nil
}

// ResetDeleted resets all changes to the "deleted" field.
func (m *TokensMutation) ResetDeleted() {
	m.deleted = nil
}

// Where appends a list predicates to the TokensMutation builder.
func (m *TokensMutation) Where(ps ...predicate.Tokens) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TokensMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TokensMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Tokens, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TokensMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TokensMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Tokens).
func (m *TokensMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TokensMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.create_time != nil {
		fields = append(fields, tokens.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, tokens.FieldUpdateTime)
	}
	if m.request_id != nil {
		fields = append(fields, tokens.FieldRequestID)
	}
	if m.expires_in != nil {
		fields = append(fields, tokens.FieldExpiresIn)
	}
	if m.token != nil {
		fields = append(fields, tokens.FieldToken)
	}
	if m.token_type != nil {
		fields = append(fields, tokens.FieldTokenType)
	}
	if m.deleted != nil {
		fields = append(fields, tokens.FieldDeleted)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TokensMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case tokens.FieldCreateTime:
		return m.CreateTime()
	case tokens.FieldUpdateTime:
		return m.UpdateTime()
	case tokens.FieldRequestID:
		return m.RequestID()
	case tokens.FieldExpiresIn:
		return m.ExpiresIn()
	case tokens.FieldToken:
		return m.Token()
	case tokens.FieldTokenType:
		return m.TokenType()
	case tokens.FieldDeleted:
		return m.Deleted()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TokensMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case tokens.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case tokens.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case tokens.FieldRequestID:
		return m.OldRequestID(ctx)
	case tokens.FieldExpiresIn:
		return m.OldExpiresIn(ctx)
	case tokens.FieldToken:
		return m.OldToken(ctx)
	case tokens.FieldTokenType:
		return m.OldTokenType(ctx)
	case tokens.FieldDeleted:
		return m.OldDeleted(ctx)
	}
	return nil, fmt.Errorf("unknown Tokens field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TokensMutation) SetField(name string, value ent.Value) error {
	switch name {
	case tokens.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case tokens.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case tokens.FieldRequestID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRequestID(v)
		return nil
	case tokens.FieldExpiresIn:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExpiresIn(v)
		return nil
	case tokens.FieldToken:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetToken(v)
		return nil
	case tokens.FieldTokenType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTokenType(v)
		return nil
	case tokens.FieldDeleted:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeleted(v)
		return nil
	}
	return fmt.Errorf("unknown Tokens field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TokensMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TokensMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TokensMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Tokens numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TokensMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TokensMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TokensMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Tokens nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TokensMutation) ResetField(name string) error {
	switch name {
	case tokens.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case tokens.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case tokens.FieldRequestID:
		m.ResetRequestID()
		return nil
	case tokens.FieldExpiresIn:
		m.ResetExpiresIn()
		return nil
	case tokens.FieldToken:
		m.ResetToken()
		return nil
	case tokens.FieldTokenType:
		m.ResetTokenType()
		return nil
	case tokens.FieldDeleted:
		m.ResetDeleted()
		return nil
	}
	return fmt.Errorf("unknown Tokens field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TokensMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TokensMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TokensMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TokensMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TokensMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TokensMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TokensMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Tokens unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TokensMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Tokens edge %s", name)
}
