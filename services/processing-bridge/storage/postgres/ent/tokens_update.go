// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/tokens"
)

// TokensUpdate is the builder for updating Tokens entities.
type TokensUpdate struct {
	config
	hooks     []Hook
	mutation  *TokensMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the TokensUpdate builder.
func (_u *TokensUpdate) Where(ps ...predicate.Tokens) *TokensUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetRequestID sets the "request_id" field.
func (_u *TokensUpdate) SetRequestID(v string) *TokensUpdate {
	_u.mutation.SetRequestID(v)
	return _u
}

// SetNillableRequestID sets the "request_id" field if the given value is not nil.
func (_u *TokensUpdate) SetNillableRequestID(v *string) *TokensUpdate {
	if v != nil {
		_u.SetRequestID(*v)
	}
	return _u
}

// SetExpiresIn sets the "expires_in" field.
func (_u *TokensUpdate) SetExpiresIn(v time.Time) *TokensUpdate {
	_u.mutation.SetExpiresIn(v)
	return _u
}

// SetNillableExpiresIn sets the "expires_in" field if the given value is not nil.
func (_u *TokensUpdate) SetNillableExpiresIn(v *time.Time) *TokensUpdate {
	if v != nil {
		_u.SetExpiresIn(*v)
	}
	return _u
}

// SetToken sets the "token" field.
func (_u *TokensUpdate) SetToken(v string) *TokensUpdate {
	_u.mutation.SetToken(v)
	return _u
}

// SetNillableToken sets the "token" field if the given value is not nil.
func (_u *TokensUpdate) SetNillableToken(v *string) *TokensUpdate {
	if v != nil {
		_u.SetToken(*v)
	}
	return _u
}

// SetTokenType sets the "token_type" field.
func (_u *TokensUpdate) SetTokenType(v string) *TokensUpdate {
	_u.mutation.SetTokenType(v)
	return _u
}

// SetNillableTokenType sets the "token_type" field if the given value is not nil.
func (_u *TokensUpdate) SetNillableTokenType(v *string) *TokensUpdate {
	if v != nil {
		_u.SetTokenType(*v)
	}
	return _u
}

// SetDeleted sets the "deleted" field.
func (_u *TokensUpdate) SetDeleted(v bool) *TokensUpdate {
	_u.mutation.SetDeleted(v)
	return _u
}

// SetNillableDeleted sets the "deleted" field if the given value is not nil.
func (_u *TokensUpdate) SetNillableDeleted(v *bool) *TokensUpdate {
	if v != nil {
		_u.SetDeleted(*v)
	}
	return _u
}

// Mutation returns the TokensMutation object of the builder.
func (_u *TokensUpdate) Mutation() *TokensMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *TokensUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *TokensUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *TokensUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *TokensUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *TokensUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := tokens.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *TokensUpdate) check() error {
	if v, ok := _u.mutation.RequestID(); ok {
		if err := tokens.RequestIDValidator(v); err != nil {
			return &ValidationError{Name: "request_id", err: fmt.Errorf(`ent: validator failed for field "Tokens.request_id": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Token(); ok {
		if err := tokens.TokenValidator(v); err != nil {
			return &ValidationError{Name: "token", err: fmt.Errorf(`ent: validator failed for field "Tokens.token": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TokenType(); ok {
		if err := tokens.TokenTypeValidator(v); err != nil {
			return &ValidationError{Name: "token_type", err: fmt.Errorf(`ent: validator failed for field "Tokens.token_type": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *TokensUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TokensUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *TokensUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(tokens.Table, tokens.Columns, sqlgraph.NewFieldSpec(tokens.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(tokens.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.RequestID(); ok {
		_spec.SetField(tokens.FieldRequestID, field.TypeString, value)
	}
	if value, ok := _u.mutation.ExpiresIn(); ok {
		_spec.SetField(tokens.FieldExpiresIn, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Token(); ok {
		_spec.SetField(tokens.FieldToken, field.TypeString, value)
	}
	if value, ok := _u.mutation.TokenType(); ok {
		_spec.SetField(tokens.FieldTokenType, field.TypeString, value)
	}
	if value, ok := _u.mutation.Deleted(); ok {
		_spec.SetField(tokens.FieldDeleted, field.TypeBool, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tokens.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// TokensUpdateOne is the builder for updating a single Tokens entity.
type TokensUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *TokensMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetRequestID sets the "request_id" field.
func (_u *TokensUpdateOne) SetRequestID(v string) *TokensUpdateOne {
	_u.mutation.SetRequestID(v)
	return _u
}

// SetNillableRequestID sets the "request_id" field if the given value is not nil.
func (_u *TokensUpdateOne) SetNillableRequestID(v *string) *TokensUpdateOne {
	if v != nil {
		_u.SetRequestID(*v)
	}
	return _u
}

// SetExpiresIn sets the "expires_in" field.
func (_u *TokensUpdateOne) SetExpiresIn(v time.Time) *TokensUpdateOne {
	_u.mutation.SetExpiresIn(v)
	return _u
}

// SetNillableExpiresIn sets the "expires_in" field if the given value is not nil.
func (_u *TokensUpdateOne) SetNillableExpiresIn(v *time.Time) *TokensUpdateOne {
	if v != nil {
		_u.SetExpiresIn(*v)
	}
	return _u
}

// SetToken sets the "token" field.
func (_u *TokensUpdateOne) SetToken(v string) *TokensUpdateOne {
	_u.mutation.SetToken(v)
	return _u
}

// SetNillableToken sets the "token" field if the given value is not nil.
func (_u *TokensUpdateOne) SetNillableToken(v *string) *TokensUpdateOne {
	if v != nil {
		_u.SetToken(*v)
	}
	return _u
}

// SetTokenType sets the "token_type" field.
func (_u *TokensUpdateOne) SetTokenType(v string) *TokensUpdateOne {
	_u.mutation.SetTokenType(v)
	return _u
}

// SetNillableTokenType sets the "token_type" field if the given value is not nil.
func (_u *TokensUpdateOne) SetNillableTokenType(v *string) *TokensUpdateOne {
	if v != nil {
		_u.SetTokenType(*v)
	}
	return _u
}

// SetDeleted sets the "deleted" field.
func (_u *TokensUpdateOne) SetDeleted(v bool) *TokensUpdateOne {
	_u.mutation.SetDeleted(v)
	return _u
}

// SetNillableDeleted sets the "deleted" field if the given value is not nil.
func (_u *TokensUpdateOne) SetNillableDeleted(v *bool) *TokensUpdateOne {
	if v != nil {
		_u.SetDeleted(*v)
	}
	return _u
}

// Mutation returns the TokensMutation object of the builder.
func (_u *TokensUpdateOne) Mutation() *TokensMutation {
	return _u.mutation
}

// Where appends a list predicates to the TokensUpdate builder.
func (_u *TokensUpdateOne) Where(ps ...predicate.Tokens) *TokensUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *TokensUpdateOne) Select(field string, fields ...string) *TokensUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Tokens entity.
func (_u *TokensUpdateOne) Save(ctx context.Context) (*Tokens, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *TokensUpdateOne) SaveX(ctx context.Context) *Tokens {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *TokensUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *TokensUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *TokensUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok {
		v := tokens.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *TokensUpdateOne) check() error {
	if v, ok := _u.mutation.RequestID(); ok {
		if err := tokens.RequestIDValidator(v); err != nil {
			return &ValidationError{Name: "request_id", err: fmt.Errorf(`ent: validator failed for field "Tokens.request_id": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Token(); ok {
		if err := tokens.TokenValidator(v); err != nil {
			return &ValidationError{Name: "token", err: fmt.Errorf(`ent: validator failed for field "Tokens.token": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TokenType(); ok {
		if err := tokens.TokenTypeValidator(v); err != nil {
			return &ValidationError{Name: "token_type", err: fmt.Errorf(`ent: validator failed for field "Tokens.token_type": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *TokensUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TokensUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *TokensUpdateOne) sqlSave(ctx context.Context) (_node *Tokens, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(tokens.Table, tokens.Columns, sqlgraph.NewFieldSpec(tokens.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Tokens.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, tokens.FieldID)
		for _, f := range fields {
			if !tokens.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != tokens.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(tokens.FieldUpdateTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.RequestID(); ok {
		_spec.SetField(tokens.FieldRequestID, field.TypeString, value)
	}
	if value, ok := _u.mutation.ExpiresIn(); ok {
		_spec.SetField(tokens.FieldExpiresIn, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Token(); ok {
		_spec.SetField(tokens.FieldToken, field.TypeString, value)
	}
	if value, ok := _u.mutation.TokenType(); ok {
		_spec.SetField(tokens.FieldTokenType, field.TypeString, value)
	}
	if value, ok := _u.mutation.Deleted(); ok {
		_spec.SetField(tokens.FieldDeleted, field.TypeBool, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Tokens{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tokens.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
