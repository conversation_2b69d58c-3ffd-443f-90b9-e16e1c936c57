// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/postgres/ent/tokens"
)

// TokensCreate is the builder for creating a Tokens entity.
type TokensCreate struct {
	config
	mutation *TokensMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *TokensCreate) SetCreateTime(v time.Time) *TokensCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *TokensCreate) SetNillableCreateTime(v *time.Time) *TokensCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *TokensCreate) SetUpdateTime(v time.Time) *TokensCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *TokensCreate) SetNillableUpdateTime(v *time.Time) *TokensCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetRequestID sets the "request_id" field.
func (_c *TokensCreate) SetRequestID(v string) *TokensCreate {
	_c.mutation.SetRequestID(v)
	return _c
}

// SetExpiresIn sets the "expires_in" field.
func (_c *TokensCreate) SetExpiresIn(v time.Time) *TokensCreate {
	_c.mutation.SetExpiresIn(v)
	return _c
}

// SetToken sets the "token" field.
func (_c *TokensCreate) SetToken(v string) *TokensCreate {
	_c.mutation.SetToken(v)
	return _c
}

// SetTokenType sets the "token_type" field.
func (_c *TokensCreate) SetTokenType(v string) *TokensCreate {
	_c.mutation.SetTokenType(v)
	return _c
}

// SetDeleted sets the "deleted" field.
func (_c *TokensCreate) SetDeleted(v bool) *TokensCreate {
	_c.mutation.SetDeleted(v)
	return _c
}

// SetNillableDeleted sets the "deleted" field if the given value is not nil.
func (_c *TokensCreate) SetNillableDeleted(v *bool) *TokensCreate {
	if v != nil {
		_c.SetDeleted(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *TokensCreate) SetID(v uuid.UUID) *TokensCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *TokensCreate) SetNillableID(v *uuid.UUID) *TokensCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// Mutation returns the TokensMutation object of the builder.
func (_c *TokensCreate) Mutation() *TokensMutation {
	return _c.mutation
}

// Save creates the Tokens in the database.
func (_c *TokensCreate) Save(ctx context.Context) (*Tokens, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *TokensCreate) SaveX(ctx context.Context) *Tokens {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *TokensCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *TokensCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *TokensCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := tokens.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := tokens.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.Deleted(); !ok {
		v := tokens.DefaultDeleted
		_c.mutation.SetDeleted(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := tokens.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *TokensCreate) check() error {
	if _, ok := _c.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "Tokens.create_time"`)}
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "Tokens.update_time"`)}
	}
	if _, ok := _c.mutation.RequestID(); !ok {
		return &ValidationError{Name: "request_id", err: errors.New(`ent: missing required field "Tokens.request_id"`)}
	}
	if v, ok := _c.mutation.RequestID(); ok {
		if err := tokens.RequestIDValidator(v); err != nil {
			return &ValidationError{Name: "request_id", err: fmt.Errorf(`ent: validator failed for field "Tokens.request_id": %w`, err)}
		}
	}
	if _, ok := _c.mutation.ExpiresIn(); !ok {
		return &ValidationError{Name: "expires_in", err: errors.New(`ent: missing required field "Tokens.expires_in"`)}
	}
	if _, ok := _c.mutation.Token(); !ok {
		return &ValidationError{Name: "token", err: errors.New(`ent: missing required field "Tokens.token"`)}
	}
	if v, ok := _c.mutation.Token(); ok {
		if err := tokens.TokenValidator(v); err != nil {
			return &ValidationError{Name: "token", err: fmt.Errorf(`ent: validator failed for field "Tokens.token": %w`, err)}
		}
	}
	if _, ok := _c.mutation.TokenType(); !ok {
		return &ValidationError{Name: "token_type", err: errors.New(`ent: missing required field "Tokens.token_type"`)}
	}
	if v, ok := _c.mutation.TokenType(); ok {
		if err := tokens.TokenTypeValidator(v); err != nil {
			return &ValidationError{Name: "token_type", err: fmt.Errorf(`ent: validator failed for field "Tokens.token_type": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Deleted(); !ok {
		return &ValidationError{Name: "deleted", err: errors.New(`ent: missing required field "Tokens.deleted"`)}
	}
	return nil
}

func (_c *TokensCreate) sqlSave(ctx context.Context) (*Tokens, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *TokensCreate) createSpec() (*Tokens, *sqlgraph.CreateSpec) {
	var (
		_node = &Tokens{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(tokens.Table, sqlgraph.NewFieldSpec(tokens.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(tokens.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(tokens.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.RequestID(); ok {
		_spec.SetField(tokens.FieldRequestID, field.TypeString, value)
		_node.RequestID = value
	}
	if value, ok := _c.mutation.ExpiresIn(); ok {
		_spec.SetField(tokens.FieldExpiresIn, field.TypeTime, value)
		_node.ExpiresIn = value
	}
	if value, ok := _c.mutation.Token(); ok {
		_spec.SetField(tokens.FieldToken, field.TypeString, value)
		_node.Token = value
	}
	if value, ok := _c.mutation.TokenType(); ok {
		_spec.SetField(tokens.FieldTokenType, field.TypeString, value)
		_node.TokenType = value
	}
	if value, ok := _c.mutation.Deleted(); ok {
		_spec.SetField(tokens.FieldDeleted, field.TypeBool, value)
		_node.Deleted = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tokens.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TokensUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *TokensCreate) OnConflict(opts ...sql.ConflictOption) *TokensUpsertOne {
	_c.conflict = opts
	return &TokensUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tokens.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *TokensCreate) OnConflictColumns(columns ...string) *TokensUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &TokensUpsertOne{
		create: _c,
	}
}

type (
	// TokensUpsertOne is the builder for "upsert"-ing
	//  one Tokens node.
	TokensUpsertOne struct {
		create *TokensCreate
	}

	// TokensUpsert is the "OnConflict" setter.
	TokensUpsert struct {
		*sql.UpdateSet
	}
)

// SetRequestID sets the "request_id" field.
func (u *TokensUpsert) SetRequestID(v string) *TokensUpsert {
	u.Set(tokens.FieldRequestID, v)
	return u
}

// UpdateRequestID sets the "request_id" field to the value that was provided on create.
func (u *TokensUpsert) UpdateRequestID() *TokensUpsert {
	u.SetExcluded(tokens.FieldRequestID)
	return u
}

// SetExpiresIn sets the "expires_in" field.
func (u *TokensUpsert) SetExpiresIn(v time.Time) *TokensUpsert {
	u.Set(tokens.FieldExpiresIn, v)
	return u
}

// UpdateExpiresIn sets the "expires_in" field to the value that was provided on create.
func (u *TokensUpsert) UpdateExpiresIn() *TokensUpsert {
	u.SetExcluded(tokens.FieldExpiresIn)
	return u
}

// SetToken sets the "token" field.
func (u *TokensUpsert) SetToken(v string) *TokensUpsert {
	u.Set(tokens.FieldToken, v)
	return u
}

// UpdateToken sets the "token" field to the value that was provided on create.
func (u *TokensUpsert) UpdateToken() *TokensUpsert {
	u.SetExcluded(tokens.FieldToken)
	return u
}

// SetTokenType sets the "token_type" field.
func (u *TokensUpsert) SetTokenType(v string) *TokensUpsert {
	u.Set(tokens.FieldTokenType, v)
	return u
}

// UpdateTokenType sets the "token_type" field to the value that was provided on create.
func (u *TokensUpsert) UpdateTokenType() *TokensUpsert {
	u.SetExcluded(tokens.FieldTokenType)
	return u
}

// SetDeleted sets the "deleted" field.
func (u *TokensUpsert) SetDeleted(v bool) *TokensUpsert {
	u.Set(tokens.FieldDeleted, v)
	return u
}

// UpdateDeleted sets the "deleted" field to the value that was provided on create.
func (u *TokensUpsert) UpdateDeleted() *TokensUpsert {
	u.SetExcluded(tokens.FieldDeleted)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Tokens.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tokens.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TokensUpsertOne) UpdateNewValues() *TokensUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(tokens.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(tokens.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(tokens.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tokens.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TokensUpsertOne) Ignore() *TokensUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TokensUpsertOne) DoNothing() *TokensUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TokensCreate.OnConflict
// documentation for more info.
func (u *TokensUpsertOne) Update(set func(*TokensUpsert)) *TokensUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TokensUpsert{UpdateSet: update})
	}))
	return u
}

// SetRequestID sets the "request_id" field.
func (u *TokensUpsertOne) SetRequestID(v string) *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.SetRequestID(v)
	})
}

// UpdateRequestID sets the "request_id" field to the value that was provided on create.
func (u *TokensUpsertOne) UpdateRequestID() *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateRequestID()
	})
}

// SetExpiresIn sets the "expires_in" field.
func (u *TokensUpsertOne) SetExpiresIn(v time.Time) *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.SetExpiresIn(v)
	})
}

// UpdateExpiresIn sets the "expires_in" field to the value that was provided on create.
func (u *TokensUpsertOne) UpdateExpiresIn() *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateExpiresIn()
	})
}

// SetToken sets the "token" field.
func (u *TokensUpsertOne) SetToken(v string) *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.SetToken(v)
	})
}

// UpdateToken sets the "token" field to the value that was provided on create.
func (u *TokensUpsertOne) UpdateToken() *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateToken()
	})
}

// SetTokenType sets the "token_type" field.
func (u *TokensUpsertOne) SetTokenType(v string) *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.SetTokenType(v)
	})
}

// UpdateTokenType sets the "token_type" field to the value that was provided on create.
func (u *TokensUpsertOne) UpdateTokenType() *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateTokenType()
	})
}

// SetDeleted sets the "deleted" field.
func (u *TokensUpsertOne) SetDeleted(v bool) *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.SetDeleted(v)
	})
}

// UpdateDeleted sets the "deleted" field to the value that was provided on create.
func (u *TokensUpsertOne) UpdateDeleted() *TokensUpsertOne {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateDeleted()
	})
}

// Exec executes the query.
func (u *TokensUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TokensCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TokensUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TokensUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: TokensUpsertOne.ID is not supported by MySQL driver. Use TokensUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TokensUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TokensCreateBulk is the builder for creating many Tokens entities in bulk.
type TokensCreateBulk struct {
	config
	err      error
	builders []*TokensCreate
	conflict []sql.ConflictOption
}

// Save creates the Tokens entities in the database.
func (_c *TokensCreateBulk) Save(ctx context.Context) ([]*Tokens, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Tokens, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TokensMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *TokensCreateBulk) SaveX(ctx context.Context) []*Tokens {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *TokensCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *TokensCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tokens.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TokensUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *TokensCreateBulk) OnConflict(opts ...sql.ConflictOption) *TokensUpsertBulk {
	_c.conflict = opts
	return &TokensUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tokens.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *TokensCreateBulk) OnConflictColumns(columns ...string) *TokensUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &TokensUpsertBulk{
		create: _c,
	}
}

// TokensUpsertBulk is the builder for "upsert"-ing
// a bulk of Tokens nodes.
type TokensUpsertBulk struct {
	create *TokensCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Tokens.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tokens.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TokensUpsertBulk) UpdateNewValues() *TokensUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(tokens.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(tokens.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(tokens.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tokens.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TokensUpsertBulk) Ignore() *TokensUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TokensUpsertBulk) DoNothing() *TokensUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TokensCreateBulk.OnConflict
// documentation for more info.
func (u *TokensUpsertBulk) Update(set func(*TokensUpsert)) *TokensUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TokensUpsert{UpdateSet: update})
	}))
	return u
}

// SetRequestID sets the "request_id" field.
func (u *TokensUpsertBulk) SetRequestID(v string) *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.SetRequestID(v)
	})
}

// UpdateRequestID sets the "request_id" field to the value that was provided on create.
func (u *TokensUpsertBulk) UpdateRequestID() *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateRequestID()
	})
}

// SetExpiresIn sets the "expires_in" field.
func (u *TokensUpsertBulk) SetExpiresIn(v time.Time) *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.SetExpiresIn(v)
	})
}

// UpdateExpiresIn sets the "expires_in" field to the value that was provided on create.
func (u *TokensUpsertBulk) UpdateExpiresIn() *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateExpiresIn()
	})
}

// SetToken sets the "token" field.
func (u *TokensUpsertBulk) SetToken(v string) *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.SetToken(v)
	})
}

// UpdateToken sets the "token" field to the value that was provided on create.
func (u *TokensUpsertBulk) UpdateToken() *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateToken()
	})
}

// SetTokenType sets the "token_type" field.
func (u *TokensUpsertBulk) SetTokenType(v string) *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.SetTokenType(v)
	})
}

// UpdateTokenType sets the "token_type" field to the value that was provided on create.
func (u *TokensUpsertBulk) UpdateTokenType() *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateTokenType()
	})
}

// SetDeleted sets the "deleted" field.
func (u *TokensUpsertBulk) SetDeleted(v bool) *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.SetDeleted(v)
	})
}

// UpdateDeleted sets the "deleted" field to the value that was provided on create.
func (u *TokensUpsertBulk) UpdateDeleted() *TokensUpsertBulk {
	return u.Update(func(s *TokensUpsert) {
		s.UpdateDeleted()
	})
}

// Exec executes the query.
func (u *TokensUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the TokensCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TokensCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TokensUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
