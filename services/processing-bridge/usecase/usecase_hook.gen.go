// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/usecase -i ProcessingBridge -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ ProcessingBridge = (*ProcessingBridgeHook)(nil)

// ProcessingBridgeHook implements ProcessingBridge interface wrapper
type ProcessingBridgeHook struct {
	ProcessingBridge
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// CreateClientAccountAndCard implements ProcessingBridge
func (_w *ProcessingBridgeHook) CreateClientAccountAndCard(ctx context.Context, req *entity.CreateClientAccountAndCardReq) (cp1 *entity.CreateClientAccountAndCardResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "CreateClientAccountAndCard", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "CreateClientAccountAndCard", _params)

	cp1, err = _w.ProcessingBridge.CreateClientAccountAndCard(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "CreateClientAccountAndCard", []any{cp1, err})
	return cp1, err
}

// CreatePaymentCreditAuth implements ProcessingBridge
func (_w *ProcessingBridgeHook) CreatePaymentCreditAuth(ctx context.Context, req *entity.CreatePaymentCreditAuthReq) (cp1 *entity.CreatePaymentCreditAuthResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "CreatePaymentCreditAuth", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "CreatePaymentCreditAuth", _params)

	cp1, err = _w.ProcessingBridge.CreatePaymentCreditAuth(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "CreatePaymentCreditAuth", []any{cp1, err})
	return cp1, err
}

// CreatePaymentDebitAuth implements ProcessingBridge
func (_w *ProcessingBridgeHook) CreatePaymentDebitAuth(ctx context.Context, req *entity.CreatePaymentDebitAuthReq) (cp1 *entity.CreatePaymentDebitAuthResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "CreatePaymentDebitAuth", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "CreatePaymentDebitAuth", _params)

	cp1, err = _w.ProcessingBridge.CreatePaymentDebitAuth(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "CreatePaymentDebitAuth", []any{cp1, err})
	return cp1, err
}

// GetCardAndFinContractStatus implements ProcessingBridge
func (_w *ProcessingBridgeHook) GetCardAndFinContractStatus(ctx context.Context, req *entity.GetCardAndFinContractStatusReq) (gp1 *entity.GetCardAndFinContractStatusResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "GetCardAndFinContractStatus", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "GetCardAndFinContractStatus", _params)

	gp1, err = _w.ProcessingBridge.GetCardAndFinContractStatus(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "GetCardAndFinContractStatus", []any{gp1, err})
	return gp1, err
}

// GetPaymentFinDoc implements ProcessingBridge
func (_w *ProcessingBridgeHook) GetPaymentFinDoc(ctx context.Context, req *entity.GetPaymentFinDocReq) (gp1 *entity.GetPaymentFinDocResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "GetPaymentFinDoc", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "GetPaymentFinDoc", _params)

	gp1, err = _w.ProcessingBridge.GetPaymentFinDoc(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "GetPaymentFinDoc", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements ProcessingBridge
func (_w *ProcessingBridgeHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "HealthCheck", _params)

	hp1, err = _w.ProcessingBridge.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements ProcessingBridge
func (_w *ProcessingBridgeHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "HealthEvent", _params)

	_w.ProcessingBridge.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "HealthEvent", []any{})
	return
}

// InitConsumer implements ProcessingBridge
func (_w *ProcessingBridgeHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "InitConsumer", _params)

	_w.ProcessingBridge.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "InitConsumer", []any{})
	return
}

// PaymentCredit implements ProcessingBridge
func (_w *ProcessingBridgeHook) PaymentCredit(ctx context.Context, req *entity.PaymentCreditReq) (pp1 *entity.PaymentCreditResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "PaymentCredit", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "PaymentCredit", _params)

	pp1, err = _w.ProcessingBridge.PaymentCredit(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "PaymentCredit", []any{pp1, err})
	return pp1, err
}

// PaymentCreditPresent implements ProcessingBridge
func (_w *ProcessingBridgeHook) PaymentCreditPresent(ctx context.Context, req *entity.PaymentCreditPresentReq) (pp1 *entity.PaymentCreditPresentResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "PaymentCreditPresent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "PaymentCreditPresent", _params)

	pp1, err = _w.ProcessingBridge.PaymentCreditPresent(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "PaymentCreditPresent", []any{pp1, err})
	return pp1, err
}

// PaymentCreditReverse implements ProcessingBridge
func (_w *ProcessingBridgeHook) PaymentCreditReverse(ctx context.Context, req *entity.PaymentCreditReverseReq) (pp1 *entity.PaymentCreditReverseResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "PaymentCreditReverse", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "PaymentCreditReverse", _params)

	pp1, err = _w.ProcessingBridge.PaymentCreditReverse(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "PaymentCreditReverse", []any{pp1, err})
	return pp1, err
}

// PaymentDebit implements ProcessingBridge
func (_w *ProcessingBridgeHook) PaymentDebit(ctx context.Context, req *entity.PaymentDebitReq) (pp1 *entity.PaymentDebitResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "PaymentDebit", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "PaymentDebit", _params)

	pp1, err = _w.ProcessingBridge.PaymentDebit(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "PaymentDebit", []any{pp1, err})
	return pp1, err
}

// PaymentDebitPresent implements ProcessingBridge
func (_w *ProcessingBridgeHook) PaymentDebitPresent(ctx context.Context, req *entity.PaymentDebitPresentReq) (pp1 *entity.PaymentDebitPresentResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "PaymentDebitPresent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "PaymentDebitPresent", _params)

	pp1, err = _w.ProcessingBridge.PaymentDebitPresent(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "PaymentDebitPresent", []any{pp1, err})
	return pp1, err
}

// PaymentDebitReverse implements ProcessingBridge
func (_w *ProcessingBridgeHook) PaymentDebitReverse(ctx context.Context, req *entity.PaymentDebitReverseReq) (pp1 *entity.PaymentDebitReverseResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ProcessingBridge, "PaymentDebitReverse", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ProcessingBridge, "PaymentDebitReverse", _params)

	pp1, err = _w.ProcessingBridge.PaymentDebitReverse(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ProcessingBridge, "PaymentDebitReverse", []any{pp1, err})
	return pp1, err
}

// NewProcessingBridgeHook returns ProcessingBridgeHook
func NewProcessingBridgeHook(object ProcessingBridge, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *ProcessingBridgeHook {
	return &ProcessingBridgeHook{
		ProcessingBridge: object,
		_beforeCall:      beforeCall,
		_postCall:        postCall,
		_onPanic:         onPanic,
	}
}
