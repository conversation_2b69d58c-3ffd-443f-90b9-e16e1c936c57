// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package usecase

import (
	"context"

	processingbridge "git.redmadrobot.com/zaman/backend/zaman/config/services/processing-bridge"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/bus"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/backend-go/rmr-pkg/proxy/hooks"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/processing-bridge/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

var _ ProcessingBridge = (*useCasesImpl)(nil)

type ProcessingBridge interface {
	HealthCheck(ctx context.Context) (*entity.Health, error)
	HealthEvent(ctx context.Context, message *kafka.Message)
	InitConsumer(ctx context.Context)
	CreateClientAccountAndCard(ctx context.Context, req *entity.CreateClientAccountAndCardReq) (*entity.CreateClientAccountAndCardResult, error)
	CreatePaymentDebitAuth(ctx context.Context, req *entity.CreatePaymentDebitAuthReq) (*entity.CreatePaymentDebitAuthResult, error)
	CreatePaymentCreditAuth(ctx context.Context, req *entity.CreatePaymentCreditAuthReq) (*entity.CreatePaymentCreditAuthResult, error)
	GetPaymentFinDoc(ctx context.Context, req *entity.GetPaymentFinDocReq) (*entity.GetPaymentFinDocResult, error)
	PaymentDebitPresent(ctx context.Context, req *entity.PaymentDebitPresentReq) (*entity.PaymentDebitPresentResult, error)
	PaymentCreditPresent(ctx context.Context, req *entity.PaymentCreditPresentReq) (*entity.PaymentCreditPresentResult, error)
	PaymentCredit(ctx context.Context, req *entity.PaymentCreditReq) (*entity.PaymentCreditResult, error)
	PaymentDebit(ctx context.Context, req *entity.PaymentDebitReq) (*entity.PaymentDebitResult, error)
	PaymentCreditReverse(ctx context.Context, req *entity.PaymentCreditReverseReq) (*entity.PaymentCreditReverseResult, error)
	PaymentDebitReverse(ctx context.Context, req *entity.PaymentDebitReverseReq) (*entity.PaymentDebitReverseResult, error)
	GetCardAndFinContractStatus(ctx context.Context, req *entity.GetCardAndFinContractStatusReq) (*entity.GetCardAndFinContractStatusResult, error)
}

type useCasesImpl struct {
	ProcessingBridge
	cfg       *processingbridge.Config
	Providers providers.ServiceLocatorImpl
}

func New(ctx context.Context, locator providers.ServiceLocatorImpl, cfg *processingbridge.Config) *ProcessingBridgeHook {
	useCases := &useCasesImpl{
		cfg:       cfg,
		Providers: locator,
	}

	logger := logs.FromContext(ctx)

	hook := NewProcessingBridgeHook(
		useCases,
		hooks.GrpcServiceLogBeforeCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPostCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPanic(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
	)
	useCases.ProcessingBridge = hook

	return hook
}
func (u *useCasesImpl) InitConsumer(ctx context.Context) {
	register := func(ctx context.Context, busName string, handler func(context.Context, *kafka.Message)) error {
		subscriber := bus.SubscriberFn[*kafka.Message](func(ctx context.Context, messages ...*kafka.Message) error {
			for _, message := range messages {
				handler(ctx, message)
				message.Ack(ctx)
			}
			return nil
		})

		err := u.Providers.Event.Subscribe(ctx, busName, subscriber)
		if err != nil {
			return err
		}
		return nil
	}

	eventRegistry := u.EventRegistry()
	for busName, handler := range eventRegistry {
		err := register(ctx, busName, handler)
		if err != nil {
			logs.FromContext(ctx).Err(err).Msg("unable to register handler")
		}
	}
}
