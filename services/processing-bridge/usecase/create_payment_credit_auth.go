package usecase

import (
	"context"
	"fmt"

	silkpay_entity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/mongo"
)

func (u *useCasesImpl) CreatePaymentCreditAuth(ctx context.Context, req *entity.CreatePaymentCreditAuthReq) (*entity.CreatePaymentCreditAuthResult, error) {
	token, err := u.requestSilkpayToken(ctx)
	if err != nil {
		return nil, err
	}

	rawData := utils.NewRawData[*entity.CreatePaymentCreditAuthReq,
		silkpay_entity.CreatePaymentCreditAuthResp, any](req).
		WithRequestID(utils.ExtractRequestID(ctx))
	resp, errSilkpay := u.Providers.SilkpayProvider.CreatePaymentCreditAuth(ctx, silkpay_entity.CreatePaymentCreditAuthReq{
		OneOf: silkpay_entity.OneOf{
			ContractRid: req.ContractRid,
			Iban:        req.Iban,
		},
		Amount:       req.Amount,
		Currency:     req.Currency,
		Rrn:          req.Rrn,
		ExternalID:   req.ExternalID,
		CategoryText: req.CategoryText,
		Token:        token,
	})

	rawData.WithTimeStamp().WithResponse(resp).WithError(err)

	if err = u.Providers.Storage.SaveRawData(ctx, &rawData, mongo.CollectionSilkpayCreatePaymentCreditAuth); err != nil {
		return nil, fmt.Errorf("silkpay mongo save raw data err: %w", err)
	}

	if errSilkpay != nil {
		return nil, errSilkpay
	}

	return &entity.CreatePaymentCreditAuthResult{TransactionID: resp.TransactionID}, nil
}
