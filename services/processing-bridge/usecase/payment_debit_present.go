package usecase

import (
	"context"
	"fmt"

	silkpay_entity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/mongo"
)

func (u *useCasesImpl) PaymentDebitPresent(ctx context.Context, req *entity.PaymentDebitPresentReq) (*entity.PaymentDebitPresentResult, error) {
	token, err := u.requestSilkpayToken(ctx)
	if err != nil {
		return nil, err
	}

	rawData := utils.NewRawData[*entity.PaymentDebitPresentReq,
		silkpay_entity.PaymentDebitPresentResp, any](req).
		WithRequestID(utils.ExtractRequestID(ctx))
	resp, errSilkpay := u.Providers.SilkpayProvider.PaymentDebitPresent(ctx, silkpay_entity.PaymentDebitPresentReq{
		OneOf: silkpay_entity.OneOf{
			ContractRid: req.ContractRid,
			Iban:        req.Iban,
		},
		Amount:         req.Amount,
		Currency:       req.Currency,
		Rrn:            req.Rrn,
		AuthExternalID: req.AuthExternalID,
		CategoryText:   req.CategoryText,
		Token:          token,
	})

	rawData.WithTimeStamp().WithResponse(resp).WithError(err)

	if err = u.Providers.Storage.SaveRawData(ctx, &rawData, mongo.CollectionSilkpayPaymentDebitPresent); err != nil {
		return nil, fmt.Errorf("silkpay mongo save raw data err: %w", err)
	}

	if errSilkpay != nil {
		return nil, errSilkpay
	}

	return &entity.PaymentDebitPresentResult{TransactionID: resp.TransactionID}, nil
}
