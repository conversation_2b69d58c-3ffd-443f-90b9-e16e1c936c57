package usecase

import (
	"context"
	"fmt"

	silkpay_entity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/mongo"
)

func (u *useCasesImpl) GetPaymentFinDoc(ctx context.Context, req *entity.GetPaymentFinDocReq) (*entity.GetPaymentFinDocResult, error) {
	token, err := u.requestSilkpayToken(ctx)
	if err != nil {
		return nil, err
	}

	rawData := utils.NewRawData[*entity.GetPaymentFinDocReq,
		silkpay_entity.GetPaymentFinDocResp, any](req).
		WithRequestID(utils.ExtractRequestID(ctx))

	resp, silkpayErr := u.Providers.SilkpayProvider.GetPaymentFinDoc(ctx, silkpay_entity.GetPaymentFinDocReq{
		ExternalID:          req.ExternalID,
		PartyIban:           req.PartyIban,
		PartyFinContractRid: req.PartyFinContractRid,
		DatetimeFrom:        req.DatetimeFrom,
		DatetimeTo:          req.DatetimeTo,
		Token:               token,
	})

	rawData.WithTimeStamp().WithResponse(resp).WithError(silkpayErr)
	if err = u.Providers.Storage.SaveRawData(ctx, rawData, mongo.CollectionSilkpayPaymentFinDoc); err != nil {
		return nil, fmt.Errorf("silkpay mongo save raw data err: %w", err)
	}

	if silkpayErr != nil {
		return nil, silkpayErr
	}

	return &entity.GetPaymentFinDocResult{TransactionID: resp.TransactionID}, nil
}
