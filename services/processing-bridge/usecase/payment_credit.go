package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	silkpay_entity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/mongo"
)

func (u *useCasesImpl) PaymentCredit(ctx context.Context, req *entity.PaymentCreditReq) (*entity.PaymentCreditResult, error) {
	requestID := utils.ExtractRequestID(ctx)

	// Расширенное логирование входящих данных
	logger := logs.FromContext(ctx)
	logger.Info().
		Str("correlation_id", requestID).
		Str("external_id", req.ExternalID).
		Str("rrn", req.Rrn).
		Str("amount", req.Amount).
		Str("currency", req.Currency).
		Interface("iban", req.Iban).
		Interface("contract_rid", req.ContractRid).
		Bool("is_reversal", req.IsReversal != nil && *req.IsReversal).
		Msg("PROCESSING-BRIDGE PaymentCredit - Processing request")

	token, err := u.requestSilkpayToken(ctx)
	if err != nil {
		logger.Error().Err(err).
			Str("correlation_id", requestID).
			Msg("PROCESSING-BRIDGE PaymentCredit - Failed to get Silkpay token")
		return nil, err
	}

	logger.Info().
		Str("correlation_id", requestID).
		Str("token_length", fmt.Sprintf("%d", len(token))).
		Msg("PROCESSING-BRIDGE PaymentCredit - Got Silkpay token")

	rawData := utils.NewRawData[*entity.PaymentCreditReq,
		any, any](req).
		WithRequestID(requestID)

	silkpayReq := silkpay_entity.PaymentCreditReq{
		OneOf: silkpay_entity.OneOf{
			ContractRid: req.ContractRid,
			Iban:        req.Iban,
		},
		Amount:       req.Amount,
		Currency:     req.Currency,
		Rrn:          req.Rrn,
		IsReversal:   req.IsReversal,
		CategoryText: req.CategoryText,
		ExternalID:   req.ExternalID,
		Token:        token,
	}

	logger.Info().
		Str("correlation_id", requestID).
		Interface("silkpay_request", silkpayReq).
		Msg("PROCESSING-BRIDGE PaymentCredit - Calling Silkpay")

	errSilkpay := u.Providers.SilkpayProvider.PaymentCredit(ctx, silkpayReq)

	rawData.WithTimeStamp().WithError(errSilkpay)

	if err = u.Providers.Storage.SaveRawData(ctx, &rawData, mongo.CollectionSilkpayPaymentCredit); err != nil {
		logger.Error().Err(err).
			Str("correlation_id", requestID).
			Msg("PROCESSING-BRIDGE PaymentCredit - Failed to save raw data to MongoDB")
		return nil, fmt.Errorf("silkpay mongo save raw data err: %w", err)
	}

	if errSilkpay != nil {
		logger.Error().Err(errSilkpay).
			Str("correlation_id", requestID).
			Str("external_id", req.ExternalID).
			Str("rrn", req.Rrn).
			Msg("PROCESSING-BRIDGE PaymentCredit - Silkpay returned error")
		return nil, errSilkpay
	}

	logger.Info().
		Str("correlation_id", requestID).
		Str("external_id", req.ExternalID).
		Str("rrn", req.Rrn).
		Msg("PROCESSING-BRIDGE PaymentCredit - Success")

	return &entity.PaymentCreditResult{}, nil
}
