package usecase

import (
	"context"
	"fmt"

	silkpay_entity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/mongo"
)

func (u *useCasesImpl) CreatePaymentDebitAuth(ctx context.Context, req *entity.CreatePaymentDebitAuthReq) (*entity.CreatePaymentDebitAuthResult, error) {
	token, err := u.requestSilkpayToken(ctx)
	if err != nil {
		return nil, err
	}

	rawData := utils.NewRawData[*entity.CreatePaymentDebitAuthReq,
		silkpay_entity.CreatePaymentDebitAuthResp, any](req).
		WithRequestID(utils.ExtractRequestID(ctx))
	resp, silkpayErr := u.Providers.SilkpayProvider.CreatePaymentDebitAuth(ctx, silkpay_entity.CreatePaymentDebitAuthReq{
		OneOf: silkpay_entity.OneOf{
			ContractRid: req.ContractRid,
			Iban:        req.Iban,
		},
		Amount:       req.Amount,
		Currency:     req.Currency,
		Rrn:          req.Rrn,
		ExternalID:   req.ExternalID,
		CategoryText: req.CategoryText,
		Token:        token,
	})

	rawData.WithTimeStamp().WithResponse(resp).WithError(silkpayErr)
	if err = u.Providers.Storage.SaveRawData(ctx, rawData, mongo.CollectionSilkpayCreatePaymentDebitAuth); err != nil {
		return nil, fmt.Errorf("silkpay mongo save raw data err: %w", err)
	}

	if silkpayErr != nil {
		return nil, silkpayErr
	}

	return &entity.CreatePaymentDebitAuthResult{TransactionID: resp.TransactionID}, nil
}
