package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"
	globalUtils "git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/mongo"
)

func (u *useCasesImpl) requestSilkpayToken(ctx context.Context) (string, error) {
	tokenInfo, err := u.Providers.Storage.GetToken(ctx)
	if err != nil {
		return "", err
	}

	// если токен не нашелся или уже просрочен - сначала запросим новый
	if tokenInfo == nil || time.Now().After(tokenInfo.ExpiresIn) {
		userID, err := globalUtils.GetUserIDFromContext(ctx)
		if err != nil {
			logs.FromContext(ctx).Warn().Msg("not found userID in context")
		}

		if u.cfg.App.SilkPayCfg.AuthURL == "" {
			logs.FromContext(ctx).Warn().Msg("silkpay auth URL is not configured")
		}

		if u.cfg.App.SilkPayCfg.BaseURL == "" {
			logs.FromContext(ctx).Warn().Msg("silkpay base URL is not configured")
		}

		if u.cfg.App.SilkPayCfg.EncodedCredentials == "" {
			logs.FromContext(ctx).Warn().Msg("silkpay encoded credentials are not configured")
		}

		// создаем rawData объект для сохранения сырых данных в монго
		rawData := globalUtils.NewRawData[string, silkpay.AuthenticateResponse, any]("").WithUserID(userID)
		authResp, authErr := u.Providers.SilkpayProvider.Authenticate(ctx)
		rawData.WithTimeStamp().WithRequestID(globalUtils.ExtractRequestID(ctx)).WithResponse(authResp).WithError(authErr)

		// сохраняем результат запроса в mongo
		if err = u.Providers.Storage.SaveRawData(ctx, rawData, mongo.CollectionSilkpayAuthenticate); err != nil {
			return "", fmt.Errorf("silkpay mongo save raw data err: %w", err)
		}

		// проверяем наличие ошибки и валидность токена
		if authErr != nil {
			return "", authErr
		}

		if authResp == nil || authResp.AccessToken == "" {
			return "", errors.New("silkpay authenticate err: empty access token")
		}

		// сохраняем свежий токен в бд для дальнейшего использования
		expirationDuration := time.Duration(authResp.ExpiresIn) * time.Second

		if err = u.Providers.Storage.ReSaveToken(ctx, entity.SaveTokenParams{
			AccessToken: authResp.AccessToken,
			TokenType:   authResp.TokenType,
			RequestID:   globalUtils.ExtractRequestID(ctx),
			ExpiresIn:   time.Now().Add(expirationDuration),
		}); err != nil {
			return "", err
		}

		return authResp.AccessToken, nil
	}

	return tokenInfo.AccessToken, nil
}
