package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	globalUtils "git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/mongo"

	silkpay "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
)

// GetCardAndFinContractStatus запрос на получение статуса карт и фин контрактов вход параметры массив идентификаторов карт и фин контрактов и идентификатор клиента
func (u *useCasesImpl) GetCardAndFinContractStatus(ctx context.Context, req *entity.GetCardAndFinContractStatusReq) (*entity.GetCardAndFinContractStatusResult, error) {
	token, err := u.requestSilkpayToken(ctx)
	if err != nil {
		return nil, err
	}
	userID, err := globalUtils.GetUserIDFromContext(ctx)
	if err != nil {
		logs.FromContext(ctx).Warn().Msg("not found userID in context")
	}

	requestID := globalUtils.ExtractRequestID(ctx)
	silkPayReq := entity.MapGetCardAndFinContractStatusEntityToRequest(req, requestID, token)

	rawData := globalUtils.NewRawData[silkpay.GetCardAndFinContractStatusReq, silkpay.GetCardAndFinContractStatusResponse, any](*silkPayReq).WithUserID(userID)

	resp, silkpayErr := u.Providers.SilkpayProvider.GetFinContractStatusByClientRID(ctx, *silkPayReq)

	rawData.WithTimeStamp().WithResponse(resp).WithRequestID(globalUtils.ExtractRequestID(ctx)).WithError(silkpayErr)
	if err = u.Providers.Storage.SaveRawData(ctx, rawData, mongo.CollectionSilkpayFinContractStatus); err != nil {
		return nil, fmt.Errorf("silkpay mongo save raw data err: %w", err)
	}

	if silkpayErr != nil {
		return nil, silkpayErr
	}

	return entity.MapSilkpayGetCardAndFinContractStatusRespToEntity(resp), nil
}
