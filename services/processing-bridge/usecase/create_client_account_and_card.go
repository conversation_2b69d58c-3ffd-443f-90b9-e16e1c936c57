package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/generic"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"
	globalUtils "git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/storage/mongo"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

const (
	defaultRequestRetries = 3
)

func (u *useCasesImpl) CreateClientAccountAndCard(ctx context.Context, req *entity.CreateClientAccountAndCardReq) (*entity.CreateClientAccountAndCardResult, error) {
	token, err := u.requestSilkpayToken(ctx)
	if err != nil {
		return nil, err
	}

	silkPayReq := entity.MapCreateClientEntityToRequest(req, token)
	result, err := u.runCreateCreateClientAccountAndCardRequest(ctx, silkPayReq)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (u *useCasesImpl) runCreateCreateClientAccountAndCardRequest(ctx context.Context, req silkpay.CreateClientAccountAndCardRequest) (*entity.CreateClientAccountAndCardResult, error) {
	retryParams := generic.ParamsRetryWithBackoff[entity.CreateClientAccountAndCardResult]{
		MaxRetries: defaultRequestRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: checkRetryNecessity,
		Operation: func() (*entity.CreateClientAccountAndCardResult, error) {
			userID, err := globalUtils.GetUserIDFromContext(ctx)
			if err != nil {
				logs.FromContext(ctx).Warn().Msg("not found userID in context")
			}

			rawData := globalUtils.NewRawData[silkpay.CreateClientAccountAndCardRequest, silkpay.CreateClientAccountAndCardResponse, any](req).WithUserID(userID)

			resp, silkpayErr := u.Providers.SilkpayProvider.CreateClientAccountAndCard(ctx, req)
			rawData.WithTimeStamp().WithResponse(resp).WithRequestID(globalUtils.ExtractRequestID(ctx)).WithError(silkpayErr)

			if err = u.Providers.Storage.SaveRawData(ctx, rawData, mongo.CollectionSilkpayCreateClientAccountAndCard); err != nil {
				return nil, fmt.Errorf("silkpay mongo save raw data err: %w", err)
			}

			if silkpayErr != nil {
				return nil, silkpayErr
			}

			return entity.MapSilkpayCreateClientAndCardRespToEntity(resp), nil
		},
	}

	return generic.RetryWithBackoff(retryParams)
}

func checkRetryNecessity(err error) bool {
	return err != nil
}
