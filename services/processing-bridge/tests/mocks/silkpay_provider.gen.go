// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay (interfaces: SilkpayProvider)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	silkpay "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"
	entity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
)

// MockSilkpayProvider is a mock of SilkpayProvider interface.
type MockSilkpayProvider struct {
	ctrl     *gomock.Controller
	recorder *MockSilkpayProviderMockRecorder
}

// MockSilkpayProviderMockRecorder is the mock recorder for MockSilkpayProvider.
type MockSilkpayProviderMockRecorder struct {
	mock *MockSilkpayProvider
}

// NewMockSilkpayProvider creates a new mock instance.
func NewMockSilkpayProvider(ctrl *gomock.Controller) *MockSilkpayProvider {
	mock := &MockSilkpayProvider{ctrl: ctrl}
	mock.recorder = &MockSilkpayProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSilkpayProvider) EXPECT() *MockSilkpayProviderMockRecorder {
	return m.recorder
}

// Authenticate mocks base method.
func (m *MockSilkpayProvider) Authenticate(arg0 context.Context) (*silkpay.AuthenticateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Authenticate", arg0)
	ret0, _ := ret[0].(*silkpay.AuthenticateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Authenticate indicates an expected call of Authenticate.
func (mr *MockSilkpayProviderMockRecorder) Authenticate(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authenticate", reflect.TypeOf((*MockSilkpayProvider)(nil).Authenticate), arg0)
}

// CreateClientAccountAndCard mocks base method.
func (m *MockSilkpayProvider) CreateClientAccountAndCard(arg0 context.Context, arg1 silkpay.CreateClientAccountAndCardRequest) (*silkpay.CreateClientAccountAndCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateClientAccountAndCard", arg0, arg1)
	ret0, _ := ret[0].(*silkpay.CreateClientAccountAndCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClientAccountAndCard indicates an expected call of CreateClientAccountAndCard.
func (mr *MockSilkpayProviderMockRecorder) CreateClientAccountAndCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClientAccountAndCard", reflect.TypeOf((*MockSilkpayProvider)(nil).CreateClientAccountAndCard), arg0, arg1)
}

// CreatePaymentCreditAuth mocks base method.
func (m *MockSilkpayProvider) CreatePaymentCreditAuth(arg0 context.Context, arg1 entity.CreatePaymentCreditAuthReq) (*entity.CreatePaymentCreditAuthResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePaymentCreditAuth", arg0, arg1)
	ret0, _ := ret[0].(*entity.CreatePaymentCreditAuthResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentCreditAuth indicates an expected call of CreatePaymentCreditAuth.
func (mr *MockSilkpayProviderMockRecorder) CreatePaymentCreditAuth(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentCreditAuth", reflect.TypeOf((*MockSilkpayProvider)(nil).CreatePaymentCreditAuth), arg0, arg1)
}

// CreatePaymentDebitAuth mocks base method.
func (m *MockSilkpayProvider) CreatePaymentDebitAuth(arg0 context.Context, arg1 entity.CreatePaymentDebitAuthReq) (*entity.CreatePaymentDebitAuthResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePaymentDebitAuth", arg0, arg1)
	ret0, _ := ret[0].(*entity.CreatePaymentDebitAuthResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentDebitAuth indicates an expected call of CreatePaymentDebitAuth.
func (mr *MockSilkpayProviderMockRecorder) CreatePaymentDebitAuth(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentDebitAuth", reflect.TypeOf((*MockSilkpayProvider)(nil).CreatePaymentDebitAuth), arg0, arg1)
}

// GetFinContractStatusByClientRID mocks base method.
func (m *MockSilkpayProvider) GetFinContractStatusByClientRID(arg0 context.Context, arg1 entity.GetCardAndFinContractStatusReq) (*entity.GetCardAndFinContractStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFinContractStatusByClientRID", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetCardAndFinContractStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFinContractStatusByClientRID indicates an expected call of GetFinContractStatusByClientRID.
func (mr *MockSilkpayProviderMockRecorder) GetFinContractStatusByClientRID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFinContractStatusByClientRID", reflect.TypeOf((*MockSilkpayProvider)(nil).GetFinContractStatusByClientRID), arg0, arg1)
}

// GetPaymentFinDoc mocks base method.
func (m *MockSilkpayProvider) GetPaymentFinDoc(arg0 context.Context, arg1 entity.GetPaymentFinDocReq) (*entity.GetPaymentFinDocResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentFinDoc", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetPaymentFinDocResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentFinDoc indicates an expected call of GetPaymentFinDoc.
func (mr *MockSilkpayProviderMockRecorder) GetPaymentFinDoc(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentFinDoc", reflect.TypeOf((*MockSilkpayProvider)(nil).GetPaymentFinDoc), arg0, arg1)
}

// PaymentCredit mocks base method.
func (m *MockSilkpayProvider) PaymentCredit(arg0 context.Context, arg1 entity.PaymentCreditReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentCredit", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PaymentCredit indicates an expected call of PaymentCredit.
func (mr *MockSilkpayProviderMockRecorder) PaymentCredit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentCredit", reflect.TypeOf((*MockSilkpayProvider)(nil).PaymentCredit), arg0, arg1)
}

// PaymentCreditPresent mocks base method.
func (m *MockSilkpayProvider) PaymentCreditPresent(arg0 context.Context, arg1 entity.PaymentCreditPresentReq) (*entity.PaymentCreditPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentCreditPresent", arg0, arg1)
	ret0, _ := ret[0].(*entity.PaymentCreditPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentCreditPresent indicates an expected call of PaymentCreditPresent.
func (mr *MockSilkpayProviderMockRecorder) PaymentCreditPresent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentCreditPresent", reflect.TypeOf((*MockSilkpayProvider)(nil).PaymentCreditPresent), arg0, arg1)
}

// PaymentCreditReverse mocks base method.
func (m *MockSilkpayProvider) PaymentCreditReverse(arg0 context.Context, arg1 entity.PaymentCreditReverseReq) (*entity.PaymentCreditReverseResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentCreditReverse", arg0, arg1)
	ret0, _ := ret[0].(*entity.PaymentCreditReverseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentCreditReverse indicates an expected call of PaymentCreditReverse.
func (mr *MockSilkpayProviderMockRecorder) PaymentCreditReverse(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentCreditReverse", reflect.TypeOf((*MockSilkpayProvider)(nil).PaymentCreditReverse), arg0, arg1)
}

// PaymentDebit mocks base method.
func (m *MockSilkpayProvider) PaymentDebit(arg0 context.Context, arg1 entity.PaymentDebitReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentDebit", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PaymentDebit indicates an expected call of PaymentDebit.
func (mr *MockSilkpayProviderMockRecorder) PaymentDebit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentDebit", reflect.TypeOf((*MockSilkpayProvider)(nil).PaymentDebit), arg0, arg1)
}

// PaymentDebitPresent mocks base method.
func (m *MockSilkpayProvider) PaymentDebitPresent(arg0 context.Context, arg1 entity.PaymentDebitPresentReq) (*entity.PaymentDebitPresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentDebitPresent", arg0, arg1)
	ret0, _ := ret[0].(*entity.PaymentDebitPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentDebitPresent indicates an expected call of PaymentDebitPresent.
func (mr *MockSilkpayProviderMockRecorder) PaymentDebitPresent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentDebitPresent", reflect.TypeOf((*MockSilkpayProvider)(nil).PaymentDebitPresent), arg0, arg1)
}

// PaymentDebitReverse mocks base method.
func (m *MockSilkpayProvider) PaymentDebitReverse(arg0 context.Context, arg1 entity.PaymentDebitReverseReq) (*entity.PaymentDebitReverseResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaymentDebitReverse", arg0, arg1)
	ret0, _ := ret[0].(*entity.PaymentDebitReverseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentDebitReverse indicates an expected call of PaymentDebitReverse.
func (mr *MockSilkpayProviderMockRecorder) PaymentDebitReverse(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentDebitReverse", reflect.TypeOf((*MockSilkpayProvider)(nil).PaymentDebitReverse), arg0, arg1)
}
