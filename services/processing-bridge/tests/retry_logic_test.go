package tests

import (
	"context"
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func TestRetryConfig_NextInterval(t *testing.T) {
	tests := []struct {
		name     string
		config   *entity.RetryConfig
		attempt  int
		expected time.Duration
	}{
		{
			name:     "First attempt",
			config:   entity.DefaultRetryConfig(),
			attempt:  1,
			expected: 5 * time.Second,
		},
		{
			name:     "Second attempt - exponential backoff",
			config:   entity.DefaultRetryConfig(),
			attempt:  2,
			expected: 10 * time.Second,
		},
		{
			name:     "Third attempt - max interval reached",
			config:   entity.DefaultRetryConfig(),
			attempt:  3,
			expected: 20 * time.Second,
		},
		{
			name: "Custom config",
			config: &entity.RetryConfig{
				MaxAttempts:     2,
				InitialInterval: 1 * time.Second,
				MaxInterval:     3 * time.Second,
				Multiplier:      3.0,
			},
			attempt:  2,
			expected: 3 * time.Second,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.config.NextInterval(tt.attempt)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestRetryErrorClassifier_ClassifyError(t *testing.T) {
	classifier := entity.NewRetryErrorClassifier()

	tests := []struct {
		name           string
		err            error
		httpStatus     int
		responseBody   string
		expectedResult entity.RetryDecision
	}{
		{
			name:           "No error",
			err:            nil,
			httpStatus:     200,
			responseBody:   "",
			expectedResult: entity.RetryDecisionStop,
		},
		{
			name:           "409 Duplicate request - success",
			err:            errors.New("conflict error"),
			httpStatus:     http.StatusConflict,
			responseBody:   "duplicate request on transaction",
			expectedResult: entity.RetryDecisionStopSuccess,
		},
		{
			name:           "400 Validation failed - retry once",
			err:            errors.New("validation error"),
			httpStatus:     http.StatusBadRequest,
			responseBody:   "validation failed",
			expectedResult: entity.RetryDecisionRetry,
		},
		{
			name:           "400 Insufficient funds - stop",
			err:            errors.New("insufficient funds"),
			httpStatus:     http.StatusBadRequest,
			responseBody:   "insufficient funds",
			expectedResult: entity.RetryDecisionStop,
		},
		{
			name:           "400 Contract not found - stop",
			err:            errors.New("contract error"),
			httpStatus:     http.StatusBadRequest,
			responseBody:   "customer contract not found",
			expectedResult: entity.RetryDecisionStop,
		},
		{
			name:           "500 Internal server error - retry",
			err:            errors.New("server error"),
			httpStatus:     http.StatusInternalServerError,
			responseBody:   "internal server error",
			expectedResult: entity.RetryDecisionRetry,
		},
		{
			name:           "408 Timeout - retry",
			err:            errors.New("timeout"),
			httpStatus:     http.StatusRequestTimeout,
			responseBody:   "",
			expectedResult: entity.RetryDecisionRetry,
		},
		{
			name:           "Network error - retry",
			err:            errors.New("connection refused"),
			httpStatus:     0,
			responseBody:   "",
			expectedResult: entity.RetryDecisionRetry,
		},
		{
			name:           "Context timeout - retry",
			err:            errors.New("context deadline exceeded"),
			httpStatus:     0,
			responseBody:   "",
			expectedResult: entity.RetryDecisionRetry,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := classifier.ClassifyError(tt.err, tt.httpStatus, tt.responseBody)
			require.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestRetryErrorClassifier_ShouldRetryValidationError(t *testing.T) {
	classifier := entity.NewRetryErrorClassifier()

	tests := []struct {
		name     string
		attempt  int
		expected bool
	}{
		{
			name:     "First attempt - should retry",
			attempt:  1,
			expected: true,
		},
		{
			name:     "Second attempt - should retry",
			attempt:  2,
			expected: true,
		},
		{
			name:     "Third attempt - should not retry",
			attempt:  3,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := classifier.ShouldRetryValidationError(tt.attempt)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestExecuteWithRetry_Success(t *testing.T) {
	ctx := context.Background()
	retryMechanism := entity.NewRetryMechanism(entity.DefaultRetryConfig())

	operation := func(ctx context.Context, attempt int) (string, int, string, error) {
		return "success", http.StatusOK, "", nil
	}

	result := entity.ExecuteWithRetry(ctx, retryMechanism, "test_operation", operation)

	require.True(t, result.Success)
	require.Equal(t, "success", result.Result)
	require.Equal(t, 1, result.Attempts)
	require.Nil(t, result.LastError)
	require.False(t, result.IsSuccessDuplicate)
}

func TestExecuteWithRetry_SuccessAfterRetries(t *testing.T) {
	ctx := context.Background()
	retryMechanism := entity.NewRetryMechanism(&entity.RetryConfig{
		MaxAttempts:     3,
		InitialInterval: 1 * time.Millisecond,
		MaxInterval:     5 * time.Millisecond,
		Multiplier:      2.0,
	})

	attempts := 0
	operation := func(ctx context.Context, attempt int) (string, int, string, error) {
		attempts++
		if attempts < 3 {
			return "", http.StatusInternalServerError, "server error", errors.New("temporary error")
		}
		return "success", http.StatusOK, "", nil
	}

	result := entity.ExecuteWithRetry(ctx, retryMechanism, "test_operation", operation)

	require.True(t, result.Success)
	require.Equal(t, "success", result.Result)
	require.Equal(t, 3, result.Attempts)
	require.Nil(t, result.LastError)
}

func TestExecuteWithRetry_DuplicateRequest(t *testing.T) {
	ctx := context.Background()
	retryMechanism := entity.NewRetryMechanism(entity.DefaultRetryConfig())

	operation := func(ctx context.Context, attempt int) (string, int, string, error) {
		return "duplicate", http.StatusConflict, "duplicate request on transaction", errors.New("conflict")
	}

	result := entity.ExecuteWithRetry(ctx, retryMechanism, "test_operation", operation)

	require.True(t, result.Success)
	require.Equal(t, "duplicate", result.Result)
	require.Equal(t, 1, result.Attempts)
	require.True(t, result.IsSuccessDuplicate)
}

func TestExecuteWithRetry_PermanentFailure(t *testing.T) {
	ctx := context.Background()
	retryMechanism := entity.NewRetryMechanism(entity.DefaultRetryConfig())

	operation := func(ctx context.Context, attempt int) (string, int, string, error) {
		return "", http.StatusBadRequest, "insufficient funds", errors.New("insufficient funds")
	}

	result := entity.ExecuteWithRetry(ctx, retryMechanism, "test_operation", operation)

	require.False(t, result.Success)
	require.Equal(t, 1, result.Attempts)
	require.NotNil(t, result.LastError)
	require.False(t, result.IsSuccessDuplicate)
}

func TestExecuteWithRetry_MaxAttemptsReached(t *testing.T) {
	ctx := context.Background()
	retryMechanism := entity.NewRetryMechanism(&entity.RetryConfig{
		MaxAttempts:     2,
		InitialInterval: 1 * time.Millisecond,
		MaxInterval:     5 * time.Millisecond,
		Multiplier:      2.0,
	})

	operation := func(ctx context.Context, attempt int) (string, int, string, error) {
		return "", http.StatusInternalServerError, "server error", errors.New("persistent error")
	}

	result := entity.ExecuteWithRetry(ctx, retryMechanism, "test_operation", operation)

	require.False(t, result.Success)
	require.Equal(t, 2, result.Attempts)
	require.NotNil(t, result.LastError)
}

func TestExecuteWithRetry_ContextCancellation(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	retryMechanism := entity.NewRetryMechanism(&entity.RetryConfig{
		MaxAttempts:     3,
		InitialInterval: 100 * time.Millisecond,
		MaxInterval:     500 * time.Millisecond,
		Multiplier:      2.0,
	})

	attempts := 0
	operation := func(ctx context.Context, attempt int) (string, int, string, error) {
		attempts++
		if attempts == 1 {
			go func() {
				time.Sleep(10 * time.Millisecond)
				cancel()
			}()
		}
		return "", http.StatusInternalServerError, "server error", errors.New("server error")
	}

	result := entity.ExecuteWithRetry(ctx, retryMechanism, "test_operation", operation)

	require.False(t, result.Success)
	require.Equal(t, 1, result.Attempts)
	require.Equal(t, context.Canceled, result.LastError)
}
