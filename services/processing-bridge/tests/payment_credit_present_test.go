package tests

import (
	"errors"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"

	processing_entity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	processing_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

func (s *Suite) TestPaymentCreditPresent_Success() {
	expected := int64(1)
	contractRid := "01"

	s.mocks.Providers.SilkpayProvider.EXPECT().PaymentCreditPresent(gomock.Any(), gomock.Any()).
		Return(&processing_entity.PaymentCreditPresentResp{TransactionID: expected}, nil).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	resp, err := s.grpc.PaymentCreditPresent(s.ctx, &processing_bridge.PaymentCreditPresentReq{
		ContractRid:    &contractRid,
		Amount:         "1000",
		Currency:       "KZT",
		Rrn:            "rrn",
		AuthExternalID: "001",
	})

	s.Require().NoError(err)
	s.Require().Equal(expected, resp.TransactionID)
}

func (s *Suite) TestPaymentCreditPresent_Error() {
	contractRid := "01"

	s.mocks.Providers.SilkpayProvider.EXPECT().PaymentCreditPresent(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("Validation failed")).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	resp, err := s.grpc.PaymentCreditPresent(s.ctx, &processing_bridge.PaymentCreditPresentReq{
		ContractRid:    &contractRid,
		Amount:         "1000",
		Currency:       "KZT",
		Rrn:            "rrn",
		AuthExternalID: "001",
	})

	s.Require().Nil(resp)
	s.Require().ErrorContains(err, "Validation failed")
}
