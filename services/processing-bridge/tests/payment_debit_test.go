package tests

import (
	"errors"

	"github.com/golang/mock/gomock"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"
	processing_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

func (s *Suite) TestPaymentDebit_Success() {
	contractRid := "01"

	s.mocks.Providers.SilkpayProvider.EXPECT().PaymentDebit(gomock.Any(), gomock.Any()).
		Return(nil).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	_, err := s.grpc.PaymentDebit(s.ctx, &processing_bridge.PaymentDebitReq{
		ContractRid: &contractRid,
		Amount:      "1000",
		Currency:    "KZT",
		Rrn:         "rrn",
		IsReversal:  false,
	})

	s.Require().NoError(err)
}

func (s *Suite) TestPaymentDebit_Error() {
	contractRid := "01"

	s.mocks.Providers.SilkpayProvider.EXPECT().PaymentDebit(gomock.Any(), gomock.Any()).
		Return(errors.New("Validation failed")).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	resp, err := s.grpc.PaymentDebit(s.ctx, &processing_bridge.PaymentDebitReq{
		ContractRid: &contractRid,
		Amount:      "1000",
		Currency:    "KZT",
		Rrn:         "rrn",
		IsReversal:  false,
	})

	s.Require().Nil(resp)
	s.Require().ErrorContains(err, "Validation failed")
}
