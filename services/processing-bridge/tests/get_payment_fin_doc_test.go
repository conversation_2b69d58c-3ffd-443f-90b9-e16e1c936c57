package tests

import (
	"errors"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"

	processing_entity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	processing_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

func (s *Suite) TestGetPaymentFinDoc_Success() {
	expected := int64(1)
	testString := "123"
	s.mocks.Providers.SilkpayProvider.EXPECT().GetPaymentFinDoc(gomock.Any(), gomock.Any()).
		Return(&processing_entity.GetPaymentFinDocResp{
			TransactionID: expected,
		},
			nil).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	resp, err := s.grpc.GetPaymentFinDoc(s.ctx, &processing_bridge.GetPaymentFinDocReq{
		ExternalID:          testString,
		PartyIban:           &testString,
		PartyFinContractRid: &testString,
		DatetimeFrom:        timestamppb.Now(),
		DatetimeTo:          timestamppb.Now(),
	})

	s.Require().NoError(err)
	s.Require().Equal(expected, resp.TransactionID)
}

func (s *Suite) TestGetPaymentFinDoc_Error() {
	testString := "123"
	s.mocks.Providers.SilkpayProvider.EXPECT().GetPaymentFinDoc(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("Validation failed")).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	resp, err := s.grpc.GetPaymentFinDoc(s.ctx, &processing_bridge.GetPaymentFinDocReq{
		ExternalID:          testString,
		PartyIban:           &testString,
		PartyFinContractRid: &testString,
		DatetimeFrom:        timestamppb.Now(),
		DatetimeTo:          timestamppb.Now(),
	})

	s.Require().Nil(resp)
	s.Require().ErrorContains(err, "Validation failed")
}
