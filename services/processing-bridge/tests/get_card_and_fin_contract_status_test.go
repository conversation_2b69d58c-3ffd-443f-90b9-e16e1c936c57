package tests

import (
	"errors"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"
	silkEntity "git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay/entity"
	globalUtils "git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	pbProcessingBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

var testSilkPayResp = silkEntity.GetCardAndFinContractStatusResponse{
	CardStatuses: []silkEntity.CardStatus{
		{
			Status:       "Active",
			ContractRid:  "contract-rid-001",
			ErrorMessage: "",
			Successful:   true,
		},
	},
	ContractStatuses: []silkEntity.ContractStatus{
		{
			Status:       "Active",
			ContractRid:  "contract-rid-001",
			ErrorMessage: "",
			Successful:   true,
		},
	},
}

// TestGetCardAndFinContractStatus_Success проверяет успешное получение статуса финансового контракта и карты.
func (s *Suite) TestGetCardAndFinContractStatus_Success() {
	ctx := globalUtils.SetRequestID(s.ctx, uuid.New().String())

	s.mocks.Providers.SilkpayProvider.EXPECT().GetFinContractStatusByClientRID(gomock.Any(), gomock.Any()).
		Return(&testSilkPayResp, nil).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	resp, err := s.grpc.GetCardAndFinContractStatus(ctx, &pbProcessingBridge.GetFinContractStatusReq{
		CardRIDs:     []string{"card-rid-001", "card-rid-002"},
		ContractRIDs: []string{"contract-rid-001", "contract-rid-002"},
		ClientID:     "client-id-001",
	})
	s.Require().NoError(err)
	s.Require().NotEmpty(resp)
}

// TestGetCardAndFinContractStatus_Error проверяет обработку ошибки при получении статуса финансового контракта и карты.
func (s *Suite) TestGetCardAndFinContractStatus_Error() {
	ctx := globalUtils.SetRequestID(s.ctx, uuid.New().String())
	ctx = globalUtils.AddUserIDToContext(ctx, uuid.New().String())

	s.mocks.Providers.SilkpayProvider.EXPECT().GetFinContractStatusByClientRID(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("some error")).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	resp, err := s.grpc.GetCardAndFinContractStatus(ctx, &pbProcessingBridge.GetFinContractStatusReq{
		CardRIDs:     []string{"card-rid-001", "card-rid-002"},
		ContractRIDs: []string{"contract-rid-001", "contract-rid-002"},
		ClientID:     "client-id-001",
	})

	s.Require().Nil(resp)
	s.Require().ErrorContains(err, "some error")
}
