package tests

import (
	"errors"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/silkpay"
	globalUtils "git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

var exampleSilkPayReq = &pb.CreateClientAccountAndCardReq{
	Person: &pb.Person{
		Iin:           "************",
		Rid:           "person-rid-001",
		LastName:      "Иванов",
		FirstName:     "Иван",
		MiddleName:    "<PERSON>ва<PERSON><PERSON>",
		ScreenName:    "ivan.ivanov",
		Gender:        "M",
		BirthDate:     "1987-01-01",
		MobilePhone:   "+***********",
		WorkPhone:     "+***********",
		Email:         "<EMAIL>",
		Citizenship:   398, // Казахстан
		BirthPlace:    "Алматы",
		BirthName:     "Иванов",
		LastNameLat:   "<PERSON><PERSON>",
		FirstNameLat:  "<PERSON>",
		EmployerTitle: "А<PERSON> Банк",
		Notes:         "Тестовый клиент",
		RiskLevel:     "LOW",
		Document: &pb.Document{
			Type:      "ID",
			Number:    "*********",
			IssueDate: "2010-01-01",
			ExpDate:   "2030-01-01",
			Issuer:    "МВД РК",
		},
		LanguageCode: "ru",
		ResidentAddress: &pb.ResidentAddress{
			CountryId:     398,
			CityRid:       "city-rid-001",
			CityTitle:     "Алматы",
			AddressInCity: "ул. Абая, д. 10, кв. 5",
			Zip:           "050000",
		},
	},
	AccountDetails: &pb.AccountDetails{
		ContractTypeRid:  "contract-type-acc-001",
		ContractRid:      "contract-rid-acc-001",
		ContractCurrency: "KZT",
		Iban:             "********************",
	},
	CardDetails: &pb.CardDetails{
		ContractTypeRid: "contract-type-card-001",
		ContractRid:     "contract-rid-card-001",
		CardParams: &pb.CardParams{
			ExpDate:    "2030-12",
			EmbossName: "IVAN IVANOV",
			Status:     "ACTIVE",
		},
		DeliveryAddress: &pb.DeliveryAddress{
			CountryId: 398,
			City:      "Алматы",
			Street:    "пр. Достык",
			House:     "15А",
		},
		Attrs: []*pb.Attr{
			{Key: "color", Value: "gold"},
			{Key: "contactless", Value: "true"},
		},
	},
}

func (s *Suite) TestCreateClientAccountAndCard_Success() {
	exampleSilkpayResp := &silkpay.CreateClientAccountAndCardResponse{
		Pan:        "some pan",
		EmbossName: "some emboss",
		ExpiryDate: "2078-12-31",
		Status:     "ok",
	}

	ctx := globalUtils.SetRequestID(s.ctx, uuid.New().String())

	// кейс когда токен уже имеется в бд
	err := s.postgresDB.Tokens.Create().
		SetToken("some_token").
		SetTokenType("Bearer").
		SetExpiresIn(time.Date(2900, time.January, 1, 0, 0, 0, 0, time.UTC)).
		SetRequestID(uuid.New().String()).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Exec(ctx)
	s.Require().NoError(err)

	s.mocks.Providers.SilkpayProvider.EXPECT().
		CreateClientAccountAndCard(gomock.Any(), gomock.Any()).Return(exampleSilkpayResp, nil).Times(1)

	resp, err := s.grpc.CreateClientAccountAndCard(ctx, exampleSilkPayReq)
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(resp.Pan, exampleSilkpayResp.Pan)
	s.Require().Equal(resp.EmbossName, exampleSilkpayResp.EmbossName)
	s.Require().Equal(resp.ExpiryDate, exampleSilkpayResp.ExpiryDate)
	s.Require().Equal(resp.Status, exampleSilkpayResp.Status)
}

func (s *Suite) TestCreateClientAccountAndCard_Success_WithoutToken() {
	exampleSilkpayResp := &silkpay.CreateClientAccountAndCardResponse{
		Pan:        "some pan",
		EmbossName: "some emboss",
		ExpiryDate: "2078-12-31",
		Status:     "ok",
	}

	ctx := globalUtils.SetRequestID(s.ctx, uuid.New().String())

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().
		CreateClientAccountAndCard(gomock.Any(), gomock.Any()).Return(exampleSilkpayResp, nil).Times(1)

	resp, err := s.grpc.CreateClientAccountAndCard(ctx, exampleSilkPayReq)
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(resp.Pan, exampleSilkpayResp.Pan)
	s.Require().Equal(resp.EmbossName, exampleSilkpayResp.EmbossName)
	s.Require().Equal(resp.ExpiryDate, exampleSilkpayResp.ExpiryDate)
	s.Require().Equal(resp.Status, exampleSilkpayResp.Status)
}

func (s *Suite) TestCreateClientAccountAndCard_ExpiredToken() {
	exampleSilkpayResp := &silkpay.CreateClientAccountAndCardResponse{
		Pan:        "some pan",
		EmbossName: "some emboss",
		ExpiryDate: "2078-12-31",
		Status:     "ok",
	}

	ctx := globalUtils.SetRequestID(s.ctx, uuid.New().String())

	// кейс когда токен уже имеется в бд
	err := s.postgresDB.Tokens.Create().
		SetToken("some_token").
		SetTokenType("Bearer").
		SetExpiresIn(time.Date(2007, time.January, 1, 0, 0, 0, 0, time.UTC)).
		SetRequestID(uuid.New().String()).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Exec(ctx)
	s.Require().NoError(err)

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(&silkpay.AuthenticateResponse{
		AccessToken: "some_token",
		TokenType:   "Bearer",
		ExpiresIn:   8234234,
	}, nil).Times(1)

	s.mocks.Providers.SilkpayProvider.EXPECT().
		CreateClientAccountAndCard(gomock.Any(), gomock.Any()).Return(exampleSilkpayResp, nil).Times(1)

	resp, err := s.grpc.CreateClientAccountAndCard(ctx, exampleSilkPayReq)
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(resp.Pan, exampleSilkpayResp.Pan)
	s.Require().Equal(resp.EmbossName, exampleSilkpayResp.EmbossName)
	s.Require().Equal(resp.ExpiryDate, exampleSilkpayResp.ExpiryDate)
	s.Require().Equal(resp.Status, exampleSilkpayResp.Status)
}

func (s *Suite) TestCreateClientAccountAndCard_Success_WithRetry() {
	exampleSilkpayResp := &silkpay.CreateClientAccountAndCardResponse{
		Pan:        "some pan",
		EmbossName: "some emboss",
		ExpiryDate: "2078-12-31",
		Status:     "ok",
	}

	ctx := globalUtils.SetRequestID(s.ctx, uuid.New().String())

	// кейс когда токен уже имеется в бд
	err := s.postgresDB.Tokens.Create().
		SetToken("some_token").
		SetTokenType("Bearer").
		SetExpiresIn(time.Date(2900, time.January, 1, 0, 0, 0, 0, time.UTC)).
		SetRequestID(uuid.New().String()).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Exec(ctx)
	s.Require().NoError(err)

	s.mocks.Providers.SilkpayProvider.EXPECT().
		CreateClientAccountAndCard(gomock.Any(), gomock.Any()).Return(exampleSilkpayResp, nil).
		After(s.mocks.Providers.SilkpayProvider.EXPECT().CreateClientAccountAndCard(gomock.Any(), gomock.Any()).Return(nil, errors.New("some internal err")).Times(1)).
		Times(1)

	resp, err := s.grpc.CreateClientAccountAndCard(ctx, exampleSilkPayReq)
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(resp.Pan, exampleSilkpayResp.Pan)
	s.Require().Equal(resp.EmbossName, exampleSilkpayResp.EmbossName)
	s.Require().Equal(resp.ExpiryDate, exampleSilkpayResp.ExpiryDate)
	s.Require().Equal(resp.Status, exampleSilkpayResp.Status)
}

func (s *Suite) TestCreateClientAccountAndCard_TokenReqErr() {
	ctx := globalUtils.SetRequestID(s.ctx, uuid.New().String())

	s.mocks.Providers.SilkpayProvider.EXPECT().Authenticate(gomock.Any()).Return(nil, errors.New("some auth err")).Times(1)

	resp, err := s.grpc.CreateClientAccountAndCard(ctx, exampleSilkPayReq)
	s.Require().NotNil(err)
	s.Require().Nil(resp)
}

func (s *Suite) TestCreateClientAccountAndCard_SilkpayErr() {
	ctx := globalUtils.SetRequestID(s.ctx, uuid.New().String())

	// кейс когда токен уже имеется в бд
	err := s.postgresDB.Tokens.Create().
		SetToken("some_token").
		SetTokenType("Bearer").
		SetExpiresIn(time.Date(2900, time.January, 1, 0, 0, 0, 0, time.UTC)).
		SetRequestID(uuid.New().String()).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Exec(ctx)
	s.Require().NoError(err)

	s.mocks.Providers.SilkpayProvider.EXPECT().
		CreateClientAccountAndCard(gomock.Any(), gomock.Any()).Return(nil, errors.New("some internal err")).Times(3)

	resp, err := s.grpc.CreateClientAccountAndCard(ctx, exampleSilkPayReq)
	s.Require().NotNil(err)
	s.Require().Nil(resp)
}
