package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) PaymentDebit(ctx context.Context, req *pb.PaymentDebitReq) (*pb.PaymentDebitResp, error) {
	paymentDebitEntity := entity.MakePaymentDebitPbToEntity(req)

	paymentDebit, err := s.useCase.PaymentDebit(ctx, paymentDebitEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakePaymentDebitEntityToPb(paymentDebit), nil
}
