package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) PaymentCredit(ctx context.Context, req *pb.PaymentCreditReq) (*pb.PaymentCreditResp, error) {
	paymentCreditEntity := entity.MakePaymentCreditPbToEntity(req)

	paymentCredit, err := s.useCase.PaymentCredit(ctx, paymentCreditEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakePaymentCreditEntityToPb(paymentCredit), nil
}
