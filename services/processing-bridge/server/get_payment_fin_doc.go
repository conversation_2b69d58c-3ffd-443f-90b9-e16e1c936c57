package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) GetPaymentFinDoc(ctx context.Context, req *pb.GetPaymentFinDocReq) (*pb.GetPaymentFinDocResp, error) {
	createPaymentFfinDocEntity := entity.MakeGetPaymentFinDocPbToEntity(req)

	createPaymentFfinDoc, err := s.useCase.GetPaymentFinDoc(ctx, createPaymentFfinDocEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetPaymentFinDocEntityToPb(createPaymentFfinDoc), nil
}
