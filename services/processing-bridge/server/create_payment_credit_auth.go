package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) CreatePaymentCreditAuth(ctx context.Context, req *pb.CreatePaymentCreditAuthReq) (*pb.CreatePaymentCreditAuthResp, error) {
	createPaymentCreditAuthEntity := entity.MakeCreatePaymentCreditAuthPbToEntity(req)

	createPaymentCreditAuth, err := s.useCase.CreatePaymentCreditAuth(ctx, createPaymentCreditAuthEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeCreatePaymentCreditAuthEntityToPb(createPaymentCreditAuth), nil
}
