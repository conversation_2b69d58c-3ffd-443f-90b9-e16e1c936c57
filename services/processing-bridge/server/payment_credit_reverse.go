package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) PaymentCreditReverse(ctx context.Context, req *pb.PaymentCreditReverseReq) (*pb.PaymentCreditReverseResp, error) {
	paymentCreditReverseEntity := entity.MakePaymentCreditReversePbToEntity(req)

	paymentCreditReverse, err := s.useCase.PaymentCreditReverse(ctx, paymentCreditReverseEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakePaymentCreditReverseEntityToPb(paymentCreditReverse), nil
}
