package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) PaymentDebitPresent(ctx context.Context, req *pb.PaymentDebitPresentReq) (*pb.PaymentDebitPresentResp, error) {
	paymentDebitPresentEntity := entity.MakePaymentDebitPresentPbToEntity(req)

	paymentDebitPresent, err := s.useCase.PaymentDebitPresent(ctx, paymentDebitPresentEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakePaymentDebitPresentEntityToPb(paymentDebitPresent), nil
}
