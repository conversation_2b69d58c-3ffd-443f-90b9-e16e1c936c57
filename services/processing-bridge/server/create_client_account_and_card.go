package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) CreateClientAccountAndCard(ctx context.Context, req *pb.CreateClientAccountAndCardReq) (*pb.CreateClientAccountAndCardResp, error) {
	createClientAccountAndCardEntity := entity.MakeCreateClientAccountAndCardPbToEntity(req)

	createClientAccountAndCard, err := s.useCase.CreateClientAccountAndCard(ctx, createClientAccountAndCardEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeCreateClientAccountAndCardEntityToPb(createClientAccountAndCard), nil
}
