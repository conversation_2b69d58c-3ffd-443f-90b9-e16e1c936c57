package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) PaymentDebitReverse(ctx context.Context, req *pb.PaymentDebitReverseReq) (*pb.PaymentDebitReverseResp, error) {
	paymentDebitReverseEntity := entity.MakePaymentDebitReversePbToEntity(req)

	paymentDebitReverse, err := s.useCase.PaymentDebitReverse(ctx, paymentDebitReverseEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakePaymentDebitReverseEntityToPb(paymentDebitReverse), nil
}
