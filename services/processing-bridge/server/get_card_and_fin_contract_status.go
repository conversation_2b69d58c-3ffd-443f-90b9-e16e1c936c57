package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) GetCardAndFinContractStatus(ctx context.Context, req *pb.GetFinContractStatusReq) (*pb.GetFinContractStatusResp, error) {
	getCardAndFinContractStatusEntity := entity.MakeGetCardAndFinContractStatusPbToEntity(req)

	getCardAndFinContractStatus, err := s.useCase.GetCardAndFinContractStatus(ctx, getCardAndFinContractStatusEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeGetCardAndFinContractStatusEntityToPb(getCardAndFinContractStatus), nil
}
