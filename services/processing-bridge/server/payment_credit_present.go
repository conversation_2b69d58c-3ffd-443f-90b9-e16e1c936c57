package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) PaymentCreditPresent(ctx context.Context, req *pb.PaymentCreditPresentReq) (*pb.PaymentCreditPresentResp, error) {
	paymentCreditPresentEntity := entity.MakePaymentCreditPresentPbToEntity(req)

	paymentCreditPresent, err := s.useCase.PaymentCreditPresent(ctx, paymentCreditPresentEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakePaymentCreditPresentEntityToPb(paymentCreditPresent), nil
}
