package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"

	"git.redmadrobot.com/zaman/backend/zaman/services/processing-bridge/entity"
)

func (s *Server) CreatePaymentDebitAuth(ctx context.Context, req *pb.CreatePaymentDebitAuthReq) (*pb.CreatePaymentDebitAuthResp, error) {
	createPaymentDebitAuthEntity := entity.MakeCreatePaymentDebitAuthPbToEntity(req)

	createPaymentDebitAuth, err := s.useCase.CreatePaymentDebitAuth(ctx, createPaymentDebitAuthEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeCreatePaymentDebitAuthEntityToPb(createPaymentDebitAuth), nil
}
