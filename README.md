## Zaman

Бэкэнд с микросервисной архитектурой для сервис<PERSON>.

**Оглавление**

- [Zaman](#zaman)
- [Среды исполнения](#среды-исполнения)
- [1. Общие правила работы](#1-общие-правила-работы)
  - [1.1 Работа с GitLab](#11-работа-с-gitlab)
  - [1.2 Требования к старту работы над проектом](#12-требования-к-старту-работы-над-проектом)
- [2. Подход к разработке и деплою](#2-подход-к-разработке-и-деплою)
  - [2.1 Git Flow](#21-git-flow)
  - [2.2 Работа с несколькими репозиториями](#22-работа-с-несколькими-репозиториями)
  - [2.3 Правила работы с Jira](#23-правила-работы-с-jira)
- [3. Запуск проекта](#3-запуск-проекта)
  - [Шаги запуска](#шаги-запуска)
- [4. Логирование](#4-логирование)
- [5. Особенности использования Meroving](#5-особенности-использования-meroving)
  - [5.1 Кодогенерация](#51-кодогенерация)
  - [5.2 Подключение кастомных провайдеров и клиентов](#52-подключение-кастомных-провайдеров-и-клиентов)
- [6. Работа с микросервисами](#6-работа-с-микросервисами)
  - [6.1 Добавление нового микросервиса](#61-добавление-нового-микросервиса)
  - [6.2 Деплой нового микросервиса](#62-деплой-нового-микросервиса-подготовка-к-первому-мержу-деплой-на-прод)
  - [6.3 Работа с ошибками](#63-работа-с-ошибками)
  - [6.4 Работа с proto-файлами](#64-работа-с-proto-файлами)
  - [6.5 Работа с кастомными переменными окружения](#65-работа-с-кастомными-переменными-окружения)
- [7. Тестирование](#7-тестирование)
  - [7.1 Реализация тестов](#71-реализация-тестов)
  - [7.2 Использование фикстур и golden files](#72-использование-фикстур-и-golden-files)
  - [7.3 Примеры использования в тестах](#73-примеры-использования-в-тестах)
  - [8. Планировщик Chronos](#8-планировщик-chronos)
- [9. Работа с шлюзом (gateway)](#9-работа-с-шлюзом-gateway)
  - [9.1 Создание нового метода](#91-создание-нового-метода)
  - [9.2 Связь метода на gateway с grpc-методами микросервиса](#92-связь-метода-на-gateway-с-grpc-методами-микросервиса)
- [10. Примеры реализации бизнес-функционала](#10-примеры-реализации-бизнес-функционала)
- [11. Архитектурные решения](#11-архитектурные-решения)
- [12. Общие правила разработки](#12-общие-правила-разработки)
- [13. Использование bridge сервисов с ETL паттерном](#13-использование-bridge-сервисов-с-etl-паттерном)
  - [13.1 Обзор паттерна](#131-обзор-паттерна)
  - [13.2 Пошаговое описание ETL процесса](#132-пошаговое-описание-etl-процесса)
  - [13.2.1 Получение данных из сторонней интеграции](#1321-получение-данных-из-сторонней-интеграции)
  - [13.2.3 Трансформация и загрузка данных в PostgreSQL](#1323-трансформация-и-загрузка-данных-в-postgresql)

**Полезные ссылки**

- [Команда Backend](https://git.redmadrobot.com/zaman/backend/zaman/-/wikis/%D0%9A%D0%BE%D0%BC%D0%B0%D0%BD%D0%B4%D0%B0-Backend)
- [Confluence](https://conf.redmadrobot.com/pages/viewpage.action?pageId=4256963411)
- [Jira](https://jira.redmadrobot.com/secure/RapidBoard.jspa?rapidView=833&projectKey=ZMN)
- [Miro](https://miro.com/app/board/uXjVKxKBBzI=/)
- [Команда Backend](https://git.redmadrobot.com/zaman/backend/zaman/-/wikis/%D0%9A%D0%BE%D0%BC%D0%B0%D0%BD%D0%B4%D0%B0-Backend)

## Среды исполнения

| ENV   | API                                                | SPEC                                                     |
|-------|----------------------------------------------------|----------------------------------------------------------|
| DEV   | http://mobile-dev.zaman.redmadrobot.com/api/v1     | http://mobile-dev.zaman.redmadrobot.com/spec/swagger     |
| DEV   | http://paymentsgw-dev.zaman.redmadrobot.com/api/v1 | http://paymentsgw-dev.zaman.redmadrobot.com/spec/swagger |
| DEV   | http://sme-dev.zaman.redmadrobot.com/api/v1        | http://sme-dev.zaman.redmadrobot.com/spec/swagger        |
| STAGE | http://mobile.zaman.redmadrobot.com/api/v1         | http://mobile.zaman.redmadrobot.com/spec/swagger         |
| STAGE | http://paymentsgw.zaman.redmadrobot.com/api/v1     | http://paymentsgw.zaman.redmadrobot.com/spec/swagger     |
| STAGE | http://sme.zaman.redmadrobot.com/api/v1            | http://sme.zaman.redmadrobot.com/spec/swagger            |

## 1. Общие правила работы

### 1.1 Работа с GitLab

Для обеспечения качества разработки и стабильности кода необходимо соблюдать следующие правила:

- **Работа с ветками:**
    - Каждый разработчик использует свою рабочую ветку для разработки новых функций от `develop`, исправления багов или внесения изменений.
    - Формат имен веток:
        - `feature/<название_фичи>` — новые функции.
        - `bugfix/<номер_тикета>` — исправление ошибок.
        - `hotfix/<описание>` — экстренные исправления.
    - Регулярно синхронизируйте свою ветку с `develop` для предотвращения конфликтов.
    - Мерж в `develop` и `release-*` разрешена только мейнтейнерам.
    - Все изменения проходят через Merge Request (MR).

- **Коммиты:**
    - Сообщения коммитов пишутся на русском языке в пассивном залоге. Это ответ на вопрос "Что было сделано".
    - Если затрагиваются пакеты `rmr-pkg` или `meroving`, это указывается в начале сообщения.
    - При наличии нескольких изменений разделяйте их точкой с запятой (`;`).
    - Пример: “Добавлены новые метрики; Обновлены зависимости; Исправлена ошибка в тестах;”
    - Коммит должен кратко и чётко описывать, что и почему было изменено.
    - В коммите должны указываться тикеты задач, в рамках которых внесены изменения
    - Если фикс вносится без тикета, начни сообщение с "Fix: ", чтобы текст попал в changelog

- **Создание Merge Request:**
    - Перед созданием MR убедитесь, что:
        - Локально пройдены все тесты и линтеры.
        - Добавлены или обновлены необходимые тесты.
        - Изменения в проектах `rmr-pkg` и `meroving` проведены и используются их актуальные версии.
    - В описании MR указывайте:
        - Что было сделано.
        - Номер тикета или описание задачи.
        - Причину экстренного слияния (если требуется).
    - Без одобрения одного из ответственных разработчиков слияние в `main` не допускается.

- **Код-ревью и тестирование:**
    - Код-ревью обязателен перед слиянием.
    - Перед отправкой MR запустите все тесты.
    - Если изменения затрагивают пакеты `meroving` или `rmr-pkg`, сначала протестируйте их локально.

- **Работа с багами и экстренными исправлениями:**
    - Экстренные изменения оформляются в ветке `hotfix/<описание>` от актуальной версии (последний тег).
    - При критической ситуации уведомляйте лидов направлений через общий чат и получайте подтверждение о срочности.

- **Разрешение конфликтов:**
    - Конфликты разрешаются в рабочей ветке.
    - После их устранения обязательно убедитесь в прохождении тестов.
    - Регулярно подтягивайте develop, особенно в момент перед созданием финального MR

- **Коммуникация:**
    - Регулярно сообщайте команде о статусе работы через тикеты, MR или чаты.
    - В случае вопросов обращайтесь к ответственным лицам.
    - Периодически публикуйте мемы в канал #random-memes-and-cries

### 1.2 Требования к старту работы над проектом

- **Версия языка Golang**
  - На проекте используется версия `Golang 1.23.x` (для установки необходимой версии можно использовать менеджер [GVM GitHub](https://github.com/moovweb/gvm))
  > Версия Golang 1.24 и выше не поддерживается!

- **Версия и запуск линтеров при разработке**
  - На проекте используется `golangci-lint` [Golangci-lint GitHub](https://github.com/golangci/golangci-lint)
  - Проверенной на корректность работы является версия `golangci-lint has version 1.64.4`
  - Устанавливать рекомендуется как бинарник [install](https://golangci-lint.run/welcome/install/)

- **Версия OpenAPI утилиты Redocly**
  - На проекте для проверки спецификаций используется версия `redocly 1.25.6` [Redocly GitHub](https://github.com/Redocly/redocly-cli)
  - Установка:

```bash
npm i -g @redocly/cli@1.25.6
```
- **Старт работы**
  - Убедиться в наличии необходимых доступов и настроек окружения и установить последнюю версию [meroving](https://git.redmadrobot.com/backend-go/meroving/-/blob/main/README.MD)
  - Скачать последнюю версию проекта из репозитория `zaman`
  - В корне скачанного репозитория проекта выполнить команду `meroving build` и дождаться её успешного завершения
  - Приступать к изучению кодовой базы и реализации задач

## 2. Подход к разработке и деплою

### 2.1 Git Flow

1. **Фичевая разработка**

  - Происходит в своих ветках, которые затем сливаются в `develop`.
  - Мерж реквест должен быть зааппрувлен лидом команды, отвечающей за сервис, или 2 другими разработчиками.
  - Крайне желательно на финальном этапе перед запросом ревью еще раз подмержить к себе в ветку develop, чтобы минимизировать конфликты.

2. **Отладочный стенд (dev) и разработческие версии (dev versions)**

  - Все изменения, попавшие в `develop`, автоматически выкатываются на dev стенд.
  - Dev-стенд нестабильная среда, и предназначен для отладки изменений, которые сложно проверить локально.
  - Если на dev-стенд выкатывается заведомо нестабильный функционал, который потенциально ломает уже существующий флоу, об этом нужно явно написать во внутренний канал.
Это вполне допустимая ситуация. Но это блокирует дальнейшую цепочку поставок, поэтому должно быть кратковременным.
Правильным решением такой ситуации является разбиение на небольшие задачи, в которых новые изменения активируются фичер-тоглами (в простом случае через энвы).
  - QA по умолчанию не используют dev-стенд, но их можно просить помочь проверить на нем какие-то конкретные вещи.
  - Для выката изменений на стабильный `stage-стенд` нужно собрать dev-версию командой `meroving release --dev` (создаст коммит и его нужно потом запушить).
  - Дев-версия имеет вид dev-DDMM-sha-1. Выпускать её может любой разработчик, имеющий права на мерж в `develop`.
  - Перед выпуском версии нужно убедиться, что в `develop` нет нестабильного функционала, `stage` после деплоя должен оказаться стабильным.
  - Сбор дев-версии нужен для того, чтобы отслеживать изменения в [CHANGELOG_DEV.md](./CHANGELOG_DEV.md) на стабильный стенд и оповещать QA об изменениях. Также там расcчитываются изменения в API.

3. **Релизный кандидат (release candidate)**

  - В определенный момент принимается решение о переносе накопленного на `stage` функционала на `production`.
  - Для этого в `develop` создается RC-версия с помощью команды `meroving release --rc --bump=minor`. В **крайне особых** ситуациях тип бампа может быть другим.
  - Перед выпуском версии нужно убедиться, что в `develop` нет нестабильного функционала (ок, если они закрыты тоглом).
  - Rc-версия рассчитывается от актуальной версии на проде, которую обозначает самый свежий тег в гитлабе.
  - В [CHANGELOG.md](./CHANGELOG.md) попадает -rc-1 версия с объединенным контентом CHANGELOG_DEV.md, который в свою очередь обнуляется.
  - Rс-версия попадает на отдельный стабильный стенд `preprod`, где QA тестируют понятный скоуп изменений.
  - От созданного коммита rc-версии создается ветка `release-*`, соответствующая версии.
  - Разработка продолжает вносить изменения в `develop`. Эти изменения попадут на прод уже в релизе, после текущего.
  - Если для rc-версии требуется фикс, он должен быть вмержен как в `release-*`, так и в `develop`. Это неудобно.
Поэтому лучше убедиться в готовности своего функционала к попаданию в релизную сборку.
_Возможно, если функционал попадет в `develop` накануне релизной сборки, лучше подождать с мержем в `develop`_.
  - Для выката фиксов на стабильный `preprod` нужно собрать новую версию командой `meroving release --rc`.
Не торопитесь ее выпускать сразу после мержа, уточните, нет ли других готовых фиксов. Частые версии и уведомления раздражают.
  - Момент выхода новой rc-версии определяется по договоренности команды; но не раньше момента выпуска основной релизной версии.
  - В общем случае, чем быстрее версия доезжает до прода, тем круче команда.
Неудачники докатывают за месяцы, посредственности за недели, молодцы за дни, а элита - за часы.
В текущем флоу мы должны стремиться помогать QA быстрее давать аппрув нашей -rc сборки (да, это и наша задача тоже). Для этого нужно
помогать с наблюдаемостью изменений (много логов, доступы к базам, понятные комменты к тикетам что надо проверить и могло сломаться),
научить пользоваться моковыми интеграциями, а также проработать автоматические тесты на уровне сценариев.
  - Описанный процесс актуален на состояние июнь 2025, монорепозиторий. В целевом варианте назначения сред должны сдвинуться.
Каждая команда или фича должна иметь свой `dev`-стенд, `stage` станет единым стабильным стендом, где проверяются -rc-версии.
А `preprod` превратится в `standby` - копию `production` для отлавливания и проверки ошибок.

4. **Релизная версия (release version)**

  Сборку релизной версии осуществляет определенный разработчик (`Диспетчер релиза`) в соответствии с отдельным процессом (график или т.п.).
  Последовательность действий.
   1. Вмержить main в релизную ветку.
   1. Сделать mr и вмержить релизную ветку в main.
   1. Выпустить в main основную версию с помощью команды `meroving release --final --bump=<patch,minor,major>`.
   Перед пушем в origin проверить корректность формирования CHANGELOG.md (должны собраться все -rc версии в одну основную), обновление latest спецификации.
   На коммит должен поставить тег,его вместе с main пушим в основномй репозиторий, проверяем что запустился пайплайн с build шагами.
   1. Вмержить в `develop` полученный `main`.
   1. Сформировать данные для **CAB** (Change advisory board) - они необходимы для релиза на прод.
   Это необходимо запросить от лидов команд, чьи изменения попали в версию.
   У каждой фичевой команды еще есть Delivery Manager (DM), кто также участвует в этом процессе.
   Это важный этап. Он заставляет проверить - подумали ли над миграциями, не забыли ли про энвы и т.п.
   1. После одобрения **CAB** посматривать в канал с уведомлениями о деплое на `production`.

  После успешной поставки задаться вопросом о создании нового релизного кандидата, если согласованный в команде процесс это подразумевает.


5. **Горячие исправления (hotfixes)**

Каждый баг на `production` должен оцениваться по импакту и критичности. На их основе может быть принято, что фикс доедет по обычному процессу и попадает в -rc версию.
В случае высокого импакта СТО может принимать решение о том, чтобы фикс попал на прод как можно быстрее. Для этого:

1. От последнего тега в `main` создается ветка `hotfix`. В рамках нее разрабатывается фикс.
1. Rc-версию из `hotfix` деплоится на отдельный `hotfix`-стенд для проверки QA на каждый коммит аналогично девелопу, руками в пайплайне по кнопке.
1. `Диспетчер релиза` должен лишний раз проверить код на нежелательные сайд-эффекты, т.к. QA не будут проверять целиком всё приложение.
1. После аппрува от QA `Диспетчер релиза` мержит в `main` и выпускает там основную версию `meroving release --final --bump=patch`
1. `Диспетчер релиза` собирает **CAB** для внесения изменений в `production` по этому хотфиксу, и отслеживает статус.
1. В случае 2х параллельных хотфиксов, нужно сделать историю последовательной.
Можно смерживать разные `hotfix` ветки и одним патчем собирать несколько фиксов.
Можно последовательно сделать 2 фикса в двух версиях. Тогда в CAB должны быть указаны они оба, а для поставки на прод выбрана самая последняя.
1. После успешного хотфикса `Диспетчер релиза` должен подмержить `main` в `develop`. А также в `release-*`, если она существует.


### 2.2 Работа с несколькими репозиториями

- Если задача затрагивает несколько репозиториев:
    - Сначала подготовьте изменения в `rmr-pkg` и `meroving` (при необходимости).
    - После одобрения MR и выпуска новых версий через тег приступайте к изменению в `zaman`.
    - Перед внесением изменений протестируйте их локально.

### 2.3 Правила работы с Jira

- При взятии задачи изменяйте её статус на `DEV`.
- После одобрения MR и деплоя на тестовый стенд статус переводится в `READY FOR TEST`.
- При недостатке описания или аналитики обращайтесь к авторам задачи или аналитикам.

## 3. Запуск проекта

Проект собирается с помощью пакета `meroving` и библиотеки `rmr-pkg`.

### Шаги запуска

1. **Клонирование репозитория:**
```bash
git clone https://git.redmadrobot.com/zaman/backend/zaman
```

2.	**Установка meroving:**
```bash
go install git.redmadrobot.com/backend-go/meroving@latest
```

3. **Обновление meroving девтулзов **
```bash
meroving devtools
```

4. **Сборка проекта:**
- Для сборки проекта требуется запущенный Docker.
```bash
meroving build
```

5.	**Локальный запуск микросервисов и инфраструктуры:**
```bash
meroving run
```

## 4. Логирование
- При инициализации приложения в функции main() создаётся экземпляр логгера, который помещается в контекст.
- Доступ к логгеру осуществляется через создание сублоггера из контекста.
- Логируются:
    - Вызовы API (заголовки запроса, код и заголовки ответа) на уровне INFO.
    - Тела запросов и ответов на уровне TRACE.
    - Вызовы методов слоёв storage и usecase с информацией на уровнях INFO и TRACE.
    - При необходимости для логирования ORM (ent) установите переменную:
```bash
ZAMAN_DB_DEBUG=true
```
- Основные теги в логах:
    - request_id
    - user_id
    - from
    - method
    - elapsed
    - error

## 5. Особенности использования Meroving

### 5.1 Кодогенерация
- Все файлы с расширением *.gen.go генерируются автоматически. Ручное изменение таких файлов не допускается.
- Для внесения изменений, не связанных с зависимостями (например, PostgreSQL, Redis, Kafka и пр.), необходимо изменить базовый шаблон в проекте meroving и выполнить meroving build.

### 5.2 Подключение кастомных провайдеров и клиентов
- Провайдеры и клиенты подключаются через конфигурацию в meroving.json с использованием пакета rmr-pkg.
- Если нужного провайдера нет, его следует разработать и разместить в папке pkg.
- Для подключения провайдера отредактируйте файл cmd/services/<name_service>/providers.go.

## 6. Работа с микросервисами
### 6.1 Добавление нового микросервиса
- В файле meroving.json в разделе services добавьте новый элемент по шаблону:
    - name: название микросервиса (используется для формирования имен файлов и директорий).
    - description: описание микросервиса.
    - dependencies: список зависимостей (названия должны совпадать с описанными в документации meroving).
    - После изменения выполните:

```bash
meroving build
```

- Будут созданы следующие директории и файлы:
    - cmd/services/<name_service> – файл запуска микросервиса с автоматическим подключением провайдеров.
    - config/services/<name_service>/config.gen.go – автоматически сгенерированная конфигурация.
    - config/services/<name_service>/config.go – дополнительная конфигурация.
    - deploy – файлы для локального деплоя с использованием docker-compose.
    - errs/<serviceName> – файлы описания ошибок.
    - services/<name_service>/ – директория с файлами микросервиса (слои: entity, server, storage, test, usecase).
    - specs/proto/<service_name>/<service_name>.proto – описание grpc-сервиса для подключения через gateway.

### 6.2 Деплой нового микросервиса (подготовка к первому мержу, деплой на прод)
Выкат на dev происходит из ветки `develop` автоматически. Для этого
- Добавьте конфиги CI в папке ./deploy руками. gen-файл не используется и устарел.
  - deploy/.gitlab-ci.build-develop.yml
  - deploy/.gitlab-ci.build-rc.yml
  - deploy/.gitlab-ci.deploy-develop.yml
  - deploy/.gitlab-ci.deploy-release.yml
  - deploy/.gitlab-ci.check.yml

Пример заполнения:
```bash
build_awesomegw:
  extends: .build_gateway
  variables:
    GATEWAY_NAME: awesomegw
    GATEWAY_PATH: awesomegw

build_awesomeservice:
  extends: .build_service
  variables:
    SERVICE_NAME: awesomeservice
    SERVICE_PATH: awesome-service
```

- В MR укажите все переменные окружения, которые необходимо установить в vault для dev/stage.**Шаг обязательнный**.
- Пример переменных окружения можно посмотреть в любом secret существующего сервиса.
- Проконтролируйте установку переменных в vault. Мы даем туда доступ разработчикам и QA по запросу. Можно поставить задачу devOps, разработчику с доступом или самиму.
- После мержа откройте пайплайн (Build -> Pipelines, и ссылка должна в письме о мерже тоже быть) и отследите выполнение шагов `build` и `deploy`. Если они зеленые, вы великолепны.
- Для гейтвеев может дополнительно потребоваться завести ingress и прописать домен, заводим задачу на devOps.

Когда придет пора релизить изменения на прод, за что отвечает DM в команде, он придет с вопросом о формировании CAB (Change Advisory Board).
В нем вы в том числе должны указать все дополнительные действия, которые необходимо сделать для релиза. Здесь поможет уже вмерженный MR, но нужно будет уточнить данные именно для прода.

### 6.3 Работа с ошибками
- Ошибки сервиса располагаются в пакете ./errs/<serviceName>.
- При создании нового сервиса после сборки командой meroving build генерируются файлы для работы с ошибками.
- Для добавления новой ошибки:
    - Добавьте константу с кодом ошибки и описание в файле errs/<serviceName>/description.go.
    - Запустите команду:
```bash
meroving errs-gen -s <serviceName>
```
	- Дополните маппинг ошибок в функции <ServiceName>Errs().

Пример использования:
```bash
if err != nil {
    if ent.IsNotFound(err) {
        return nil, usersErrs.UsersErrs().NotFoundError()
    }
    return nil, err
}
```

### 6.4 Работа с proto-файлами
После добавления нового микросервиса и метода:
- Будет создан proto-файл:
    - specs/proto/<service_name>/<service_name>.proto
- При сборке (meroving build) будут сгенерированы:
    - specs/proto/<service_name>/<service_name>.pb.go – описание grpc-сервиса.
    - Файлы в директориях:
        - services/<name_service>/server/<endpoint>.go – обработчик grpc метода.
        - services/<name_service>/usecase/<endpoint>.go – бизнес-логика метода.
        - services/<name_service>/entity/<endpoint>.go – описание сущности.
        - services/<name_service>/tests/<endpoint>_test.go – базовый тест для метода.

### 6.5 Работа с кастомными переменными окружения
Большинств переменных окружения, при создании сервиса, будут сгенерированы meroving-ом.
Но, в некоторых случаях необходимо добавить кастомные переменные окружения, специфические для данного сервиса.
Для этого необходимо:
- В файле config/gateways/<name_service>/config.go добавить переменные в структуру Config.
- В тэгах структуры Config указать `env:"<имя_переменной>"`, чтобы сервис мог досоздать переменные окружения в Vault. ВНИМАНИЕ: тут указывается полное имя переменной с AppPrefix
- В сгенерированной функции ApplicationFromEnv() прописать логику считывания переменных из env(например, `loader.GetString("ключ_переменной")`). ВНИМАНИЕ: здесь ключ указывается уже БЕЗ префикса - его допишет viper

В целом логика вфглядит так: сервис подтягивает переменные окружения, прописанные в аппе. Потом идет в Vault-e и синхронизирует список переменных с ним, создавая(/удаляя?) переменные и подтягивая значения. Затем значения забиваются SetEnv() в окружение сервиса и снова вычитывается в Config.


## 7. Тестирование

### 7.1 Реализация тестов
- Для каждого нового метода генерируется базовый тест (happy path) в файле:
    - services/<name_service>/tests/<endpoint>_test.go
- Дополнительно:
    - Папка tests/mocks/ содержит моки провайдеров/клиентов.
    - Папка tests/fixtures/ содержит фикстуры для тестов.

### 7.2 Использование фикстур и golden files
- Фикстуры: Название файла соответствует имени структуры ответа с постфиксом Resp и расширением .json.
  Пример: ValidateCodeResp.json для структуры ValidateCode.
- Golden files: Файлы с запросом (req.json) и ответом (resp.json) хранятся в:
```bash
services/<name_service>/tests/goldenfiles/<name_test>/
```
- Для загрузки больших объёмов данных в Redis используйте метод:
```bash
s.LoadRedisFixture()
```
- Для работы с базой данных и s3 используйте прямой доступ к клиентам (s.db, s.s3).

### 7.3 Примеры использования в тестах
- Мокирование ответа с использованием fixtures:
```bash
s.mocks.GRPC.Otp.EXPECT().
    ValidateCode(gomock.Any(), gomock.Any(), gomock.Any()).
    Return(validateCodeResp, nil)
```

- Мокирование с использованием Go-структур:
```bash
s.mocks.GRPC.Keycloakproxy.EXPECT().CreateUser(gomock.Any(), gomock.Any(), gomock.Any()).
    Return(&keycloakproxy.CreateUserResp{
        Succeed: true,
        UserID:  "a3c72dbc-ac5a-4dc1-96bd-271a487d4a92",
    }, nil)
```

- Вызов метода без golden files:
```bash
resp, err := s.grpc.ConfirmLogin(ctx, &pb.ConfirmLoginReq{
    AttemptID: uuid.New().String(),
    Code:      "1111",
})
s.Require().Nil(resp)
s.Require().ErrorContains(err, reason.InvalidCode.Err())
```

- Вызов метода с использованием golden files:
```bash
_, err := s.CallGRPCMethod(s.grpc.HealthCheck)
s.Require().NoError(err)
```
### 8. Планировщик Chronos
- Для добавления Chronos-провайдера укажите в dependencies нужный сервис с опцией chronos в файле meroving.json:
```bash
{
  "name": "service",
  "type": "logic",
  "description": "Some service",
  "dependencies": [
    "vault",
    "chronos"
  ],
  "clients": {
    "grpc": []
  }
}
```

- После выполнения meroving build в директории services/<service_name>/usecase будет создан файл cron.go с маппингом cron-спецификаций к вызываемым функциям.
- Chronos использует следующие переменные окружения:
    - {SERVICE_NAME}_CHRONOS_TIME_ZONE – таймзона (например, Asia/Almaty, по умолчанию Local).
    - {SERVICE_NAME}_CHRONOS_SECONDS_REQUIRED – необходимость указания секунд в cron-спецификации (по умолчанию false).

## 9. Работа с шлюзом (gateway)
Шлюз представляет собой отдельный HTTP-сервис, выполняющий функции прокси для микросервисов.

### 9.1 Создание нового метода
- Реализуйте метод в спецификации Swagger, расположенной в папке specs/openapi/<gateway_name>.
- После выполнения meroving build будут сгенерированы файлы:
    - gateways/<gateway_name>/mappers/<method_name>.gen.go – маппинг из структур protobuf в структуры Swagger (требуется реализовать маппинг полей).
    - gateways/<gateway_name>/<method_name>.gen.go – реализация метода, включающая вызовы grpc и обработку ошибок.
    - gateways/<gateway_name>/openapi/ – сгенерированный HTTP-сервер из спецификации.

### 9.2 Связь метода на gateway с grpc-методами микросервиса
- В файле meroving.json в разделе gateways в параметре endpoints добавьте объект для связывания метода шлюза с методами микросервисов:
```bash
{
  "endpoints": [
    {
      "name": "health",
      "extra_config": {
        "rate": {
          "max_rate": 10,
          "every": "1s"
        }
      },
      "backend": [
        {
          "service": "users",
          "method": "HealthCheck"
        },
        {
          "service": "otp",
          "method": "HealthCheck"
        }
      ]
    }
  ]
}
```

- После повторной сборки (meroving build) вызов метода будет выполняться реальным обращением к микросервисам, а не возвращать фейковые данные.

### 9.3 Список текущих гейтвеев

### 9.3.1 MOBILE
#### Назначение
Главный шлюз для всех операций мобильного приложения розницы
#### Маппинг адреса
https://mobile.zamanbank.kz/api/v1/health
### 9.3.2 SME
#### Назначение
Главный шлюз для всех операций мобильного приложения МСБ
#### Маппинг адреса
https://sme.zamanbank.kz/api/v1/health
### 9.3.3 PAYMENTSGW
#### Назначение
Шлюз сервиса плавтежей(для получения коллбэков от astanaplat?)
#### Маппинг адреса
todo
### 9.3.4 CRMGW
#### Назначение
Шлюз бэкенда CRM zamanbank-a
#### Маппинг адреса
todo
### 9.3.5 DOCUMENTSGW
#### Назначение
Шлюз сервиса документов - получений выписок и т.д
#### Маппинг адреса
todo
### 9.3.6 COLLECTIONGW
#### Назначение
todo
#### Маппинг адреса
todo

## 10. Примеры реализации бизнес-функционала

Примеры шагов для реализации бизнес функционала будут добавлены позже (TBD).

## 11. Архитектурные решения
- Используется архитектура с разделением на gateway и microservices.
- В микросервисах применяется clean architecture с разделением на слои: entity, server, storage, usecase, test.
- Шлюз не содержит бизнес-логики, а агрегирует данные, вызывая grpc-методы микросервисов.
- Синхронное взаимодействие между микросервисами реализуется через grpc, асинхронное — через Kafka
- В микросервисах возвращаются ошибки из пакета reason с кодами и описаниями.
- Все подробные решения смотри в папке adrs

## 12. Общие правила разработки
- Изменения в файлах *.gen.go недопустимы — они генерируются автоматически.
- Перед каждым коммитом проверяйте код с помощью команд:
    - meroving imports
    - meroving fmt
    - meroving lint

## 13. Использование bridge сервисов с ETL паттерном

### 13.1 Обзор паттерна
Bridge сервисы – это микросервисы, отвечающие за интеграцию через отдельного клиента, реализованного в пакете `pkg`, для взаимодействия со сторонними сервисами. Основные особенности bridge сервисов:

- **Интеграция и трансляция запросов:**
Bridge сервис реализует grpc методы, которые используются другими микросервисами для реализации своей бизнес логики. Все входящие запросы транслируются во внешнюю интеграцию, после чего ответы сохраняются в MongoDB в сыром виде (формат JSON).
При разработке bridge сервисов вы должны учитывать бизнес логику, описанную аналитикой. Если сервис платный, либо аналитика четко указала это в требованиях, вы должны реализовать возможность получения сначала данных из Postgresql bridge сервиса и если данные отсутствуют, либо считаются устаревшими, должен быть сделан запрос в интеграцию.
Это поведение реализовывается дополнительными параметрами в grpc спецификации.

- **ETL процесс:**
После получения данных через Kafka bridge сервис должен:
- Извлечь (Extract) сырые данные из Kafka.
- Трансформировать (Transform) их в структурированный вид с учетом требований бизнес-логики.
- Загрузить (Load) структурированные данные в PostgreSQL для дальнейшего использования другими бизнес микросервисами.

- **Двунаправленная интеграция:**
При необходимости интеграция может сама присылать данные. Для этого следует реализовать Gateway, который принимает входящие запросы. Весь последующий процесс обработки данных остается неизменным.


### 13.2 Пошаговое описание ETL процесса

### 13.2.1 Получение данных из сторонней интеграции

1. **Запрос через grpc метод:**
При вызове grpc метода bridge сервис отправляет запрос через интеграционный клиент, реализованный в пакете `pkg`.

2. **Сохранение сырого ответа:**
Полученные данные в формате JSON сохраняются в MongoDB в сыром виде. Это обеспечивает возможность дальнейшей обработки данных без потерь оригинальной информации.

2. **Сборка проекта:**
   Выполните команду:
```bash
meroving build
```

### 13.2.3 Трансформация и загрузка данных в PostgreSQL
- Получение данных из Kafka:
Реализуйте в bridge сервисе механизм, который получает сырые данные из Kafka.
- Трансформация данных:
Преобразуйте полученные данные в структурированный вид. Для определения того, какие поля и структуры должны быть сохранены, обязательно согласуйте требования с аналитиками.
- Загрузка в PostgreSQL:


## 14.Среды исполнения исходного кода
### 14.1 Dev
Использует только моки
### 14.2 Stage
Частично использует моки
### 14.3 Preprod
Аналогичен продовой среде, предназначен для преднаката
### 14.4 Prod

## 15.Взаимодействие с QA
### 15.1 Тестирование на dev и stage. Принцип работы и настройки моков сервисов
Сервис моков - развенутьый в данный момент на отдельной машине zaman-logs сервис, который может сохранять ограниченные по времени фиксированные ответы.
Запрос для создания мока /jursearch/v1/external/send-request на 24 часа c выглядит так

```curl
curl --location 'https://pkb-pr.zaman.redmadrobot.com/save_with_time?user_id=5de6a1a3-7be3-42d6-9e12-174e297e0033&endpoint=%2Fjursearch%2Fv1%2Fexternal%2Fsend-request&method=POST&status_code=200&ttl=24h' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic авторизация, есть у QA' \
--data '{ %тело ответа% }'
```

База сервиса крутится в докере: на той же машине - так что посмотреть внутрь получится только в консольном режиме.
Обязательно убедитесь в правильности user_id - он может различаться на dev и на stage

## 16.Деплой кода в препрод и прод
### 16.1 Деплой на препрод
Деплой на препрод осуществляется созданием заявки на DO(DevOps) d https://jira.zaman.kz/. Там же указываюся все доп действия которые надо выполнить, такие как миграции и манипуляции с файлами шаблонов.
Девопсам передается пайплайн релизной версии, который они раскатывают. Т.е должна существовать такая ветка(сейчас это решается и может измениться, перепроверьте), порожденная от main, на которой будет выполнен meroving release --rc (--bump=minor - по необходимости повысить версию, а не версию релиз-кандидата)
### 16.2 Деплой на прод
Для деплоя на прод создается заявка формата
```
Заявка
1) Обязательное краткое описание изменения/бага/деффекта
   https://rmrkz.atlassian.net/browse/ZP-1555
   Выставить минимальную сумму кредита в справочнике
2) Обязательное указание Риска изменений (что будет, если изменение не перенести в прод)
  Если не перенести на прод то там будет другая минимальная сумма кредита
3) Обязательное указание влияние на доступность сервиса (будет ли простой процесса, либо простой смежного процесса, нужен ли перезапуск поды)
   0 влияния, точечная правка в бд
4) План установки / отката (указать версию для возврата на предыдущее состояние сервис, UNDO файл, если изменения в базе, компоненте)
  SQL скрипт для БД dictionary, указанный в описании задачи
5) Команда инициатор (Стримы)
  Credits Retail(указать актуальный)
6) version 0.31.1-rc-5(указать актуальную)
Исполнитель (указать исполнителя)
```



Этот документ описывает ключевые аспекты работы над проектом Zaman, начиная от правил работы с GitLab и заканчивая особенностями тестирования и интеграции микросервисов через gateway. Соблюдение описанных правил и стандартов позволит обеспечить стабильную разработку и быстрый выпуск изменений.
