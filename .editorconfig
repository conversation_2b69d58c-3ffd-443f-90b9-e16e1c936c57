root = true

[*] # <-- rules for all files
charset = utf-8
end_of_line = lf
ij_visual_guides = 119
indent_size = 2
indent_style = space
insert_final_newline = true
max_line_length = 119
tab_width = 4
trim_trailing_whitespace = true

[{go.mod,go.sum,*.go,*.tmpl}] # <-- rules only for Go's project files
indent_style = tab
indent_size = 4
max_line_length = 119

[Makefile]
indent_style = tab
indent_size = 4

[*.{js,json}]
indent_style = space
indent_size = 2

[*.{yml,yaml}]
indent_style = space
indent_size = 2

[*.sql]
indent_style = space
indent_size = 2